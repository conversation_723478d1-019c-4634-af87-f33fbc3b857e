<?php
/**
 * AstroGenix - Cron скрипт автоматического реинвестирования
 * Выполняется каждые 30 минут для обработки автореинвестирования
 */

if (php_sapi_name() !== 'cli') {
    die('Этот скрипт может быть запущен только из командной строки');
}

require_once dirname(__DIR__) . '/config/config.php';
require_once dirname(__DIR__) . '/classes/AutoReinvestment.php';

function logMessage($message) {
    $timestamp = date('Y-m-d H:i:s');
    $log_file = dirname(__DIR__) . '/logs/auto-reinvestment.log';
    
    $log_dir = dirname($log_file);
    if (!is_dir($log_dir)) {
        mkdir($log_dir, 0755, true);
    }
    
    file_put_contents($log_file, "[$timestamp] $message" . PHP_EOL, FILE_APPEND | LOCK_EX);
    echo "[$timestamp] $message" . PHP_EOL;
}

try {
    logMessage("Запуск автоматического реинвестирования");
    
    $database = new Database();
    $db = $database->getConnection();
    $auto_reinvestment = new AutoReinvestment($db);
    
    // Обработка автореинвестирования для всех пользователей
    $result = $auto_reinvestment->processAllUsersReinvestment();
    
    if ($result['success']) {
        logMessage("Обработка завершена успешно:");
        logMessage("- Обработано пользователей: {$result['processed']}");
        logMessage("- Успешных реинвестиций: {$result['successful']}");
        
        if (!empty($result['errors'])) {
            logMessage("Ошибки:");
            foreach ($result['errors'] as $error) {
                logMessage("  - $error");
            }
        }
        
        // Обновление статистики системы
        updateSystemStats($db, $result);
        
    } else {
        logMessage("ОШИБКА: " . $result['error']);
        exit(1);
    }
    
    logMessage("Автоматическое реинвестирование завершено");
    
} catch (Exception $e) {
    logMessage("КРИТИЧЕСКАЯ ОШИБКА: " . $e->getMessage());
    logMessage("Трассировка: " . $e->getTraceAsString());
    exit(1);
}

/**
 * Обновление системной статистики
 */
function updateSystemStats($db, $result) {
    try {
        // Получение текущей статистики
        $stats_query = "SELECT setting_value FROM system_settings WHERE setting_key = 'auto_reinvestment_stats'";
        $stats_stmt = $db->prepare($stats_query);
        $stats_stmt->execute();
        $current_stats = $stats_stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($current_stats) {
            $stats = json_decode($current_stats['setting_value'], true);
        } else {
            $stats = [
                'total_processed' => 0,
                'total_successful' => 0,
                'total_errors' => 0,
                'last_run' => null,
                'daily_stats' => []
            ];
        }
        
        // Обновление статистики
        $stats['total_processed'] += $result['processed'];
        $stats['total_successful'] += $result['successful'];
        $stats['total_errors'] += count($result['errors']);
        $stats['last_run'] = date('Y-m-d H:i:s');
        
        // Ежедневная статистика
        $today = date('Y-m-d');
        if (!isset($stats['daily_stats'][$today])) {
            $stats['daily_stats'][$today] = [
                'processed' => 0,
                'successful' => 0,
                'errors' => 0
            ];
        }
        
        $stats['daily_stats'][$today]['processed'] += $result['processed'];
        $stats['daily_stats'][$today]['successful'] += $result['successful'];
        $stats['daily_stats'][$today]['errors'] += count($result['errors']);
        
        // Очистка старых ежедневных статистик (оставляем только последние 30 дней)
        $cutoff_date = date('Y-m-d', strtotime('-30 days'));
        foreach ($stats['daily_stats'] as $date => $daily_stat) {
            if ($date < $cutoff_date) {
                unset($stats['daily_stats'][$date]);
            }
        }
        
        // Сохранение обновленной статистики
        $update_stats_query = "INSERT INTO system_settings 
                              (setting_key, setting_value, updated_at) 
                              VALUES 
                              ('auto_reinvestment_stats', :stats, NOW()) 
                              ON DUPLICATE KEY UPDATE 
                              setting_value = :stats, updated_at = NOW()";
        
        $update_stats_stmt = $db->prepare($update_stats_query);
        $update_stats_stmt->bindParam(':stats', json_encode($stats, JSON_UNESCAPED_UNICODE));
        $update_stats_stmt->execute();
        
        logMessage("Системная статистика обновлена");
        
    } catch (Exception $e) {
        logMessage("Ошибка обновления системной статистики: " . $e->getMessage());
    }
}
?>
