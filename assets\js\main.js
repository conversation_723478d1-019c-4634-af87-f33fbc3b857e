/**
 * AstroGenix - Основной JavaScript файл
 * Эко-майнинговая инвестиционная платформа
 */

document.addEventListener('DOMContentLoaded', function() {
    initializeMain();
});

function initializeMain() {
    // Проверка производительности устройства
    checkPerformance();

    // Инициализация прелоадера
    initPreloader();

    // Инициализация общих компонентов
    initCommonComponents();

    // Инициализация экологических счетчиков
    initEcoCounters();

    // Инициализация уведомлений
    initNotifications();

    // Инициализация ленивой загрузки изображений
    initLazyLoading();
}

// Прелоадер
function initPreloader() {
    const preloader = document.getElementById('preloader');
    if (!preloader) return;
    
    // Симуляция загрузки
    const progressBar = preloader.querySelector('.loading-progress');
    let progress = 0;
    
    const loadingInterval = setInterval(() => {
        progress += Math.random() * 15;
        if (progress >= 100) {
            progress = 100;
            clearInterval(loadingInterval);
            
            setTimeout(() => {
                preloader.style.opacity = '0';
                setTimeout(() => {
                    preloader.style.display = 'none';
                }, 500);
            }, 500);
        }
        
        if (progressBar) {
            progressBar.style.width = progress + '%';
        }
    }, 100);
}

// Общие компоненты
function initCommonComponents() {
    // Плавная прокрутка для якорных ссылок
    const anchorLinks = document.querySelectorAll('a[href^="#"]');
    anchorLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            const targetId = this.getAttribute('href').substring(1);
            const targetElement = document.getElementById(targetId);
            
            if (targetElement) {
                e.preventDefault();
                targetElement.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
    
    // Кнопка "Наверх"
    createBackToTopButton();
    
    // Обработка форм
    initFormHandlers();
}

// Кнопка "Наверх"
function createBackToTopButton() {
    const backToTop = document.createElement('button');
    backToTop.className = 'back-to-top';
    backToTop.innerHTML = '<i class="fas fa-arrow-up"></i>';
    backToTop.title = 'Наверх';
    
    backToTop.style.cssText = `
        position: fixed;
        bottom: 30px;
        right: 30px;
        width: 50px;
        height: 50px;
        background: var(--gradient-primary);
        color: white;
        border: none;
        border-radius: 50%;
        cursor: pointer;
        opacity: 0;
        visibility: hidden;
        transition: all 0.3s ease;
        z-index: 1000;
        box-shadow: 0 4px 12px rgba(46, 204, 113, 0.3);
    `;
    
    document.body.appendChild(backToTop);
    
    // Показ/скрытие кнопки при прокрутке
    window.addEventListener('scroll', () => {
        if (window.pageYOffset > 300) {
            backToTop.style.opacity = '1';
            backToTop.style.visibility = 'visible';
        } else {
            backToTop.style.opacity = '0';
            backToTop.style.visibility = 'hidden';
        }
    });
    
    // Прокрутка наверх
    backToTop.addEventListener('click', () => {
        window.scrollTo({
            top: 0,
            behavior: 'smooth'
        });
    });
}

// Обработчики форм
function initFormHandlers() {
    const forms = document.querySelectorAll('form');
    
    forms.forEach(form => {
        // Валидация в реальном времени
        const inputs = form.querySelectorAll('input, textarea, select');
        inputs.forEach(input => {
            input.addEventListener('blur', function() {
                validateInput(this);
            });
            
            input.addEventListener('input', function() {
                clearInputError(this);
            });
        });
        
        // Обработка отправки формы
        form.addEventListener('submit', function(e) {
            if (!validateForm(this)) {
                e.preventDefault();
            }
        });
    });
}

// Валидация поля ввода
function validateInput(input) {
    const value = input.value.trim();
    const type = input.type;
    const required = input.hasAttribute('required');
    
    // Очистка предыдущих ошибок
    clearInputError(input);
    
    // Проверка обязательных полей
    if (required && !value) {
        showInputError(input, 'Это поле обязательно для заполнения');
        return false;
    }
    
    // Проверка email
    if (type === 'email' && value && !isValidEmail(value)) {
        showInputError(input, 'Введите корректный email адрес');
        return false;
    }
    
    // Проверка пароля
    if (type === 'password' && value && value.length < 8) {
        showInputError(input, 'Пароль должен содержать минимум 8 символов');
        return false;
    }
    
    // Проверка телефона
    if (type === 'tel' && value && !isValidPhone(value)) {
        showInputError(input, 'Введите корректный номер телефона');
        return false;
    }
    
    return true;
}

// Валидация формы
function validateForm(form) {
    const inputs = form.querySelectorAll('input[required], textarea[required], select[required]');
    let isValid = true;
    
    inputs.forEach(input => {
        if (!validateInput(input)) {
            isValid = false;
        }
    });
    
    return isValid;
}

// Показ ошибки поля
function showInputError(input, message) {
    input.classList.add('error');
    
    const errorElement = document.createElement('div');
    errorElement.className = 'input-error';
    errorElement.textContent = message;
    
    input.parentNode.appendChild(errorElement);
}

// Очистка ошибки поля
function clearInputError(input) {
    input.classList.remove('error');
    
    const errorElement = input.parentNode.querySelector('.input-error');
    if (errorElement) {
        errorElement.remove();
    }
}

// Валидация email
function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

// Валидация телефона
function isValidPhone(phone) {
    const phoneRegex = /^\+?[1-9]\d{1,14}$/;
    return phoneRegex.test(phone.replace(/[\s\-\(\)]/g, ''));
}

// Экологические счетчики
function initEcoCounters() {
    const greenEnergyElement = document.getElementById('totalGreenEnergy');
    const treesElement = document.getElementById('treesEquivalent');
    
    if (greenEnergyElement || treesElement) {
        // Получение данных с сервера
        fetchEcoStats().then(stats => {
            if (greenEnergyElement) {
                animateCounter(greenEnergyElement, 0, stats.greenEnergy, 2000);
            }
            if (treesElement) {
                animateCounter(treesElement, 0, stats.trees, 2000);
            }
        });
    }
}

// Получение экологической статистики
async function fetchEcoStats() {
    try {
        // Здесь должен быть реальный API запрос
        // const response = await fetch('/api/eco-stats');
        // return await response.json();
        
        // Заглушка для демонстрации
        return {
            greenEnergy: 15420.5,
            trees: 1542
        };
    } catch (error) {
        console.error('Error fetching eco stats:', error);
        return { greenEnergy: 0, trees: 0 };
    }
}

// Анимация счетчика
function animateCounter(element, start, end, duration) {
    const startTime = performance.now();
    const isDecimal = end % 1 !== 0;
    
    function updateCounter(currentTime) {
        const elapsed = currentTime - startTime;
        const progress = Math.min(elapsed / duration, 1);
        
        const current = start + (end - start) * easeOutQuart(progress);
        
        if (isDecimal) {
            element.textContent = current.toFixed(1);
        } else {
            element.textContent = Math.floor(current).toLocaleString();
        }
        
        if (progress < 1) {
            requestAnimationFrame(updateCounter);
        }
    }
    
    requestAnimationFrame(updateCounter);
}

// Функция плавности анимации
function easeOutQuart(t) {
    return 1 - Math.pow(1 - t, 4);
}

// Система уведомлений
function initNotifications() {
    // Проверка поддержки уведомлений браузера
    if ('Notification' in window) {
        // Запрос разрешения на уведомления (опционально)
        // Notification.requestPermission();
    }
}

// Показ уведомления
function showNotification(message, type = 'info', duration = 5000) {
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
        <i class="fas fa-${getNotificationIcon(type)}"></i>
        <span>${message}</span>
        <button class="notification-close">
            <i class="fas fa-times"></i>
        </button>
    `;
    
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: ${getNotificationColor(type)};
        color: white;
        padding: 16px 20px;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        z-index: 9999;
        display: flex;
        align-items: center;
        gap: 12px;
        max-width: 400px;
        transform: translateX(100%);
        transition: transform 0.3s ease;
    `;
    
    document.body.appendChild(notification);
    
    // Анимация появления
    setTimeout(() => {
        notification.style.transform = 'translateX(0)';
    }, 100);
    
    // Обработчик закрытия
    const closeBtn = notification.querySelector('.notification-close');
    closeBtn.addEventListener('click', () => {
        hideNotification(notification);
    });
    
    // Автоматическое скрытие
    setTimeout(() => {
        hideNotification(notification);
    }, duration);
}

function getNotificationIcon(type) {
    const icons = {
        success: 'check-circle',
        error: 'exclamation-triangle',
        warning: 'exclamation-circle',
        info: 'info-circle'
    };
    return icons[type] || icons.info;
}

function getNotificationColor(type) {
    const colors = {
        success: '#28a745',
        error: '#dc3545',
        warning: '#ffc107',
        info: '#17a2b8'
    };
    return colors[type] || colors.info;
}

function hideNotification(notification) {
    notification.style.transform = 'translateX(100%)';
    setTimeout(() => {
        if (notification.parentElement) {
            notification.parentElement.removeChild(notification);
        }
    }, 300);
}

// Утилиты
function formatCurrency(amount) {
    return new Intl.NumberFormat('ru-RU', {
        style: 'currency',
        currency: 'RUB'
    }).format(amount);
}

function formatNumber(number, decimals = 0) {
    return new Intl.NumberFormat('ru-RU', {
        minimumFractionDigits: decimals,
        maximumFractionDigits: decimals
    }).format(number);
}

// Проверка производительности устройства
function checkPerformance() {
    // Проверка на слабые устройства
    const isLowEndDevice = navigator.hardwareConcurrency <= 2 ||
                          navigator.deviceMemory <= 2 ||
                          /Android.*Chrome\/[.0-9]*\s/.test(navigator.userAgent);

    if (isLowEndDevice) {
        document.documentElement.classList.add('low-performance');
        // Отключение сложных анимаций для слабых устройств
        const style = document.createElement('style');
        style.textContent = `
            .low-performance .eco-particles,
            .low-performance .energy-wave,
            .low-performance .floating-particle {
                display: none !important;
            }
            .low-performance * {
                animation-duration: 0.1s !important;
                transition-duration: 0.1s !important;
            }
        `;
        document.head.appendChild(style);
    }
}

// Ленивая загрузка изображений
function initLazyLoading() {
    if ('IntersectionObserver' in window) {
        const imageObserver = new IntersectionObserver((entries, observer) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const img = entry.target;
                    img.src = img.dataset.src;
                    img.classList.remove('lazy');
                    observer.unobserve(img);
                }
            });
        });

        const lazyImages = document.querySelectorAll('img[data-src]');
        lazyImages.forEach(img => imageObserver.observe(img));
    }
}

// Оптимизированная функция debounce
function debounce(func, wait, immediate) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            timeout = null;
            if (!immediate) func(...args);
        };
        const callNow = immediate && !timeout;
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
        if (callNow) func(...args);
    };
}

// Оптимизированная функция throttle
function throttle(func, limit) {
    let inThrottle;
    return function(...args) {
        if (!inThrottle) {
            func.apply(this, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    };
}

// Экспорт функций для глобального использования
window.AstroGenix = {
    showNotification,
    formatCurrency,
    formatNumber,
    validateInput,
    validateForm,
    debounce,
    throttle
};
