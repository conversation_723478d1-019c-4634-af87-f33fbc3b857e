-- Миграция для перехода с рублей на USDT
-- Дата: 29.06.2025
-- Описание: Обновление валютной системы с RUB на USDT

-- Начало транзакции
START TRANSACTION;

-- 1. Обновление инвестиционных пакетов
-- Удаляем старые пакеты
DELETE FROM investment_packages;

-- Добавляем новые пакеты с ценами в USDT
INSERT INTO investment_packages (name, description, min_amount, max_amount, daily_profit_percent, duration_days) VALUES
('Стартовый', 'Начальный пакет для новичков в эко-майнинге', 50.00, 499.00, 1.50, 30),
('Стандартный', 'Средний пакет с хорошей доходностью', 500.00, 1999.00, 2.50, 30),
('Премиум', 'Премиальный пакет с максимальной доходностью', 2000.00, 50000.00, 3.50, 30);

-- 2. Конвертация балансов пользователей (RUB -> USDT, примерно /100)
-- ВНИМАНИЕ: Это примерная конвертация! Используйте актуальный курс
UPDATE users SET 
    balance = ROUND(balance / 100, 2),
    total_invested = ROUND(total_invested / 100, 2),
    total_earned = ROUND(total_earned / 100, 2)
WHERE balance > 0 OR total_invested > 0 OR total_earned > 0;

-- 3. Конвертация сумм в транзакциях
UPDATE transactions SET 
    amount = ROUND(amount / 100, 2)
WHERE amount > 0;

-- 4. Конвертация сумм в инвестициях пользователей
UPDATE user_investments SET 
    amount = ROUND(amount / 100, 2),
    total_profit = ROUND(total_profit / 100, 2)
WHERE amount > 0;

-- 5. Конвертация реферальных комиссий
UPDATE referral_commissions SET 
    amount = ROUND(amount / 100, 2)
WHERE amount > 0;

-- 6. Обновление настроек системы
UPDATE system_settings SET value = 'USDT' WHERE setting_key = 'default_currency';

-- Если настройки валюты нет, добавляем её
INSERT IGNORE INTO system_settings (setting_key, setting_value, description) 
VALUES ('default_currency', 'USDT', 'Основная валюта платформы');

-- 7. Обновление статистики зеленой энергии (конвертируем в соответствующие единицы)
UPDATE system_settings SET 
    setting_value = ROUND(CAST(setting_value AS DECIMAL(15,2)) / 100, 2)
WHERE setting_key = 'total_green_energy' AND CAST(setting_value AS DECIMAL(15,2)) > 0;

-- Подтверждение транзакции
COMMIT;

-- Логирование миграции
INSERT INTO system_logs (log_type, message, created_at) 
VALUES ('migration', 'Currency migration from RUB to USDT completed successfully', NOW());

-- Вывод результата
SELECT 'Migration to USDT completed successfully!' as result;
