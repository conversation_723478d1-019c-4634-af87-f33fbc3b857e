<?php
/**
 * AstroGenix - Рейтинги и лидерборды
 * Система рейтингов пользователей по различным критериям
 */

session_start();
require_once 'config/config.php';
require_once 'classes/Leaderboards.php';

// Проверка авторизации
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit();
}

$database = new Database();
$db = $database->getConnection();
$leaderboards = new Leaderboards($db);

// Получение параметров
$current_type = $_GET['type'] ?? 'referrals';
$limit = intval($_GET['limit'] ?? 50);

// Получение данных
$ranking_types = $leaderboards->getRankingTypes();
$current_leaderboard = $leaderboards->getLeaderboard($current_type, $limit, $_SESSION['user_id']);
$user_rankings = $leaderboards->getUserRanking($_SESSION['user_id']);
$top_users_overall = $leaderboards->getTopUsersOverall();

$page_title = 'Рейтинги и лидерборды - AstroGenix';
?>
<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?></title>
    <meta name="description" content="Рейтинги и лидерборды пользователей платформы AstroGenix">
    
    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="assets/images/favicon.svg">
    
    <!-- Стили -->
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="assets/css/leaderboards.css">
    <link rel="stylesheet" href="assets/css/animations.css">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <?php include 'includes/header.php'; ?>

    <main class="main-content">
        <!-- Hero секция -->
        <section class="hero-section leaderboards-hero">
            <div class="container">
                <div class="hero-content animate-fade-in">
                    <h1 class="hero-title">
                        Рейтинги и <span class="gradient-text">лидерборды</span>
                    </h1>
                    <p class="hero-subtitle">
                        Соревнуйтесь с другими пользователями и поднимайтесь в рейтингах по различным категориям. 
                        Зарабатывайте награды и достижения за активность на платформе.
                    </p>
                </div>
            </div>
        </section>

        <div class="container">
            <!-- Мои позиции в рейтингах -->
            <section class="section my-rankings">
                <div class="section-header">
                    <h2 class="section-title">Мои позиции</h2>
                </div>
                
                <div class="rankings-grid">
                    <?php foreach ($user_rankings as $ranking): ?>
                        <div class="ranking-card animate-on-scroll">
                            <div class="ranking-icon" style="background: <?php echo $ranking['display_settings']['color']; ?>">
                                <i class="<?php echo $ranking['display_settings']['icon']; ?>"></i>
                            </div>
                            <div class="ranking-content">
                                <div class="ranking-title"><?php echo $ranking['display_settings']['title']; ?></div>
                                <div class="ranking-position">
                                    <span class="position">#<?php echo $ranking['current_rank']; ?></span>
                                    <?php if ($ranking['rank_change'] != 0): ?>
                                        <span class="change <?php echo $ranking['rank_change'] > 0 ? 'up' : 'down'; ?>">
                                            <i class="fas fa-arrow-<?php echo $ranking['rank_change'] > 0 ? 'up' : 'down'; ?>"></i>
                                            <?php echo abs($ranking['rank_change']); ?>
                                        </span>
                                    <?php endif; ?>
                                </div>
                                <div class="ranking-score"><?php echo number_format($ranking['current_score'], 2); ?> очков</div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            </section>

            <!-- Топ пользователи -->
            <section class="section top-users-overall">
                <div class="section-header">
                    <h2 class="section-title">Топ пользователи платформы</h2>
                    <p class="section-subtitle">Лучшие пользователи по всем категориям</p>
                </div>
                
                <div class="top-users-list">
                    <?php foreach ($top_users_overall as $index => $user): ?>
                        <div class="top-user-item animate-on-scroll" style="animation-delay: <?php echo $index * 0.1; ?>s">
                            <div class="user-rank">
                                <?php if ($index < 3): ?>
                                    <div class="medal medal-<?php echo ['gold', 'silver', 'bronze'][$index]; ?>">
                                        <i class="fas fa-medal"></i>
                                    </div>
                                <?php else: ?>
                                    <span class="rank-number"><?php echo $index + 1; ?></span>
                                <?php endif; ?>
                            </div>
                            <div class="user-avatar">
                                <?php if ($user['avatar']): ?>
                                    <img src="<?php echo htmlspecialchars($user['avatar']); ?>" alt="Avatar">
                                <?php else: ?>
                                    <div class="avatar-placeholder">
                                        <i class="fas fa-user"></i>
                                    </div>
                                <?php endif; ?>
                            </div>
                            <div class="user-info">
                                <div class="username"><?php echo htmlspecialchars($user['username']); ?></div>
                                <div class="user-stats">
                                    <span class="stat">
                                        <i class="fas fa-trophy"></i>
                                        <?php echo $user['top10_count']; ?> топ-10
                                    </span>
                                    <span class="stat">
                                        <i class="fas fa-chart-line"></i>
                                        Ср. ранг: <?php echo round($user['avg_rank']); ?>
                                    </span>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            </section>

            <!-- Категории рейтингов -->
            <section class="section ranking-categories">
                <div class="section-header">
                    <h2 class="section-title">Категории рейтингов</h2>
                </div>
                
                <div class="categories-tabs">
                    <?php foreach ($ranking_types as $type): ?>
                        <button class="category-tab <?php echo $current_type === $type['ranking_type'] ? 'active' : ''; ?>"
                                data-type="<?php echo $type['ranking_type']; ?>"
                                style="--category-color: <?php echo $type['display_settings']['color']; ?>">
                            <i class="<?php echo $type['display_settings']['icon']; ?>"></i>
                            <span><?php echo $type['display_settings']['title']; ?></span>
                        </button>
                    <?php endforeach; ?>
                </div>
            </section>

            <!-- Лидерборд -->
            <section class="section current-leaderboard">
                <div class="leaderboard-header">
                    <?php 
                    $current_type_data = array_filter($ranking_types, function($type) use ($current_type) {
                        return $type['ranking_type'] === $current_type;
                    });
                    $current_type_data = reset($current_type_data);
                    ?>
                    <h2 class="leaderboard-title">
                        <i class="<?php echo $current_type_data['display_settings']['icon']; ?>" 
                           style="color: <?php echo $current_type_data['display_settings']['color']; ?>"></i>
                        <?php echo $current_type_data['display_settings']['title']; ?>
                    </h2>
                    
                    <div class="leaderboard-controls">
                        <select id="limit-select" class="form-control">
                            <option value="25" <?php echo $limit == 25 ? 'selected' : ''; ?>>Топ 25</option>
                            <option value="50" <?php echo $limit == 50 ? 'selected' : ''; ?>>Топ 50</option>
                            <option value="100" <?php echo $limit == 100 ? 'selected' : ''; ?>>Топ 100</option>
                        </select>
                        
                        <button class="btn btn-secondary" id="refresh-leaderboard">
                            <i class="fas fa-sync-alt"></i>
                            Обновить
                        </button>
                    </div>
                </div>
                
                <div class="leaderboard-container">
                    <div class="leaderboard-table">
                        <div class="table-header">
                            <div class="header-rank">Ранг</div>
                            <div class="header-user">Пользователь</div>
                            <div class="header-score">Очки</div>
                            <div class="header-change">Изменение</div>
                            <div class="header-since">На платформе</div>
                        </div>
                        
                        <div class="table-body" id="leaderboard-body">
                            <?php foreach ($current_leaderboard as $entry): ?>
                                <div class="leaderboard-row <?php echo $entry['is_current_user'] ? 'current-user' : ''; ?> animate-on-scroll">
                                    <div class="row-rank">
                                        <?php if ($entry['current_rank'] <= 3): ?>
                                            <div class="medal medal-<?php echo ['', 'gold', 'silver', 'bronze'][$entry['current_rank']]; ?>">
                                                <i class="fas fa-medal"></i>
                                                <span class="medal-number"><?php echo $entry['current_rank']; ?></span>
                                            </div>
                                        <?php else: ?>
                                            <span class="rank-number"><?php echo $entry['current_rank']; ?></span>
                                        <?php endif; ?>
                                    </div>
                                    
                                    <div class="row-user">
                                        <div class="user-avatar">
                                            <?php if ($entry['avatar']): ?>
                                                <img src="<?php echo htmlspecialchars($entry['avatar']); ?>" alt="Avatar">
                                            <?php else: ?>
                                                <div class="avatar-placeholder">
                                                    <i class="fas fa-user"></i>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                        <div class="user-details">
                                            <div class="username">
                                                <?php echo htmlspecialchars($entry['username']); ?>
                                                <?php if ($entry['is_current_user']): ?>
                                                    <span class="you-badge">Вы</span>
                                                <?php endif; ?>
                                            </div>
                                            <div class="user-since">
                                                С <?php echo date('M Y', strtotime($entry['user_since'])); ?>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="row-score">
                                        <span class="score-value"><?php echo number_format($entry['current_score'], 2); ?></span>
                                        <span class="score-label">очков</span>
                                    </div>
                                    
                                    <div class="row-change">
                                        <?php if ($entry['rank_change'] > 0): ?>
                                            <span class="change up">
                                                <i class="fas fa-arrow-up"></i>
                                                +<?php echo $entry['rank_change']; ?>
                                            </span>
                                        <?php elseif ($entry['rank_change'] < 0): ?>
                                            <span class="change down">
                                                <i class="fas fa-arrow-down"></i>
                                                <?php echo $entry['rank_change']; ?>
                                            </span>
                                        <?php else: ?>
                                            <span class="change same">
                                                <i class="fas fa-minus"></i>
                                                0
                                            </span>
                                        <?php endif; ?>
                                    </div>
                                    
                                    <div class="row-since">
                                        <?php 
                                        $days = (time() - strtotime($entry['user_since'])) / (60 * 60 * 24);
                                        if ($days < 30) {
                                            echo round($days) . ' дн.';
                                        } elseif ($days < 365) {
                                            echo round($days / 30) . ' мес.';
                                        } else {
                                            echo round($days / 365, 1) . ' г.';
                                        }
                                        ?>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Информация о рейтингах -->
            <section class="section ranking-info">
                <div class="section-header">
                    <h2 class="section-title">Как работают рейтинги</h2>
                </div>
                
                <div class="info-grid">
                    <div class="info-card">
                        <div class="info-icon">
                            <i class="fas fa-users" style="color: #22c55e;"></i>
                        </div>
                        <div class="info-content">
                            <h3>Рейтинг рефералов</h3>
                            <p>Основан на количестве приглашенных пользователей и заработке с реферальной программы. 
                               За каждого прямого реферала начисляется 10 очков, плюс заработок в USDT.</p>
                        </div>
                    </div>
                    
                    <div class="info-card">
                        <div class="info-icon">
                            <i class="fas fa-chart-line" style="color: #3b82f6;"></i>
                        </div>
                        <div class="info-content">
                            <h3>Рейтинг инвесторов</h3>
                            <p>Учитывает общую сумму инвестиций, количество активных инвестиций (100 очков за каждую) 
                               и разнообразие пакетов (50 очков за каждый уникальный пакет).</p>
                        </div>
                    </div>
                    
                    <div class="info-card">
                        <div class="info-icon">
                            <i class="fas fa-coins" style="color: #f59e0b;"></i>
                        </div>
                        <div class="info-content">
                            <h3>Рейтинг по прибыли</h3>
                            <p>Основан на общей заработанной прибыли, текущем ежедневном доходе (удваивается) 
                               и бонусе за ROI (10 очков за каждый процент).</p>
                        </div>
                    </div>
                    
                    <div class="info-card">
                        <div class="info-icon">
                            <i class="fas fa-fire" style="color: #ef4444;"></i>
                        </div>
                        <div class="info-content">
                            <h3>Рейтинг активности</h3>
                            <p>Учитывает количество входов в систему (10 очков за каждый), 
                               количество транзакций и инвестиций (5 очков за каждую).</p>
                        </div>
                    </div>
                    
                    <div class="info-card">
                        <div class="info-icon">
                            <i class="fas fa-leaf" style="color: #16a34a;"></i>
                        </div>
                        <div class="info-content">
                            <h3>Эко-рейтинг</h3>
                            <p>Основан на вкладе в экологию: выработанная зеленая энергия, 
                               сохраненный CO₂ (10 очков за кг) и посаженные деревья (50 очков за дерево).</p>
                        </div>
                    </div>
                </div>
            </section>
        </div>
    </main>

    <?php include 'includes/footer.php'; ?>

    <!-- JavaScript -->
    <script>
        // Передача данных в JavaScript
        window.leaderboardData = {
            currentType: '<?php echo $current_type; ?>',
            currentLimit: <?php echo $limit; ?>
        };
    </script>
    <script src="assets/js/main.js"></script>
    <script src="assets/js/leaderboards.js"></script>
</body>
</html>
