<?php
/**
 * AstroGenix - Генератор фейковых данных
 * Класс для создания реалистичных пользователей и активности
 */

class FakeDataGenerator {
    private $db;
    
    // Массивы данных для генерации
    private $first_names_male = [
        'Александр', 'Дмитрий', 'Максим', 'Сергей', 'Андрей', 'Алексей', 'Артем', 'Илья', 'Кирилл', 'Михаил',
        'Никита', 'Матвей', 'Роман', 'Егор', 'Арсений', 'Иван', 'Денис', 'Евгений', 'Данил', 'Тимур',
        'Владислав', 'Игорь', 'Владимир', 'Павел', 'Руслан', 'Марк', 'Лев', 'Константин', 'Богдан', 'Елисей'
    ];
    
    private $first_names_female = [
        'София', 'Мария', 'Анна', 'Виктория', 'Анастасия', 'Милана', 'Полина', 'Алиса', 'Елизавета', 'Екатерина',
        'Дарья', 'Арина', 'Кира', 'Алина', 'Ксения', 'Вероника', 'Валерия', 'Ульяна', 'Ева', 'Таисия',
        'Варвара', 'Амелия', 'Злата', 'Мирослава', 'Ангелина', 'Маргарита', 'Диана', 'Василиса', 'Стефания', 'Николь'
    ];
    
    private $last_names = [
        'Иванов', 'Петров', 'Сидоров', 'Смирнов', 'Кузнецов', 'Попов', 'Васильев', 'Соколов', 'Михайлов', 'Новиков',
        'Федоров', 'Морозов', 'Волков', 'Алексеев', 'Лебедев', 'Семенов', 'Егоров', 'Павлов', 'Козлов', 'Степанов',
        'Николаев', 'Орлов', 'Андреев', 'Макаров', 'Никитин', 'Захаров', 'Зайцев', 'Соловьев', 'Борисов', 'Яковлев'
    ];
    
    private $countries = [
        'Россия', 'Украина', 'Беларусь', 'Казахстан', 'Узбекистан', 'Азербайджан', 'Грузия', 'Армения', 'Молдова', 'Киргизия',
        'Таджикистан', 'Туркменистан', 'Латвия', 'Литва', 'Эстония', 'Германия', 'Польша', 'Чехия', 'Словакия', 'Венгрия'
    ];
    
    private $cities = [
        'Москва', 'Санкт-Петербург', 'Новосибирск', 'Екатеринбург', 'Казань', 'Нижний Новгород', 'Челябинск', 'Самара', 'Омск', 'Ростов-на-Дону',
        'Уфа', 'Красноярск', 'Воронеж', 'Пермь', 'Волгоград', 'Краснодар', 'Саратов', 'Тюмень', 'Тольятти', 'Ижевск',
        'Киев', 'Харьков', 'Одесса', 'Днепр', 'Львов', 'Минск', 'Алматы', 'Ташкент', 'Баку', 'Тбилиси'
    ];
    
    private $crypto_addresses = [
        'bitcoin' => ['**********************************', '**********************************', '**********************************'],
        'ethereum' => ['******************************************', '0x8ba1f109551bD432803012645Hac136c22C501e5', '******************************************'],
        'usdt_trc20' => ['TLa2f6VPqDgRE67v1736s7bJ8Ray5wYjU7', 'TG3XXyExBkPp9nzdajDZsozEu4BkaSJozs', 'TKHuVq1oKVruCGLvqVexFs6dawKv6fQgFs'],
        'usdt_erc20' => ['******************************************', '******************************************', '******************************************']
    ];
    
    public function __construct($database) {
        $this->db = $database;
    }
    
    /**
     * Генерация случайного имени
     */
    public function generateName($gender = null) {
        if ($gender === null) {
            $gender = rand(0, 1) ? 'male' : 'female';
        }
        
        $first_name = $gender === 'male' ? 
            $this->first_names_male[array_rand($this->first_names_male)] : 
            $this->first_names_female[array_rand($this->first_names_female)];
        
        $last_name = $this->last_names[array_rand($this->last_names)];
        
        // Склонение фамилии для женщин
        if ($gender === 'female' && substr($last_name, -2) === 'ов') {
            $last_name = substr($last_name, 0, -2) . 'ова';
        } elseif ($gender === 'female' && substr($last_name, -2) === 'ин') {
            $last_name = substr($last_name, 0, -2) . 'ина';
        } elseif ($gender === 'female' && substr($last_name, -2) === 'ев') {
            $last_name = substr($last_name, 0, -2) . 'ева';
        }
        
        return [$first_name, $last_name, $gender];
    }
    
    /**
     * Генерация email адреса
     */
    public function generateEmail($first_name, $last_name) {
        $domains = ['gmail.com', 'yandex.ru', 'mail.ru', 'outlook.com', 'yahoo.com', 'hotmail.com'];
        $domain = $domains[array_rand($domains)];
        
        $transliteration = [
            'а' => 'a', 'б' => 'b', 'в' => 'v', 'г' => 'g', 'д' => 'd', 'е' => 'e', 'ё' => 'yo',
            'ж' => 'zh', 'з' => 'z', 'и' => 'i', 'й' => 'y', 'к' => 'k', 'л' => 'l', 'м' => 'm',
            'н' => 'n', 'о' => 'o', 'п' => 'p', 'р' => 'r', 'с' => 's', 'т' => 't', 'у' => 'u',
            'ф' => 'f', 'х' => 'h', 'ц' => 'ts', 'ч' => 'ch', 'ш' => 'sh', 'щ' => 'sch',
            'ъ' => '', 'ы' => 'y', 'ь' => '', 'э' => 'e', 'ю' => 'yu', 'я' => 'ya'
        ];
        
        $first_translit = strtr(mb_strtolower($first_name), $transliteration);
        $last_translit = strtr(mb_strtolower($last_name), $transliteration);
        
        $patterns = [
            $first_translit . '.' . $last_translit,
            $first_translit . '_' . $last_translit,
            $first_translit . $last_translit,
            substr($first_translit, 0, 1) . '.' . $last_translit,
            $first_translit . rand(10, 99),
            $last_translit . '.' . $first_translit
        ];
        
        $username = $patterns[array_rand($patterns)];
        return $username . '@' . $domain;
    }
    
    /**
     * Генерация username
     */
    public function generateUsername($first_name, $last_name) {
        $transliteration = [
            'а' => 'a', 'б' => 'b', 'в' => 'v', 'г' => 'g', 'д' => 'd', 'е' => 'e', 'ё' => 'yo',
            'ж' => 'zh', 'з' => 'z', 'и' => 'i', 'й' => 'y', 'к' => 'k', 'л' => 'l', 'м' => 'm',
            'н' => 'n', 'о' => 'o', 'п' => 'p', 'р' => 'r', 'с' => 's', 'т' => 't', 'у' => 'u',
            'ф' => 'f', 'х' => 'h', 'ц' => 'ts', 'ч' => 'ch', 'ш' => 'sh', 'щ' => 'sch',
            'ъ' => '', 'ы' => 'y', 'ь' => '', 'э' => 'e', 'ю' => 'yu', 'я' => 'ya'
        ];
        
        $first_translit = strtr(mb_strtolower($first_name), $transliteration);
        $last_translit = strtr(mb_strtolower($last_name), $transliteration);
        
        $patterns = [
            $first_translit . '_' . $last_translit,
            $first_translit . $last_translit,
            substr($first_translit, 0, 1) . $last_translit,
            $first_translit . rand(100, 999),
            $last_translit . rand(10, 99),
            'user_' . $first_translit,
            $first_translit . '_' . rand(2020, 2024)
        ];
        
        return $patterns[array_rand($patterns)];
    }
    
    /**
     * Генерация телефона
     */
    public function generatePhone() {
        $codes = ['+7', '+380', '+375', '+7', '+998', '+994', '+995', '+374', '+373', '+996'];
        $code = $codes[array_rand($codes)];
        
        if ($code === '+7') {
            // Российские номера
            $operators = ['903', '905', '906', '909', '951', '952', '953', '960', '961', '962', '963', '964', '965', '966', '967', '968'];
            $operator = $operators[array_rand($operators)];
            return $code . $operator . rand(1000000, 9999999);
        } else {
            // Другие страны
            return $code . rand(100000000, 999999999);
        }
    }
    
    /**
     * Генерация криптовалютного адреса
     */
    public function generateCryptoAddress($type = null) {
        if ($type === null) {
            $types = array_keys($this->crypto_addresses);
            $type = $types[array_rand($types)];
        }
        
        if (isset($this->crypto_addresses[$type])) {
            return $this->crypto_addresses[$type][array_rand($this->crypto_addresses[$type])];
        }
        
        // Генерация случайного адреса если тип не найден
        return 'fake_' . $type . '_' . bin2hex(random_bytes(16));
    }
    
    /**
     * Генерация реалистичного баланса
     */
    public function generateBalance($type = 'random') {
        switch ($type) {
            case 'newbie':
                return rand(0, 50);
            case 'active':
                return rand(50, 500);
            case 'vip':
                return rand(500, 5000);
            case 'whale':
                return rand(5000, 50000);
            default:
                $types = ['newbie', 'active', 'vip', 'whale'];
                $weights = [50, 30, 15, 5]; // Вероятности в процентах
                
                $random = rand(1, 100);
                $cumulative = 0;
                
                for ($i = 0; $i < count($types); $i++) {
                    $cumulative += $weights[$i];
                    if ($random <= $cumulative) {
                        return $this->generateBalance($types[$i]);
                    }
                }
                
                return $this->generateBalance('newbie');
        }
    }
    
    /**
     * Генерация даты регистрации
     */
    public function generateRegistrationDate() {
        $start = strtotime('-2 years');
        $end = time();
        $random_timestamp = rand($start, $end);
        
        return date('Y-m-d H:i:s', $random_timestamp);
    }
    
    /**
     * Генерация даты последнего входа
     */
    public function generateLastLogin($registration_date) {
        $reg_timestamp = strtotime($registration_date);
        $now = time();
        
        // 80% пользователей заходили в последние 30 дней
        if (rand(1, 100) <= 80) {
            $start = strtotime('-30 days');
            $end = $now;
        } else {
            $start = $reg_timestamp;
            $end = $now;
        }
        
        $random_timestamp = rand($start, $end);
        return date('Y-m-d H:i:s', $random_timestamp);
    }
    
    /**
     * Создание фейкового пользователя
     */
    public function createFakeUser($options = []) {
        try {
            // Генерация базовых данных
            list($first_name, $last_name, $gender) = $this->generateName($options['gender'] ?? null);
            $email = $this->generateEmail($first_name, $last_name);
            $username = $this->generateUsername($first_name, $last_name);
            
            // Проверка уникальности email и username
            $check_query = "SELECT COUNT(*) FROM users WHERE email = :email OR username = :username";
            $check_stmt = $this->db->prepare($check_query);
            $check_stmt->bindParam(':email', $email);
            $check_stmt->bindParam(':username', $username);
            $check_stmt->execute();
            
            if ($check_stmt->fetchColumn() > 0) {
                // Если пользователь уже существует, добавляем случайные цифры
                $email = explode('@', $email)[0] . rand(100, 999) . '@' . explode('@', $email)[1];
                $username = $username . rand(100, 999);
            }
            
            // Генерация остальных данных
            $balance = $this->generateBalance($options['balance_type'] ?? 'random');
            $country = $this->countries[array_rand($this->countries)];
            $phone = $this->generatePhone();
            $registration_date = $this->generateRegistrationDate();
            $last_login = $this->generateLastLogin($registration_date);
            
            // Создание пользователя
            $password_hash = password_hash('password123', PASSWORD_DEFAULT);
            
            $insert_query = "INSERT INTO users 
                            (username, email, password, first_name, last_name, balance, country, phone, 
                             is_active, email_verified, created_at, last_login, referral_code) 
                            VALUES 
                            (:username, :email, :password, :first_name, :last_name, :balance, :country, :phone, 
                             1, 1, :created_at, :last_login, :referral_code)";
            
            $referral_code = strtoupper(substr(md5($username . time()), 0, 8));
            
            $insert_stmt = $this->db->prepare($insert_query);
            $insert_stmt->bindParam(':username', $username);
            $insert_stmt->bindParam(':email', $email);
            $insert_stmt->bindParam(':password', $password_hash);
            $insert_stmt->bindParam(':first_name', $first_name);
            $insert_stmt->bindParam(':last_name', $last_name);
            $insert_stmt->bindParam(':balance', $balance);
            $insert_stmt->bindParam(':country', $country);
            $insert_stmt->bindParam(':phone', $phone);
            $insert_stmt->bindParam(':created_at', $registration_date);
            $insert_stmt->bindParam(':last_login', $last_login);
            $insert_stmt->bindParam(':referral_code', $referral_code);
            
            if ($insert_stmt->execute()) {
                $user_id = $this->db->lastInsertId();
                
                // Создание записи в green_energy_stats
                $this->createGreenEnergyStats($user_id);
                
                return [
                    'id' => $user_id,
                    'username' => $username,
                    'email' => $email,
                    'first_name' => $first_name,
                    'last_name' => $last_name,
                    'balance' => $balance,
                    'country' => $country,
                    'phone' => $phone,
                    'created_at' => $registration_date,
                    'last_login' => $last_login,
                    'referral_code' => $referral_code
                ];
            }
            
            return false;
        } catch (Exception $e) {
            error_log("Create fake user error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Создание записи экологической статистики
     */
    private function createGreenEnergyStats($user_id) {
        try {
            $energy_generated = rand(0, 1000);
            $co2_saved = round($energy_generated * 0.4, 2);
            $trees_planted = rand(0, 50);
            
            $stats_query = "INSERT INTO green_energy_stats 
                           (user_id, energy_generated, co2_saved, trees_planted) 
                           VALUES 
                           (:user_id, :energy_generated, :co2_saved, :trees_planted)";
            
            $stats_stmt = $this->db->prepare($stats_query);
            $stats_stmt->bindParam(':user_id', $user_id, PDO::PARAM_INT);
            $stats_stmt->bindParam(':energy_generated', $energy_generated);
            $stats_stmt->bindParam(':co2_saved', $co2_saved);
            $stats_stmt->bindParam(':trees_planted', $trees_planted, PDO::PARAM_INT);
            
            return $stats_stmt->execute();
        } catch (Exception $e) {
            error_log("Create green energy stats error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Массовое создание пользователей
     */
    public function createMultipleUsers($count, $options = []) {
        $created_users = [];
        $errors = [];
        
        for ($i = 0; $i < $count; $i++) {
            $user = $this->createFakeUser($options);
            
            if ($user) {
                $created_users[] = $user;
            } else {
                $errors[] = "Ошибка создания пользователя #" . ($i + 1);
            }
            
            // Небольшая задержка для избежания дублирования
            usleep(10000); // 0.01 секунды
        }
        
        return [
            'created' => $created_users,
            'errors' => $errors,
            'total_created' => count($created_users),
            'total_errors' => count($errors)
        ];
    }

    /**
     * Создание реалистичной инвестиции
     */
    public function createFakeInvestment($user_id = null, $package_id = null, $options = []) {
        try {
            // Если пользователь не указан, выбираем случайного
            if (!$user_id) {
                $user_query = "SELECT id, balance FROM users WHERE is_active = 1 AND balance >= 50 ORDER BY RAND() LIMIT 1";
                $user_stmt = $this->db->prepare($user_query);
                $user_stmt->execute();
                $user = $user_stmt->fetch(PDO::FETCH_ASSOC);

                if (!$user) {
                    return false;
                }

                $user_id = $user['id'];
                $user_balance = $user['balance'];
            } else {
                // Получаем баланс указанного пользователя
                $balance_query = "SELECT balance FROM users WHERE id = :id AND is_active = 1";
                $balance_stmt = $this->db->prepare($balance_query);
                $balance_stmt->bindParam(':id', $user_id, PDO::PARAM_INT);
                $balance_stmt->execute();
                $balance_result = $balance_stmt->fetch(PDO::FETCH_ASSOC);

                if (!$balance_result) {
                    return false;
                }

                $user_balance = $balance_result['balance'];
            }

            // Если пакет не указан, выбираем подходящий
            if (!$package_id) {
                $package_query = "SELECT * FROM investment_packages WHERE is_active = 1 AND min_amount <= :balance ORDER BY RAND() LIMIT 1";
                $package_stmt = $this->db->prepare($package_query);
                $package_stmt->bindParam(':balance', $user_balance);
                $package_stmt->execute();
                $package = $package_stmt->fetch(PDO::FETCH_ASSOC);

                if (!$package) {
                    return false;
                }
            } else {
                // Получаем указанный пакет
                $package_query = "SELECT * FROM investment_packages WHERE id = :id AND is_active = 1";
                $package_stmt = $this->db->prepare($package_query);
                $package_stmt->bindParam(':id', $package_id, PDO::PARAM_INT);
                $package_stmt->execute();
                $package = $package_stmt->fetch(PDO::FETCH_ASSOC);

                if (!$package) {
                    return false;
                }
            }

            // Генерация суммы инвестиции
            $min_amount = max($package['min_amount'], 50);
            $max_amount = min($package['max_amount'], $user_balance);

            if ($min_amount > $max_amount) {
                return false; // Недостаточно средств
            }

            // Различные стратегии выбора суммы
            $strategy = $options['amount_strategy'] ?? 'random';

            switch ($strategy) {
                case 'conservative':
                    $amount = rand($min_amount, $min_amount + ($max_amount - $min_amount) * 0.3);
                    break;
                case 'aggressive':
                    $amount = rand($min_amount + ($max_amount - $min_amount) * 0.7, $max_amount);
                    break;
                case 'moderate':
                    $amount = rand($min_amount + ($max_amount - $min_amount) * 0.3, $min_amount + ($max_amount - $min_amount) * 0.7);
                    break;
                default:
                    $amount = rand($min_amount, $max_amount);
            }

            // Округление до красивых чисел
            if ($amount >= 1000) {
                $amount = round($amount / 100) * 100; // Округление до сотен
            } elseif ($amount >= 100) {
                $amount = round($amount / 50) * 50; // Округление до 50
            } else {
                $amount = round($amount / 10) * 10; // Округление до десятков
            }

            // Расчет параметров инвестиции
            $daily_profit = ($amount * $package['daily_profit_percent']) / 100;

            // Генерация дат
            $start_date = $options['start_date'] ?? date('Y-m-d');
            $end_date = date('Y-m-d', strtotime($start_date . " +{$package['duration_days']} days"));

            // Создание инвестиции
            $insert_query = "INSERT INTO user_investments
                            (user_id, package_id, amount, daily_profit, start_date, end_date, status, created_at)
                            VALUES
                            (:user_id, :package_id, :amount, :daily_profit, :start_date, :end_date, 'active', NOW())";

            $insert_stmt = $this->db->prepare($insert_query);
            $insert_stmt->bindParam(':user_id', $user_id, PDO::PARAM_INT);
            $insert_stmt->bindParam(':package_id', $package['id'], PDO::PARAM_INT);
            $insert_stmt->bindParam(':amount', $amount);
            $insert_stmt->bindParam(':daily_profit', $daily_profit);
            $insert_stmt->bindParam(':start_date', $start_date);
            $insert_stmt->bindParam(':end_date', $end_date);

            if ($insert_stmt->execute()) {
                $investment_id = $this->db->lastInsertId();

                // Списание суммы с баланса пользователя
                $update_balance_query = "UPDATE users SET balance = balance - :amount, total_invested = total_invested + :amount WHERE id = :user_id";
                $update_balance_stmt = $this->db->prepare($update_balance_query);
                $update_balance_stmt->bindParam(':amount', $amount);
                $update_balance_stmt->bindParam(':user_id', $user_id, PDO::PARAM_INT);
                $update_balance_stmt->execute();

                // Создание транзакции
                $transaction_query = "INSERT INTO transactions
                                     (user_id, type, amount, status, description, created_at)
                                     VALUES
                                     (:user_id, 'investment', :amount, 'completed', :description, NOW())";

                $description = "Инвестиция в пакет: " . $package['name'];

                $transaction_stmt = $this->db->prepare($transaction_query);
                $transaction_stmt->bindParam(':user_id', $user_id, PDO::PARAM_INT);
                $transaction_stmt->bindParam(':amount', $amount);
                $transaction_stmt->bindParam(':description', $description);
                $transaction_stmt->execute();

                return [
                    'id' => $investment_id,
                    'user_id' => $user_id,
                    'package_id' => $package['id'],
                    'package_name' => $package['name'],
                    'amount' => $amount,
                    'daily_profit' => $daily_profit,
                    'start_date' => $start_date,
                    'end_date' => $end_date,
                    'duration_days' => $package['duration_days']
                ];
            }

            return false;
        } catch (Exception $e) {
            error_log("Create fake investment error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Массовое создание инвестиций
     */
    public function createMultipleInvestments($count, $options = []) {
        $created_investments = [];
        $errors = [];

        for ($i = 0; $i < $count; $i++) {
            $investment = $this->createFakeInvestment(null, null, $options);

            if ($investment) {
                $created_investments[] = $investment;
            } else {
                $errors[] = "Ошибка создания инвестиции #" . ($i + 1);
            }

            // Небольшая задержка
            usleep(5000); // 0.005 секунды
        }

        return [
            'created' => $created_investments,
            'errors' => $errors,
            'total_created' => count($created_investments),
            'total_errors' => count($errors)
        ];
    }

    /**
     * Симуляция активности существующих инвестиций
     */
    public function simulateInvestmentActivity($options = []) {
        try {
            $activity_count = 0;

            // Получение активных инвестиций
            $investments_query = "SELECT ui.*, u.username, ip.name as package_name
                                 FROM user_investments ui
                                 JOIN users u ON ui.user_id = u.id
                                 JOIN investment_packages ip ON ui.package_id = ip.id
                                 WHERE ui.status = 'active'
                                 AND ui.start_date <= CURDATE()
                                 AND ui.end_date >= CURDATE()
                                 AND u.is_active = 1
                                 ORDER BY RAND()
                                 LIMIT " . ($options['max_investments'] ?? 50);

            $investments_stmt = $this->db->prepare($investments_query);
            $investments_stmt->execute();
            $investments = $investments_stmt->fetchAll(PDO::FETCH_ASSOC);

            foreach ($investments as $investment) {
                // Случайная активность (30% вероятность для каждой инвестиции)
                if (rand(1, 100) <= 30) {
                    // Генерация бонусной прибыли (5-15% от дневной прибыли)
                    $bonus_profit = $investment['daily_profit'] * (rand(5, 15) / 100);

                    // Создание транзакции бонуса
                    $bonus_transaction_query = "INSERT INTO transactions
                                               (user_id, type, amount, status, description, created_at)
                                               VALUES
                                               (:user_id, 'profit', :amount, 'completed', :description, NOW())";

                    $description = "Бонусная прибыль по инвестиции #{$investment['id']} ({$investment['package_name']})";

                    $bonus_transaction_stmt = $this->db->prepare($bonus_transaction_query);
                    $bonus_transaction_stmt->bindParam(':user_id', $investment['user_id'], PDO::PARAM_INT);
                    $bonus_transaction_stmt->bindParam(':amount', $bonus_profit);
                    $bonus_transaction_stmt->bindParam(':description', $description);

                    if ($bonus_transaction_stmt->execute()) {
                        // Обновление баланса пользователя
                        $update_balance_query = "UPDATE users SET balance = balance + :profit, total_earned = total_earned + :profit WHERE id = :user_id";
                        $update_balance_stmt = $this->db->prepare($update_balance_query);
                        $update_balance_stmt->bindParam(':profit', $bonus_profit);
                        $update_balance_stmt->bindParam(':user_id', $investment['user_id'], PDO::PARAM_INT);
                        $update_balance_stmt->execute();

                        // Обновление общей прибыли по инвестиции
                        $update_investment_query = "UPDATE user_investments SET total_earned = total_earned + :profit WHERE id = :investment_id";
                        $update_investment_stmt = $this->db->prepare($update_investment_query);
                        $update_investment_stmt->bindParam(':profit', $bonus_profit);
                        $update_investment_stmt->bindParam(':investment_id', $investment['id'], PDO::PARAM_INT);
                        $update_investment_stmt->execute();

                        $activity_count++;
                    }
                }
            }

            return [
                'success' => true,
                'activity_count' => $activity_count,
                'total_investments' => count($investments)
            ];

        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Создание реалистичной транзакции
     */
    public function createFakeTransaction($user_id = null, $type = null, $options = []) {
        try {
            // Если пользователь не указан, выбираем случайного
            if (!$user_id) {
                $user_query = "SELECT id, balance FROM users WHERE is_active = 1 ORDER BY RAND() LIMIT 1";
                $user_stmt = $this->db->prepare($user_query);
                $user_stmt->execute();
                $user = $user_stmt->fetch(PDO::FETCH_ASSOC);

                if (!$user) {
                    return false;
                }

                $user_id = $user['id'];
                $user_balance = $user['balance'];
            } else {
                $balance_query = "SELECT balance FROM users WHERE id = :id AND is_active = 1";
                $balance_stmt = $this->db->prepare($balance_query);
                $balance_stmt->bindParam(':id', $user_id, PDO::PARAM_INT);
                $balance_stmt->execute();
                $balance_result = $balance_stmt->fetch(PDO::FETCH_ASSOC);

                if (!$balance_result) {
                    return false;
                }

                $user_balance = $balance_result['balance'];
            }

            // Если тип не указан, выбираем случайный
            if (!$type) {
                $types = ['deposit', 'withdrawal', 'profit'];
                $weights = [50, 20, 30]; // Вероятности в процентах

                $random = rand(1, 100);
                $cumulative = 0;

                for ($i = 0; $i < count($types); $i++) {
                    $cumulative += $weights[$i];
                    if ($random <= $cumulative) {
                        $type = $types[$i];
                        break;
                    }
                }
            }

            // Генерация суммы в зависимости от типа
            switch ($type) {
                case 'deposit':
                    $amount = $this->generateRealisticDepositAmount();
                    $status = rand(1, 100) <= 85 ? 'completed' : 'pending';
                    $description = "Пополнение баланса";
                    break;

                case 'withdrawal':
                    $max_withdrawal = min($user_balance * 0.8, 1000); // Максимум 80% баланса или 1000 USDT
                    if ($max_withdrawal < 10) {
                        return false; // Недостаточно средств для вывода
                    }
                    $amount = rand(10, $max_withdrawal);
                    $status = rand(1, 100) <= 70 ? 'completed' : 'pending';
                    $description = "Вывод средств";
                    break;

                case 'profit':
                    $amount = rand(1, 50);
                    $status = 'completed';
                    $description = "Прибыль от инвестиций";
                    break;

                default:
                    return false;
            }

            // Округление суммы
            $amount = round($amount, 2);

            // Генерация времени создания (последние 24 часа)
            $created_at = date('Y-m-d H:i:s', rand(strtotime('-24 hours'), time()));

            // Создание транзакции
            $insert_query = "INSERT INTO transactions
                            (user_id, type, amount, status, description, created_at)
                            VALUES
                            (:user_id, :type, :amount, :status, :description, :created_at)";

            $insert_stmt = $this->db->prepare($insert_query);
            $insert_stmt->bindParam(':user_id', $user_id, PDO::PARAM_INT);
            $insert_stmt->bindParam(':type', $type);
            $insert_stmt->bindParam(':amount', $amount);
            $insert_stmt->bindParam(':status', $status);
            $insert_stmt->bindParam(':description', $description);
            $insert_stmt->bindParam(':created_at', $created_at);

            if ($insert_stmt->execute()) {
                $transaction_id = $this->db->lastInsertId();

                // Обновление баланса пользователя для завершенных транзакций
                if ($status === 'completed') {
                    $balance_change = 0;

                    switch ($type) {
                        case 'deposit':
                        case 'profit':
                            $balance_change = $amount;
                            break;
                        case 'withdrawal':
                            $balance_change = -$amount;
                            break;
                    }

                    if ($balance_change != 0) {
                        $update_balance_query = "UPDATE users SET balance = GREATEST(0, balance + :change) WHERE id = :user_id";
                        $update_balance_stmt = $this->db->prepare($update_balance_query);
                        $update_balance_stmt->bindParam(':change', $balance_change);
                        $update_balance_stmt->bindParam(':user_id', $user_id, PDO::PARAM_INT);
                        $update_balance_stmt->execute();
                    }
                }

                return [
                    'id' => $transaction_id,
                    'user_id' => $user_id,
                    'type' => $type,
                    'amount' => $amount,
                    'status' => $status,
                    'description' => $description,
                    'created_at' => $created_at
                ];
            }

            return false;
        } catch (Exception $e) {
            error_log("Create fake transaction error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Генерация реалистичной суммы депозита
     */
    private function generateRealisticDepositAmount() {
        $patterns = [
            ['min' => 10, 'max' => 50, 'weight' => 40],      // Мелкие депозиты
            ['min' => 50, 'max' => 200, 'weight' => 30],     // Средние депозиты
            ['min' => 200, 'max' => 500, 'weight' => 20],    // Крупные депозиты
            ['min' => 500, 'max' => 2000, 'weight' => 8],    // Очень крупные депозиты
            ['min' => 2000, 'max' => 10000, 'weight' => 2]   // Киты
        ];

        $random = rand(1, 100);
        $cumulative = 0;

        foreach ($patterns as $pattern) {
            $cumulative += $pattern['weight'];
            if ($random <= $cumulative) {
                $amount = rand($pattern['min'], $pattern['max']);

                // Округление до красивых чисел
                if ($amount >= 1000) {
                    return round($amount / 100) * 100;
                } elseif ($amount >= 100) {
                    return round($amount / 50) * 50;
                } else {
                    return round($amount / 10) * 10;
                }
            }
        }

        return 100; // Fallback
    }
}
?>
