<?php
/**
 * AstroGenix - Общие функции
 * Вспомогательные функции для всего приложения
 */

/**
 * Проверка авторизации пользователя
 */
function is_logged_in() {
    return isset($_SESSION['user_id']) && !empty($_SESSION['user_id']);
}

/**
 * Получение информации о текущем пользователе
 */
function get_current_user() {
    if (!is_logged_in()) {
        return null;
    }
    
    return [
        'id' => $_SESSION['user_id'],
        'username' => $_SESSION['username'] ?? '',
        'email' => $_SESSION['email'] ?? '',
        'avatar' => $_SESSION['avatar'] ?? '',
        'balance' => $_SESSION['balance'] ?? 0,
        'role' => $_SESSION['role'] ?? 'user'
    ];
}

/**
 * Проверка роли пользователя
 */
function has_role($role) {
    return is_logged_in() && ($_SESSION['role'] ?? 'user') === $role;
}

/**
 * Проверка прав администратора
 */
function is_admin() {
    return has_role('admin');
}

/**
 * Безопасное перенаправление
 */
function safe_redirect($url, $status_code = 302) {
    // Проверка на валидный URL
    if (filter_var($url, FILTER_VALIDATE_URL) === false && !preg_match('/^\//', $url)) {
        $url = '/';
    }
    
    header("Location: $url", true, $status_code);
    exit();
}

/**
 * Генерация CSRF токена
 */
function generate_csrf_token() {
    if (!isset($_SESSION['csrf_token'])) {
        $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
    }
    return $_SESSION['csrf_token'];
}

/**
 * Проверка CSRF токена
 */
function verify_csrf_token($token) {
    return isset($_SESSION['csrf_token']) && hash_equals($_SESSION['csrf_token'], $token);
}

/**
 * Очистка и валидация входных данных
 */
function clean_input($data) {
    if (is_array($data)) {
        return array_map('clean_input', $data);
    }
    
    $data = trim($data);
    $data = stripslashes($data);
    $data = htmlspecialchars($data, ENT_QUOTES, 'UTF-8');
    
    return $data;
}

/**
 * Валидация email
 */
function validate_email($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
}

/**
 * Валидация пароля
 */
function validate_password($password) {
    // Минимум 8 символов, содержит буквы и цифры
    return strlen($password) >= 8 && 
           preg_match('/[A-Za-z]/', $password) && 
           preg_match('/[0-9]/', $password);
}

/**
 * Хеширование пароля
 */
function hash_password($password) {
    return password_hash($password, PASSWORD_DEFAULT);
}

/**
 * Проверка пароля
 */
function verify_password($password, $hash) {
    return password_verify($password, $hash);
}

/**
 * Форматирование валюты
 */
function format_currency($amount, $currency = 'USDT', $decimals = 2) {
    return number_format($amount, $decimals, '.', ',') . ' ' . $currency;
}

/**
 * Форматирование числа
 */
function format_number($number, $decimals = 2) {
    return number_format($number, $decimals, '.', ',');
}

/**
 * Форматирование даты
 */
function format_date($date, $format = 'd.m.Y H:i') {
    if (is_string($date)) {
        $date = new DateTime($date);
    }
    return $date->format($format);
}

/**
 * Получение времени "назад"
 */
function time_ago($datetime) {
    $time = time() - strtotime($datetime);
    
    if ($time < 60) return 'только что';
    if ($time < 3600) return floor($time/60) . ' мин. назад';
    if ($time < 86400) return floor($time/3600) . ' ч. назад';
    if ($time < 2592000) return floor($time/86400) . ' дн. назад';
    if ($time < 31536000) return floor($time/2592000) . ' мес. назад';
    
    return floor($time/31536000) . ' г. назад';
}

/**
 * Генерация случайной строки
 */
function generate_random_string($length = 10) {
    return bin2hex(random_bytes($length / 2));
}

/**
 * Логирование ошибок
 */
function log_error($message, $context = []) {
    $log_entry = [
        'timestamp' => date('Y-m-d H:i:s'),
        'message' => $message,
        'context' => $context,
        'user_id' => $_SESSION['user_id'] ?? null,
        'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
        'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown'
    ];
    
    $log_file = __DIR__ . '/../logs/error.log';
    $log_dir = dirname($log_file);
    
    if (!is_dir($log_dir)) {
        mkdir($log_dir, 0755, true);
    }
    
    file_put_contents($log_file, json_encode($log_entry) . PHP_EOL, FILE_APPEND | LOCK_EX);
}

/**
 * Логирование активности пользователя
 */
function log_user_activity($action, $details = []) {
    if (!is_logged_in()) {
        return;
    }
    
    $log_entry = [
        'timestamp' => date('Y-m-d H:i:s'),
        'user_id' => $_SESSION['user_id'],
        'action' => $action,
        'details' => $details,
        'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
        'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown'
    ];
    
    $log_file = __DIR__ . '/../logs/user_activity.log';
    $log_dir = dirname($log_file);
    
    if (!is_dir($log_dir)) {
        mkdir($log_dir, 0755, true);
    }
    
    file_put_contents($log_file, json_encode($log_entry) . PHP_EOL, FILE_APPEND | LOCK_EX);
}

/**
 * Отправка уведомления
 */
function send_notification($user_id, $type, $title, $message, $data = []) {
    try {
        $database = new Database();
        $db = $database->getConnection();
        
        $query = "INSERT INTO notifications (user_id, type, title, message, data) 
                  VALUES (:user_id, :type, :title, :message, :data)";
        
        $stmt = $db->prepare($query);
        $stmt->bindParam(':user_id', $user_id, PDO::PARAM_INT);
        $stmt->bindParam(':type', $type);
        $stmt->bindParam(':title', $title);
        $stmt->bindParam(':message', $message);
        $stmt->bindParam(':data', json_encode($data));
        
        return $stmt->execute();
        
    } catch (Exception $e) {
        log_error("Send notification error: " . $e->getMessage(), [
            'user_id' => $user_id,
            'type' => $type,
            'title' => $title
        ]);
        return false;
    }
}

/**
 * Проверка лимитов запросов
 */
function check_rate_limit($key, $limit = 60, $window = 3600) {
    $cache_file = __DIR__ . "/../cache/rate_limit_{$key}.json";
    $cache_dir = dirname($cache_file);
    
    if (!is_dir($cache_dir)) {
        mkdir($cache_dir, 0755, true);
    }
    
    $current_time = time();
    $requests = [];
    
    // Чтение существующих запросов
    if (file_exists($cache_file)) {
        $content = file_get_contents($cache_file);
        $requests = $content ? json_decode($content, true) : [];
    }
    
    // Фильтрация запросов в пределах окна
    $requests = array_filter($requests, function($timestamp) use ($current_time, $window) {
        return ($current_time - $timestamp) < $window;
    });
    
    // Проверка лимита
    if (count($requests) >= $limit) {
        return false;
    }
    
    // Добавление текущего запроса
    $requests[] = $current_time;
    file_put_contents($cache_file, json_encode($requests), LOCK_EX);
    
    return true;
}

/**
 * Получение IP адреса пользователя
 */
function get_user_ip() {
    $ip_keys = ['HTTP_X_FORWARDED_FOR', 'HTTP_X_REAL_IP', 'HTTP_CLIENT_IP', 'REMOTE_ADDR'];
    
    foreach ($ip_keys as $key) {
        if (!empty($_SERVER[$key])) {
            $ip = $_SERVER[$key];
            if (strpos($ip, ',') !== false) {
                $ip = trim(explode(',', $ip)[0]);
            }
            if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                return $ip;
            }
        }
    }
    
    return $_SERVER['REMOTE_ADDR'] ?? 'unknown';
}

/**
 * Проверка безопасности файла
 */
function is_safe_file($filename, $allowed_extensions = ['jpg', 'jpeg', 'png', 'gif', 'pdf', 'doc', 'docx']) {
    $extension = strtolower(pathinfo($filename, PATHINFO_EXTENSION));
    return in_array($extension, $allowed_extensions);
}

/**
 * Генерация безопасного имени файла
 */
function generate_safe_filename($original_filename) {
    $extension = pathinfo($original_filename, PATHINFO_EXTENSION);
    $safe_name = preg_replace('/[^a-zA-Z0-9_-]/', '_', pathinfo($original_filename, PATHINFO_FILENAME));
    $safe_name = substr($safe_name, 0, 50); // Ограничение длины
    
    return $safe_name . '_' . time() . '.' . $extension;
}
?>
