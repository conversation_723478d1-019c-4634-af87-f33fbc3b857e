<?php
/**
 * AstroGenix - Портфель инвестиций
 * Детальная аналитика и управление портфелем
 */

session_start();
require_once 'config/config.php';
require_once 'classes/InvestmentPortfolio.php';

// Проверка авторизации
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit();
}

$database = new Database();
$db = $database->getConnection();
$portfolio = new InvestmentPortfolio($db);

// Получение аналитики портфеля
$analytics = $portfolio->getPortfolioAnalytics($_SESSION['user_id']);
$user_balance = $_SESSION['balance'] ?? 0;

$page_title = 'Портфель инвестиций - AstroGenix';
?>
<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?></title>
    <meta name="description" content="Детальная аналитика портфеля инвестиций на платформе AstroGenix">
    
    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="assets/images/favicon.svg">
    
    <!-- Стили -->
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="assets/css/investments.css">
    <link rel="stylesheet" href="assets/css/portfolio.css">
    <link rel="stylesheet" href="assets/css/animations.css">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <!-- Chart.js для графиков -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-adapter-date-fns/dist/chartjs-adapter-date-fns.bundle.min.js"></script>
</head>
<body>
    <?php include 'includes/header.php'; ?>

    <main class="main-content">
        <!-- Hero секция -->
        <section class="hero-section portfolio-hero">
            <div class="container">
                <div class="hero-content animate-fade-in">
                    <h1 class="hero-title">
                        Портфель <span class="gradient-text">инвестиций</span>
                    </h1>
                    <p class="hero-subtitle">
                        Полная аналитика вашего инвестиционного портфеля с детальными графиками, 
                        прогнозами и рекомендациями по оптимизации.
                    </p>
                </div>
            </div>
        </section>

        <div class="container">
            <!-- Обзор портфеля -->
            <section class="section portfolio-overview">
                <div class="section-header">
                    <h2 class="section-title">Обзор портфеля</h2>
                    <div class="portfolio-status status-<?php echo $analytics['overview']['portfolio_status']['status']; ?>">
                        <i class="fas fa-circle"></i>
                        <?php echo $analytics['overview']['portfolio_status']['message']; ?>
                    </div>
                </div>
                
                <div class="overview-grid">
                    <div class="overview-card total-invested">
                        <div class="card-icon">
                            <i class="fas fa-wallet"></i>
                        </div>
                        <div class="card-content">
                            <div class="card-value"><?php echo number_format($analytics['overview']['total_invested'], 2); ?> USDT</div>
                            <div class="card-label">Всего инвестировано</div>
                            <div class="card-detail">
                                Активных: <?php echo number_format($analytics['overview']['active_amount'], 2); ?> USDT
                            </div>
                        </div>
                    </div>
                    
                    <div class="overview-card total-earned">
                        <div class="card-icon">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <div class="card-content">
                            <div class="card-value"><?php echo number_format($analytics['overview']['total_earned'], 2); ?> USDT</div>
                            <div class="card-label">Общая прибыль</div>
                            <div class="card-detail">
                                ROI: <?php echo $analytics['overview']['roi_percent']; ?>%
                            </div>
                        </div>
                    </div>
                    
                    <div class="overview-card daily-income">
                        <div class="card-icon">
                            <i class="fas fa-coins"></i>
                        </div>
                        <div class="card-content">
                            <div class="card-value"><?php echo number_format($analytics['overview']['daily_income'], 2); ?> USDT</div>
                            <div class="card-label">Ежедневный доход</div>
                            <div class="card-detail">
                                В месяц: ~<?php echo number_format($analytics['overview']['monthly_forecast'], 2); ?> USDT
                            </div>
                        </div>
                    </div>
                    
                    <div class="overview-card investments-count">
                        <div class="card-icon">
                            <i class="fas fa-layer-group"></i>
                        </div>
                        <div class="card-content">
                            <div class="card-value"><?php echo $analytics['overview']['total_investments']; ?></div>
                            <div class="card-label">Всего инвестиций</div>
                            <div class="card-detail">
                                Активных: <?php echo $analytics['overview']['active_investments']; ?> | 
                                Завершенных: <?php echo $analytics['overview']['completed_investments']; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Графики и аналитика -->
            <section class="section portfolio-charts">
                <div class="charts-grid">
                    <!-- График динамики доходности -->
                    <div class="chart-container performance-chart">
                        <div class="chart-header">
                            <h3>Динамика доходности</h3>
                            <div class="chart-controls">
                                <button class="chart-period-btn active" data-period="30">30 дней</button>
                                <button class="chart-period-btn" data-period="90">90 дней</button>
                                <button class="chart-period-btn" data-period="365">1 год</button>
                            </div>
                        </div>
                        <div class="chart-wrapper">
                            <canvas id="performance-chart"></canvas>
                        </div>
                    </div>
                    
                    <!-- Распределение по пакетам -->
                    <div class="chart-container distribution-chart">
                        <div class="chart-header">
                            <h3>Распределение по пакетам</h3>
                        </div>
                        <div class="chart-wrapper">
                            <canvas id="distribution-chart"></canvas>
                        </div>
                        <div class="distribution-legend">
                            <?php foreach ($analytics['distribution'] as $package): ?>
                                <div class="legend-item">
                                    <div class="legend-color" style="background-color: <?php echo $package['color']; ?>"></div>
                                    <div class="legend-content">
                                        <div class="legend-name"><?php echo htmlspecialchars($package['package_name']); ?></div>
                                        <div class="legend-value"><?php echo $package['percentage']; ?>% (<?php echo number_format($package['total_amount'], 2); ?> USDT)</div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Прогноз доходов -->
            <section class="section forecast-section">
                <div class="section-header">
                    <h2 class="section-title">Прогноз доходов</h2>
                    <p class="section-subtitle">Ожидаемые доходы на основе текущих активных инвестиций</p>
                </div>
                
                <div class="forecast-container">
                    <div class="forecast-chart-wrapper">
                        <canvas id="forecast-chart"></canvas>
                    </div>
                    
                    <div class="forecast-summary">
                        <h3>Прогноз на 30 дней</h3>
                        <div class="forecast-metrics">
                            <?php 
                            $forecast_30 = array_slice($analytics['forecast'], -1)[0] ?? ['cumulative_profit' => 0];
                            $forecast_7 = $analytics['forecast'][6] ?? ['cumulative_profit' => 0];
                            ?>
                            <div class="forecast-metric">
                                <div class="metric-label">Прибыль за 7 дней</div>
                                <div class="metric-value"><?php echo number_format($forecast_7['cumulative_profit'], 2); ?> USDT</div>
                            </div>
                            <div class="forecast-metric">
                                <div class="metric-label">Прибыль за 30 дней</div>
                                <div class="metric-value"><?php echo number_format($forecast_30['cumulative_profit'], 2); ?> USDT</div>
                            </div>
                            <div class="forecast-metric">
                                <div class="metric-label">Средний дневной доход</div>
                                <div class="metric-value"><?php echo number_format($analytics['overview']['daily_income'], 2); ?> USDT</div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Анализ рисков -->
            <section class="section risk-analysis">
                <div class="section-header">
                    <h2 class="section-title">Анализ рисков</h2>
                </div>
                
                <div class="risk-container">
                    <div class="risk-overview">
                        <div class="risk-score risk-<?php echo $analytics['risk_analysis']['overall_risk']; ?>">
                            <div class="risk-score-value"><?php echo $analytics['risk_analysis']['risk_score']; ?></div>
                            <div class="risk-score-label">Оценка риска</div>
                        </div>
                        <div class="risk-description">
                            <h3><?php echo $analytics['risk_analysis']['risk_description']; ?></h3>
                            <div class="risk-recommendations">
                                <h4>Рекомендации:</h4>
                                <ul>
                                    <?php foreach ($analytics['risk_analysis']['recommendations'] as $recommendation): ?>
                                        <li><?php echo htmlspecialchars($recommendation); ?></li>
                                    <?php endforeach; ?>
                                </ul>
                            </div>
                        </div>
                    </div>
                    
                    <div class="risk-distribution">
                        <h3>Распределение по уровням риска</h3>
                        <div class="risk-bars">
                            <?php foreach ($analytics['risk_analysis']['distribution'] as $risk): ?>
                                <div class="risk-bar">
                                    <div class="risk-bar-label">
                                        <span class="risk-level risk-<?php echo $risk['risk_level']; ?>">
                                            <?php echo ucfirst($risk['risk_level']); ?>
                                        </span>
                                        <span class="risk-percentage"><?php echo $risk['percentage']; ?>%</span>
                                    </div>
                                    <div class="risk-bar-fill">
                                        <div class="risk-bar-progress risk-<?php echo $risk['risk_level']; ?>" 
                                             style="width: <?php echo $risk['percentage']; ?>%"></div>
                                    </div>
                                    <div class="risk-bar-amount"><?php echo number_format($risk['total_amount'], 2); ?> USDT</div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Детали по пакетам -->
            <section class="section package-details">
                <div class="section-header">
                    <h2 class="section-title">Детали по пакетам</h2>
                </div>
                
                <div class="packages-table-container">
                    <table class="packages-table">
                        <thead>
                            <tr>
                                <th>Пакет</th>
                                <th>Тип</th>
                                <th>Инвестиций</th>
                                <th>Сумма</th>
                                <th>Заработано</th>
                                <th>ROI</th>
                                <th>Дневной доход</th>
                                <th>Риск</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($analytics['distribution'] as $package): ?>
                                <tr>
                                    <td>
                                        <div class="package-name">
                                            <div class="package-color" style="background-color: <?php echo $package['color']; ?>"></div>
                                            <?php echo htmlspecialchars($package['package_name']); ?>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="package-type"><?php echo ucfirst($package['package_type']); ?></span>
                                    </td>
                                    <td><?php echo $package['investment_count']; ?></td>
                                    <td><?php echo number_format($package['total_amount'], 2); ?> USDT</td>
                                    <td><?php echo number_format($package['total_earned'], 2); ?> USDT</td>
                                    <td>
                                        <span class="roi-value <?php echo $package['roi_percent'] > 0 ? 'positive' : 'neutral'; ?>">
                                            <?php echo $package['roi_percent']; ?>%
                                        </span>
                                    </td>
                                    <td><?php echo number_format($package['avg_daily_profit'], 2); ?> USDT</td>
                                    <td>
                                        <span class="risk-badge risk-<?php echo $package['risk_level']; ?>">
                                            <?php echo ucfirst($package['risk_level']); ?>
                                        </span>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </section>

            <!-- История транзакций -->
            <section class="section transaction-history">
                <div class="section-header">
                    <h2 class="section-title">История транзакций</h2>
                    <button class="btn btn-secondary" id="export-history">
                        <i class="fas fa-download"></i>
                        Экспорт
                    </button>
                </div>
                
                <div class="transactions-container">
                    <div class="transactions-filters">
                        <select id="transaction-type-filter" class="form-control">
                            <option value="">Все типы</option>
                            <option value="investment">Инвестиции</option>
                            <option value="profit">Прибыль</option>
                            <option value="withdrawal">Выводы</option>
                            <option value="deposit">Пополнения</option>
                        </select>
                        
                        <input type="date" id="date-from-filter" class="form-control">
                        <input type="date" id="date-to-filter" class="form-control">
                        
                        <button class="btn btn-primary" id="apply-filters">
                            <i class="fas fa-filter"></i>
                            Применить
                        </button>
                    </div>
                    
                    <div class="transactions-list">
                        <?php foreach ($analytics['transaction_history'] as $transaction): ?>
                            <div class="transaction-item transaction-<?php echo $transaction['type']; ?>">
                                <div class="transaction-icon">
                                    <i class="fas <?php 
                                        echo $transaction['type'] === 'investment' ? 'fa-arrow-up' : 
                                            ($transaction['type'] === 'profit' ? 'fa-plus' : 
                                            ($transaction['type'] === 'withdrawal' ? 'fa-arrow-down' : 'fa-wallet')); 
                                    ?>"></i>
                                </div>
                                <div class="transaction-content">
                                    <div class="transaction-title"><?php echo htmlspecialchars($transaction['type_name']); ?></div>
                                    <div class="transaction-description"><?php echo htmlspecialchars($transaction['description']); ?></div>
                                    <div class="transaction-date"><?php echo date('d.m.Y H:i', strtotime($transaction['created_at'])); ?></div>
                                </div>
                                <div class="transaction-amount">
                                    <span class="amount <?php echo in_array($transaction['type'], ['profit', 'deposit']) ? 'positive' : 'negative'; ?>">
                                        <?php echo in_array($transaction['type'], ['profit', 'deposit']) ? '+' : '-'; ?>
                                        <?php echo number_format($transaction['amount'], 2); ?> USDT
                                    </span>
                                    <span class="status status-<?php echo $transaction['status']; ?>">
                                        <?php echo ucfirst($transaction['status']); ?>
                                    </span>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </section>
        </div>
    </main>

    <?php include 'includes/footer.php'; ?>

    <!-- JavaScript -->
    <script>
        // Передача данных аналитики в JavaScript
        window.portfolioData = {
            performance: <?php echo json_encode($analytics['performance']); ?>,
            distribution: <?php echo json_encode($analytics['distribution']); ?>,
            forecast: <?php echo json_encode($analytics['forecast']); ?>
        };
    </script>
    <script src="assets/js/main.js"></script>
    <script src="assets/js/portfolio.js"></script>
</body>
</html>
