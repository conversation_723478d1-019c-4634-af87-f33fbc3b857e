<?php
/**
 * AstroGenix - Класс для работы с CMS
 * Система управления контентом
 */

class CMS {
    private $db;
    
    public function __construct($database) {
        $this->db = $database;
    }
    
    // ==================== СТРАНИЦЫ ====================
    
    /**
     * Получение страницы по slug
     */
    public function getPageBySlug($slug) {
        try {
            $query = "SELECT p.*, u.username as author_name 
                      FROM cms_pages p 
                      LEFT JOIN users u ON p.author_id = u.id 
                      WHERE p.slug = :slug AND p.status = 'published'";
            
            $stmt = $this->db->prepare($query);
            $stmt->bindParam(':slug', $slug);
            $stmt->execute();
            
            return $stmt->fetch(PDO::FETCH_ASSOC);
        } catch (Exception $e) {
            error_log("CMS getPageBySlug error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Получение всех страниц
     */
    public function getPages($status = null, $limit = null, $offset = 0) {
        try {
            $where_clause = '';
            $params = [];
            
            if ($status) {
                $where_clause = "WHERE p.status = :status";
                $params[':status'] = $status;
            }
            
            $limit_clause = $limit ? "LIMIT :limit OFFSET :offset" : "";
            
            $query = "SELECT p.*, u.username as author_name 
                      FROM cms_pages p 
                      LEFT JOIN users u ON p.author_id = u.id 
                      $where_clause 
                      ORDER BY p.sort_order ASC, p.created_at DESC 
                      $limit_clause";
            
            $stmt = $this->db->prepare($query);
            
            foreach ($params as $key => $value) {
                $stmt->bindValue($key, $value);
            }
            
            if ($limit) {
                $stmt->bindValue(':limit', $limit, PDO::PARAM_INT);
                $stmt->bindValue(':offset', $offset, PDO::PARAM_INT);
            }
            
            $stmt->execute();
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (Exception $e) {
            error_log("CMS getPages error: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Создание/обновление страницы
     */
    public function savePage($data, $page_id = null) {
        try {
            $this->db->beginTransaction();
            
            if ($page_id) {
                // Обновление существующей страницы
                $query = "UPDATE cms_pages SET 
                          title = :title, slug = :slug, content = :content, excerpt = :excerpt,
                          meta_title = :meta_title, meta_description = :meta_description, 
                          meta_keywords = :meta_keywords, template = :template, status = :status,
                          featured_image = :featured_image, parent_id = :parent_id, 
                          sort_order = :sort_order, show_in_menu = :show_in_menu,
                          custom_css = :custom_css, custom_js = :custom_js,
                          updated_at = NOW()
                          WHERE id = :id";
                
                $stmt = $this->db->prepare($query);
                $stmt->bindParam(':id', $page_id, PDO::PARAM_INT);
            } else {
                // Создание новой страницы
                $query = "INSERT INTO cms_pages 
                          (title, slug, content, excerpt, meta_title, meta_description, 
                           meta_keywords, template, status, featured_image, author_id, 
                           parent_id, sort_order, show_in_menu, custom_css, custom_js, created_at) 
                          VALUES 
                          (:title, :slug, :content, :excerpt, :meta_title, :meta_description, 
                           :meta_keywords, :template, :status, :featured_image, :author_id, 
                           :parent_id, :sort_order, :show_in_menu, :custom_css, :custom_js, NOW())";
                
                $stmt = $this->db->prepare($query);
                $stmt->bindParam(':author_id', $_SESSION['user_id'], PDO::PARAM_INT);
            }
            
            // Привязка параметров
            $stmt->bindParam(':title', $data['title']);
            $stmt->bindParam(':slug', $data['slug']);
            $stmt->bindParam(':content', $data['content']);
            $stmt->bindParam(':excerpt', $data['excerpt']);
            $stmt->bindParam(':meta_title', $data['meta_title']);
            $stmt->bindParam(':meta_description', $data['meta_description']);
            $stmt->bindParam(':meta_keywords', $data['meta_keywords']);
            $stmt->bindParam(':template', $data['template']);
            $stmt->bindParam(':status', $data['status']);
            $stmt->bindParam(':featured_image', $data['featured_image']);
            $stmt->bindParam(':parent_id', $data['parent_id'], PDO::PARAM_INT);
            $stmt->bindParam(':sort_order', $data['sort_order'], PDO::PARAM_INT);
            $stmt->bindParam(':show_in_menu', $data['show_in_menu'], PDO::PARAM_BOOL);
            $stmt->bindParam(':custom_css', $data['custom_css']);
            $stmt->bindParam(':custom_js', $data['custom_js']);
            
            $result = $stmt->execute();
            
            if (!$page_id) {
                $page_id = $this->db->lastInsertId();
            }
            
            // Сохранение ревизии
            $this->saveRevision('page', $page_id, $data, $page_id ? 'update' : 'create');
            
            $this->db->commit();
            return $page_id;
        } catch (Exception $e) {
            $this->db->rollback();
            error_log("CMS savePage error: " . $e->getMessage());
            return false;
        }
    }
    
    // ==================== БЛОКИ ====================
    
    /**
     * Получение блока по идентификатору
     */
    public function getBlockByIdentifier($identifier) {
        try {
            $query = "SELECT * FROM cms_blocks WHERE identifier = :identifier AND status = 'active'";
            $stmt = $this->db->prepare($query);
            $stmt->bindParam(':identifier', $identifier);
            $stmt->execute();
            
            return $stmt->fetch(PDO::FETCH_ASSOC);
        } catch (Exception $e) {
            error_log("CMS getBlockByIdentifier error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Получение блоков по позиции
     */
    public function getBlocksByPosition($position) {
        try {
            $query = "SELECT * FROM cms_blocks 
                      WHERE position = :position AND status = 'active' 
                      ORDER BY sort_order ASC";
            
            $stmt = $this->db->prepare($query);
            $stmt->bindParam(':position', $position);
            $stmt->execute();
            
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (Exception $e) {
            error_log("CMS getBlocksByPosition error: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Сохранение блока
     */
    public function saveBlock($data, $block_id = null) {
        try {
            if ($block_id) {
                $query = "UPDATE cms_blocks SET 
                          name = :name, identifier = :identifier, content = :content,
                          description = :description, type = :type, status = :status,
                          position = :position, sort_order = :sort_order,
                          updated_at = NOW()
                          WHERE id = :id";
                
                $stmt = $this->db->prepare($query);
                $stmt->bindParam(':id', $block_id, PDO::PARAM_INT);
            } else {
                $query = "INSERT INTO cms_blocks 
                          (name, identifier, content, description, type, status, position, sort_order) 
                          VALUES 
                          (:name, :identifier, :content, :description, :type, :status, :position, :sort_order)";
                
                $stmt = $this->db->prepare($query);
            }
            
            $stmt->bindParam(':name', $data['name']);
            $stmt->bindParam(':identifier', $data['identifier']);
            $stmt->bindParam(':content', $data['content']);
            $stmt->bindParam(':description', $data['description']);
            $stmt->bindParam(':type', $data['type']);
            $stmt->bindParam(':status', $data['status']);
            $stmt->bindParam(':position', $data['position']);
            $stmt->bindParam(':sort_order', $data['sort_order'], PDO::PARAM_INT);
            
            $result = $stmt->execute();
            
            if (!$block_id) {
                $block_id = $this->db->lastInsertId();
            }
            
            return $block_id;
        } catch (Exception $e) {
            error_log("CMS saveBlock error: " . $e->getMessage());
            return false;
        }
    }
    
    // ==================== МЕНЮ ====================
    
    /**
     * Получение меню по идентификатору
     */
    public function getMenuByIdentifier($identifier) {
        try {
            $query = "SELECT m.*, 
                      (SELECT COUNT(*) FROM cms_menu_items WHERE menu_id = m.id AND status = 'active') as items_count
                      FROM cms_menus m 
                      WHERE m.identifier = :identifier AND m.status = 'active'";
            
            $stmt = $this->db->prepare($query);
            $stmt->bindParam(':identifier', $identifier);
            $stmt->execute();
            
            return $stmt->fetch(PDO::FETCH_ASSOC);
        } catch (Exception $e) {
            error_log("CMS getMenuByIdentifier error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Получение элементов меню
     */
    public function getMenuItems($menu_id, $parent_id = null) {
        try {
            $query = "SELECT mi.*, p.title as page_title 
                      FROM cms_menu_items mi 
                      LEFT JOIN cms_pages p ON mi.page_id = p.id 
                      WHERE mi.menu_id = :menu_id AND mi.status = 'active'";
            
            if ($parent_id === null) {
                $query .= " AND mi.parent_id IS NULL";
            } else {
                $query .= " AND mi.parent_id = :parent_id";
            }
            
            $query .= " ORDER BY mi.sort_order ASC";
            
            $stmt = $this->db->prepare($query);
            $stmt->bindParam(':menu_id', $menu_id, PDO::PARAM_INT);
            
            if ($parent_id !== null) {
                $stmt->bindParam(':parent_id', $parent_id, PDO::PARAM_INT);
            }
            
            $stmt->execute();
            $items = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            // Получение дочерних элементов
            foreach ($items as &$item) {
                $item['children'] = $this->getMenuItems($menu_id, $item['id']);
            }
            
            return $items;
        } catch (Exception $e) {
            error_log("CMS getMenuItems error: " . $e->getMessage());
            return [];
        }
    }
    
    // ==================== НАСТРОЙКИ ====================
    
    /**
     * Получение настройки
     */
    public function getSetting($key, $default = null) {
        try {
            $query = "SELECT setting_value FROM cms_settings WHERE setting_key = :key";
            $stmt = $this->db->prepare($query);
            $stmt->bindParam(':key', $key);
            $stmt->execute();
            
            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            return $result ? $result['setting_value'] : $default;
        } catch (Exception $e) {
            error_log("CMS getSetting error: " . $e->getMessage());
            return $default;
        }
    }
    
    /**
     * Сохранение настройки
     */
    public function setSetting($key, $value, $type = 'string', $group = 'general') {
        try {
            $query = "INSERT INTO cms_settings (setting_key, setting_value, setting_type, group_name) 
                      VALUES (:key, :value, :type, :group) 
                      ON DUPLICATE KEY UPDATE 
                      setting_value = :value, setting_type = :type, group_name = :group, updated_at = NOW()";
            
            $stmt = $this->db->prepare($query);
            $stmt->bindParam(':key', $key);
            $stmt->bindParam(':value', $value);
            $stmt->bindParam(':type', $type);
            $stmt->bindParam(':group', $group);
            
            return $stmt->execute();
        } catch (Exception $e) {
            error_log("CMS setSetting error: " . $e->getMessage());
            return false;
        }
    }
    
    // ==================== РЕВИЗИИ ====================
    
    /**
     * Сохранение ревизии
     */
    private function saveRevision($entity_type, $entity_id, $content, $action = 'update') {
        try {
            $query = "INSERT INTO cms_revisions (entity_type, entity_id, content, meta_data, user_id, action) 
                      VALUES (:entity_type, :entity_id, :content, :meta_data, :user_id, :action)";
            
            $stmt = $this->db->prepare($query);
            $stmt->bindParam(':entity_type', $entity_type);
            $stmt->bindParam(':entity_id', $entity_id, PDO::PARAM_INT);
            $stmt->bindParam(':content', json_encode($content, JSON_UNESCAPED_UNICODE));
            $stmt->bindParam(':meta_data', json_encode(['ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown'], JSON_UNESCAPED_UNICODE));
            $stmt->bindParam(':user_id', $_SESSION['user_id'], PDO::PARAM_INT);
            $stmt->bindParam(':action', $action);
            
            return $stmt->execute();
        } catch (Exception $e) {
            error_log("CMS saveRevision error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Генерация уникального slug
     */
    public function generateSlug($title, $page_id = null) {
        $slug = strtolower(trim($title));
        $slug = preg_replace('/[^a-z0-9-]/', '-', $slug);
        $slug = preg_replace('/-+/', '-', $slug);
        $slug = trim($slug, '-');
        
        // Проверка уникальности
        $original_slug = $slug;
        $counter = 1;
        
        while ($this->slugExists($slug, $page_id)) {
            $slug = $original_slug . '-' . $counter;
            $counter++;
        }
        
        return $slug;
    }
    
    /**
     * Проверка существования slug
     */
    private function slugExists($slug, $exclude_id = null) {
        try {
            $query = "SELECT id FROM cms_pages WHERE slug = :slug";
            $params = [':slug' => $slug];
            
            if ($exclude_id) {
                $query .= " AND id != :exclude_id";
                $params[':exclude_id'] = $exclude_id;
            }
            
            $stmt = $this->db->prepare($query);
            foreach ($params as $key => $value) {
                $stmt->bindValue($key, $value, is_int($value) ? PDO::PARAM_INT : PDO::PARAM_STR);
            }
            $stmt->execute();
            
            return $stmt->rowCount() > 0;
        } catch (Exception $e) {
            error_log("CMS slugExists error: " . $e->getMessage());
            return false;
        }
    }
}
?>
