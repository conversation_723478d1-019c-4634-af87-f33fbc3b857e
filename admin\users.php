<?php
/**
 * AstroGenix - Управление пользователями
 * Эко-майнинговая инвестиционная платформа
 */

require_once '../config/config.php';

// Проверка авторизации и прав администратора
require_admin();

// Параметры фильтрации и пагинации
$search = sanitize_input($_GET['search'] ?? '');
$status_filter = sanitize_input($_GET['status'] ?? '');
$page = max(1, intval($_GET['page'] ?? 1));
$per_page = 20;
$offset = ($page - 1) * $per_page;

// Инициализация переменных
$users = [];
$total_users = 0;
$error_message = '';
$success_message = '';

try {
    $database = new Database();
    $db = $database->getConnection();
    
    // Обработка действий с пользователями
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        if (!verify_csrf_token($_POST['csrf_token'] ?? '')) {
            $error_message = 'Ошибка безопасности. Попробуйте еще раз.';
        } else {
            $action = sanitize_input($_POST['action'] ?? '');
            $user_id = intval($_POST['user_id'] ?? 0);
            
            if ($action === 'toggle_status' && $user_id > 0) {
                $toggle_query = "UPDATE users SET is_active = NOT is_active WHERE id = :user_id";
                $toggle_stmt = $db->prepare($toggle_query);
                $toggle_stmt->bindParam(':user_id', $user_id, PDO::PARAM_INT);
                
                if ($toggle_stmt->execute()) {
                    $success_message = 'Статус пользователя изменен успешно.';
                } else {
                    $error_message = 'Ошибка при изменении статуса пользователя.';
                }
            } elseif ($action === 'update_balance' && $user_id > 0) {
                $new_balance = floatval($_POST['new_balance'] ?? 0);
                $admin_note = sanitize_input($_POST['admin_note'] ?? '');
                
                $update_query = "UPDATE users SET balance = :balance WHERE id = :user_id";
                $update_stmt = $db->prepare($update_query);
                $update_stmt->bindParam(':balance', $new_balance);
                $update_stmt->bindParam(':user_id', $user_id, PDO::PARAM_INT);
                
                if ($update_stmt->execute()) {
                    // Создание записи о транзакции
                    $transaction_query = "INSERT INTO transactions (user_id, type, amount, status, description, processed_by) 
                                         VALUES (:user_id, 'admin_adjustment', :amount, 'completed', :description, :admin_id)";
                    $transaction_stmt = $db->prepare($transaction_query);
                    $amount = $new_balance;
                    $description = "Корректировка баланса администратором. " . $admin_note;
                    $transaction_stmt->bindParam(':user_id', $user_id, PDO::PARAM_INT);
                    $transaction_stmt->bindParam(':amount', $amount);
                    $transaction_stmt->bindParam(':description', $description);
                    $transaction_stmt->bindParam(':admin_id', $_SESSION['user_id'], PDO::PARAM_INT);
                    $transaction_stmt->execute();
                    
                    $success_message = 'Баланс пользователя обновлен успешно.';
                } else {
                    $error_message = 'Ошибка при обновлении баланса.';
                }
            }
        }
    }
    
    // Построение запроса для получения пользователей
    $where_conditions = [];
    $params = [];
    
    if (!empty($search)) {
        $where_conditions[] = "(username LIKE :search OR email LIKE :search OR first_name LIKE :search OR last_name LIKE :search)";
        $params[':search'] = '%' . $search . '%';
    }
    
    if ($status_filter === 'active') {
        $where_conditions[] = "is_active = 1";
    } elseif ($status_filter === 'inactive') {
        $where_conditions[] = "is_active = 0";
    } elseif ($status_filter === 'admin') {
        $where_conditions[] = "is_admin = 1";
    }
    
    $where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';
    
    // Получение общего количества пользователей
    $count_query = "SELECT COUNT(*) as total FROM users $where_clause";
    $count_stmt = $db->prepare($count_query);
    foreach ($params as $key => $value) {
        $count_stmt->bindValue($key, $value);
    }
    $count_stmt->execute();
    $total_users = $count_stmt->fetch(PDO::FETCH_ASSOC)['total'];
    
    // Получение пользователей с пагинацией
    $users_query = "SELECT u.*, 
                           (SELECT COUNT(*) FROM users WHERE referred_by = u.id) as referrals_count,
                           (SELECT COUNT(*) FROM user_investments WHERE user_id = u.id AND status = 'active') as active_investments
                    FROM users u 
                    $where_clause 
                    ORDER BY u.created_at DESC 
                    LIMIT :limit OFFSET :offset";
    
    $users_stmt = $db->prepare($users_query);
    foreach ($params as $key => $value) {
        $users_stmt->bindValue($key, $value);
    }
    $users_stmt->bindValue(':limit', $per_page, PDO::PARAM_INT);
    $users_stmt->bindValue(':offset', $offset, PDO::PARAM_INT);
    $users_stmt->execute();
    $users = $users_stmt->fetchAll(PDO::FETCH_ASSOC);
    
} catch (Exception $e) {
    error_log("Admin users error: " . $e->getMessage());
    $error_message = "Ошибка загрузки данных. Попробуйте обновить страницу.";
}

$total_pages = ceil($total_users / $per_page);
$page_title = 'Управление пользователями - Админ-панель';
?>
<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?></title>
    
    <!-- CSS -->
    <link rel="stylesheet" href="../assets/css/style.css">
    <link rel="stylesheet" href="../assets/css/dashboard.css">
    <link rel="stylesheet" href="../assets/css/admin.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body class="dashboard-page admin-page">
    <!-- Admin Sidebar -->
    <?php include 'includes/admin-sidebar.php'; ?>

    <!-- Main Content -->
    <main class="main-content">
        <!-- Header -->
        <header class="dashboard-header">
            <div class="header-left">
                <button class="mobile-menu-toggle" id="mobileMenuToggle">
                    <i class="fas fa-bars"></i>
                </button>
                <h1>Управление пользователями</h1>
            </div>
            <div class="header-right">
                <div class="header-stats">
                    <span class="stat-item">
                        <i class="fas fa-users"></i>
                        Всего: <?php echo number_format($total_users); ?>
                    </span>
                </div>
            </div>
        </header>

        <!-- Dashboard Content -->
        <div class="dashboard-content">
            <?php if (!empty($error_message)): ?>
                <div class="alert alert-error">
                    <i class="fas fa-exclamation-triangle"></i>
                    <?php echo htmlspecialchars($error_message); ?>
                </div>
            <?php endif; ?>

            <?php if (!empty($success_message)): ?>
                <div class="alert alert-success">
                    <i class="fas fa-check-circle"></i>
                    <?php echo htmlspecialchars($success_message); ?>
                </div>
            <?php endif; ?>

            <!-- Filters -->
            <div class="admin-filters">
                <form method="GET" class="filters-form">
                    <div class="filter-group">
                        <input type="text" name="search" value="<?php echo htmlspecialchars($search); ?>" 
                               placeholder="Поиск по имени, email или username" class="filter-input">
                    </div>
                    <div class="filter-group">
                        <select name="status" class="filter-select">
                            <option value="">Все статусы</option>
                            <option value="active" <?php echo $status_filter === 'active' ? 'selected' : ''; ?>>Активные</option>
                            <option value="inactive" <?php echo $status_filter === 'inactive' ? 'selected' : ''; ?>>Неактивные</option>
                            <option value="admin" <?php echo $status_filter === 'admin' ? 'selected' : ''; ?>>Администраторы</option>
                        </select>
                    </div>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search"></i>
                        Найти
                    </button>
                    <a href="users.php" class="btn btn-outline">
                        <i class="fas fa-times"></i>
                        Сбросить
                    </a>
                </form>
            </div>

            <!-- Users Table -->
            <div class="admin-table-card">
                <div class="table-header">
                    <h3><i class="fas fa-users"></i> Пользователи</h3>
                    <div class="table-actions">
                        <button class="btn btn-primary" onclick="exportUsers()">
                            <i class="fas fa-download"></i>
                            Экспорт
                        </button>
                    </div>
                </div>
                <div class="table-container">
                    <?php if (empty($users)): ?>
                        <div class="empty-state">
                            <i class="fas fa-users"></i>
                            <p>Пользователи не найдены</p>
                        </div>
                    <?php else: ?>
                        <table class="admin-table">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Пользователь</th>
                                    <th>Email</th>
                                    <th>Баланс</th>
                                    <th>Инвестировано</th>
                                    <th>Рефералы</th>
                                    <th>Статус</th>
                                    <th>Дата регистрации</th>
                                    <th>Действия</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($users as $user): ?>
                                    <tr>
                                        <td><?php echo $user['id']; ?></td>
                                        <td>
                                            <div class="user-info">
                                                <strong><?php echo htmlspecialchars($user['first_name'] . ' ' . $user['last_name']); ?></strong>
                                                <small>@<?php echo htmlspecialchars($user['username']); ?></small>
                                                <?php if ($user['is_admin']): ?>
                                                    <span class="badge badge-admin">Админ</span>
                                                <?php endif; ?>
                                            </div>
                                        </td>
                                        <td>
                                            <?php echo htmlspecialchars($user['email']); ?>
                                            <?php if (!$user['email_verified']): ?>
                                                <span class="badge badge-warning">Не подтвержден</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <span class="balance-amount"><?php echo format_currency($user['balance']); ?></span>
                                        </td>
                                        <td><?php echo format_currency($user['total_invested']); ?></td>
                                        <td><?php echo $user['referrals_count']; ?></td>
                                        <td>
                                            <span class="status-badge status-<?php echo $user['is_active'] ? 'active' : 'inactive'; ?>">
                                                <?php echo $user['is_active'] ? 'Активен' : 'Неактивен'; ?>
                                            </span>
                                        </td>
                                        <td><?php echo date('d.m.Y H:i', strtotime($user['created_at'])); ?></td>
                                        <td>
                                            <div class="action-buttons">
                                                <button class="btn btn-small btn-outline" onclick="editUser(<?php echo $user['id']; ?>)">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                <button class="btn btn-small <?php echo $user['is_active'] ? 'btn-warning' : 'btn-success'; ?>" 
                                                        onclick="toggleUserStatus(<?php echo $user['id']; ?>)">
                                                    <i class="fas fa-<?php echo $user['is_active'] ? 'ban' : 'check'; ?>"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    <?php endif; ?>
                </div>
                
                <!-- Pagination -->
                <?php if ($total_pages > 1): ?>
                    <div class="pagination">
                        <?php if ($page > 1): ?>
                            <a href="?page=<?php echo $page - 1; ?>&search=<?php echo urlencode($search); ?>&status=<?php echo urlencode($status_filter); ?>" 
                               class="pagination-btn">
                                <i class="fas fa-chevron-left"></i>
                            </a>
                        <?php endif; ?>
                        
                        <?php for ($i = max(1, $page - 2); $i <= min($total_pages, $page + 2); $i++): ?>
                            <a href="?page=<?php echo $i; ?>&search=<?php echo urlencode($search); ?>&status=<?php echo urlencode($status_filter); ?>" 
                               class="pagination-btn <?php echo $i === $page ? 'active' : ''; ?>">
                                <?php echo $i; ?>
                            </a>
                        <?php endfor; ?>
                        
                        <?php if ($page < $total_pages): ?>
                            <a href="?page=<?php echo $page + 1; ?>&search=<?php echo urlencode($search); ?>&status=<?php echo urlencode($status_filter); ?>" 
                               class="pagination-btn">
                                <i class="fas fa-chevron-right"></i>
                            </a>
                        <?php endif; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </main>

    <!-- Edit User Modal -->
    <div id="editUserModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Редактирование пользователя</h3>
                <button class="modal-close" onclick="closeEditUserModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <form id="editUserForm" method="POST">
                <input type="hidden" name="csrf_token" value="<?php echo generate_csrf_token(); ?>">
                <input type="hidden" name="action" value="update_balance">
                <input type="hidden" name="user_id" id="editUserId">
                
                <div class="modal-body">
                    <div class="form-group">
                        <label for="newBalance">Новый баланс (USDT)</label>
                        <input type="number" id="newBalance" name="new_balance" step="0.01" min="0" required>
                    </div>
                    <div class="form-group">
                        <label for="adminNote">Примечание администратора</label>
                        <textarea id="adminNote" name="admin_note" rows="3" 
                                  placeholder="Причина изменения баланса"></textarea>
                    </div>
                </div>
                
                <div class="modal-footer">
                    <button type="button" class="btn btn-outline" onclick="closeEditUserModal()">Отмена</button>
                    <button type="submit" class="btn btn-primary">Сохранить</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Toggle Status Form -->
    <form id="toggleStatusForm" method="POST" style="display: none;">
        <input type="hidden" name="csrf_token" value="<?php echo generate_csrf_token(); ?>">
        <input type="hidden" name="action" value="toggle_status">
        <input type="hidden" name="user_id" id="toggleUserId">
    </form>

    <!-- JavaScript -->
    <script src="../assets/js/admin.js"></script>
    <script>
        function editUser(userId) {
            document.getElementById('editUserId').value = userId;
            document.getElementById('editUserModal').style.display = 'block';
        }
        
        function closeEditUserModal() {
            document.getElementById('editUserModal').style.display = 'none';
        }
        
        function toggleUserStatus(userId) {
            if (confirm('Вы уверены, что хотите изменить статус пользователя?')) {
                document.getElementById('toggleUserId').value = userId;
                document.getElementById('toggleStatusForm').submit();
            }
        }
        
        function exportUsers() {
            window.location.href = 'export.php?type=users';
        }
    </script>
</body>
</html>
