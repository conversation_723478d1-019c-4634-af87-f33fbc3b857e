<?php
/**
 * AstroGenix - API для работы с уведомлениями
 * Эко-майнинговая инвестиционная платформа
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

require_once '../config/config.php';
require_once '../classes/Notification.php';

// Проверка авторизации
if (!is_logged_in()) {
    http_response_code(401);
    echo json_encode(['error' => 'Unauthorized']);
    exit;
}

try {
    $database = new Database();
    $db = $database->getConnection();
    $notification = new Notification($db);
    
    $method = $_SERVER['REQUEST_METHOD'];
    $user_id = $_SESSION['user_id'];
    
    switch ($method) {
        case 'GET':
            handleGetRequest($notification, $user_id);
            break;
            
        case 'POST':
            handlePostRequest($notification, $user_id);
            break;
            
        case 'PUT':
            handlePutRequest($notification, $user_id);
            break;
            
        case 'DELETE':
            handleDeleteRequest($notification, $user_id);
            break;
            
        default:
            http_response_code(405);
            echo json_encode(['error' => 'Method not allowed']);
            break;
    }
    
} catch (Exception $e) {
    error_log("Notifications API error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['error' => 'Internal server error']);
}

function handleGetRequest($notification, $user_id) {
    $action = $_GET['action'] ?? 'list';
    
    switch ($action) {
        case 'list':
            $limit = intval($_GET['limit'] ?? 10);
            $unread_only = isset($_GET['unread_only']) && $_GET['unread_only'] === 'true';
            
            $notifications = $notification->getUserNotifications($user_id, $limit, $unread_only);
            $unread_count = $notification->getUnreadCount($user_id);
            
            echo json_encode([
                'success' => true,
                'notifications' => $notifications,
                'unread_count' => $unread_count
            ]);
            break;
            
        case 'count':
            $unread_count = $notification->getUnreadCount($user_id);
            
            echo json_encode([
                'success' => true,
                'unread_count' => $unread_count
            ]);
            break;
            
        default:
            http_response_code(400);
            echo json_encode(['error' => 'Invalid action']);
            break;
    }
}

function handlePostRequest($notification, $user_id) {
    $input = json_decode(file_get_contents('php://input'), true);
    $action = $input['action'] ?? '';
    
    switch ($action) {
        case 'mark_all_read':
            $result = $notification->markAllAsRead($user_id);
            
            echo json_encode([
                'success' => $result,
                'message' => $result ? 'Все уведомления отмечены как прочитанные' : 'Ошибка при обновлении уведомлений'
            ]);
            break;
            
        case 'create_system':
            // Только для администраторов
            if (!is_admin()) {
                http_response_code(403);
                echo json_encode(['error' => 'Access denied']);
                return;
            }
            
            $title = $input['title'] ?? '';
            $message = $input['message'] ?? '';
            $type = $input['type'] ?? 'info';
            
            if (empty($title) || empty($message)) {
                http_response_code(400);
                echo json_encode(['error' => 'Title and message are required']);
                return;
            }
            
            $created_count = $notification->createSystemNotification($title, $message, $type);
            
            echo json_encode([
                'success' => $created_count > 0,
                'created_count' => $created_count,
                'message' => "Создано $created_count уведомлений"
            ]);
            break;
            
        default:
            http_response_code(400);
            echo json_encode(['error' => 'Invalid action']);
            break;
    }
}

function handlePutRequest($notification, $user_id) {
    $input = json_decode(file_get_contents('php://input'), true);
    $notification_id = intval($input['id'] ?? 0);
    $action = $input['action'] ?? 'mark_read';
    
    if ($notification_id <= 0) {
        http_response_code(400);
        echo json_encode(['error' => 'Invalid notification ID']);
        return;
    }
    
    switch ($action) {
        case 'mark_read':
            $result = $notification->markAsRead($notification_id, $user_id);
            
            echo json_encode([
                'success' => $result,
                'message' => $result ? 'Уведомление отмечено как прочитанное' : 'Ошибка при обновлении уведомления'
            ]);
            break;
            
        default:
            http_response_code(400);
            echo json_encode(['error' => 'Invalid action']);
            break;
    }
}

function handleDeleteRequest($notification, $user_id) {
    $input = json_decode(file_get_contents('php://input'), true);
    $notification_id = intval($input['id'] ?? 0);
    
    if ($notification_id <= 0) {
        http_response_code(400);
        echo json_encode(['error' => 'Invalid notification ID']);
        return;
    }
    
    $result = $notification->delete($notification_id, $user_id);
    
    echo json_encode([
        'success' => $result,
        'message' => $result ? 'Уведомление удалено' : 'Ошибка при удалении уведомления'
    ]);
}
?>
