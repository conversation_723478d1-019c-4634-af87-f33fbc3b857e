-- AstroGenix CMS Database Schema
-- Система управления контентом

-- Таблица страниц
CREATE TABLE IF NOT EXISTS cms_pages (
    id INT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    slug VARCHAR(255) UNIQUE NOT NULL,
    content LONGTEXT,
    excerpt TEXT,
    meta_title VARCHAR(255),
    meta_description TEXT,
    meta_keywords TEXT,
    template VARCHAR(100) DEFAULT 'default',
    status ENUM('draft', 'published', 'archived') DEFAULT 'draft',
    featured_image VARCHAR(255),
    author_id INT,
    parent_id INT DEFAULT NULL,
    sort_order INT DEFAULT 0,
    is_homepage BOOLEAN DEFAULT FALSE,
    show_in_menu BOOLEAN DEFAULT TRUE,
    custom_css TEXT,
    custom_js TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    published_at TIMESTAMP NULL,
    FOR<PERSON><PERSON><PERSON> KEY (author_id) REFERENCES users(id) ON DELETE SET NULL,
    FOREIGN KEY (parent_id) REFERENCES cms_pages(id) ON DELETE SET NULL,
    INDEX idx_slug (slug),
    INDEX idx_status (status),
    INDEX idx_parent_id (parent_id),
    INDEX idx_sort_order (sort_order)
);

-- Таблица блоков контента
CREATE TABLE IF NOT EXISTS cms_blocks (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    identifier VARCHAR(100) UNIQUE NOT NULL,
    content LONGTEXT,
    description TEXT,
    type ENUM('text', 'html', 'widget', 'banner', 'form') DEFAULT 'text',
    status ENUM('active', 'inactive') DEFAULT 'active',
    position VARCHAR(50) DEFAULT 'content',
    sort_order INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_identifier (identifier),
    INDEX idx_status (status),
    INDEX idx_position (position)
);

-- Таблица меню
CREATE TABLE IF NOT EXISTS cms_menus (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    identifier VARCHAR(100) UNIQUE NOT NULL,
    description TEXT,
    status ENUM('active', 'inactive') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_identifier (identifier),
    INDEX idx_status (status)
);

-- Таблица элементов меню
CREATE TABLE IF NOT EXISTS cms_menu_items (
    id INT AUTO_INCREMENT PRIMARY KEY,
    menu_id INT NOT NULL,
    parent_id INT DEFAULT NULL,
    title VARCHAR(255) NOT NULL,
    url VARCHAR(500),
    page_id INT DEFAULT NULL,
    target VARCHAR(20) DEFAULT '_self',
    css_class VARCHAR(100),
    icon VARCHAR(100),
    sort_order INT DEFAULT 0,
    status ENUM('active', 'inactive') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (menu_id) REFERENCES cms_menus(id) ON DELETE CASCADE,
    FOREIGN KEY (parent_id) REFERENCES cms_menu_items(id) ON DELETE CASCADE,
    FOREIGN KEY (page_id) REFERENCES cms_pages(id) ON DELETE SET NULL,
    INDEX idx_menu_id (menu_id),
    INDEX idx_parent_id (parent_id),
    INDEX idx_sort_order (sort_order),
    INDEX idx_status (status)
);

-- Таблица медиафайлов
CREATE TABLE IF NOT EXISTS cms_media (
    id INT AUTO_INCREMENT PRIMARY KEY,
    filename VARCHAR(255) NOT NULL,
    original_name VARCHAR(255) NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    file_size INT NOT NULL,
    mime_type VARCHAR(100) NOT NULL,
    file_type ENUM('image', 'document', 'video', 'audio', 'other') NOT NULL,
    alt_text VARCHAR(255),
    caption TEXT,
    description TEXT,
    width INT DEFAULT NULL,
    height INT DEFAULT NULL,
    uploaded_by INT,
    folder_id INT DEFAULT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (uploaded_by) REFERENCES users(id) ON DELETE SET NULL,
    FOREIGN KEY (folder_id) REFERENCES cms_media_folders(id) ON DELETE SET NULL,
    INDEX idx_file_type (file_type),
    INDEX idx_uploaded_by (uploaded_by),
    INDEX idx_folder_id (folder_id),
    INDEX idx_created_at (created_at)
);

-- Таблица папок для медиафайлов
CREATE TABLE IF NOT EXISTS cms_media_folders (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    parent_id INT DEFAULT NULL,
    path VARCHAR(500) NOT NULL,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (parent_id) REFERENCES cms_media_folders(id) ON DELETE CASCADE,
    INDEX idx_parent_id (parent_id),
    INDEX idx_path (path)
);

-- Таблица шаблонов
CREATE TABLE IF NOT EXISTS cms_templates (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    identifier VARCHAR(100) UNIQUE NOT NULL,
    description TEXT,
    template_path VARCHAR(255) NOT NULL,
    preview_image VARCHAR(255),
    template_type ENUM('page', 'block', 'email') DEFAULT 'page',
    variables JSON,
    status ENUM('active', 'inactive') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_identifier (identifier),
    INDEX idx_template_type (template_type),
    INDEX idx_status (status)
);

-- Таблица настроек CMS
CREATE TABLE IF NOT EXISTS cms_settings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    setting_key VARCHAR(100) UNIQUE NOT NULL,
    setting_value LONGTEXT,
    setting_type ENUM('string', 'text', 'number', 'boolean', 'json', 'file') DEFAULT 'string',
    group_name VARCHAR(50) DEFAULT 'general',
    description TEXT,
    is_public BOOLEAN DEFAULT FALSE,
    sort_order INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_setting_key (setting_key),
    INDEX idx_group_name (group_name),
    INDEX idx_is_public (is_public)
);

-- Таблица связей страниц с блоками
CREATE TABLE IF NOT EXISTS cms_page_blocks (
    id INT AUTO_INCREMENT PRIMARY KEY,
    page_id INT NOT NULL,
    block_id INT NOT NULL,
    position VARCHAR(50) NOT NULL,
    sort_order INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (page_id) REFERENCES cms_pages(id) ON DELETE CASCADE,
    FOREIGN KEY (block_id) REFERENCES cms_blocks(id) ON DELETE CASCADE,
    INDEX idx_page_id (page_id),
    INDEX idx_block_id (block_id),
    INDEX idx_position (position),
    UNIQUE KEY unique_page_block_position (page_id, block_id, position)
);

-- Таблица истории изменений
CREATE TABLE IF NOT EXISTS cms_revisions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    entity_type ENUM('page', 'block', 'menu') NOT NULL,
    entity_id INT NOT NULL,
    content LONGTEXT,
    meta_data JSON,
    user_id INT,
    action ENUM('create', 'update', 'delete') NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_entity (entity_type, entity_id),
    INDEX idx_user_id (user_id),
    INDEX idx_created_at (created_at)
);

-- Вставка базовых данных
INSERT IGNORE INTO cms_settings (setting_key, setting_value, setting_type, group_name, description, is_public) VALUES
('site_title', 'AstroGenix', 'string', 'general', 'Название сайта', true),
('site_tagline', 'Эко-майнинговая инвестиционная платформа', 'string', 'general', 'Слоган сайта', true),
('site_description', 'Инвестируйте в будущее с заботой о планете', 'text', 'general', 'Описание сайта', true),
('site_keywords', 'инвестиции, экология, майнинг, криптовалюта, зеленая энергия', 'text', 'seo', 'Ключевые слова сайта', true),
('site_logo', '', 'file', 'general', 'Логотип сайта', true),
('site_favicon', '', 'file', 'general', 'Иконка сайта', true),
('contact_email', '<EMAIL>', 'string', 'contact', 'Email для связи', true),
('contact_phone', '+7 (800) 123-45-67', 'string', 'contact', 'Телефон для связи', true),
('social_facebook', '', 'string', 'social', 'Facebook страница', true),
('social_twitter', '', 'string', 'social', 'Twitter профиль', true),
('social_instagram', '', 'string', 'social', 'Instagram профиль', true),
('social_telegram', '', 'string', 'social', 'Telegram канал', true),
('analytics_google', '', 'text', 'analytics', 'Google Analytics код', false),
('analytics_yandex', '', 'text', 'analytics', 'Яндекс.Метрика код', false),
('editor_type', 'tinymce', 'string', 'editor', 'Тип редактора контента', false),
('upload_max_size', '10485760', 'number', 'media', 'Максимальный размер файла (байты)', false),
('upload_allowed_types', 'jpg,jpeg,png,gif,pdf,doc,docx', 'string', 'media', 'Разрешенные типы файлов', false);

-- Создание базовых шаблонов
INSERT IGNORE INTO cms_templates (name, identifier, description, template_path, template_type, status) VALUES
('Стандартная страница', 'default', 'Базовый шаблон для обычных страниц', 'templates/page-default.php', 'page', 'active'),
('Главная страница', 'homepage', 'Шаблон для главной страницы', 'templates/page-homepage.php', 'page', 'active'),
('Страница с сайдбаром', 'sidebar', 'Шаблон страницы с боковой панелью', 'templates/page-sidebar.php', 'page', 'active'),
('Полноширинная страница', 'fullwidth', 'Шаблон на всю ширину экрана', 'templates/page-fullwidth.php', 'page', 'active'),
('Текстовый блок', 'text-block', 'Простой текстовый блок', 'templates/block-text.php', 'block', 'active'),
('Баннер', 'banner-block', 'Блок с баннером', 'templates/block-banner.php', 'block', 'active');

-- Создание базовых меню
INSERT IGNORE INTO cms_menus (name, identifier, description, status) VALUES
('Главное меню', 'main-menu', 'Основное меню сайта в шапке', 'active'),
('Меню подвала', 'footer-menu', 'Меню в подвале сайта', 'active'),
('Боковое меню', 'sidebar-menu', 'Меню для боковой панели', 'active');

-- Создание базовых папок для медиафайлов
INSERT IGNORE INTO cms_media_folders (name, path, description) VALUES
('Изображения', '/uploads/images/', 'Папка для изображений'),
('Документы', '/uploads/documents/', 'Папка для документов'),
('Видео', '/uploads/videos/', 'Папка для видеофайлов'),
('Иконки', '/uploads/icons/', 'Папка для иконок и логотипов');

-- Создание базовых блоков контента
INSERT IGNORE INTO cms_blocks (name, identifier, content, description, type, status, position) VALUES
('Приветственный блок', 'welcome-block', '<h2>Добро пожаловать в AstroGenix!</h2><p>Инвестируйте в будущее с заботой о планете.</p>', 'Приветственное сообщение на главной странице', 'html', 'active', 'header'),
('Контактная информация', 'contact-info', '<p><strong>Email:</strong> <EMAIL><br><strong>Телефон:</strong> +7 (800) 123-45-67</p>', 'Контактные данные компании', 'html', 'active', 'footer'),
('Социальные сети', 'social-links', '<div class="social-links"><a href="#" class="social-link"><i class="fab fa-facebook"></i></a><a href="#" class="social-link"><i class="fab fa-twitter"></i></a><a href="#" class="social-link"><i class="fab fa-instagram"></i></a></div>', 'Ссылки на социальные сети', 'html', 'active', 'footer'),
('Счетчик экологии', 'eco-counter', '<div class="eco-stats"><div class="eco-item"><span class="eco-value">1,234</span><span class="eco-label">кВт⋅ч зеленой энергии</span></div><div class="eco-item"><span class="eco-value">567</span><span class="eco-label">кг CO₂ сохранено</span></div></div>', 'Статистика экологических достижений', 'widget', 'active', 'sidebar');

-- Создание базовой страницы
INSERT IGNORE INTO cms_pages (title, slug, content, meta_title, meta_description, template, status, author_id, is_homepage, show_in_menu, published_at) VALUES
('Главная страница', 'home', '<h1>AstroGenix - Инвестиции в зеленое будущее</h1><p>Присоединяйтесь к революции экологически чистых инвестиций. Наша платформа предлагает уникальные возможности для получения стабильной прибыли, способствуя развитию зеленых технологий.</p>', 'AstroGenix - Эко-майнинговая инвестиционная платформа', 'Инвестируйте в будущее с заботой о планете. Стабильная прибыль от экологически чистых технологий.', 'homepage', 'published', 1, true, false, NOW());
