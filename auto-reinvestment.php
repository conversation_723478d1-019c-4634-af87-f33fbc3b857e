<?php
/**
 * AstroGenix - Настройки автоматического реинвестирования
 * Эко-майнинговая инвестиционная платформа
 */

session_start();
require_once 'config/config.php';
require_once 'classes/AutoReinvestment.php';
require_once 'classes/EnhancedInvestment.php';

// Проверка авторизации
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit();
}

$database = new Database();
$db = $database->getConnection();
$auto_reinvestment = new AutoReinvestment($db);
$enhanced_investment = new EnhancedInvestment($db);

$errors = [];
$success_message = '';

// Обработка сохранения настроек
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['save_settings'])) {
    if (!verify_csrf_token($_POST['csrf_token'] ?? '')) {
        $errors[] = 'Ошибка безопасности. Попробуйте еще раз.';
    } else {
        $settings = [
            'is_enabled' => isset($_POST['is_enabled']),
            'min_amount' => floatval($_POST['min_amount'] ?? 50),
            'preferred_package_id' => !empty($_POST['preferred_package_id']) ? intval($_POST['preferred_package_id']) : null,
            'reinvest_percent' => floatval($_POST['reinvest_percent'] ?? 50),
            'max_investments_per_day' => intval($_POST['max_investments_per_day'] ?? 1)
        ];

        // Валидация
        if ($settings['min_amount'] < 10) {
            $errors[] = 'Минимальная сумма не может быть меньше 10 USDT.';
        }
        
        if ($settings['reinvest_percent'] < 1 || $settings['reinvest_percent'] > 100) {
            $errors[] = 'Процент реинвестирования должен быть от 1% до 100%.';
        }
        
        if ($settings['max_investments_per_day'] < 1 || $settings['max_investments_per_day'] > 10) {
            $errors[] = 'Максимальное количество инвестиций в день должно быть от 1 до 10.';
        }

        if (empty($errors)) {
            if ($auto_reinvestment->updateUserSettings($_SESSION['user_id'], $settings)) {
                $success_message = 'Настройки автореинвестирования успешно сохранены!';
            } else {
                $errors[] = 'Ошибка при сохранении настроек. Попробуйте еще раз.';
            }
        }
    }
}

// Получение данных
$user_settings = $auto_reinvestment->getUserSettings($_SESSION['user_id']);
$packages = $enhanced_investment->getAllPackages();
$reinvestment_stats = $auto_reinvestment->getUserReinvestmentStats($_SESSION['user_id']);
$user_balance = $_SESSION['balance'] ?? 0;

$page_title = 'Автореинвестирование - AstroGenix';
?>
<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?></title>
    <meta name="description" content="Настройки автоматического реинвестирования на платформе AstroGenix">
    
    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="assets/images/favicon.svg">
    
    <!-- Стили -->
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="assets/css/investments.css">
    <link rel="stylesheet" href="assets/css/animations.css">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <?php include 'includes/header.php'; ?>

    <main class="main-content">
        <!-- Hero секция -->
        <section class="hero-section auto-reinvestment-hero">
            <div class="container">
                <div class="hero-content animate-fade-in">
                    <h1 class="hero-title">
                        Автоматическое <span class="gradient-text">реинвестирование</span>
                    </h1>
                    <p class="hero-subtitle">
                        Настройте автоматическое реинвестирование прибыли для максимизации доходности. 
                        Система будет автоматически создавать новые инвестиции из вашей прибыли.
                    </p>
                </div>
            </div>
        </section>

        <div class="container">
            <!-- Сообщения -->
            <?php if (!empty($errors)): ?>
                <div class="alert alert-error">
                    <ul>
                        <?php foreach ($errors as $error): ?>
                            <li><?php echo htmlspecialchars($error); ?></li>
                        <?php endforeach; ?>
                    </ul>
                </div>
            <?php endif; ?>

            <?php if ($success_message): ?>
                <div class="alert alert-success">
                    <?php echo htmlspecialchars($success_message); ?>
                </div>
            <?php endif; ?>

            <!-- Статистика автореинвестирования -->
            <section class="section reinvestment-stats">
                <div class="section-header">
                    <h2 class="section-title">Статистика автореинвестирования</h2>
                </div>
                
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-sync-alt"></i>
                        </div>
                        <div class="stat-content">
                            <div class="stat-value"><?php echo number_format($reinvestment_stats['total_reinvestments'] ?? 0); ?></div>
                            <div class="stat-label">Всего реинвестиций</div>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-coins"></i>
                        </div>
                        <div class="stat-content">
                            <div class="stat-value"><?php echo number_format($reinvestment_stats['total_reinvested'] ?? 0, 2); ?> USDT</div>
                            <div class="stat-label">Реинвестировано</div>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <div class="stat-content">
                            <div class="stat-value"><?php echo number_format($reinvestment_stats['avg_reinvestment'] ?? 0, 2); ?> USDT</div>
                            <div class="stat-label">Средняя сумма</div>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-calendar-alt"></i>
                        </div>
                        <div class="stat-content">
                            <div class="stat-value"><?php echo number_format($reinvestment_stats['last_30_days'] ?? 0); ?></div>
                            <div class="stat-label">За 30 дней</div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Настройки автореинвестирования -->
            <section class="section reinvestment-settings">
                <div class="section-header">
                    <h2 class="section-title">Настройки автореинвестирования</h2>
                    <p class="section-subtitle">Настройте параметры автоматического реинвестирования прибыли</p>
                </div>
                
                <div class="settings-container">
                    <form method="POST" class="settings-form">
                        <input type="hidden" name="csrf_token" value="<?php echo generate_csrf_token(); ?>">
                        <input type="hidden" name="save_settings" value="1">
                        
                        <div class="settings-grid">
                            <!-- Основные настройки -->
                            <div class="settings-section">
                                <h3 class="settings-section-title">
                                    <i class="fas fa-cog"></i>
                                    Основные настройки
                                </h3>
                                
                                <div class="form-group">
                                    <label class="toggle-label">
                                        <input type="checkbox" name="is_enabled" <?php echo $user_settings['is_enabled'] ? 'checked' : ''; ?>>
                                        <span class="toggle-slider"></span>
                                        <span class="toggle-text">Включить автореинвестирование</span>
                                    </label>
                                    <div class="form-help">
                                        Автоматически создавать новые инвестиции из накопленной прибыли
                                    </div>
                                </div>
                                
                                <div class="form-group">
                                    <label for="min_amount">Минимальная сумма для реинвестирования (USDT)</label>
                                    <input type="number" id="min_amount" name="min_amount" 
                                           value="<?php echo $user_settings['min_amount']; ?>" 
                                           min="10" max="10000" step="0.01" class="form-control" required>
                                    <div class="form-help">
                                        Минимальная сумма накопленной прибыли для создания новой инвестиции
                                    </div>
                                </div>
                                
                                <div class="form-group">
                                    <label for="reinvest_percent">Процент прибыли для реинвестирования (%)</label>
                                    <div class="range-input-group">
                                        <input type="range" id="reinvest_percent" name="reinvest_percent" 
                                               value="<?php echo $user_settings['reinvest_percent']; ?>" 
                                               min="1" max="100" step="1" class="form-range">
                                        <span class="range-value"><?php echo $user_settings['reinvest_percent']; ?>%</span>
                                    </div>
                                    <div class="form-help">
                                        Какой процент от накопленной прибыли использовать для реинвестирования
                                    </div>
                                </div>
                                
                                <div class="form-group">
                                    <label for="max_investments_per_day">Максимум инвестиций в день</label>
                                    <select id="max_investments_per_day" name="max_investments_per_day" class="form-control">
                                        <?php for ($i = 1; $i <= 10; $i++): ?>
                                            <option value="<?php echo $i; ?>" <?php echo $user_settings['max_investments_per_day'] == $i ? 'selected' : ''; ?>>
                                                <?php echo $i; ?> инвестиц<?php echo $i == 1 ? 'ия' : ($i < 5 ? 'ии' : 'ий'); ?>
                                            </option>
                                        <?php endfor; ?>
                                    </select>
                                    <div class="form-help">
                                        Ограничение количества автоматических инвестиций в день
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Предпочтения пакетов -->
                            <div class="settings-section">
                                <h3 class="settings-section-title">
                                    <i class="fas fa-star"></i>
                                    Предпочтения пакетов
                                </h3>
                                
                                <div class="form-group">
                                    <label for="preferred_package_id">Предпочтительный пакет</label>
                                    <select id="preferred_package_id" name="preferred_package_id" class="form-control">
                                        <option value="">Автоматический выбор</option>
                                        <?php foreach ($packages as $package): ?>
                                            <option value="<?php echo $package['id']; ?>" 
                                                    <?php echo $user_settings['preferred_package_id'] == $package['id'] ? 'selected' : ''; ?>>
                                                <?php echo htmlspecialchars($package['name']); ?> 
                                                (<?php echo $package['daily_profit_percent']; ?>% в день)
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                    <div class="form-help">
                                        Если не выбран, система автоматически выберет наиболее подходящий пакет
                                    </div>
                                </div>
                                
                                <div class="package-recommendations">
                                    <h4>Рекомендации по выбору пакета:</h4>
                                    <ul>
                                        <li><strong>Стартовый:</strong> Для небольших сумм реинвестирования (50-500 USDT)</li>
                                        <li><strong>Стандартный:</strong> Оптимальный баланс риска и доходности</li>
                                        <li><strong>Премиум:</strong> Для крупных сумм с высокой доходностью</li>
                                        <li><strong>Компаундный:</strong> Максимальный рост за счет сложного процента</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Предварительный расчет -->
                        <div class="calculation-preview">
                            <h3 class="preview-title">
                                <i class="fas fa-calculator"></i>
                                Предварительный расчет
                            </h3>
                            
                            <div class="preview-content" id="calculation-preview">
                                <div class="preview-item">
                                    <span class="preview-label">Текущий баланс:</span>
                                    <span class="preview-value"><?php echo number_format($user_balance, 2); ?> USDT</span>
                                </div>
                                <div class="preview-item">
                                    <span class="preview-label">Доступно для реинвестирования:</span>
                                    <span class="preview-value" id="available-amount">0.00 USDT</span>
                                </div>
                                <div class="preview-item">
                                    <span class="preview-label">Сумма реинвестирования:</span>
                                    <span class="preview-value" id="reinvest-amount">0.00 USDT</span>
                                </div>
                                <div class="preview-item">
                                    <span class="preview-label">Статус:</span>
                                    <span class="preview-value" id="reinvest-status">Недостаточно средств</span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="form-actions">
                            <button type="submit" class="btn btn-primary btn-large">
                                <i class="fas fa-save"></i>
                                Сохранить настройки
                            </button>
                            
                            <button type="button" class="btn btn-secondary" id="test-reinvestment">
                                <i class="fas fa-play"></i>
                                Тестовый запуск
                            </button>
                        </div>
                    </form>
                </div>
            </section>

            <!-- Как это работает -->
            <section class="section how-it-works">
                <div class="section-header">
                    <h2 class="section-title">Как работает автореинвестирование</h2>
                </div>
                
                <div class="steps-grid">
                    <div class="step-item">
                        <div class="step-number">1</div>
                        <div class="step-content">
                            <h3>Накопление прибыли</h3>
                            <p>Система отслеживает прибыль от ваших активных инвестиций и накапливает её для реинвестирования.</p>
                        </div>
                    </div>
                    
                    <div class="step-item">
                        <div class="step-number">2</div>
                        <div class="step-content">
                            <h3>Проверка условий</h3>
                            <p>Когда накопленная прибыль достигает минимальной суммы, система проверяет ваши настройки и лимиты.</p>
                        </div>
                    </div>
                    
                    <div class="step-item">
                        <div class="step-number">3</div>
                        <div class="step-content">
                            <h3>Выбор пакета</h3>
                            <p>Система выбирает подходящий инвестиционный пакет согласно вашим предпочтениям или автоматически.</p>
                        </div>
                    </div>
                    
                    <div class="step-item">
                        <div class="step-number">4</div>
                        <div class="step-content">
                            <h3>Создание инвестиции</h3>
                            <p>Автоматически создается новая инвестиция с указанным процентом от накопленной прибыли.</p>
                        </div>
                    </div>
                </div>
            </section>
        </div>
    </main>

    <?php include 'includes/footer.php'; ?>

    <!-- JavaScript -->
    <script src="assets/js/main.js"></script>
    <script src="assets/js/auto-reinvestment.js"></script>
</body>
</html>
