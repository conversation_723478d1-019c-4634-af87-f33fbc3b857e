/**
 * AstroGenix - Стили административной панели
 * Эко-майнинговая инвестиционная платформа
 */

/* Основные стили админки */
.admin-page {
    background: linear-gradient(135deg, #f8fffe 0%, #f0f9ff 100%);
}

.admin-sidebar {
    background: linear-gradient(180deg, #1a1a1a 0%, #2d2d2d 100%);
    color: var(--white);
}

.admin-sidebar .sidebar-logo {
    color: var(--white);
}

.admin-sidebar .sidebar-logo span {
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Навигация админки */
.nav-section {
    margin: var(--space-4) 0 var(--space-2) 0;
}

.section-title {
    display: block;
    padding: 0 var(--space-6);
    font-size: var(--text-xs);
    font-weight: 600;
    color: #888;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.admin-sidebar .nav-link {
    color: #ccc;
    position: relative;
}

.admin-sidebar .nav-link:hover {
    color: var(--white);
    background: rgba(46, 204, 113, 0.1);
}

.admin-sidebar .nav-item.active .nav-link {
    color: var(--white);
    background: rgba(46, 204, 113, 0.2);
}

.nav-badge {
    position: absolute;
    right: var(--space-3);
    top: 50%;
    transform: translateY(-50%);
    background: var(--error);
    color: var(--white);
    font-size: var(--text-xs);
    padding: 2px 6px;
    border-radius: var(--radius-full);
    min-width: 18px;
    text-align: center;
    line-height: 1.2;
}

.nav-badge-warning {
    background: var(--warning);
}

/* Заголовок админки */
.admin-page .dashboard-header {
    background: var(--white);
    border-bottom: 1px solid var(--gray-200);
}

.admin-info {
    text-align: right;
}

.admin-label {
    display: block;
    font-size: var(--text-sm);
    color: var(--gray-600);
}

.admin-name {
    display: block;
    font-size: var(--text-lg);
    font-weight: 600;
    color: var(--gray-900);
}

/* Статистические карточки админки */
.admin-stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: var(--space-6);
    margin-bottom: var(--space-8);
}

.admin-stat-card {
    background: var(--white);
    border-radius: var(--radius-xl);
    padding: var(--space-6);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--gray-200);
    transition: all var(--transition-fast);
    position: relative;
    overflow: hidden;
}

.admin-stat-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
}

.admin-stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
}

.users-card::before {
    background: linear-gradient(135deg, #3498DB 0%, #2980B9 100%);
}

.investments-card::before {
    background: var(--gradient-primary);
}

.transactions-card::before {
    background: linear-gradient(135deg, #F39C12 0%, #E67E22 100%);
}

.finance-card::before {
    background: linear-gradient(135deg, #9B59B6 0%, #8E44AD 100%);
}

.energy-card::before {
    background: linear-gradient(135deg, #27AE60 0%, #2ECC71 100%);
}

.admin-stat-card .stat-icon {
    width: 60px;
    height: 60px;
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--text-2xl);
    color: var(--white);
    margin-bottom: var(--space-4);
}

.users-card .stat-icon {
    background: linear-gradient(135deg, #3498DB 0%, #2980B9 100%);
}

.investments-card .stat-icon {
    background: var(--gradient-primary);
}

.transactions-card .stat-icon {
    background: linear-gradient(135deg, #F39C12 0%, #E67E22 100%);
}

.finance-card .stat-icon {
    background: linear-gradient(135deg, #9B59B6 0%, #8E44AD 100%);
}

.energy-card .stat-icon {
    background: linear-gradient(135deg, #27AE60 0%, #2ECC71 100%);
}

.admin-stat-card .stat-content h3 {
    margin: 0 0 var(--space-2) 0;
    font-size: var(--text-base);
    color: var(--gray-600);
    font-weight: 500;
}

.admin-stat-card .stat-value {
    font-size: var(--text-3xl);
    font-weight: 700;
    color: var(--gray-900);
    margin-bottom: var(--space-2);
}

.stat-action {
    margin-top: var(--space-4);
}

.finance-stats {
    display: flex;
    flex-direction: column;
    gap: var(--space-2);
}

.finance-item {
    display: flex;
    justify-content: space-between;
    font-size: var(--text-sm);
}

.finance-item span:first-child {
    color: var(--gray-600);
}

.finance-item span:last-child {
    font-weight: 600;
    color: var(--gray-900);
}

/* Графики */
.charts-section {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--space-6);
    margin-bottom: var(--space-8);
}

.chart-card {
    background: var(--white);
    border-radius: var(--radius-lg);
    padding: var(--space-6);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--gray-200);
}

.chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--space-6);
}

.chart-header h3 {
    margin: 0;
    color: var(--gray-900);
}

.chart-container {
    height: 300px;
    position: relative;
}

/* Таблицы админки */
.admin-tables {
    display: grid;
    gap: var(--space-6);
}

.admin-table-card {
    background: var(--white);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--gray-200);
    overflow: hidden;
}

.table-header {
    padding: var(--space-6);
    border-bottom: 1px solid var(--gray-200);
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: var(--gray-50);
}

.table-header h3 {
    margin: 0;
    color: var(--gray-900);
    display: flex;
    align-items: center;
    gap: var(--space-2);
}

.table-container {
    overflow-x: auto;
}

.admin-table {
    width: 100%;
    border-collapse: collapse;
}

.admin-table th {
    background: var(--gray-100);
    padding: var(--space-4);
    text-align: left;
    font-weight: 600;
    color: var(--gray-700);
    border-bottom: 1px solid var(--gray-200);
    font-size: var(--text-sm);
}

.admin-table td {
    padding: var(--space-4);
    border-bottom: 1px solid var(--gray-200);
    vertical-align: middle;
}

.admin-table tr:hover {
    background: var(--gray-50);
}

.user-info {
    display: flex;
    flex-direction: column;
}

.user-info strong {
    color: var(--gray-900);
    margin-bottom: 2px;
}

.user-info small {
    color: var(--gray-500);
    font-size: var(--text-xs);
}

.transaction-type {
    padding: var(--space-1) var(--space-2);
    border-radius: var(--radius-sm);
    font-size: var(--text-xs);
    font-weight: 500;
}

.type-deposit {
    background: rgba(40, 167, 69, 0.2);
    color: var(--success);
}

.type-withdrawal {
    background: rgba(220, 53, 69, 0.2);
    color: var(--error);
}

.type-investment {
    background: rgba(46, 204, 113, 0.2);
    color: var(--primary-green);
}

.amount {
    font-weight: 600;
    color: var(--gray-900);
}

.amount.invested {
    color: var(--primary-green);
}

.action-buttons {
    display: flex;
    gap: var(--space-2);
}

.btn-small {
    padding: var(--space-1) var(--space-2);
    font-size: var(--text-xs);
    min-width: auto;
}

.rank {
    display: flex;
    align-items: center;
    gap: var(--space-1);
    font-weight: 600;
}

.rank-1 {
    color: #FFD700;
}

.rank-2 {
    color: #C0C0C0;
}

.rank-3 {
    color: #CD7F32;
}

/* Подвал админской боковой панели */
.sidebar-footer {
    padding: var(--space-4);
    border-top: 1px solid #444;
}

.admin-quick-stats {
    margin-bottom: var(--space-4);
}

.quick-stat {
    display: flex;
    align-items: center;
    gap: var(--space-2);
    margin-bottom: var(--space-2);
    font-size: var(--text-xs);
    color: #ccc;
}

.quick-stat i {
    color: var(--primary-green);
    width: 16px;
}

.admin-actions {
    display: flex;
    gap: var(--space-2);
}

.admin-action {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--space-1);
    padding: var(--space-2);
    color: #ccc;
    text-decoration: none;
    border-radius: var(--radius-md);
    transition: all var(--transition-fast);
    font-size: var(--text-xs);
}

.admin-action:hover {
    background: rgba(46, 204, 113, 0.1);
    color: var(--white);
}

.admin-action.logout:hover {
    background: rgba(220, 53, 69, 0.1);
    color: var(--error);
}

/* Адаптивность */
@media (max-width: 1200px) {
    .charts-section {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .admin-stats-grid {
        grid-template-columns: 1fr;
    }
    
    .admin-stat-card {
        padding: var(--space-4);
    }
    
    .chart-card {
        padding: var(--space-4);
    }
    
    .table-header {
        padding: var(--space-4);
        flex-direction: column;
        gap: var(--space-2);
        align-items: flex-start;
    }
    
    .admin-table {
        font-size: var(--text-sm);
    }
    
    .admin-table th,
    .admin-table td {
        padding: var(--space-2);
    }
    
    .action-buttons {
        flex-direction: column;
    }
}

@media (max-width: 480px) {
    .admin-stat-card .stat-value {
        font-size: var(--text-2xl);
    }
    
    .finance-stats {
        font-size: var(--text-xs);
    }
    
    .user-info {
        font-size: var(--text-xs);
    }
}
