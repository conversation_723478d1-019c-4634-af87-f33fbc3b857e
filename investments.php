<?php
/**
 * AstroGenix - Страница инвестиций
 * Эко-майнинговая инвестиционная платформа
 */

require_once 'config/config.php';

// Проверка авторизации
if (!is_logged_in()) {
    redirect('login.php');
}

$errors = [];
$success_message = '';

try {
    $database = new Database();
    $db = $database->getConnection();
    
    // Получение текущего баланса пользователя
    $user = new User($db);
    $user->getUserById($_SESSION['user_id']);
    $current_balance = $user->balance;
    
    $investment = new Investment($db);
    
    // Обработка создания инвестиции
    if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['create_investment'])) {
        if (!verify_csrf_token($_POST['csrf_token'] ?? '')) {
            $errors[] = 'Ошибка безопасности. Попробуйте еще раз.';
        } else {
            $package_id = intval($_POST['package_id'] ?? 0);
            $amount = floatval($_POST['amount'] ?? 0);

            // Валидация
            if ($package_id <= 0) {
                $errors[] = 'Выберите инвестиционный пакет.';
            }

            if ($amount <= 0) {
                $errors[] = 'Сумма должна быть больше нуля.';
            } elseif ($amount > $current_balance) {
                $errors[] = 'Недостаточно средств на балансе.';
            }

            // Проверка лимитов пакета
            if (empty($errors)) {
                $package = $investment->getPackageById($package_id);
                if (!$package) {
                    $errors[] = 'Выбранный пакет не найден.';
                } elseif ($amount < $package['min_amount'] || $amount > $package['max_amount']) {
                    $errors[] = "Сумма должна быть от {$package['min_amount']} до {$package['max_amount']} ₽.";
                }
            }

            // Создание инвестиции
            if (empty($errors)) {
                if ($investment->createInvestment($_SESSION['user_id'], $package_id, $amount)) {
                    $success_message = 'Инвестиция успешно создана! Ежедневная прибыль будет начисляться автоматически.';
                    
                    // Обновление баланса в сессии
                    $_SESSION['balance'] = $current_balance - $amount;
                    $current_balance = $_SESSION['balance'];
                } else {
                    $errors[] = 'Ошибка при создании инвестиции. Попробуйте еще раз.';
                }
            }
        }
    }
    
    // Получение данных
    $packages = $investment->getAllPackages();
    $user_investments = $investment->getUserInvestments($_SESSION['user_id'], null, 10);
    $investment_stats = $investment->getUserInvestmentStats($_SESSION['user_id']);
    
} catch (Exception $e) {
    $errors[] = 'Ошибка сервера. Попробуйте позже.';
    error_log("Investments page error: " . $e->getMessage());
    $packages = [];
    $user_investments = [];
    $investment_stats = [];
}

$page_title = 'Инвестиции - ' . SITE_NAME;
?>
<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?></title>
    
    <!-- CSS -->
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="assets/css/dashboard.css">
    <link rel="stylesheet" href="assets/css/investments.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body class="dashboard-page">
    <!-- Sidebar -->
    <?php include 'includes/sidebar.php'; ?>

    <!-- Main Content -->
    <main class="main-content">
        <!-- Header -->
        <header class="dashboard-header">
            <div class="header-left">
                <button class="mobile-menu-toggle" id="mobileMenuToggle">
                    <i class="fas fa-bars"></i>
                </button>
                <h1>Инвестиции</h1>
            </div>
            <div class="header-right">
                <div class="user-balance">
                    <span class="balance-label">Доступно для инвестиций:</span>
                    <span class="balance-amount"><?php echo format_currency($current_balance); ?></span>
                </div>
            </div>
        </header>

        <!-- Content -->
        <div class="dashboard-content">
            <?php if (!empty($errors)): ?>
                <div class="alert alert-error">
                    <i class="fas fa-exclamation-triangle"></i>
                    <ul>
                        <?php foreach ($errors as $error): ?>
                            <li><?php echo htmlspecialchars($error); ?></li>
                        <?php endforeach; ?>
                    </ul>
                </div>
            <?php endif; ?>

            <?php if (!empty($success_message)): ?>
                <div class="alert alert-success">
                    <i class="fas fa-check-circle"></i>
                    <?php echo htmlspecialchars($success_message); ?>
                </div>
            <?php endif; ?>

            <!-- Investment Stats -->
            <div class="investment-stats">
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <div class="stat-content">
                        <h3>Активные инвестиции</h3>
                        <div class="stat-value"><?php echo $investment_stats['active_investments'] ?? 0; ?></div>
                        <div class="stat-subtitle">На сумму: <?php echo format_currency($investment_stats['active_amount'] ?? 0); ?></div>
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-coins"></i>
                    </div>
                    <div class="stat-content">
                        <h3>Общая прибыль</h3>
                        <div class="stat-value"><?php echo format_currency($investment_stats['total_earned'] ?? 0); ?></div>
                        <div class="stat-subtitle">Всего инвестировано: <?php echo format_currency($investment_stats['total_invested'] ?? 0); ?></div>
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <div class="stat-content">
                        <h3>Завершенные</h3>
                        <div class="stat-value"><?php echo $investment_stats['completed_investments'] ?? 0; ?></div>
                        <div class="stat-subtitle">Всего инвестиций: <?php echo $investment_stats['total_investments'] ?? 0; ?></div>
                    </div>
                </div>
            </div>

            <!-- Investment Packages -->
            <div class="packages-section">
                <div class="section-header">
                    <h2>Инвестиционные пакеты</h2>
                    <p>Выберите подходящий пакет для максимальной прибыли</p>
                </div>

                <div class="packages-grid">
                    <?php foreach ($packages as $package): ?>
                        <div class="package-card" data-package-id="<?php echo $package['id']; ?>">
                            <div class="package-header">
                                <h3><?php echo htmlspecialchars($package['name']); ?></h3>
                                <div class="package-profit">
                                    <?php echo $package['daily_profit_percent']; ?>%
                                    <span>в день</span>
                                </div>
                            </div>
                            
                            <div class="package-body">
                                <div class="package-description">
                                    <?php echo htmlspecialchars($package['description']); ?>
                                </div>
                                
                                <div class="package-details">
                                    <div class="detail-item">
                                        <i class="fas fa-coins"></i>
                                        <span>Сумма: <?php echo format_currency($package['min_amount']); ?> - <?php echo format_currency($package['max_amount']); ?></span>
                                    </div>
                                    <div class="detail-item">
                                        <i class="fas fa-calendar"></i>
                                        <span>Срок: <?php echo $package['duration_days']; ?> дней</span>
                                    </div>
                                    <div class="detail-item">
                                        <i class="fas fa-percentage"></i>
                                        <span>Общая доходность: <?php echo ($package['daily_profit_percent'] * $package['duration_days']); ?>%</span>
                                    </div>
                                </div>
                                
                                <div class="package-calculator">
                                    <label>Сумма инвестиции:</label>
                                    <input type="number" class="investment-amount" 
                                           min="<?php echo $package['min_amount']; ?>" 
                                           max="<?php echo min($package['max_amount'], $current_balance); ?>" 
                                           value="<?php echo $package['min_amount']; ?>" 
                                           step="100">
                                    
                                    <div class="calculator-results">
                                        <div class="result-item">
                                            <span>Ежедневная прибыль:</span>
                                            <span class="daily-profit">0 ₽</span>
                                        </div>
                                        <div class="result-item">
                                            <span>Общая прибыль:</span>
                                            <span class="total-profit">0 ₽</span>
                                        </div>
                                        <div class="result-item total">
                                            <span>К получению:</span>
                                            <span class="total-return">0 ₽</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="package-footer">
                                <button class="btn btn-primary btn-full invest-btn" 
                                        data-package-id="<?php echo $package['id']; ?>"
                                        data-min-amount="<?php echo $package['min_amount']; ?>"
                                        data-max-amount="<?php echo $package['max_amount']; ?>"
                                        data-profit-percent="<?php echo $package['daily_profit_percent']; ?>"
                                        data-duration="<?php echo $package['duration_days']; ?>">
                                    <i class="fas fa-rocket"></i>
                                    Инвестировать
                                </button>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>

            <!-- User Investments -->
            <div class="user-investments-section">
                <div class="section-header">
                    <h2>Мои инвестиции</h2>
                    <a href="transactions.php?type=investment" class="view-all-link">Посмотреть все</a>
                </div>

                <?php if (empty($user_investments)): ?>
                    <div class="empty-state">
                        <i class="fas fa-chart-line"></i>
                        <h3>У вас пока нет инвестиций</h3>
                        <p>Выберите подходящий пакет выше и начните зарабатывать уже сегодня!</p>
                    </div>
                <?php else: ?>
                    <div class="investments-list">
                        <?php foreach ($user_investments as $inv): ?>
                            <div class="investment-item">
                                <div class="investment-header">
                                    <h4><?php echo htmlspecialchars($inv['package_name']); ?></h4>
                                    <span class="investment-status status-<?php echo $inv['status']; ?>">
                                        <?php 
                                        $statuses = [
                                            'active' => 'Активна',
                                            'completed' => 'Завершена',
                                            'cancelled' => 'Отменена'
                                        ];
                                        echo $statuses[$inv['status']] ?? $inv['status'];
                                        ?>
                                    </span>
                                </div>
                                
                                <div class="investment-details">
                                    <div class="detail-row">
                                        <span>Сумма инвестиции:</span>
                                        <span class="amount"><?php echo format_currency($inv['amount']); ?></span>
                                    </div>
                                    <div class="detail-row">
                                        <span>Ежедневная прибыль:</span>
                                        <span class="profit"><?php echo format_currency($inv['daily_profit']); ?></span>
                                    </div>
                                    <div class="detail-row">
                                        <span>Заработано:</span>
                                        <span class="earned"><?php echo format_currency($inv['total_earned']); ?></span>
                                    </div>
                                    <div class="detail-row">
                                        <span>Дата начала:</span>
                                        <span><?php echo date('d.m.Y', strtotime($inv['start_date'])); ?></span>
                                    </div>
                                    <div class="detail-row">
                                        <span>Дата окончания:</span>
                                        <span><?php echo date('d.m.Y', strtotime($inv['end_date'])); ?></span>
                                    </div>
                                </div>
                                
                                <?php if ($inv['status'] === 'active'): ?>
                                    <div class="investment-progress">
                                        <?php 
                                        $total_days = $inv['days_passed'] + max(0, $inv['days_left']);
                                        $progress_percent = $total_days > 0 ? ($inv['days_passed'] / $total_days) * 100 : 0;
                                        ?>
                                        <div class="progress-info">
                                            <span>Прогресс: <?php echo $inv['days_passed']; ?> из <?php echo $total_days; ?> дней</span>
                                            <span><?php echo max(0, $inv['days_left']); ?> дней осталось</span>
                                        </div>
                                        <div class="progress-bar">
                                            <div class="progress-fill" style="width: <?php echo min(100, $progress_percent); ?>%"></div>
                                        </div>
                                    </div>
                                <?php endif; ?>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </main>

    <!-- Investment Modal -->
    <div class="modal" id="investmentModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Создать инвестицию</h3>
                <button class="modal-close" id="modalClose">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <form id="investmentForm" method="POST" action="">
                    <input type="hidden" name="csrf_token" value="<?php echo generate_csrf_token(); ?>">
                    <input type="hidden" name="create_investment" value="1">
                    <input type="hidden" name="package_id" id="modalPackageId">
                    
                    <div class="form-group">
                        <label>Выбранный пакет:</label>
                        <div class="selected-package" id="selectedPackageInfo"></div>
                    </div>
                    
                    <div class="form-group">
                        <label for="modalAmount">Сумма инвестиции (₽):</label>
                        <input type="number" id="modalAmount" name="amount" required>
                        <div class="amount-info">
                            <small>Доступно: <?php echo format_currency($current_balance); ?></small>
                        </div>
                    </div>
                    
                    <div class="investment-summary" id="modalSummary">
                        <!-- Будет заполнено через JavaScript -->
                    </div>
                    
                    <div class="modal-actions">
                        <button type="button" class="btn btn-secondary" id="modalCancel">Отмена</button>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-rocket"></i>
                            Создать инвестицию
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="assets/js/dashboard.js"></script>
    <script src="assets/js/investments.js"></script>
</body>
</html>
