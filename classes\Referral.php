<?php
/**
 * AstroGenix - Класс реферальной системы
 * Эко-майнинговая инвестиционная платформа
 */

class Referral {
    private $conn;
    private $commissions_table = "referral_commissions";
    private $users_table = "users";

    public function __construct($db) {
        $this->conn = $db;
    }

    // Получение реферальной статистики пользователя
    public function getUserReferralStats($user_id) {
        $query = "SELECT 
                    COUNT(DISTINCT r.id) as total_referrals,
                    COUNT(DISTINCT CASE WHEN rc.level = 1 THEN rc.referred_id END) as level_1_referrals,
                    COUNT(DISTINCT CASE WHEN rc.level = 2 THEN rc.referred_id END) as level_2_referrals,
                    COUNT(DISTINCT CASE WHEN rc.level = 3 THEN rc.referred_id END) as level_3_referrals,
                    COALESCE(SUM(CASE WHEN rc.status = 'paid' THEN rc.amount ELSE 0 END), 0) as total_earned,
                    COALESCE(SUM(CASE WHEN rc.level = 1 AND rc.status = 'paid' THEN rc.amount ELSE 0 END), 0) as level_1_earned,
                    COALESCE(SUM(CASE WHEN rc.level = 2 AND rc.status = 'paid' THEN rc.amount ELSE 0 END), 0) as level_2_earned,
                    COALESCE(SUM(CASE WHEN rc.level = 3 AND rc.status = 'paid' THEN rc.amount ELSE 0 END), 0) as level_3_earned,
                    COALESCE(SUM(CASE WHEN rc.status = 'pending' THEN rc.amount ELSE 0 END), 0) as pending_earnings
                  FROM " . $this->users_table . " r
                  LEFT JOIN " . $this->commissions_table . " rc ON r.id = rc.referred_id AND rc.referrer_id = :user_id
                  WHERE r.referred_by = :user_id OR rc.referrer_id = :user_id";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":user_id", $user_id);
        $stmt->execute();

        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    // Получение списка рефералов пользователя
    public function getUserReferrals($user_id, $level = null, $limit = 20, $offset = 0) {
        $level_condition = "";
        if ($level) {
            $level_condition = "AND level = :level";
        }

        $query = "SELECT DISTINCT
                    u.id,
                    u.username,
                    u.first_name,
                    u.last_name,
                    u.email,
                    u.total_invested,
                    u.created_at,
                    COALESCE(rc.level, 1) as referral_level,
                    COALESCE(SUM(rc.amount), 0) as total_commission,
                    COUNT(rc.id) as commission_count
                  FROM " . $this->users_table . " u
                  LEFT JOIN " . $this->commissions_table . " rc ON u.id = rc.referred_id AND rc.referrer_id = :user_id
                  WHERE u.referred_by = :user_id {$level_condition}
                  GROUP BY u.id
                  ORDER BY u.created_at DESC
                  LIMIT :limit OFFSET :offset";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":user_id", $user_id);
        $stmt->bindParam(":limit", $limit, PDO::PARAM_INT);
        $stmt->bindParam(":offset", $offset, PDO::PARAM_INT);
        
        if ($level) {
            $stmt->bindParam(":level", $level);
        }

        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    // Получение истории реферальных комиссий
    public function getReferralCommissions($user_id, $limit = 50, $offset = 0) {
        $query = "SELECT 
                    rc.*,
                    u.username as referred_username,
                    u.first_name as referred_first_name,
                    u.last_name as referred_last_name,
                    t.type as source_type,
                    t.amount as source_amount
                  FROM " . $this->commissions_table . " rc
                  JOIN " . $this->users_table . " u ON rc.referred_id = u.id
                  LEFT JOIN transactions t ON rc.source_transaction_id = t.id
                  WHERE rc.referrer_id = :user_id
                  ORDER BY rc.created_at DESC
                  LIMIT :limit OFFSET :offset";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":user_id", $user_id);
        $stmt->bindParam(":limit", $limit, PDO::PARAM_INT);
        $stmt->bindParam(":offset", $offset, PDO::PARAM_INT);
        $stmt->execute();

        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    // Получение реферальной структуры (дерево рефералов)
    public function getReferralTree($user_id, $max_levels = 3) {
        $tree = [];
        $this->buildReferralTree($user_id, $tree, 1, $max_levels);
        return $tree;
    }

    private function buildReferralTree($user_id, &$tree, $current_level, $max_levels) {
        if ($current_level > $max_levels) {
            return;
        }

        $query = "SELECT 
                    u.id,
                    u.username,
                    u.first_name,
                    u.last_name,
                    u.total_invested,
                    u.created_at,
                    COALESCE(SUM(rc.amount), 0) as total_commission
                  FROM " . $this->users_table . " u
                  LEFT JOIN " . $this->commissions_table . " rc ON u.id = rc.referred_id AND rc.referrer_id = :user_id
                  WHERE u.referred_by = :user_id
                  GROUP BY u.id
                  ORDER BY u.created_at DESC";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":user_id", $user_id);
        $stmt->execute();
        $referrals = $stmt->fetchAll(PDO::FETCH_ASSOC);

        foreach ($referrals as $referral) {
            $referral['level'] = $current_level;
            $referral['children'] = [];
            
            // Рекурсивно получаем рефералов следующего уровня
            $this->buildReferralTree($referral['id'], $referral['children'], $current_level + 1, $max_levels);
            
            $tree[] = $referral;
        }
    }

    // Генерация реферальной ссылки
    public function generateReferralLink($user_id) {
        $user_query = "SELECT referral_code FROM " . $this->users_table . " WHERE id = :user_id";
        $user_stmt = $this->conn->prepare($user_query);
        $user_stmt->bindParam(":user_id", $user_id);
        $user_stmt->execute();
        $user_data = $user_stmt->fetch(PDO::FETCH_ASSOC);

        if ($user_data) {
            return SITE_URL . "/register.php?ref=" . $user_data['referral_code'];
        }

        return false;
    }

    // Получение настроек реферальной программы
    public function getReferralSettings() {
        $query = "SELECT setting_key, setting_value FROM system_settings 
                  WHERE setting_key LIKE 'referral_%'";
        $stmt = $this->conn->prepare($query);
        $stmt->execute();
        $settings = $stmt->fetchAll(PDO::FETCH_ASSOC);

        $result = [];
        foreach ($settings as $setting) {
            $result[$setting['setting_key']] = $setting['setting_value'];
        }

        return $result;
    }

    // Обновление настроек реферальной программы
    public function updateReferralSettings($settings) {
        try {
            $this->conn->beginTransaction();

            foreach ($settings as $key => $value) {
                $query = "UPDATE system_settings SET setting_value = :value WHERE setting_key = :key";
                $stmt = $this->conn->prepare($query);
                $stmt->bindParam(":value", $value);
                $stmt->bindParam(":key", $key);
                $stmt->execute();
            }

            $this->conn->commit();
            return true;
        } catch (Exception $e) {
            $this->conn->rollback();
            error_log("Referral settings update error: " . $e->getMessage());
            return false;
        }
    }

    // Получение топ рефереров
    public function getTopReferrers($limit = 10) {
        $query = "SELECT 
                    u.id,
                    u.username,
                    u.first_name,
                    u.last_name,
                    COUNT(DISTINCT r.id) as total_referrals,
                    COALESCE(SUM(rc.amount), 0) as total_earned
                  FROM " . $this->users_table . " u
                  LEFT JOIN " . $this->users_table . " r ON u.id = r.referred_by
                  LEFT JOIN " . $this->commissions_table . " rc ON u.id = rc.referrer_id AND rc.status = 'paid'
                  GROUP BY u.id
                  HAVING total_referrals > 0
                  ORDER BY total_earned DESC, total_referrals DESC
                  LIMIT :limit";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":limit", $limit, PDO::PARAM_INT);
        $stmt->execute();

        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    // Проверка возможности получения реферального бонуса
    public function canEarnReferralBonus($referrer_id, $referred_id, $amount) {
        // Проверка, что пользователь не пытается получить бонус с самого себя
        if ($referrer_id == $referred_id) {
            return false;
        }

        // Проверка минимальной суммы для реферального бонуса
        $min_amount = 100; // Минимальная сумма для получения бонуса
        if ($amount < $min_amount) {
            return false;
        }

        // Проверка активности реферера
        $referrer_query = "SELECT is_active FROM " . $this->users_table . " WHERE id = :referrer_id";
        $referrer_stmt = $this->conn->prepare($referrer_query);
        $referrer_stmt->bindParam(":referrer_id", $referrer_id);
        $referrer_stmt->execute();
        $referrer_data = $referrer_stmt->fetch(PDO::FETCH_ASSOC);

        return $referrer_data && $referrer_data['is_active'];
    }

    // Получение статистики по уровням рефералов
    public function getReferralLevelStats($user_id) {
        $stats = [];
        
        for ($level = 1; $level <= 3; $level++) {
            $query = "SELECT 
                        COUNT(DISTINCT rc.referred_id) as referrals_count,
                        COALESCE(SUM(CASE WHEN rc.status = 'paid' THEN rc.amount ELSE 0 END), 0) as total_earned,
                        COALESCE(SUM(CASE WHEN rc.status = 'pending' THEN rc.amount ELSE 0 END), 0) as pending_earnings,
                        AVG(rc.commission_percent) as avg_commission_percent
                      FROM " . $this->commissions_table . " rc
                      WHERE rc.referrer_id = :user_id AND rc.level = :level";

            $stmt = $this->conn->prepare($query);
            $stmt->bindParam(":user_id", $user_id);
            $stmt->bindParam(":level", $level);
            $stmt->execute();
            
            $stats[$level] = $stmt->fetch(PDO::FETCH_ASSOC);
        }

        return $stats;
    }

    // Получение активности рефералов за период
    public function getReferralActivity($user_id, $days = 30) {
        $query = "SELECT 
                    DATE(rc.created_at) as date,
                    COUNT(*) as commissions_count,
                    SUM(rc.amount) as total_amount,
                    COUNT(DISTINCT rc.referred_id) as unique_referrals
                  FROM " . $this->commissions_table . " rc
                  WHERE rc.referrer_id = :user_id 
                    AND rc.created_at >= DATE_SUB(NOW(), INTERVAL :days DAY)
                    AND rc.status = 'paid'
                  GROUP BY DATE(rc.created_at)
                  ORDER BY date DESC";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":user_id", $user_id);
        $stmt->bindParam(":days", $days, PDO::PARAM_INT);
        $stmt->execute();

        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    // Создание реферального промокода
    public function createPromoCode($user_id, $code, $bonus_percent, $max_uses = null, $expires_at = null) {
        $query = "INSERT INTO referral_promo_codes 
                  SET user_id=:user_id, code=:code, bonus_percent=:bonus_percent, 
                      max_uses=:max_uses, expires_at=:expires_at, is_active=1";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":user_id", $user_id);
        $stmt->bindParam(":code", $code);
        $stmt->bindParam(":bonus_percent", $bonus_percent);
        $stmt->bindParam(":max_uses", $max_uses);
        $stmt->bindParam(":expires_at", $expires_at);

        return $stmt->execute();
    }

    // Валидация промокода
    public function validatePromoCode($code) {
        $query = "SELECT * FROM referral_promo_codes 
                  WHERE code = :code AND is_active = 1 
                    AND (expires_at IS NULL OR expires_at > NOW())
                    AND (max_uses IS NULL OR uses_count < max_uses)";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":code", $code);
        $stmt->execute();

        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    // Использование промокода
    public function usePromoCode($code, $user_id) {
        $promo = $this->validatePromoCode($code);
        if (!$promo) {
            return false;
        }

        // Обновление счетчика использований
        $update_query = "UPDATE referral_promo_codes SET uses_count = uses_count + 1 WHERE id = :id";
        $update_stmt = $this->conn->prepare($update_query);
        $update_stmt->bindParam(":id", $promo['id']);
        $update_stmt->execute();

        // Создание записи об использовании
        $usage_query = "INSERT INTO referral_promo_usage 
                        SET promo_code_id=:promo_code_id, user_id=:user_id, used_at=NOW()";
        $usage_stmt = $this->conn->prepare($usage_query);
        $usage_stmt->bindParam(":promo_code_id", $promo['id']);
        $usage_stmt->bindParam(":user_id", $user_id);
        $usage_stmt->execute();

        return $promo;
    }
}
?>
