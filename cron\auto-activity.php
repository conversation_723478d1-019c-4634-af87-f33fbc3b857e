<?php
/**
 * AstroGenix - Автоматическая симуляция активности
 * Cron скрипт для периодического создания активности
 */

// Запуск только из командной строки
if (php_sapi_name() !== 'cli') {
    die('Этот скрипт может быть запущен только из командной строки');
}

require_once dirname(__DIR__) . '/config/config.php';
require_once dirname(__DIR__) . '/classes/FakeDataGenerator.php';

// Логирование
function logMessage($message) {
    $timestamp = date('Y-m-d H:i:s');
    $log_file = dirname(__DIR__) . '/logs/auto-activity.log';
    
    // Создание директории логов если не существует
    $log_dir = dirname($log_file);
    if (!is_dir($log_dir)) {
        mkdir($log_dir, 0755, true);
    }
    
    file_put_contents($log_file, "[$timestamp] $message" . PHP_EOL, FILE_APPEND | LOCK_EX);
    echo "[$timestamp] $message" . PHP_EOL;
}

try {
    logMessage("Запуск автоматической симуляции активности");
    
    $database = new Database();
    $db = $database->getConnection();
    $fake_generator = new FakeDataGenerator($db);
    
    // Получение настроек автоактивности
    $config_query = "SELECT setting_value FROM system_settings WHERE setting_key = 'auto_activity_config'";
    $config_stmt = $db->prepare($config_query);
    $config_stmt->execute();
    $config_result = $config_stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$config_result) {
        logMessage("Настройки автоактивности не найдены. Завершение.");
        exit(0);
    }
    
    $config = json_decode($config_result['setting_value'], true);
    
    // Проверка активности
    if (!$config['enabled'] || time() > $config['end_time']) {
        logMessage("Автоактивность отключена или время истекло. Завершение.");
        
        // Отключение автоактивности
        $config['enabled'] = false;
        $update_config_query = "UPDATE system_settings SET setting_value = :config WHERE setting_key = 'auto_activity_config'";
        $update_config_stmt = $db->prepare($update_config_query);
        $update_config_stmt->bindParam(':config', json_encode($config));
        $update_config_stmt->execute();
        
        exit(0);
    }
    
    $intensity_config = $config['config'];
    logMessage("Интенсивность: {$config['intensity']} - " . json_encode($intensity_config));
    
    // Расчет количества действий для этого запуска (предполагается запуск каждые 5 минут)
    $minutes_interval = 5;
    $users_to_create = max(1, round(($intensity_config['users_per_hour'] * $minutes_interval) / 60));
    $transactions_to_create = max(1, round(($intensity_config['transactions_per_hour'] * $minutes_interval) / 60));
    $investments_to_create = max(1, round(($intensity_config['investments_per_hour'] * $minutes_interval) / 60));
    
    $total_actions = 0;
    
    // Создание пользователей
    if ($users_to_create > 0) {
        logMessage("Создание $users_to_create пользователей...");
        $result = $fake_generator->createMultipleUsers($users_to_create, ['balance_type' => 'random']);
        
        if ($result['total_created'] > 0) {
            logMessage("Создано пользователей: {$result['total_created']}");
            $total_actions += $result['total_created'];
        }
        
        if ($result['total_errors'] > 0) {
            logMessage("Ошибок при создании пользователей: {$result['total_errors']}");
        }
    }
    
    // Создание транзакций
    if ($transactions_to_create > 0) {
        logMessage("Создание $transactions_to_create транзакций...");
        $transaction_types = ['deposit', 'withdrawal', 'investment', 'profit'];
        $result = simulateTransactions($db, $transactions_to_create, $transaction_types);
        
        if ($result['success'] && $result['created'] > 0) {
            logMessage("Создано транзакций: {$result['created']}");
            $total_actions += $result['created'];
        } else {
            logMessage("Ошибка создания транзакций: " . ($result['error'] ?? 'Неизвестная ошибка'));
        }
    }
    
    // Создание инвестиций (реже)
    if ($investments_to_create > 0 && rand(1, 100) <= 70) { // 70% вероятность
        logMessage("Создание $investments_to_create инвестиций...");
        $result = simulateInvestments($db, $investments_to_create);
        
        if ($result['success'] && $result['created'] > 0) {
            logMessage("Создано инвестиций: {$result['created']}");
            $total_actions += $result['created'];
        } else {
            logMessage("Ошибка создания инвестиций: " . ($result['error'] ?? 'Неизвестная ошибка'));
        }
    }
    
    // Обновление статистики (каждые 30 минут)
    if (rand(1, 100) <= 20) { // 20% вероятность
        logMessage("Обновление статистики платформы...");
        $result = updatePlatformStats($db);
        
        if ($result['success']) {
            logMessage("Статистика обновлена: {$result['total_users']} пользователей, {$result['total_energy']} кВт⋅ч");
        } else {
            logMessage("Ошибка обновления статистики: " . ($result['error'] ?? 'Неизвестная ошибка'));
        }
    }
    
    // Начисление прибыли по активным инвестициям
    logMessage("Начисление прибыли по активным инвестициям...");
    $profit_result = processDailyProfits($db);
    
    if ($profit_result['success']) {
        logMessage("Обработано инвестиций: {$profit_result['processed']}, начислено прибыли: {$profit_result['total_profit']} USDT");
        $total_actions += $profit_result['processed'];
    } else {
        logMessage("Ошибка начисления прибыли: " . ($profit_result['error'] ?? 'Неизвестная ошибка'));
    }
    
    logMessage("Автоматическая симуляция завершена. Всего действий: $total_actions");
    
} catch (Exception $e) {
    logMessage("ОШИБКА: " . $e->getMessage());
    logMessage("Трассировка: " . $e->getTraceAsString());
    exit(1);
}

/**
 * Функции симуляции (копии из admin/simulate-activity.php)
 */

function simulateTransactions($db, $count, $types) {
    try {
        $created = 0;
        
        // Получение списка пользователей
        $users_query = "SELECT id FROM users WHERE is_active = 1 ORDER BY RAND() LIMIT " . ($count * 2);
        $users_stmt = $db->prepare($users_query);
        $users_stmt->execute();
        $users = $users_stmt->fetchAll(PDO::FETCH_COLUMN);
        
        if (empty($users)) {
            return ['success' => false, 'error' => 'Нет активных пользователей'];
        }
        
        for ($i = 0; $i < $count; $i++) {
            $user_id = $users[array_rand($users)];
            $type = $types[array_rand($types)];
            
            // Генерация суммы
            switch ($type) {
                case 'deposit':
                    $amount = rand(10, 1000);
                    $status = rand(1, 100) <= 85 ? 'completed' : 'pending';
                    break;
                case 'withdrawal':
                    $amount = rand(5, 500);
                    $status = rand(1, 100) <= 70 ? 'completed' : 'pending';
                    break;
                case 'investment':
                    $amount = rand(50, 2000);
                    $status = 'completed';
                    break;
                case 'profit':
                    $amount = rand(1, 100);
                    $status = 'completed';
                    break;
                default:
                    $amount = rand(10, 500);
                    $status = 'completed';
            }
            
            $created_at = date('Y-m-d H:i:s', rand(strtotime('-1 hour'), time()));
            
            $insert_query = "INSERT INTO transactions 
                            (user_id, type, amount, status, description, created_at) 
                            VALUES 
                            (:user_id, :type, :amount, :status, :description, :created_at)";
            
            $description = "Автоматическая симуляция: " . ucfirst($type);
            
            $insert_stmt = $db->prepare($insert_query);
            $insert_stmt->bindParam(':user_id', $user_id, PDO::PARAM_INT);
            $insert_stmt->bindParam(':type', $type);
            $insert_stmt->bindParam(':amount', $amount);
            $insert_stmt->bindParam(':status', $status);
            $insert_stmt->bindParam(':description', $description);
            $insert_stmt->bindParam(':created_at', $created_at);
            
            if ($insert_stmt->execute()) {
                $created++;
                
                // Обновление баланса для завершенных транзакций
                if ($status === 'completed') {
                    $balance_change = 0;
                    
                    switch ($type) {
                        case 'deposit':
                        case 'profit':
                            $balance_change = $amount;
                            break;
                        case 'withdrawal':
                        case 'investment':
                            $balance_change = -$amount;
                            break;
                    }
                    
                    if ($balance_change != 0) {
                        $update_balance_query = "UPDATE users SET balance = GREATEST(0, balance + :change) WHERE id = :user_id";
                        $update_balance_stmt = $db->prepare($update_balance_query);
                        $update_balance_stmt->bindParam(':change', $balance_change);
                        $update_balance_stmt->bindParam(':user_id', $user_id, PDO::PARAM_INT);
                        $update_balance_stmt->execute();
                    }
                }
            }
        }
        
        return ['success' => true, 'created' => $created];
        
    } catch (Exception $e) {
        return ['success' => false, 'error' => $e->getMessage()];
    }
}

function simulateInvestments($db, $count) {
    try {
        $created = 0;
        
        $users_query = "SELECT id, balance FROM users WHERE is_active = 1 AND balance >= 50 ORDER BY RAND() LIMIT " . ($count * 2);
        $users_stmt = $db->prepare($users_query);
        $users_stmt->execute();
        $users = $users_stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (empty($users)) {
            return ['success' => false, 'error' => 'Нет пользователей с достаточным балансом'];
        }
        
        $packages_query = "SELECT * FROM investment_packages WHERE is_active = 1";
        $packages_stmt = $db->prepare($packages_query);
        $packages_stmt->execute();
        $packages = $packages_stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (empty($packages)) {
            return ['success' => false, 'error' => 'Нет активных пакетов'];
        }
        
        for ($i = 0; $i < $count; $i++) {
            $user = $users[array_rand($users)];
            $package = $packages[array_rand($packages)];
            
            $min_amount = max($package['min_amount'], 50);
            $max_amount = min($package['max_amount'], $user['balance']);
            
            if ($min_amount > $max_amount) continue;
            
            $amount = rand($min_amount, $max_amount);
            $daily_profit = ($amount * $package['daily_profit_percent']) / 100;
            $start_date = date('Y-m-d');
            $end_date = date('Y-m-d', strtotime("+{$package['duration_days']} days"));
            
            $insert_query = "INSERT INTO user_investments 
                            (user_id, package_id, amount, daily_profit, start_date, end_date, status) 
                            VALUES 
                            (:user_id, :package_id, :amount, :daily_profit, :start_date, :end_date, 'active')";
            
            $insert_stmt = $db->prepare($insert_query);
            $insert_stmt->bindParam(':user_id', $user['id'], PDO::PARAM_INT);
            $insert_stmt->bindParam(':package_id', $package['id'], PDO::PARAM_INT);
            $insert_stmt->bindParam(':amount', $amount);
            $insert_stmt->bindParam(':daily_profit', $daily_profit);
            $insert_stmt->bindParam(':start_date', $start_date);
            $insert_stmt->bindParam(':end_date', $end_date);
            
            if ($insert_stmt->execute()) {
                $created++;
                
                // Списание с баланса
                $update_balance_query = "UPDATE users SET balance = balance - :amount, total_invested = total_invested + :amount WHERE id = :user_id";
                $update_balance_stmt = $db->prepare($update_balance_query);
                $update_balance_stmt->bindParam(':amount', $amount);
                $update_balance_stmt->bindParam(':user_id', $user['id'], PDO::PARAM_INT);
                $update_balance_stmt->execute();
            }
        }
        
        return ['success' => true, 'created' => $created];
        
    } catch (Exception $e) {
        return ['success' => false, 'error' => $e->getMessage()];
    }
}

function updatePlatformStats($db) {
    try {
        $total_users_query = "SELECT COUNT(*) as total FROM users WHERE is_active = 1";
        $total_users_stmt = $db->prepare($total_users_query);
        $total_users_stmt->execute();
        $total_users = $total_users_stmt->fetch(PDO::FETCH_ASSOC)['total'];
        
        $total_energy = $total_users * rand(50, 200);
        $total_co2_saved = round($total_energy * 0.4, 2);
        $total_trees = $total_users * rand(1, 10);
        
        $update_stats_query = "INSERT INTO green_energy_stats 
                              (user_id, energy_generated, co2_saved, trees_planted, updated_at) 
                              VALUES 
                              (0, :energy, :co2, :trees, NOW()) 
                              ON DUPLICATE KEY UPDATE 
                              energy_generated = :energy, co2_saved = :co2, trees_planted = :trees, updated_at = NOW()";
        
        $update_stats_stmt = $db->prepare($update_stats_query);
        $update_stats_stmt->bindParam(':energy', $total_energy);
        $update_stats_stmt->bindParam(':co2', $total_co2_saved);
        $update_stats_stmt->bindParam(':trees', $total_trees, PDO::PARAM_INT);
        $update_stats_stmt->execute();
        
        return [
            'success' => true,
            'total_users' => $total_users,
            'total_energy' => $total_energy,
            'total_co2_saved' => $total_co2_saved,
            'total_trees' => $total_trees
        ];
        
    } catch (Exception $e) {
        return ['success' => false, 'error' => $e->getMessage()];
    }
}

function processDailyProfits($db) {
    try {
        $processed = 0;
        $total_profit = 0;
        
        // Получение активных инвестиций
        $investments_query = "SELECT ui.*, u.id as user_id 
                             FROM user_investments ui 
                             JOIN users u ON ui.user_id = u.id 
                             WHERE ui.status = 'active' 
                             AND ui.start_date <= CURDATE() 
                             AND ui.end_date >= CURDATE()
                             AND u.is_active = 1";
        
        $investments_stmt = $db->prepare($investments_query);
        $investments_stmt->execute();
        $investments = $investments_stmt->fetchAll(PDO::FETCH_ASSOC);
        
        foreach ($investments as $investment) {
            // Проверка, была ли уже начислена прибыль сегодня
            $profit_check_query = "SELECT COUNT(*) FROM transactions 
                                  WHERE user_id = :user_id 
                                  AND type = 'profit' 
                                  AND description LIKE :description 
                                  AND DATE(created_at) = CURDATE()";
            
            $profit_check_stmt = $db->prepare($profit_check_query);
            $profit_check_stmt->bindParam(':user_id', $investment['user_id'], PDO::PARAM_INT);
            $description_pattern = "Прибыль по инвестиции #{$investment['id']}%";
            $profit_check_stmt->bindParam(':description', $description_pattern);
            $profit_check_stmt->execute();
            
            if ($profit_check_stmt->fetchColumn() > 0) {
                continue; // Прибыль уже начислена сегодня
            }
            
            // Начисление прибыли
            $profit_amount = $investment['daily_profit'];
            
            // Создание транзакции прибыли
            $profit_transaction_query = "INSERT INTO transactions 
                                        (user_id, type, amount, status, description) 
                                        VALUES 
                                        (:user_id, 'profit', :amount, 'completed', :description)";
            
            $description = "Прибыль по инвестиции #{$investment['id']} (автоматическое начисление)";
            
            $profit_transaction_stmt = $db->prepare($profit_transaction_query);
            $profit_transaction_stmt->bindParam(':user_id', $investment['user_id'], PDO::PARAM_INT);
            $profit_transaction_stmt->bindParam(':amount', $profit_amount);
            $profit_transaction_stmt->bindParam(':description', $description);
            
            if ($profit_transaction_stmt->execute()) {
                // Обновление баланса пользователя
                $update_balance_query = "UPDATE users SET balance = balance + :profit, total_earned = total_earned + :profit WHERE id = :user_id";
                $update_balance_stmt = $db->prepare($update_balance_query);
                $update_balance_stmt->bindParam(':profit', $profit_amount);
                $update_balance_stmt->bindParam(':user_id', $investment['user_id'], PDO::PARAM_INT);
                $update_balance_stmt->execute();
                
                // Обновление общей прибыли по инвестиции
                $update_investment_query = "UPDATE user_investments SET total_earned = total_earned + :profit WHERE id = :investment_id";
                $update_investment_stmt = $db->prepare($update_investment_query);
                $update_investment_stmt->bindParam(':profit', $profit_amount);
                $update_investment_stmt->bindParam(':investment_id', $investment['id'], PDO::PARAM_INT);
                $update_investment_stmt->execute();
                
                $processed++;
                $total_profit += $profit_amount;
            }
        }
        
        return [
            'success' => true,
            'processed' => $processed,
            'total_profit' => round($total_profit, 2)
        ];
        
    } catch (Exception $e) {
        return ['success' => false, 'error' => $e->getMessage()];
    }
}
?>
