/**
 * AstroGenix - JavaScript для страницы пополнения
 * Эко-майнинговая инвестиционная платформа
 */

document.addEventListener('DOMContentLoaded', function() {
    initializeDeposit();
});

function initializeDeposit() {
    // Инициализация кнопок быстрого выбора суммы
    initAmountButtons();
    
    // Инициализация обновления итоговой суммы
    initSummaryUpdate();
    
    // Инициализация валидации формы
    initDepositValidation();
    
    // Инициализация способов оплаты
    initPaymentMethods();
}

// Кнопки быстрого выбора суммы
function initAmountButtons() {
    const amountInput = document.getElementById('amount');
    const amountButtons = document.querySelectorAll('.amount-btn');
    
    amountButtons.forEach(button => {
        button.addEventListener('click', function() {
            const amount = this.dataset.amount;
            amountInput.value = amount;
            
            // Удаление активного класса у всех кнопок
            amountButtons.forEach(btn => btn.classList.remove('active'));
            
            // Добавление активного класса к текущей кнопке
            this.classList.add('active');
            
            // Обновление итоговой суммы
            updateSummary();
            
            // Анимация
            amountInput.style.transform = 'scale(1.05)';
            setTimeout(() => {
                amountInput.style.transform = 'scale(1)';
            }, 200);
        });
    });
    
    // Сброс активного состояния при ручном вводе
    amountInput.addEventListener('input', function() {
        amountButtons.forEach(btn => btn.classList.remove('active'));
        updateSummary();
    });
}

// Обновление итоговой суммы
function initSummaryUpdate() {
    const amountInput = document.getElementById('amount');
    
    amountInput.addEventListener('input', updateSummary);
    
    // Обновление при изменении способа оплаты
    const paymentMethods = document.querySelectorAll('input[name="payment_method"]');
    paymentMethods.forEach(method => {
        method.addEventListener('change', updateSummary);
    });
    
    // Первоначальное обновление
    updateSummary();
}

function updateSummary() {
    const amountInput = document.getElementById('amount');
    const summaryAmount = document.getElementById('summaryAmount');
    const summaryTotal = document.getElementById('summaryTotal');
    const summaryFee = document.querySelector('.summary-fee');
    
    const amount = parseFloat(amountInput.value) || 0;
    const selectedPaymentMethod = document.querySelector('input[name="payment_method"]:checked');
    
    // Расчет комиссии в зависимости от способа оплаты
    let fee = 0;
    if (selectedPaymentMethod) {
        const paymentType = selectedPaymentMethod.value;
        switch (paymentType) {
            case 'bank_card':
                fee = amount * 0.03; // 3%
                break;
            case 'crypto':
                fee = amount * 0.01; // 1%
                break;
            case 'qiwi':
            case 'yandex_money':
                fee = amount * 0.02; // 2%
                break;
            default:
                fee = 0;
        }
    }
    
    const total = amount + fee;
    
    // Обновление отображения
    summaryAmount.textContent = formatCurrency(amount);
    summaryFee.textContent = formatCurrency(fee);
    summaryTotal.textContent = formatCurrency(total);
    
    // Анимация изменения
    [summaryAmount, summaryFee, summaryTotal].forEach(element => {
        element.style.transform = 'scale(1.1)';
        element.style.color = 'var(--primary-green)';
        setTimeout(() => {
            element.style.transform = 'scale(1)';
            element.style.color = '';
        }, 300);
    });
}

// Валидация формы
function initDepositValidation() {
    const form = document.querySelector('.deposit-form');
    const amountInput = document.getElementById('amount');
    const paymentDetailsTextarea = document.getElementById('payment_details');
    
    form.addEventListener('submit', function(e) {
        if (!validateDepositForm()) {
            e.preventDefault();
        }
    });
    
    // Валидация суммы в реальном времени
    amountInput.addEventListener('blur', function() {
        validateAmount(this);
    });
    
    amountInput.addEventListener('input', function() {
        clearFieldError(this);
        
        // Ограничение ввода
        const value = parseFloat(this.value);
        if (value > 10000) {
            this.value = 10000;
        }
    });
    
    // Автоматическое заполнение деталей платежа
    const paymentMethods = document.querySelectorAll('input[name="payment_method"]');
    paymentMethods.forEach(method => {
        method.addEventListener('change', function() {
            updatePaymentDetailsPlaceholder(this.value);
        });
    });
}

function validateDepositForm() {
    let isValid = true;
    
    // Валидация суммы
    const amountInput = document.getElementById('amount');
    if (!validateAmount(amountInput)) {
        isValid = false;
    }
    
    // Валидация способа оплаты
    const selectedPaymentMethod = document.querySelector('input[name="payment_method"]:checked');
    if (!selectedPaymentMethod) {
        showFormError('Выберите способ оплаты');
        isValid = false;
    }
    
    // Валидация деталей платежа для определенных способов
    const paymentDetailsTextarea = document.getElementById('payment_details');
    if (selectedPaymentMethod && ['bank_card', 'bank_transfer'].includes(selectedPaymentMethod.value)) {
        if (!paymentDetailsTextarea.value.trim()) {
            showFieldError(paymentDetailsTextarea, 'Укажите детали платежа');
            isValid = false;
        }
    }
    
    return isValid;
}

function validateAmount(input) {
    const value = parseFloat(input.value);
    
    clearFieldError(input);
    
    if (!value || value <= 0) {
        showFieldError(input, 'Введите корректную сумму');
        return false;
    }
    
    if (value < 10) {
        showFieldError(input, 'Минимальная сумма пополнения: 10 USDT');
        return false;
    }

    if (value > 10000) {
        showFieldError(input, 'Максимальная сумма пополнения: 10,000 USDT');
        return false;
    }
    
    return true;
}

// Способы оплаты
function initPaymentMethods() {
    const paymentOptions = document.querySelectorAll('.payment-method-option');
    
    paymentOptions.forEach(option => {
        option.addEventListener('click', function() {
            const radio = this.querySelector('input[type="radio"]');
            radio.checked = true;
            
            // Удаление активного класса у всех опций
            paymentOptions.forEach(opt => opt.classList.remove('active'));
            
            // Добавление активного класса к выбранной опции
            this.classList.add('active');
            
            // Обновление итоговой суммы
            updateSummary();
            
            // Обновление placeholder для деталей платежа
            updatePaymentDetailsPlaceholder(radio.value);
            
            // Анимация
            const card = this.querySelector('.payment-method-card');
            card.style.transform = 'scale(0.95)';
            setTimeout(() => {
                card.style.transform = 'scale(1)';
            }, 150);
        });
    });
}

function updatePaymentDetailsPlaceholder(paymentMethod) {
    const paymentDetailsTextarea = document.getElementById('payment_details');
    
    const placeholders = {
        'bank_card': 'Укажите последние 4 цифры карты или другую информацию для идентификации платежа',
        'bank_transfer': 'Укажите банк и способ перевода (онлайн-банк, отделение и т.д.)',
        'qiwi': 'Укажите номер QIWI кошелька с которого будет произведен платеж',
        'yandex_money': 'Укажите номер ЮMoney кошелька',
        'crypto': 'Укажите тип криптовалюты (Bitcoin, Ethereum и т.д.)',
        'sbp': 'Укажите банк через который будет произведен перевод по СБП'
    };
    
    paymentDetailsTextarea.placeholder = placeholders[paymentMethod] || 'Дополнительная информация для обработки платежа';
    
    // Анимация изменения
    paymentDetailsTextarea.style.borderColor = 'var(--primary-green)';
    setTimeout(() => {
        paymentDetailsTextarea.style.borderColor = '';
    }, 1000);
}

// Утилиты
function formatCurrency(amount) {
    return new Intl.NumberFormat('en-US', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
    }).format(amount) + ' USDT';
}

function showFieldError(field, message) {
    field.classList.add('error');
    
    // Удаление существующего сообщения об ошибке
    const existingError = field.parentElement.querySelector('.field-error');
    if (existingError) {
        existingError.remove();
    }
    
    // Добавление нового сообщения об ошибке
    const errorElement = document.createElement('div');
    errorElement.className = 'field-error';
    errorElement.textContent = message;
    errorElement.style.cssText = `
        color: var(--error);
        font-size: var(--text-xs);
        margin-top: var(--space-1);
        display: flex;
        align-items: center;
        gap: var(--space-1);
    `;
    errorElement.innerHTML = `<i class="fas fa-exclamation-triangle"></i> ${message}`;
    
    field.parentElement.appendChild(errorElement);
}

function clearFieldError(field) {
    field.classList.remove('error');
    const errorElement = field.parentElement.querySelector('.field-error');
    if (errorElement) {
        errorElement.remove();
    }
}

function showFormError(message) {
    // Создание общего уведомления об ошибке
    const alert = document.createElement('div');
    alert.className = 'alert alert-error';
    alert.innerHTML = `
        <i class="fas fa-exclamation-triangle"></i>
        ${message}
    `;
    
    // Вставка в начало контента
    const content = document.querySelector('.dashboard-content');
    content.insertBefore(alert, content.firstChild);
    
    // Прокрутка к ошибке
    alert.scrollIntoView({ behavior: 'smooth', block: 'center' });
    
    // Автоматическое скрытие через 5 секунд
    setTimeout(() => {
        if (alert.parentElement) {
            alert.style.opacity = '0';
            setTimeout(() => {
                alert.remove();
            }, 300);
        }
    }, 5000);
}

// Добавление стилей
const style = document.createElement('style');
style.textContent = `
    .amount-btn {
        padding: 8px 16px;
        border: 2px solid var(--gray-300);
        background: var(--white);
        color: var(--gray-700);
        border-radius: var(--radius-md);
        cursor: pointer;
        transition: all var(--transition-fast);
        font-size: var(--text-sm);
        font-weight: 500;
    }
    
    .amount-btn:hover {
        border-color: var(--primary-green);
        color: var(--primary-green);
    }
    
    .amount-btn.active {
        background: var(--primary-green);
        border-color: var(--primary-green);
        color: var(--white);
    }
    
    .amount-buttons {
        display: flex;
        gap: var(--space-2);
        margin-top: var(--space-2);
        flex-wrap: wrap;
    }
    
    .payment-method-option {
        cursor: pointer;
        transition: transform var(--transition-fast);
    }
    
    .payment-method-option:hover {
        transform: translateY(-2px);
    }
    
    .payment-method-option.active .payment-method-card {
        border-color: var(--primary-green);
        background: rgba(46, 204, 113, 0.05);
    }
    
    .payment-method-card {
        border: 2px solid var(--gray-200);
        border-radius: var(--radius-lg);
        padding: var(--space-4);
        text-align: center;
        transition: all var(--transition-fast);
        background: var(--white);
    }
    
    .payment-method-card i {
        font-size: var(--text-2xl);
        margin-bottom: var(--space-2);
        color: var(--primary-green);
    }
    
    .payment-method-card span {
        display: block;
        font-weight: 600;
        margin-bottom: var(--space-1);
        color: var(--gray-900);
    }
    
    .payment-method-card small {
        color: var(--gray-600);
        font-size: var(--text-xs);
    }
    
    .payment-methods {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: var(--space-3);
        margin-top: var(--space-2);
    }
    
    .deposit-summary {
        background: var(--gray-100);
        border-radius: var(--radius-lg);
        padding: var(--space-4);
        margin: var(--space-6) 0;
    }
    
    .summary-row {
        display: flex;
        justify-content: space-between;
        margin-bottom: var(--space-2);
    }
    
    .summary-row.total {
        border-top: 1px solid var(--gray-300);
        padding-top: var(--space-2);
        font-weight: 600;
        font-size: var(--text-lg);
    }
    
    .deposit-container {
        display: grid;
        grid-template-columns: 2fr 1fr;
        gap: var(--space-6);
    }
    
    @media (max-width: 768px) {
        .deposit-container {
            grid-template-columns: 1fr;
        }
        
        .payment-methods {
            grid-template-columns: repeat(2, 1fr);
        }
        
        .amount-buttons {
            grid-template-columns: repeat(2, 1fr);
        }
    }
    
    @media (max-width: 480px) {
        .payment-methods {
            grid-template-columns: 1fr;
        }
    }
`;
document.head.appendChild(style);
