<?php
/**
 * AstroGenix - Просмотр логов системы
 * Эко-майнинговая инвестиционная платформа
 */

require_once '../config/config.php';
require_once '../classes/Logger.php';

// Проверка авторизации и прав администратора
require_admin();

// Параметры фильтрации и пагинации
$filters = [
    'user_id' => intval($_GET['user_id'] ?? 0) ?: null,
    'action' => sanitize_input($_GET['action'] ?? ''),
    'ip_address' => sanitize_input($_GET['ip_address'] ?? ''),
    'date_from' => sanitize_input($_GET['date_from'] ?? ''),
    'date_to' => sanitize_input($_GET['date_to'] ?? '')
];

$page = max(1, intval($_GET['page'] ?? 1));
$per_page = 50;
$offset = ($page - 1) * $per_page;

// Инициализация переменных
$logs = [];
$total_logs = 0;
$error_message = '';
$success_message = '';

try {
    $database = new Database();
    $db = $database->getConnection();
    $logger = new Logger($db);
    
    // Обработка действий
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        if (!verify_csrf_token($_POST['csrf_token'] ?? '')) {
            $error_message = 'Ошибка безопасности. Попробуйте еще раз.';
        } else {
            $action = sanitize_input($_POST['action'] ?? '');
            
            if ($action === 'cleanup_logs') {
                $days = intval($_POST['cleanup_days'] ?? 90);
                if ($logger->cleanupOldLogs($days)) {
                    $success_message = "Логи старше $days дней успешно удалены.";
                } else {
                    $error_message = 'Ошибка при очистке логов.';
                }
            }
        }
    }
    
    // Получение логов
    $logs = $logger->getLogs($filters, $per_page, $offset);
    $total_logs = $logger->getLogsCount($filters);
    
} catch (Exception $e) {
    error_log("Admin logs error: " . $e->getMessage());
    $error_message = "Ошибка загрузки логов. Попробуйте обновить страницу.";
}

$total_pages = ceil($total_logs / $per_page);
$page_title = 'Логи системы - Админ-панель';
?>
<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?></title>
    
    <!-- CSS -->
    <link rel="stylesheet" href="../assets/css/style.css">
    <link rel="stylesheet" href="../assets/css/dashboard.css">
    <link rel="stylesheet" href="../assets/css/admin.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body class="dashboard-page admin-page">
    <!-- Admin Sidebar -->
    <?php include 'includes/admin-sidebar.php'; ?>

    <!-- Main Content -->
    <main class="main-content">
        <!-- Header -->
        <header class="dashboard-header">
            <div class="header-left">
                <button class="mobile-menu-toggle" id="mobileMenuToggle">
                    <i class="fas fa-bars"></i>
                </button>
                <h1>Логи системы</h1>
            </div>
            <div class="header-right">
                <div class="header-stats">
                    <span class="stat-item">
                        <i class="fas fa-list"></i>
                        Всего: <?php echo number_format($total_logs); ?>
                    </span>
                </div>
            </div>
        </header>

        <!-- Dashboard Content -->
        <div class="dashboard-content">
            <?php if (!empty($error_message)): ?>
                <div class="alert alert-error">
                    <i class="fas fa-exclamation-triangle"></i>
                    <?php echo htmlspecialchars($error_message); ?>
                </div>
            <?php endif; ?>

            <?php if (!empty($success_message)): ?>
                <div class="alert alert-success">
                    <i class="fas fa-check-circle"></i>
                    <?php echo htmlspecialchars($success_message); ?>
                </div>
            <?php endif; ?>

            <!-- Filters and Actions -->
            <div class="logs-controls">
                <!-- Filters -->
                <div class="admin-filters">
                    <form method="GET" class="filters-form">
                        <div class="filter-group">
                            <input type="text" name="action" value="<?php echo htmlspecialchars($filters['action']); ?>" 
                                   placeholder="Поиск по действию" class="filter-input">
                        </div>
                        <div class="filter-group">
                            <input type="text" name="ip_address" value="<?php echo htmlspecialchars($filters['ip_address']); ?>" 
                                   placeholder="IP адрес" class="filter-input">
                        </div>
                        <div class="filter-group">
                            <input type="date" name="date_from" value="<?php echo htmlspecialchars($filters['date_from']); ?>" 
                                   class="filter-input" title="Дата от">
                        </div>
                        <div class="filter-group">
                            <input type="date" name="date_to" value="<?php echo htmlspecialchars($filters['date_to']); ?>" 
                                   class="filter-input" title="Дата до">
                        </div>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search"></i>
                            Найти
                        </button>
                        <a href="logs.php" class="btn btn-outline">
                            <i class="fas fa-times"></i>
                            Сбросить
                        </a>
                    </form>
                </div>

                <!-- Actions -->
                <div class="logs-actions">
                    <button class="btn btn-warning" onclick="showCleanupModal()">
                        <i class="fas fa-trash"></i>
                        Очистить логи
                    </button>
                    <button class="btn btn-info" onclick="exportLogs()">
                        <i class="fas fa-download"></i>
                        Экспорт
                    </button>
                </div>
            </div>

            <!-- Logs Table -->
            <div class="admin-table-card">
                <div class="table-header">
                    <h3><i class="fas fa-list"></i> Логи системы</h3>
                </div>
                <div class="table-container">
                    <?php if (empty($logs)): ?>
                        <div class="empty-state">
                            <i class="fas fa-list"></i>
                            <p>Логи не найдены</p>
                        </div>
                    <?php else: ?>
                        <table class="admin-table logs-table">
                            <thead>
                                <tr>
                                    <th>Дата/Время</th>
                                    <th>Пользователь</th>
                                    <th>Действие</th>
                                    <th>Описание</th>
                                    <th>IP адрес</th>
                                    <th>Детали</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($logs as $log): ?>
                                    <tr class="log-row log-<?php echo $this->getLogLevel($log['action']); ?>">
                                        <td class="log-date">
                                            <?php echo date('d.m.Y H:i:s', strtotime($log['created_at'])); ?>
                                        </td>
                                        <td class="log-user">
                                            <?php if ($log['username']): ?>
                                                <div class="user-info">
                                                    <strong><?php echo htmlspecialchars($log['username']); ?></strong>
                                                    <small><?php echo htmlspecialchars($log['email']); ?></small>
                                                </div>
                                            <?php else: ?>
                                                <span class="text-muted">Система</span>
                                            <?php endif; ?>
                                        </td>
                                        <td class="log-action">
                                            <span class="action-badge action-<?php echo $this->getActionType($log['action']); ?>">
                                                <?php echo htmlspecialchars($log['action']); ?>
                                            </span>
                                        </td>
                                        <td class="log-description">
                                            <?php echo htmlspecialchars($log['description'] ?? '—'); ?>
                                        </td>
                                        <td class="log-ip">
                                            <?php echo htmlspecialchars($log['ip_address']); ?>
                                        </td>
                                        <td class="log-details">
                                            <?php if ($log['additional_data']): ?>
                                                <button class="btn btn-small btn-outline" 
                                                        onclick="showLogDetails(<?php echo htmlspecialchars(json_encode($log)); ?>)">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                            <?php else: ?>
                                                <span class="text-muted">—</span>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    <?php endif; ?>
                </div>
                
                <!-- Pagination -->
                <?php if ($total_pages > 1): ?>
                    <div class="pagination">
                        <?php if ($page > 1): ?>
                            <a href="?page=<?php echo $page - 1; ?>&<?php echo http_build_query($filters); ?>" 
                               class="pagination-btn">
                                <i class="fas fa-chevron-left"></i>
                            </a>
                        <?php endif; ?>
                        
                        <?php for ($i = max(1, $page - 2); $i <= min($total_pages, $page + 2); $i++): ?>
                            <a href="?page=<?php echo $i; ?>&<?php echo http_build_query($filters); ?>" 
                               class="pagination-btn <?php echo $i === $page ? 'active' : ''; ?>">
                                <?php echo $i; ?>
                            </a>
                        <?php endfor; ?>
                        
                        <?php if ($page < $total_pages): ?>
                            <a href="?page=<?php echo $page + 1; ?>&<?php echo http_build_query($filters); ?>" 
                               class="pagination-btn">
                                <i class="fas fa-chevron-right"></i>
                            </a>
                        <?php endif; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </main>

    <!-- Log Details Modal -->
    <div id="logDetailsModal" class="modal">
        <div class="modal-content modal-large">
            <div class="modal-header">
                <h3>Детали лога</h3>
                <button class="modal-close" onclick="closeLogDetailsModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="log-details-content">
                    <div class="detail-group">
                        <label>Дата и время:</label>
                        <span id="detailDateTime"></span>
                    </div>
                    <div class="detail-group">
                        <label>Пользователь:</label>
                        <span id="detailUser"></span>
                    </div>
                    <div class="detail-group">
                        <label>Действие:</label>
                        <span id="detailAction"></span>
                    </div>
                    <div class="detail-group">
                        <label>Описание:</label>
                        <span id="detailDescription"></span>
                    </div>
                    <div class="detail-group">
                        <label>IP адрес:</label>
                        <span id="detailIP"></span>
                    </div>
                    <div class="detail-group">
                        <label>User Agent:</label>
                        <span id="detailUserAgent"></span>
                    </div>
                    <div class="detail-group">
                        <label>Дополнительные данные:</label>
                        <pre id="detailAdditionalData"></pre>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Cleanup Modal -->
    <div id="cleanupModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Очистка логов</h3>
                <button class="modal-close" onclick="closeCleanupModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <form method="POST">
                <input type="hidden" name="csrf_token" value="<?php echo generate_csrf_token(); ?>">
                <input type="hidden" name="action" value="cleanup_logs">
                
                <div class="modal-body">
                    <p><strong>Внимание!</strong> Это действие удалит старые логи и не может быть отменено.</p>
                    
                    <div class="form-group">
                        <label for="cleanupDays">Удалить логи старше (дней):</label>
                        <input type="number" id="cleanupDays" name="cleanup_days" value="90" min="1" max="365" required>
                    </div>
                </div>
                
                <div class="modal-footer">
                    <button type="button" class="btn btn-outline" onclick="closeCleanupModal()">Отмена</button>
                    <button type="submit" class="btn btn-danger">Удалить логи</button>
                </div>
            </form>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="../assets/js/admin.js"></script>
    <script>
        function showLogDetails(log) {
            document.getElementById('detailDateTime').textContent = new Date(log.created_at).toLocaleString('ru-RU');
            document.getElementById('detailUser').textContent = log.username || 'Система';
            document.getElementById('detailAction').textContent = log.action;
            document.getElementById('detailDescription').textContent = log.description || '—';
            document.getElementById('detailIP').textContent = log.ip_address;
            document.getElementById('detailUserAgent').textContent = log.user_agent || '—';
            
            let additionalData = '—';
            if (log.additional_data) {
                try {
                    const data = typeof log.additional_data === 'string' ? JSON.parse(log.additional_data) : log.additional_data;
                    additionalData = JSON.stringify(data, null, 2);
                } catch (e) {
                    additionalData = log.additional_data;
                }
            }
            document.getElementById('detailAdditionalData').textContent = additionalData;
            
            document.getElementById('logDetailsModal').style.display = 'block';
        }
        
        function closeLogDetailsModal() {
            document.getElementById('logDetailsModal').style.display = 'none';
        }
        
        function showCleanupModal() {
            document.getElementById('cleanupModal').style.display = 'block';
        }
        
        function closeCleanupModal() {
            document.getElementById('cleanupModal').style.display = 'none';
        }
        
        function exportLogs() {
            const params = new URLSearchParams(window.location.search);
            params.set('export', '1');
            window.location.href = 'export.php?type=logs&' + params.toString();
        }
    </script>
</body>
</html>

<?php
// Вспомогательные функции для отображения
function getLogLevel($action) {
    if (strpos($action, 'error') !== false || strpos($action, 'failed') !== false || strpos($action, 'security') !== false) {
        return 'error';
    } elseif (strpos($action, 'warning') !== false) {
        return 'warning';
    } elseif (strpos($action, 'success') !== false || strpos($action, 'login') !== false) {
        return 'success';
    }
    return 'info';
}

function getActionType($action) {
    if (strpos($action, 'user_') === 0) {
        return 'user';
    } elseif (strpos($action, 'admin_') === 0) {
        return 'admin';
    } elseif (strpos($action, 'transaction_') === 0) {
        return 'transaction';
    } elseif (strpos($action, 'security_') === 0) {
        return 'security';
    }
    return 'system';
}
?>
