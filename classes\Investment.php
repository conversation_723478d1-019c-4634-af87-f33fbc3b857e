<?php
/**
 * AstroGenix - Класс инвестиций
 * Эко-майнинговая инвестиционная платформа
 */

class Investment {
    private $conn;
    private $table_name = "user_investments";
    private $packages_table = "investment_packages";

    public $id;
    public $user_id;
    public $package_id;
    public $amount;
    public $daily_profit;
    public $total_earned;
    public $start_date;
    public $end_date;
    public $status;
    public $created_at;
    public $updated_at;

    public function __construct($db) {
        $this->conn = $db;
    }

    // Создание новой инвестиции
    public function createInvestment($user_id, $package_id, $amount) {
        // Проверка баланса пользователя
        $user = new User($this->conn);
        $user->getUserById($user_id);
        
        if ($user->balance < $amount) {
            return false;
        }

        // Получение данных пакета
        $package = $this->getPackageById($package_id);
        if (!$package || !$package['is_active']) {
            return false;
        }

        // Проверка лимитов пакета
        if ($amount < $package['min_amount'] || $amount > $package['max_amount']) {
            return false;
        }

        try {
            $this->conn->beginTransaction();

            // Расчет ежедневной прибыли
            $daily_profit = ($amount * $package['daily_profit_percent']) / 100;

            // Создание инвестиции
            $query = "INSERT INTO " . $this->table_name . " 
                      SET user_id=:user_id, package_id=:package_id, amount=:amount, 
                          daily_profit=:daily_profit, start_date=CURDATE(), 
                          end_date=DATE_ADD(CURDATE(), INTERVAL :duration DAY), 
                          status='active'";

            $stmt = $this->conn->prepare($query);
            $stmt->bindParam(":user_id", $user_id);
            $stmt->bindParam(":package_id", $package_id);
            $stmt->bindParam(":amount", $amount);
            $stmt->bindParam(":daily_profit", $daily_profit);
            $stmt->bindParam(":duration", $package['duration_days']);

            if ($stmt->execute()) {
                $this->id = $this->conn->lastInsertId();

                // Списание средств с баланса
                $user->updateBalance($amount, 'subtract');

                // Обновление общей суммы инвестиций пользователя
                $update_user_query = "UPDATE users SET total_invested = total_invested + :amount WHERE id = :user_id";
                $update_user_stmt = $this->conn->prepare($update_user_query);
                $update_user_stmt->bindParam(":amount", $amount);
                $update_user_stmt->bindParam(":user_id", $user_id);
                $update_user_stmt->execute();

                // Создание транзакции
                $transaction = new Transaction($this->conn);
                $transaction->user_id = $user_id;
                $transaction->type = 'investment';
                $transaction->amount = $amount;
                $transaction->status = 'completed';
                $transaction->description = "Инвестиция в пакет '{$package['name']}'";
                $transaction->create();

                // Обработка реферальных бонусов
                $this->processReferralBonuses($user_id, $amount);

                // Обновление зеленой энергии
                $this->updateGreenEnergy($user_id, $amount);

                $this->conn->commit();
                return true;
            }

            $this->conn->rollback();
            return false;

        } catch (Exception $e) {
            $this->conn->rollback();
            error_log("Investment creation error: " . $e->getMessage());
            return false;
        }
    }

    // Получение пакета по ID
    public function getPackageById($package_id) {
        $query = "SELECT * FROM " . $this->packages_table . " WHERE id = :id AND is_active = 1";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":id", $package_id);
        $stmt->execute();

        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    // Получение всех активных пакетов
    public function getAllPackages() {
        $query = "SELECT * FROM " . $this->packages_table . " WHERE is_active = 1 ORDER BY min_amount ASC";
        $stmt = $this->conn->prepare($query);
        $stmt->execute();

        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    // Получение инвестиций пользователя
    public function getUserInvestments($user_id, $status = null, $limit = 20, $offset = 0) {
        $where_clause = "WHERE ui.user_id = :user_id";
        if ($status) {
            $where_clause .= " AND ui.status = :status";
        }

        $query = "SELECT ui.*, ip.name as package_name, ip.daily_profit_percent,
                         DATEDIFF(ui.end_date, CURDATE()) as days_left,
                         DATEDIFF(CURDATE(), ui.start_date) as days_passed
                  FROM " . $this->table_name . " ui 
                  JOIN " . $this->packages_table . " ip ON ui.package_id = ip.id 
                  {$where_clause} 
                  ORDER BY ui.created_at DESC 
                  LIMIT :limit OFFSET :offset";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":user_id", $user_id);
        $stmt->bindParam(":limit", $limit, PDO::PARAM_INT);
        $stmt->bindParam(":offset", $offset, PDO::PARAM_INT);
        
        if ($status) {
            $stmt->bindParam(":status", $status);
        }

        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    // Обработка ежедневных прибылей
    public function processDailyProfits() {
        // Получение всех активных инвестиций
        $query = "SELECT ui.*, u.id as user_id 
                  FROM " . $this->table_name . " ui 
                  JOIN users u ON ui.user_id = u.id 
                  WHERE ui.status = 'active' AND ui.end_date >= CURDATE()";

        $stmt = $this->conn->prepare($query);
        $stmt->execute();
        $active_investments = $stmt->fetchAll(PDO::FETCH_ASSOC);

        foreach ($active_investments as $investment) {
            // Проверка, была ли уже начислена прибыль за сегодня
            $profit_check_query = "SELECT id FROM daily_profits 
                                   WHERE investment_id = :investment_id AND profit_date = CURDATE()";
            $profit_check_stmt = $this->conn->prepare($profit_check_query);
            $profit_check_stmt->bindParam(":investment_id", $investment['id']);
            $profit_check_stmt->execute();

            if ($profit_check_stmt->rowCount() == 0) {
                // Начисление ежедневной прибыли
                $this->addDailyProfit($investment);
            }

            // Проверка истечения срока инвестиции
            if ($investment['end_date'] <= date('Y-m-d')) {
                $this->completeInvestment($investment['id']);
            }
        }
    }

    // Начисление ежедневной прибыли
    private function addDailyProfit($investment) {
        try {
            $this->conn->beginTransaction();

            // Добавление записи о ежедневной прибыли
            $daily_profit_query = "INSERT INTO daily_profits 
                                   SET investment_id=:investment_id, user_id=:user_id, 
                                       amount=:amount, profit_date=CURDATE()";
            $daily_profit_stmt = $this->conn->prepare($daily_profit_query);
            $daily_profit_stmt->bindParam(":investment_id", $investment['id']);
            $daily_profit_stmt->bindParam(":user_id", $investment['user_id']);
            $daily_profit_stmt->bindParam(":amount", $investment['daily_profit']);
            $daily_profit_stmt->execute();

            // Обновление общей заработанной суммы по инвестиции
            $update_investment_query = "UPDATE " . $this->table_name . " 
                                        SET total_earned = total_earned + :amount 
                                        WHERE id = :id";
            $update_investment_stmt = $this->conn->prepare($update_investment_query);
            $update_investment_stmt->bindParam(":amount", $investment['daily_profit']);
            $update_investment_stmt->bindParam(":id", $investment['id']);
            $update_investment_stmt->execute();

            // Создание транзакции прибыли
            $transaction = new Transaction($this->conn);
            $transaction->createProfitTransaction(
                $investment['user_id'], 
                $investment['daily_profit'], 
                $investment['id']
            );

            // Обновление общей заработанной суммы пользователя
            $update_user_query = "UPDATE users SET total_earned = total_earned + :amount WHERE id = :user_id";
            $update_user_stmt = $this->conn->prepare($update_user_query);
            $update_user_stmt->bindParam(":amount", $investment['daily_profit']);
            $update_user_stmt->bindParam(":user_id", $investment['user_id']);
            $update_user_stmt->execute();

            $this->conn->commit();

        } catch (Exception $e) {
            $this->conn->rollback();
            error_log("Daily profit error: " . $e->getMessage());
        }
    }

    // Завершение инвестиции
    private function completeInvestment($investment_id) {
        $query = "UPDATE " . $this->table_name . " SET status='completed' WHERE id = :id";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":id", $investment_id);
        $stmt->execute();
    }

    // Обработка реферальных бонусов
    private function processReferralBonuses($user_id, $amount) {
        // Получение реферера пользователя
        $user_query = "SELECT referred_by FROM users WHERE id = :user_id";
        $user_stmt = $this->conn->prepare($user_query);
        $user_stmt->bindParam(":user_id", $user_id);
        $user_stmt->execute();
        $user_data = $user_stmt->fetch(PDO::FETCH_ASSOC);

        if (!$user_data || !$user_data['referred_by']) {
            return;
        }

        // Получение настроек реферальной программы
        $referral_settings = [
            1 => 5.00, // 1-й уровень - 5%
            2 => 3.00, // 2-й уровень - 3%
            3 => 2.00  // 3-й уровень - 2%
        ];

        $current_referrer = $user_data['referred_by'];
        $level = 1;

        while ($current_referrer && $level <= 3) {
            $bonus_percent = $referral_settings[$level];
            $bonus_amount = ($amount * $bonus_percent) / 100;

            // Создание записи о реферальной комиссии
            $commission_query = "INSERT INTO referral_commissions 
                                SET referrer_id=:referrer_id, referred_id=:referred_id, 
                                    level=:level, commission_percent=:commission_percent, 
                                    amount=:amount, source_transaction_id=:source_transaction_id, 
                                    status='paid'";
            $commission_stmt = $this->conn->prepare($commission_query);
            $commission_stmt->bindParam(":referrer_id", $current_referrer);
            $commission_stmt->bindParam(":referred_id", $user_id);
            $commission_stmt->bindParam(":level", $level);
            $commission_stmt->bindParam(":commission_percent", $bonus_percent);
            $commission_stmt->bindParam(":amount", $bonus_amount);
            $commission_stmt->bindParam(":source_transaction_id", $this->id);
            $commission_stmt->execute();

            // Создание транзакции реферального бонуса
            $transaction = new Transaction($this->conn);
            $transaction->createReferralTransaction($current_referrer, $bonus_amount, $level, $this->id);

            // Получение следующего уровня реферера
            $next_referrer_query = "SELECT referred_by FROM users WHERE id = :user_id";
            $next_referrer_stmt = $this->conn->prepare($next_referrer_query);
            $next_referrer_stmt->bindParam(":user_id", $current_referrer);
            $next_referrer_stmt->execute();
            $next_referrer_data = $next_referrer_stmt->fetch(PDO::FETCH_ASSOC);

            $current_referrer = $next_referrer_data ? $next_referrer_data['referred_by'] : null;
            $level++;
        }
    }

    // Обновление зеленой энергии
    private function updateGreenEnergy($user_id, $amount) {
        // Расчет зеленой энергии (1 рубль = 0.1 кВт⋅ч)
        $energy_generated = $amount * 0.1;

        $query = "INSERT INTO green_energy_stats (user_id, energy_generated) 
                  VALUES (:user_id, :energy_generated) 
                  ON DUPLICATE KEY UPDATE 
                  energy_generated = energy_generated + :energy_generated";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":user_id", $user_id);
        $stmt->bindParam(":energy_generated", $energy_generated);
        $stmt->execute();

        // Обновление общей зеленой энергии в настройках
        $total_energy_query = "UPDATE system_settings 
                               SET setting_value = setting_value + :energy_generated 
                               WHERE setting_key = 'total_green_energy'";
        $total_energy_stmt = $this->conn->prepare($total_energy_query);
        $total_energy_stmt->bindParam(":energy_generated", $energy_generated);
        $total_energy_stmt->execute();
    }

    // Получение статистики инвестиций пользователя
    public function getUserInvestmentStats($user_id) {
        $query = "SELECT 
                    COUNT(*) as total_investments,
                    COUNT(CASE WHEN status = 'active' THEN 1 END) as active_investments,
                    COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_investments,
                    COALESCE(SUM(amount), 0) as total_invested,
                    COALESCE(SUM(total_earned), 0) as total_earned,
                    COALESCE(SUM(CASE WHEN status = 'active' THEN amount ELSE 0 END), 0) as active_amount
                  FROM " . $this->table_name . " 
                  WHERE user_id = :user_id";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":user_id", $user_id);
        $stmt->execute();

        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    // Получение инвестиции по ID
    public function getInvestmentById($id) {
        $query = "SELECT ui.*, ip.name as package_name, ip.daily_profit_percent,
                         DATEDIFF(ui.end_date, CURDATE()) as days_left,
                         DATEDIFF(CURDATE(), ui.start_date) as days_passed
                  FROM " . $this->table_name . " ui 
                  JOIN " . $this->packages_table . " ip ON ui.package_id = ip.id 
                  WHERE ui.id = :id";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":id", $id);
        $stmt->execute();

        return $stmt->fetch(PDO::FETCH_ASSOC);
    }
}
?>
