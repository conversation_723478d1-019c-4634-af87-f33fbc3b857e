<?php
/**
 * AstroGenix - Cron-задача для ежедневного начисления прибыли
 * Эко-майнинговая инвестиционная платформа
 * 
 * Запускать ежедневно в 00:01
 * Cron: 1 0 * * * /usr/bin/php /path/to/cron/daily_profits.php
 */

// Установка рабочей директории
chdir(dirname(__DIR__));

require_once 'config/config.php';

// Проверка, что скрипт запущен из командной строки
if (php_sapi_name() !== 'cli') {
    die('Этот скрипт может быть запущен только из командной строки');
}

// Логирование начала работы
error_log("Daily profits cron started at " . date('Y-m-d H:i:s'));

try {
    $database = new Database();
    $db = $database->getConnection();
    $investment = new Investment($db);
    
    // Начало транзакции
    $db->beginTransaction();
    
    // Получение всех активных инвестиций
    $query = "SELECT ui.*, ip.name as package_name, ip.daily_profit_percent, ip.duration_days,
                     u.username, u.email, u.first_name, u.last_name
              FROM user_investments ui 
              JOIN investment_packages ip ON ui.package_id = ip.id 
              JOIN users u ON ui.user_id = u.id
              WHERE ui.status = 'active' 
                AND ui.start_date <= CURDATE() 
                AND ui.end_date >= CURDATE()
              ORDER BY ui.id";
    
    $stmt = $db->prepare($query);
    $stmt->execute();
    $active_investments = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $processed_count = 0;
    $total_profit_paid = 0;
    $completed_investments = 0;
    $errors = [];
    
    foreach ($active_investments as $investment_data) {
        try {
            // Проверка, была ли уже начислена прибыль за сегодня
            $profit_check_query = "SELECT id FROM daily_profits 
                                   WHERE investment_id = :investment_id 
                                     AND profit_date = CURDATE()";
            $profit_check_stmt = $db->prepare($profit_check_query);
            $profit_check_stmt->bindParam(':investment_id', $investment_data['id']);
            $profit_check_stmt->execute();
            
            if ($profit_check_stmt->rowCount() > 0) {
                // Прибыль уже начислена за сегодня
                continue;
            }
            
            // Начисление ежедневной прибыли
            $daily_profit = $investment_data['daily_profit'];
            
            // Добавление записи о ежедневной прибыли
            $daily_profit_query = "INSERT INTO daily_profits 
                                   SET investment_id = :investment_id, 
                                       user_id = :user_id, 
                                       amount = :amount, 
                                       profit_date = CURDATE()";
            $daily_profit_stmt = $db->prepare($daily_profit_query);
            $daily_profit_stmt->bindParam(':investment_id', $investment_data['id']);
            $daily_profit_stmt->bindParam(':user_id', $investment_data['user_id']);
            $daily_profit_stmt->bindParam(':amount', $daily_profit);
            $daily_profit_stmt->execute();
            
            // Обновление общей заработанной суммы по инвестиции
            $update_investment_query = "UPDATE user_investments 
                                        SET total_earned = total_earned + :amount 
                                        WHERE id = :id";
            $update_investment_stmt = $db->prepare($update_investment_query);
            $update_investment_stmt->bindParam(':amount', $daily_profit);
            $update_investment_stmt->bindParam(':id', $investment_data['id']);
            $update_investment_stmt->execute();
            
            // Обновление баланса пользователя
            $update_balance_query = "UPDATE users 
                                     SET balance = balance + :amount,
                                         total_earned = total_earned + :amount
                                     WHERE id = :user_id";
            $update_balance_stmt = $db->prepare($update_balance_query);
            $update_balance_stmt->bindParam(':amount', $daily_profit);
            $update_balance_stmt->bindParam(':user_id', $investment_data['user_id']);
            $update_balance_stmt->execute();
            
            // Создание транзакции прибыли
            $transaction_query = "INSERT INTO transactions 
                                  SET user_id = :user_id,
                                      type = 'profit',
                                      amount = :amount,
                                      status = 'completed',
                                      description = :description";
            $transaction_stmt = $db->prepare($transaction_query);
            $description = "Ежедневная прибыль от инвестиции #{$investment_data['id']} ({$investment_data['package_name']})";
            $transaction_stmt->bindParam(':user_id', $investment_data['user_id']);
            $transaction_stmt->bindParam(':amount', $daily_profit);
            $transaction_stmt->bindParam(':description', $description);
            $transaction_stmt->execute();
            
            // Проверка завершения инвестиции
            if ($investment_data['end_date'] <= date('Y-m-d')) {
                $complete_investment_query = "UPDATE user_investments 
                                              SET status = 'completed' 
                                              WHERE id = :id";
                $complete_investment_stmt = $db->prepare($complete_investment_query);
                $complete_investment_stmt->bindParam(':id', $investment_data['id']);
                $complete_investment_stmt->execute();
                
                $completed_investments++;
                
                // Логирование завершения инвестиции
                error_log("Investment #{$investment_data['id']} completed for user {$investment_data['username']}");
            }
            
            $processed_count++;
            $total_profit_paid += $daily_profit;
            
            // Логирование успешного начисления
            error_log("Profit {$daily_profit} paid to user {$investment_data['username']} for investment #{$investment_data['id']}");
            
        } catch (Exception $e) {
            $error_msg = "Error processing investment #{$investment_data['id']}: " . $e->getMessage();
            $errors[] = $error_msg;
            error_log($error_msg);
        }
    }
    
    // Обновление общей статистики зеленой энергии
    if ($total_profit_paid > 0) {
        $energy_generated = $total_profit_paid * 0.05; // 1 рубль прибыли = 0.05 кВт⋅ч
        
        $update_energy_query = "INSERT INTO green_energy_stats (user_id, energy_generated, generated_date) 
                                SELECT DISTINCT user_id, :energy_per_user, CURDATE()
                                FROM user_investments 
                                WHERE status = 'active'
                                ON DUPLICATE KEY UPDATE 
                                energy_generated = energy_generated + VALUES(energy_generated)";
        $update_energy_stmt = $db->prepare($update_energy_query);
        $energy_per_user = $energy_generated / max(1, $processed_count);
        $update_energy_stmt->bindParam(':energy_per_user', $energy_per_user);
        $update_energy_stmt->execute();
        
        // Обновление общей зеленой энергии
        $total_energy_query = "UPDATE system_settings 
                               SET setting_value = setting_value + :energy_generated 
                               WHERE setting_key = 'total_green_energy'";
        $total_energy_stmt = $db->prepare($total_energy_query);
        $total_energy_stmt->bindParam(':energy_generated', $energy_generated);
        $total_energy_stmt->execute();
    }
    
    // Создание записи о выполнении cron-задачи
    $cron_log_query = "INSERT INTO cron_logs 
                       SET task_name = 'daily_profits',
                           executed_at = NOW(),
                           status = 'success',
                           details = :details";
    $cron_log_stmt = $db->prepare($cron_log_query);
    $details = json_encode([
        'processed_investments' => $processed_count,
        'total_profit_paid' => $total_profit_paid,
        'completed_investments' => $completed_investments,
        'errors_count' => count($errors)
    ]);
    $cron_log_stmt->bindParam(':details', $details);
    $cron_log_stmt->execute();
    
    // Подтверждение транзакции
    $db->commit();
    
    // Итоговое логирование
    $summary = "Daily profits cron completed successfully:\n";
    $summary .= "- Processed investments: {$processed_count}\n";
    $summary .= "- Total profit paid: " . number_format($total_profit_paid, 2) . " USDT\n";
    $summary .= "- Completed investments: {$completed_investments}\n";
    $summary .= "- Errors: " . count($errors) . "\n";
    
    if (!empty($errors)) {
        $summary .= "Errors:\n" . implode("\n", $errors) . "\n";
    }
    
    error_log($summary);
    echo $summary;
    
    // Отправка уведомления администратору при наличии ошибок
    if (!empty($errors)) {
        sendAdminNotification("Daily Profits Cron Errors", $summary);
    }
    
} catch (Exception $e) {
    // Откат транзакции при ошибке
    if ($db->inTransaction()) {
        $db->rollback();
    }
    
    $error_msg = "Critical error in daily profits cron: " . $e->getMessage();
    error_log($error_msg);
    
    // Создание записи об ошибке
    try {
        $cron_log_query = "INSERT INTO cron_logs 
                           SET task_name = 'daily_profits',
                               executed_at = NOW(),
                               status = 'error',
                               details = :details";
        $cron_log_stmt = $db->prepare($cron_log_query);
        $cron_log_stmt->bindParam(':details', $error_msg);
        $cron_log_stmt->execute();
    } catch (Exception $log_error) {
        error_log("Failed to log cron error: " . $log_error->getMessage());
    }
    
    // Отправка критического уведомления
    sendAdminNotification("Critical: Daily Profits Cron Failed", $error_msg);
    
    echo "CRITICAL ERROR: " . $error_msg . "\n";
    exit(1);
}

/**
 * Отправка уведомления администратору
 */
function sendAdminNotification($subject, $message) {
    // Здесь можно добавить отправку email, Telegram, Slack и т.д.
    
    // Пример отправки email (требует настройки SMTP)
    /*
    $admin_email = '<EMAIL>';
    $headers = 'From: <EMAIL>' . "\r\n" .
               'Reply-To: <EMAIL>' . "\r\n" .
               'X-Mailer: PHP/' . phpversion();
    
    mail($admin_email, $subject, $message, $headers);
    */
    
    // Логирование уведомления
    error_log("Admin notification: {$subject} - {$message}");
}

error_log("Daily profits cron finished at " . date('Y-m-d H:i:s'));
?>
