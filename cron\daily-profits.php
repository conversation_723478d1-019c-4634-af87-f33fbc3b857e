<?php
/**
 * AstroGenix - Ежедневное начисление прибыли
 * Cron скрипт для начисления прибыли по активным инвестициям
 */

if (php_sapi_name() !== 'cli') {
    die('Этот скрипт может быть запущен только из командной строки');
}

require_once dirname(__DIR__) . '/config/config.php';

function logMessage($message) {
    $timestamp = date('Y-m-d H:i:s');
    $log_file = dirname(__DIR__) . '/logs/daily-profits.log';
    
    $log_dir = dirname($log_file);
    if (!is_dir($log_dir)) {
        mkdir($log_dir, 0755, true);
    }
    
    file_put_contents($log_file, "[$timestamp] $message" . PHP_EOL, FILE_APPEND | LOCK_EX);
    echo "[$timestamp] $message" . PHP_EOL;
}

try {
    logMessage("Запуск ежедневного начисления прибыли");
    
    $database = new Database();
    $db = $database->getConnection();
    
    $processed = 0;
    $total_profit = 0;
    $completed_investments = 0;
    
    // Получение активных инвестиций
    $investments_query = "SELECT ui.*, u.username, ip.name as package_name 
                         FROM user_investments ui 
                         JOIN users u ON ui.user_id = u.id 
                         JOIN investment_packages ip ON ui.package_id = ip.id
                         WHERE ui.status = 'active' 
                         AND ui.start_date <= CURDATE() 
                         AND u.is_active = 1
                         ORDER BY ui.id";
    
    $investments_stmt = $db->prepare($investments_query);
    $investments_stmt->execute();
    $investments = $investments_stmt->fetchAll(PDO::FETCH_ASSOC);
    
    logMessage("Найдено активных инвестиций: " . count($investments));
    
    foreach ($investments as $investment) {
        // Проверка истечения срока инвестиции
        if (strtotime($investment['end_date']) < strtotime(date('Y-m-d'))) {
            // Завершение инвестиции
            $complete_query = "UPDATE user_investments SET status = 'completed' WHERE id = :id";
            $complete_stmt = $db->prepare($complete_query);
            $complete_stmt->bindParam(':id', $investment['id'], PDO::PARAM_INT);
            $complete_stmt->execute();
            
            logMessage("Инвестиция #{$investment['id']} завершена (истек срок)");
            $completed_investments++;
            continue;
        }
        
        // Проверка, была ли уже начислена прибыль сегодня
        $profit_check_query = "SELECT COUNT(*) FROM transactions 
                              WHERE user_id = :user_id 
                              AND type = 'profit' 
                              AND description LIKE :description 
                              AND DATE(created_at) = CURDATE()";
        
        $profit_check_stmt = $db->prepare($profit_check_query);
        $profit_check_stmt->bindParam(':user_id', $investment['user_id'], PDO::PARAM_INT);
        $description_pattern = "Прибыль по инвестиции #{$investment['id']}%";
        $profit_check_stmt->bindParam(':description', $description_pattern);
        $profit_check_stmt->execute();
        
        if ($profit_check_stmt->fetchColumn() > 0) {
            continue; // Прибыль уже начислена сегодня
        }
        
        // Начисление прибыли
        $profit_amount = $investment['daily_profit'];
        
        // Создание транзакции прибыли
        $profit_transaction_query = "INSERT INTO transactions 
                                    (user_id, type, amount, status, description, created_at) 
                                    VALUES 
                                    (:user_id, 'profit', :amount, 'completed', :description, NOW())";
        
        $description = "Прибыль по инвестиции #{$investment['id']} ({$investment['package_name']})";
        
        $profit_transaction_stmt = $db->prepare($profit_transaction_query);
        $profit_transaction_stmt->bindParam(':user_id', $investment['user_id'], PDO::PARAM_INT);
        $profit_transaction_stmt->bindParam(':amount', $profit_amount);
        $profit_transaction_stmt->bindParam(':description', $description);
        
        if ($profit_transaction_stmt->execute()) {
            // Обновление баланса пользователя
            $update_balance_query = "UPDATE users SET balance = balance + :profit, total_earned = total_earned + :profit WHERE id = :user_id";
            $update_balance_stmt = $db->prepare($update_balance_query);
            $update_balance_stmt->bindParam(':profit', $profit_amount);
            $update_balance_stmt->bindParam(':user_id', $investment['user_id'], PDO::PARAM_INT);
            $update_balance_stmt->execute();
            
            // Обновление общей прибыли по инвестиции
            $update_investment_query = "UPDATE user_investments SET total_earned = total_earned + :profit WHERE id = :investment_id";
            $update_investment_stmt = $db->prepare($update_investment_query);
            $update_investment_stmt->bindParam(':profit', $profit_amount);
            $update_investment_stmt->bindParam(':investment_id', $investment['id'], PDO::PARAM_INT);
            $update_investment_stmt->execute();
            
            // Создание уведомления пользователю
            if (class_exists('Notification')) {
                $notification = new Notification($db);
                $notification->create(
                    $investment['user_id'],
                    'Начислена прибыль',
                    "Начислена прибыль {$profit_amount} USDT по инвестиции в пакет \"{$investment['package_name']}\"",
                    'success'
                );
            }
            
            $processed++;
            $total_profit += $profit_amount;
            
            logMessage("Начислена прибыль: {$profit_amount} USDT пользователю {$investment['username']} (инвестиция #{$investment['id']})");
        } else {
            logMessage("ОШИБКА: Не удалось начислить прибыль для инвестиции #{$investment['id']}");
        }
    }
    
    // Обновление экологической статистики
    if ($processed > 0) {
        $energy_generated = $processed * rand(5, 15); // Каждая прибыль генерирует энергию
        $co2_saved = round($energy_generated * 0.4, 2);
        
        $update_eco_query = "UPDATE green_energy_stats 
                            SET energy_generated = energy_generated + :energy, 
                                co2_saved = co2_saved + :co2,
                                updated_at = NOW()
                            WHERE user_id = 0";
        
        $update_eco_stmt = $db->prepare($update_eco_query);
        $update_eco_stmt->bindParam(':energy', $energy_generated);
        $update_eco_stmt->bindParam(':co2', $co2_saved);
        $update_eco_stmt->execute();
        
        logMessage("Обновлена экологическая статистика: +{$energy_generated} кВт⋅ч, +{$co2_saved} кг CO₂");
    }
    
    logMessage("Ежедневное начисление завершено:");
    logMessage("- Обработано инвестиций: $processed");
    logMessage("- Общая прибыль: " . round($total_profit, 2) . " USDT");
    logMessage("- Завершено инвестиций: $completed_investments");
    
} catch (Exception $e) {
    logMessage("ОШИБКА: " . $e->getMessage());
    logMessage("Трассировка: " . $e->getTraceAsString());
    exit(1);
}
?>
