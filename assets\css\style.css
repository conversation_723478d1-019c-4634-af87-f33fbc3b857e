/**
 * AstroGenix - Основные стили
 * Эко-майнинговая инвестиционная платформа
 */

/* Сброс стилей и базовые настройки */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* Основная цветовая палитра */
    --primary-green: #2ECC71;
    --primary-purple: #8E44AD;
    --dark-green: #27AE60;
    --dark-purple: #7D3C98;
    --light-green: #58D68D;
    --light-purple: #BB8FCE;
    
    /* Градиенты */
    --gradient-primary: linear-gradient(135deg, var(--primary-green) 0%, var(--primary-purple) 100%);
    --gradient-secondary: linear-gradient(135deg, var(--light-green) 0%, var(--light-purple) 100%);
    --gradient-dark: linear-gradient(135deg, var(--dark-green) 0%, var(--dark-purple) 100%);
    
    /* Нейтральные цвета */
    --white: #FFFFFF;
    --black: #1A1A1A;
    --gray-100: #F8F9FA;
    --gray-200: #E9ECEF;
    --gray-300: #DEE2E6;
    --gray-400: #CED4DA;
    --gray-500: #ADB5BD;
    --gray-600: #6C757D;
    --gray-700: #495057;
    --gray-800: #343A40;
    --gray-900: #212529;
    
    /* Статусные цвета */
    --success: #28A745;
    --warning: #FFC107;
    --error: #DC3545;
    --info: #17A2B8;
    
    /* Тени */
    --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.1);
    --shadow-md: 0 4px 8px rgba(0, 0, 0, 0.15);
    --shadow-lg: 0 8px 16px rgba(0, 0, 0, 0.2);
    --shadow-xl: 0 16px 32px rgba(0, 0, 0, 0.25);
    
    /* Радиусы */
    --radius-sm: 4px;
    --radius-md: 8px;
    --radius-lg: 12px;
    --radius-xl: 16px;
    --radius-full: 50%;
    
    /* Переходы */
    --transition-fast: 0.2s ease;
    --transition-normal: 0.3s ease;
    --transition-slow: 0.5s ease;
    
    /* Шрифты */
    --font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    --font-secondary: 'Poppins', sans-serif;
    
    /* Размеры шрифтов */
    --text-xs: 0.75rem;
    --text-sm: 0.875rem;
    --text-base: 1rem;
    --text-lg: 1.125rem;
    --text-xl: 1.25rem;
    --text-2xl: 1.5rem;
    --text-3xl: 1.875rem;
    --text-4xl: 2.25rem;
    --text-5xl: 3rem;
    
    /* Отступы */
    --space-1: 0.25rem;
    --space-2: 0.5rem;
    --space-3: 0.75rem;
    --space-4: 1rem;
    --space-5: 1.25rem;
    --space-6: 1.5rem;
    --space-8: 2rem;
    --space-10: 2.5rem;
    --space-12: 3rem;
    --space-16: 4rem;
    --space-20: 5rem;
    
    /* Контейнеры */
    --container-sm: 640px;
    --container-md: 768px;
    --container-lg: 1024px;
    --container-xl: 1280px;
    --container-2xl: 1536px;
}

/* Базовые стили */
html {
    scroll-behavior: smooth;
}

body {
    font-family: var(--font-primary);
    font-size: var(--text-base);
    line-height: 1.6;
    color: var(--gray-800);
    background-color: var(--white);
    overflow-x: hidden;
}

/* Контейнеры */
.container {
    width: 100%;
    max-width: var(--container-xl);
    margin: 0 auto;
    padding: 0 var(--space-4);
}

@media (min-width: 640px) {
    .container {
        padding: 0 var(--space-6);
    }
}

@media (min-width: 1024px) {
    .container {
        padding: 0 var(--space-8);
    }
}

/* Типография */
h1, h2, h3, h4, h5, h6 {
    font-family: var(--font-secondary);
    font-weight: 600;
    line-height: 1.2;
    margin-bottom: var(--space-4);
    color: var(--gray-900);
}

h1 { font-size: var(--text-4xl); }
h2 { font-size: var(--text-3xl); }
h3 { font-size: var(--text-2xl); }
h4 { font-size: var(--text-xl); }
h5 { font-size: var(--text-lg); }
h6 { font-size: var(--text-base); }

p {
    margin-bottom: var(--space-4);
}

a {
    color: var(--primary-green);
    text-decoration: none;
    transition: color var(--transition-fast);
}

a:hover {
    color: var(--dark-green);
}

/* Кнопки */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--space-2);
    padding: var(--space-3) var(--space-6);
    font-family: var(--font-primary);
    font-size: var(--text-sm);
    font-weight: 500;
    text-decoration: none;
    border: none;
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: all var(--transition-fast);
    white-space: nowrap;
    user-select: none;
}

.btn:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(46, 204, 113, 0.3);
}

.btn-primary {
    background: var(--gradient-primary);
    color: var(--white);
    box-shadow: var(--shadow-sm);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
    color: var(--white);
}

.btn-outline {
    background: transparent;
    color: var(--primary-green);
    border: 2px solid var(--primary-green);
}

.btn-outline:hover {
    background: var(--primary-green);
    color: var(--white);
}

.btn-secondary {
    background: var(--gray-200);
    color: var(--gray-700);
}

.btn-secondary:hover {
    background: var(--gray-300);
    color: var(--gray-800);
}

.btn-success {
    background: var(--success);
    color: var(--white);
}

.btn-warning {
    background: var(--warning);
    color: var(--gray-900);
}

.btn-error {
    background: var(--error);
    color: var(--white);
}

.btn-large {
    padding: var(--space-4) var(--space-8);
    font-size: var(--text-base);
}

.btn-small {
    padding: var(--space-2) var(--space-4);
    font-size: var(--text-xs);
}

.btn-full {
    width: 100%;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
}

/* Формы */
.form-group {
    margin-bottom: var(--space-5);
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--space-4);
}

@media (max-width: 768px) {
    .form-row {
        grid-template-columns: 1fr;
    }
}

label {
    display: block;
    font-weight: 500;
    margin-bottom: var(--space-2);
    color: var(--gray-700);
}

input, textarea, select {
    width: 100%;
    padding: var(--space-3);
    font-family: var(--font-primary);
    font-size: var(--text-sm);
    border: 2px solid var(--gray-300);
    border-radius: var(--radius-md);
    background-color: var(--white);
    transition: border-color var(--transition-fast);
}

input:focus, textarea:focus, select:focus {
    outline: none;
    border-color: var(--primary-green);
    box-shadow: 0 0 0 3px rgba(46, 204, 113, 0.1);
}

input.error, textarea.error, select.error {
    border-color: var(--error);
}

/* Алерты */
.alert {
    padding: var(--space-4);
    border-radius: var(--radius-md);
    margin-bottom: var(--space-5);
    display: flex;
    align-items: flex-start;
    gap: var(--space-3);
}

.alert i {
    margin-top: var(--space-1);
}

.alert ul {
    margin: 0;
    padding-left: var(--space-4);
}

.alert-success {
    background-color: rgba(40, 167, 69, 0.1);
    color: var(--success);
    border: 1px solid rgba(40, 167, 69, 0.3);
}

.alert-error {
    background-color: rgba(220, 53, 69, 0.1);
    color: var(--error);
    border: 1px solid rgba(220, 53, 69, 0.3);
}

.alert-warning {
    background-color: rgba(255, 193, 7, 0.1);
    color: #856404;
    border: 1px solid rgba(255, 193, 7, 0.3);
}

.alert-info {
    background-color: rgba(23, 162, 184, 0.1);
    color: var(--info);
    border: 1px solid rgba(23, 162, 184, 0.3);
}

/* Карточки */
.card {
    background: var(--white);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-sm);
    overflow: hidden;
    transition: box-shadow var(--transition-fast);
}

.card:hover {
    box-shadow: var(--shadow-md);
}

.card-header {
    padding: var(--space-6);
    border-bottom: 1px solid var(--gray-200);
}

.card-body {
    padding: var(--space-6);
}

.card-footer {
    padding: var(--space-6);
    border-top: 1px solid var(--gray-200);
    background-color: var(--gray-100);
}

/* Утилиты */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.text-gradient {
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.d-none { display: none; }
.d-block { display: block; }
.d-flex { display: flex; }
.d-grid { display: grid; }

.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.align-center { align-items: center; }

.w-full { width: 100%; }
.h-full { height: 100%; }

.mt-auto { margin-top: auto; }
.mb-auto { margin-bottom: auto; }

/* Адаптивность */
@media (max-width: 768px) {
    :root {
        --text-4xl: 2rem;
        --text-3xl: 1.5rem;
        --text-2xl: 1.25rem;
    }
    
    .container {
        padding: 0 var(--space-4);
    }
    
    .btn-large {
        padding: var(--space-3) var(--space-6);
        font-size: var(--text-sm);
    }
}
