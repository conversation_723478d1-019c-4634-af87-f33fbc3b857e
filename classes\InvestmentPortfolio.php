<?php
/**
 * AstroGenix - Портфель инвестиций
 * Детальная аналитика и управление портфелем инвестиций
 */

class InvestmentPortfolio {
    private $db;
    
    public function __construct($database) {
        $this->db = $database;
    }
    
    /**
     * Получение полной аналитики портфеля пользователя
     */
    public function getPortfolioAnalytics($user_id) {
        try {
            $analytics = [];
            
            // Основная статистика
            $analytics['overview'] = $this->getPortfolioOverview($user_id);
            
            // Распределение по пакетам
            $analytics['distribution'] = $this->getPackageDistribution($user_id);
            
            // Динамика доходности
            $analytics['performance'] = $this->getPerformanceData($user_id);
            
            // Прогноз доходов
            $analytics['forecast'] = $this->getForecastData($user_id);
            
            // Риск-анализ
            $analytics['risk_analysis'] = $this->getRiskAnalysis($user_id);
            
            // История транзакций
            $analytics['transaction_history'] = $this->getTransactionHistory($user_id);
            
            return $analytics;
            
        } catch (Exception $e) {
            error_log("Get portfolio analytics error: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Общий обзор портфеля
     */
    private function getPortfolioOverview($user_id) {
        try {
            $query = "SELECT 
                        COUNT(*) as total_investments,
                        COUNT(CASE WHEN status = 'active' THEN 1 END) as active_investments,
                        COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_investments,
                        COALESCE(SUM(amount), 0) as total_invested,
                        COALESCE(SUM(total_earned), 0) as total_earned,
                        COALESCE(SUM(compound_earnings), 0) as compound_earnings,
                        COALESCE(AVG(daily_profit), 0) as avg_daily_profit,
                        COALESCE(SUM(CASE WHEN status = 'active' THEN amount ELSE 0 END), 0) as active_amount,
                        COALESCE(SUM(CASE WHEN status = 'active' THEN daily_profit ELSE 0 END), 0) as daily_income
                      FROM user_investments 
                      WHERE user_id = :user_id";
            
            $stmt = $this->db->prepare($query);
            $stmt->bindParam(':user_id', $user_id, PDO::PARAM_INT);
            $stmt->execute();
            $overview = $stmt->fetch(PDO::FETCH_ASSOC);
            
            // Расчет дополнительных метрик
            if ($overview['total_invested'] > 0) {
                $overview['roi_percent'] = round(($overview['total_earned'] / $overview['total_invested']) * 100, 2);
                $overview['avg_roi'] = round($overview['roi_percent'] / max($overview['total_investments'], 1), 2);
            } else {
                $overview['roi_percent'] = 0;
                $overview['avg_roi'] = 0;
            }
            
            // Прогноз месячного дохода
            $overview['monthly_forecast'] = $overview['daily_income'] * 30;
            
            // Статус портфеля
            $overview['portfolio_status'] = $this->getPortfolioStatus($overview);
            
            return $overview;
            
        } catch (Exception $e) {
            error_log("Get portfolio overview error: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Распределение инвестиций по пакетам
     */
    private function getPackageDistribution($user_id) {
        try {
            $query = "SELECT 
                        ip.name as package_name,
                        ip.package_type,
                        ip.daily_profit_percent,
                        ip.risk_level,
                        ip.color,
                        COUNT(ui.id) as investment_count,
                        SUM(ui.amount) as total_amount,
                        SUM(ui.total_earned) as total_earned,
                        AVG(ui.daily_profit) as avg_daily_profit,
                        SUM(CASE WHEN ui.status = 'active' THEN ui.amount ELSE 0 END) as active_amount
                      FROM user_investments ui
                      JOIN investment_packages ip ON ui.package_id = ip.id
                      WHERE ui.user_id = :user_id
                      GROUP BY ip.id, ip.name, ip.package_type, ip.daily_profit_percent, ip.risk_level, ip.color
                      ORDER BY total_amount DESC";
            
            $stmt = $this->db->prepare($query);
            $stmt->bindParam(':user_id', $user_id, PDO::PARAM_INT);
            $stmt->execute();
            $distribution = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            // Расчет процентного распределения
            $total_invested = array_sum(array_column($distribution, 'total_amount'));
            
            foreach ($distribution as &$package) {
                if ($total_invested > 0) {
                    $package['percentage'] = round(($package['total_amount'] / $total_invested) * 100, 1);
                } else {
                    $package['percentage'] = 0;
                }
                
                // ROI по пакету
                if ($package['total_amount'] > 0) {
                    $package['roi_percent'] = round(($package['total_earned'] / $package['total_amount']) * 100, 2);
                } else {
                    $package['roi_percent'] = 0;
                }
            }
            
            return $distribution;
            
        } catch (Exception $e) {
            error_log("Get package distribution error: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Данные о динамике доходности
     */
    private function getPerformanceData($user_id, $days = 30) {
        try {
            $query = "SELECT 
                        DATE(created_at) as date,
                        SUM(CASE WHEN type = 'investment' THEN amount ELSE 0 END) as invested,
                        SUM(CASE WHEN type = 'profit' THEN amount ELSE 0 END) as profit,
                        COUNT(CASE WHEN type = 'investment' THEN 1 END) as investments_count
                      FROM transactions 
                      WHERE user_id = :user_id 
                      AND created_at >= DATE_SUB(CURDATE(), INTERVAL :days DAY)
                      AND type IN ('investment', 'profit')
                      GROUP BY DATE(created_at)
                      ORDER BY date ASC";
            
            $stmt = $this->db->prepare($query);
            $stmt->bindParam(':user_id', $user_id, PDO::PARAM_INT);
            $stmt->bindParam(':days', $days, PDO::PARAM_INT);
            $stmt->execute();
            $daily_data = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            // Заполнение пропущенных дней
            $performance = [];
            $start_date = date('Y-m-d', strtotime("-{$days} days"));
            $end_date = date('Y-m-d');
            
            $current_date = $start_date;
            $cumulative_invested = 0;
            $cumulative_profit = 0;
            
            while ($current_date <= $end_date) {
                $day_data = array_filter($daily_data, function($item) use ($current_date) {
                    return $item['date'] === $current_date;
                });
                
                if (!empty($day_data)) {
                    $day_data = reset($day_data);
                    $cumulative_invested += $day_data['invested'];
                    $cumulative_profit += $day_data['profit'];
                } else {
                    $day_data = [
                        'date' => $current_date,
                        'invested' => 0,
                        'profit' => 0,
                        'investments_count' => 0
                    ];
                }
                
                $day_data['cumulative_invested'] = $cumulative_invested;
                $day_data['cumulative_profit'] = $cumulative_profit;
                $day_data['cumulative_roi'] = $cumulative_invested > 0 ? 
                    round(($cumulative_profit / $cumulative_invested) * 100, 2) : 0;
                
                $performance[] = $day_data;
                $current_date = date('Y-m-d', strtotime($current_date . ' +1 day'));
            }
            
            return $performance;
            
        } catch (Exception $e) {
            error_log("Get performance data error: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Прогноз доходов
     */
    private function getForecastData($user_id, $days = 30) {
        try {
            // Получение активных инвестиций
            $query = "SELECT 
                        ui.*,
                        ip.package_type,
                        DATEDIFF(ui.end_date, CURDATE()) as days_remaining
                      FROM user_investments ui
                      JOIN investment_packages ip ON ui.package_id = ip.id
                      WHERE ui.user_id = :user_id 
                      AND ui.status = 'active'
                      AND ui.end_date > CURDATE()";
            
            $stmt = $this->db->prepare($query);
            $stmt->bindParam(':user_id', $user_id, PDO::PARAM_INT);
            $stmt->execute();
            $active_investments = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            $forecast = [];
            
            for ($i = 1; $i <= $days; $i++) {
                $date = date('Y-m-d', strtotime("+{$i} days"));
                $daily_profit = 0;
                $active_amount = 0;
                
                foreach ($active_investments as $investment) {
                    if ($investment['days_remaining'] >= $i) {
                        $daily_profit += $investment['daily_profit'];
                        $active_amount += $investment['amount'];
                        
                        // Для компаундных инвестиций учитываем реинвестирование
                        if ($investment['package_type'] === 'compound' && $investment['auto_reinvest']) {
                            $compound_factor = 1 + ($investment['reinvest_percent'] / 100) * 0.01; // Упрощенный расчет
                            $daily_profit *= pow($compound_factor, $i);
                        }
                    }
                }
                
                $forecast[] = [
                    'date' => $date,
                    'daily_profit' => round($daily_profit, 2),
                    'active_amount' => round($active_amount, 2),
                    'cumulative_profit' => $i == 1 ? round($daily_profit, 2) : 
                        round($forecast[$i-2]['cumulative_profit'] + $daily_profit, 2)
                ];
            }
            
            return $forecast;
            
        } catch (Exception $e) {
            error_log("Get forecast data error: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Анализ рисков портфеля
     */
    private function getRiskAnalysis($user_id) {
        try {
            $query = "SELECT 
                        ip.risk_level,
                        COUNT(ui.id) as investment_count,
                        SUM(ui.amount) as total_amount,
                        AVG(ip.daily_profit_percent) as avg_profit_percent
                      FROM user_investments ui
                      JOIN investment_packages ip ON ui.package_id = ip.id
                      WHERE ui.user_id = :user_id AND ui.status = 'active'
                      GROUP BY ip.risk_level";
            
            $stmt = $this->db->prepare($query);
            $stmt->bindParam(':user_id', $user_id, PDO::PARAM_INT);
            $stmt->execute();
            $risk_distribution = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            $total_amount = array_sum(array_column($risk_distribution, 'total_amount'));
            
            // Расчет общего риска портфеля
            $risk_score = 0;
            $risk_weights = [
                'low' => 1,
                'medium' => 2,
                'high' => 3,
                'very_high' => 4
            ];
            
            foreach ($risk_distribution as &$risk) {
                if ($total_amount > 0) {
                    $risk['percentage'] = round(($risk['total_amount'] / $total_amount) * 100, 1);
                    $risk_score += $risk_weights[$risk['risk_level']] * $risk['percentage'];
                } else {
                    $risk['percentage'] = 0;
                }
            }
            
            $risk_score = $risk_score / 100; // Нормализация
            
            // Определение общего уровня риска
            if ($risk_score <= 1.5) {
                $overall_risk = 'low';
                $risk_description = 'Консервативный портфель с низким риском';
            } elseif ($risk_score <= 2.5) {
                $overall_risk = 'medium';
                $risk_description = 'Сбалансированный портфель со средним риском';
            } elseif ($risk_score <= 3.5) {
                $overall_risk = 'high';
                $risk_description = 'Агрессивный портфель с высоким риском';
            } else {
                $overall_risk = 'very_high';
                $risk_description = 'Очень рискованный портфель';
            }
            
            return [
                'distribution' => $risk_distribution,
                'overall_risk' => $overall_risk,
                'risk_score' => round($risk_score, 2),
                'risk_description' => $risk_description,
                'recommendations' => $this->getRiskRecommendations($overall_risk, $risk_distribution)
            ];
            
        } catch (Exception $e) {
            error_log("Get risk analysis error: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * История транзакций портфеля
     */
    private function getTransactionHistory($user_id, $limit = 50) {
        try {
            $query = "SELECT 
                        t.*,
                        CASE 
                            WHEN t.type = 'investment' THEN 'Инвестиция'
                            WHEN t.type = 'profit' THEN 'Прибыль'
                            WHEN t.type = 'withdrawal' THEN 'Вывод'
                            WHEN t.type = 'deposit' THEN 'Пополнение'
                            ELSE t.type
                        END as type_name
                      FROM transactions t
                      WHERE t.user_id = :user_id
                      AND t.type IN ('investment', 'profit', 'withdrawal', 'deposit')
                      ORDER BY t.created_at DESC
                      LIMIT :limit";
            
            $stmt = $this->db->prepare($query);
            $stmt->bindParam(':user_id', $user_id, PDO::PARAM_INT);
            $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
            $stmt->execute();
            
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
            
        } catch (Exception $e) {
            error_log("Get transaction history error: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Определение статуса портфеля
     */
    private function getPortfolioStatus($overview) {
        if ($overview['active_investments'] == 0) {
            return ['status' => 'inactive', 'message' => 'Нет активных инвестиций'];
        }
        
        if ($overview['roi_percent'] >= 20) {
            return ['status' => 'excellent', 'message' => 'Отличная доходность'];
        } elseif ($overview['roi_percent'] >= 10) {
            return ['status' => 'good', 'message' => 'Хорошая доходность'];
        } elseif ($overview['roi_percent'] >= 5) {
            return ['status' => 'average', 'message' => 'Средняя доходность'];
        } else {
            return ['status' => 'low', 'message' => 'Низкая доходность'];
        }
    }
    
    /**
     * Рекомендации по управлению рисками
     */
    private function getRiskRecommendations($overall_risk, $risk_distribution) {
        $recommendations = [];
        
        switch ($overall_risk) {
            case 'low':
                $recommendations[] = 'Рассмотрите добавление инвестиций со средним риском для увеличения доходности';
                $recommendations[] = 'Ваш портфель хорошо защищен от волатильности';
                break;
                
            case 'medium':
                $recommendations[] = 'Оптимальный баланс риска и доходности';
                $recommendations[] = 'Следите за распределением между различными типами пакетов';
                break;
                
            case 'high':
                $recommendations[] = 'Рассмотрите добавление консервативных инвестиций для снижения риска';
                $recommendations[] = 'Не инвестируйте больше, чем можете позволить себе потерять';
                break;
                
            case 'very_high':
                $recommendations[] = 'ВНИМАНИЕ: Очень высокий уровень риска!';
                $recommendations[] = 'Настоятельно рекомендуется диверсификация в сторону менее рискованных активов';
                $recommendations[] = 'Рассмотрите частичный вывод прибыли';
                break;
        }
        
        return $recommendations;
    }
}
?>
