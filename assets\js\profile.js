/**
 * AstroGenix - JavaScript для страницы профиля
 * Эко-майнинговая инвестиционная платформа
 */

document.addEventListener('DOMContentLoaded', function() {
    initializeProfile();
});

function initializeProfile() {
    // Инициализация вкладок
    initProfileTabs();
    
    // Инициализация копирования реферальной ссылки
    initReferralLinkCopy();
    
    // Инициализация валидации форм
    initFormValidation();
    
    // Инициализация анимаций
    initProfileAnimations();
}

// Переключение вкладок
function initProfileTabs() {
    const tabButtons = document.querySelectorAll('.tab-btn');
    const tabContents = document.querySelectorAll('.tab-content');
    
    tabButtons.forEach(button => {
        button.addEventListener('click', function() {
            const targetTab = this.dataset.tab;
            
            // Удаление активного класса у всех кнопок и контента
            tabButtons.forEach(btn => btn.classList.remove('active'));
            tabContents.forEach(content => content.classList.remove('active'));
            
            // Добавление активного класса к текущей кнопке
            this.classList.add('active');
            
            // Показ соответствующего контента
            const targetContent = document.getElementById(targetTab);
            if (targetContent) {
                targetContent.classList.add('active');
                
                // Анимация появления контента
                targetContent.style.opacity = '0';
                targetContent.style.transform = 'translateY(20px)';
                
                setTimeout(() => {
                    targetContent.style.transition = 'all 0.3s ease';
                    targetContent.style.opacity = '1';
                    targetContent.style.transform = 'translateY(0)';
                }, 50);
            }
        });
    });
}

// Копирование реферальной ссылки
function initReferralLinkCopy() {
    const copyButton = document.getElementById('copyReferralLink');
    const linkInput = document.getElementById('referralLinkInput');
    
    if (copyButton && linkInput) {
        copyButton.addEventListener('click', function() {
            // Выделение текста
            linkInput.select();
            linkInput.setSelectionRange(0, 99999); // Для мобильных устройств
            
            // Копирование в буфер обмена
            try {
                const successful = document.execCommand('copy');
                if (successful) {
                    showCopySuccess();
                } else {
                    // Fallback для современных браузеров
                    navigator.clipboard.writeText(linkInput.value).then(() => {
                        showCopySuccess();
                    }).catch(() => {
                        showCopyError();
                    });
                }
            } catch (err) {
                // Fallback для современных браузеров
                if (navigator.clipboard) {
                    navigator.clipboard.writeText(linkInput.value).then(() => {
                        showCopySuccess();
                    }).catch(() => {
                        showCopyError();
                    });
                } else {
                    showCopyError();
                }
            }
        });
    }
}

function showCopySuccess() {
    const copyButton = document.getElementById('copyReferralLink');
    const originalText = copyButton.innerHTML;
    
    copyButton.innerHTML = '<i class="fas fa-check"></i> Скопировано!';
    copyButton.style.background = 'var(--success)';
    copyButton.classList.add('copy-success');
    
    setTimeout(() => {
        copyButton.innerHTML = originalText;
        copyButton.style.background = '';
        copyButton.classList.remove('copy-success');
    }, 2000);
    
    showNotification('Реферальная ссылка скопирована в буфер обмена!', 'success');
}

function showCopyError() {
    showNotification('Не удалось скопировать ссылку. Попробуйте выделить и скопировать вручную.', 'error');
}

// Валидация форм
function initFormValidation() {
    const profileForm = document.querySelector('.profile-form');
    const passwordForm = document.querySelector('.password-form');
    
    if (profileForm) {
        profileForm.addEventListener('submit', function(e) {
            if (!validateProfileForm()) {
                e.preventDefault();
            }
        });
        
        // Валидация в реальном времени
        const inputs = profileForm.querySelectorAll('input[required]');
        inputs.forEach(input => {
            input.addEventListener('blur', function() {
                validateField(this);
            });
            
            input.addEventListener('input', function() {
                clearFieldError(this);
            });
        });
    }
    
    if (passwordForm) {
        passwordForm.addEventListener('submit', function(e) {
            if (!validatePasswordForm()) {
                e.preventDefault();
            }
        });
        
        // Валидация паролей в реальном времени
        const newPasswordInput = document.getElementById('new_password');
        const confirmPasswordInput = document.getElementById('confirm_password');
        
        if (newPasswordInput && confirmPasswordInput) {
            confirmPasswordInput.addEventListener('input', function() {
                validatePasswordMatch();
            });
            
            newPasswordInput.addEventListener('input', function() {
                validatePasswordStrength();
                if (confirmPasswordInput.value) {
                    validatePasswordMatch();
                }
            });
        }
    }
}

function validateProfileForm() {
    const firstName = document.getElementById('first_name');
    const lastName = document.getElementById('last_name');
    const phone = document.getElementById('phone');
    
    let isValid = true;
    
    // Валидация имени
    if (!firstName.value.trim()) {
        showFieldError(firstName, 'Имя обязательно для заполнения');
        isValid = false;
    }
    
    // Валидация фамилии
    if (!lastName.value.trim()) {
        showFieldError(lastName, 'Фамилия обязательна для заполнения');
        isValid = false;
    }
    
    // Валидация телефона (если заполнен)
    if (phone.value.trim() && !isValidPhone(phone.value)) {
        showFieldError(phone, 'Неверный формат телефона');
        isValid = false;
    }
    
    return isValid;
}

function validatePasswordForm() {
    const currentPassword = document.getElementById('current_password');
    const newPassword = document.getElementById('new_password');
    const confirmPassword = document.getElementById('confirm_password');
    
    let isValid = true;
    
    // Валидация текущего пароля
    if (!currentPassword.value) {
        showFieldError(currentPassword, 'Введите текущий пароль');
        isValid = false;
    }
    
    // Валидация нового пароля
    if (!newPassword.value) {
        showFieldError(newPassword, 'Введите новый пароль');
        isValid = false;
    } else if (newPassword.value.length < 8) {
        showFieldError(newPassword, 'Пароль должен содержать минимум 8 символов');
        isValid = false;
    }
    
    // Валидация подтверждения пароля
    if (!confirmPassword.value) {
        showFieldError(confirmPassword, 'Подтвердите новый пароль');
        isValid = false;
    } else if (newPassword.value !== confirmPassword.value) {
        showFieldError(confirmPassword, 'Пароли не совпадают');
        isValid = false;
    }
    
    return isValid;
}

function validateField(field) {
    const value = field.value.trim();
    
    if (field.hasAttribute('required') && !value) {
        showFieldError(field, 'Это поле обязательно для заполнения');
        return false;
    }
    
    if (field.type === 'tel' && value && !isValidPhone(value)) {
        showFieldError(field, 'Неверный формат телефона');
        return false;
    }
    
    clearFieldError(field);
    return true;
}

function validatePasswordStrength() {
    const newPassword = document.getElementById('new_password');
    const value = newPassword.value;
    
    if (value.length > 0 && value.length < 8) {
        showFieldError(newPassword, 'Пароль должен содержать минимум 8 символов');
        return false;
    }
    
    clearFieldError(newPassword);
    return true;
}

function validatePasswordMatch() {
    const newPassword = document.getElementById('new_password');
    const confirmPassword = document.getElementById('confirm_password');
    
    if (confirmPassword.value && newPassword.value !== confirmPassword.value) {
        showFieldError(confirmPassword, 'Пароли не совпадают');
        return false;
    }
    
    clearFieldError(confirmPassword);
    return true;
}

function showFieldError(field, message) {
    clearFieldError(field);
    
    field.style.borderColor = 'var(--error)';
    
    const errorElement = document.createElement('div');
    errorElement.className = 'field-error';
    errorElement.textContent = message;
    errorElement.style.cssText = `
        color: var(--error);
        font-size: var(--text-xs);
        margin-top: var(--space-1);
    `;
    
    field.parentNode.appendChild(errorElement);
}

function clearFieldError(field) {
    field.style.borderColor = '';
    
    const existingError = field.parentNode.querySelector('.field-error');
    if (existingError) {
        existingError.remove();
    }
}

function isValidPhone(phone) {
    // Простая валидация телефона
    const phoneRegex = /^\+?[1-9]\d{1,14}$/;
    return phoneRegex.test(phone.replace(/[\s\-\(\)]/g, ''));
}

// Анимации
function initProfileAnimations() {
    // Анимация карточки профиля
    const profileCard = document.querySelector('.profile-card');
    if (profileCard) {
        profileCard.style.opacity = '0';
        profileCard.style.transform = 'translateY(30px)';
        
        setTimeout(() => {
            profileCard.style.transition = 'all 0.8s ease';
            profileCard.style.opacity = '1';
            profileCard.style.transform = 'translateY(0)';
        }, 200);
    }
    
    // Анимация статистических элементов
    const statItems = document.querySelectorAll('.stat-item');
    statItems.forEach((item, index) => {
        item.style.opacity = '0';
        item.style.transform = 'translateX(-20px)';
        
        setTimeout(() => {
            item.style.transition = 'all 0.5s ease';
            item.style.opacity = '1';
            item.style.transform = 'translateX(0)';
        }, 400 + (index * 100));
    });
    
    // Анимация экологических элементов
    const ecologyItems = document.querySelectorAll('.ecology-item');
    const ecologyObserver = new IntersectionObserver((entries) => {
        entries.forEach((entry, index) => {
            if (entry.isIntersecting) {
                setTimeout(() => {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }, index * 150);
                ecologyObserver.unobserve(entry.target);
            }
        });
    });
    
    ecologyItems.forEach(item => {
        item.style.opacity = '0';
        item.style.transform = 'translateY(30px)';
        item.style.transition = 'all 0.6s ease';
        ecologyObserver.observe(item);
    });
    
    // Анимация прогресс-бара
    const progressFill = document.querySelector('.progress-fill');
    if (progressFill) {
        const targetWidth = progressFill.style.width;
        progressFill.style.width = '0%';
        
        setTimeout(() => {
            progressFill.style.width = targetWidth;
        }, 1000);
    }
}

// Утилиты
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
        <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-triangle' : 'info-circle'}"></i>
        <span>${message}</span>
        <button class="notification-close">
            <i class="fas fa-times"></i>
        </button>
    `;
    
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: ${type === 'success' ? '#28a745' : type === 'error' ? '#dc3545' : '#17a2b8'};
        color: white;
        padding: 16px 20px;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        z-index: 9999;
        display: flex;
        align-items: center;
        gap: 12px;
        max-width: 400px;
        transform: translateX(100%);
        transition: transform 0.3s ease;
    `;
    
    document.body.appendChild(notification);
    
    // Анимация появления
    setTimeout(() => {
        notification.style.transform = 'translateX(0)';
    }, 100);
    
    // Обработчик закрытия
    const closeBtn = notification.querySelector('.notification-close');
    closeBtn.addEventListener('click', () => {
        hideNotification(notification);
    });
    
    // Автоматическое скрытие
    setTimeout(() => {
        hideNotification(notification);
    }, 5000);
}

function hideNotification(notification) {
    notification.style.transform = 'translateX(100%)';
    setTimeout(() => {
        if (notification.parentElement) {
            notification.parentElement.removeChild(notification);
        }
    }, 300);
}

// Добавление дополнительных стилей
const style = document.createElement('style');
style.textContent = `
    .notification-close {
        background: none;
        border: none;
        color: inherit;
        cursor: pointer;
        padding: 4px;
        border-radius: 4px;
        transition: background 0.2s ease;
    }
    
    .notification-close:hover {
        background: rgba(255, 255, 255, 0.2);
    }
    
    .copy-success {
        animation: copyPulse 0.3s ease;
    }
    
    @keyframes copyPulse {
        0% { transform: scale(1); }
        50% { transform: scale(1.05); }
        100% { transform: scale(1); }
    }
    
    .field-error {
        animation: errorShake 0.3s ease;
    }
    
    @keyframes errorShake {
        0%, 100% { transform: translateX(0); }
        25% { transform: translateX(-5px); }
        75% { transform: translateX(5px); }
    }
`;
document.head.appendChild(style);
