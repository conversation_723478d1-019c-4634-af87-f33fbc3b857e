<?php
/**
 * AstroGenix - Основная конфигурация
 * Эко-майнинговая инвестиционная платформа
 */

// Запуск сессии
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Основные настройки
define('SITE_NAME', 'AstroGenix');
define('SITE_URL', 'http://localhost/genix');
define('SITE_DESCRIPTION', 'Эко-майнинговая инвестиционная платформа');

// Настройки безопасности
define('ENCRYPTION_KEY', 'your-secret-encryption-key-here');
define('JWT_SECRET', 'your-jwt-secret-key-here');

// Настройки email
define('SMTP_HOST', 'smtp.gmail.com');
define('SMTP_PORT', 587);
define('SMTP_USERNAME', '<EMAIL>');
define('SMTP_PASSWORD', 'your-app-password');
define('FROM_EMAIL', '<EMAIL>');
define('FROM_NAME', 'AstroGenix');

// Настройки файлов
define('UPLOAD_PATH', 'uploads/');
define('MAX_FILE_SIZE', 5 * 1024 * 1024); // 5MB

// Настройки пагинации
define('ITEMS_PER_PAGE', 20);

// Настройки валидации
define('MIN_PASSWORD_LENGTH', 8);
define('MAX_LOGIN_ATTEMPTS', 5);
define('LOGIN_LOCKOUT_TIME', 900); // 15 минут

// Настройки инвестиций
define('MIN_INVESTMENT', 10);
define('MAX_INVESTMENT', 50000);
define('INVESTMENT_DURATION_DAYS', 30);

// Настройки реферальной программы
define('REFERRAL_LEVEL_1_PERCENT', 5.00);
define('REFERRAL_LEVEL_2_PERCENT', 3.00);
define('REFERRAL_LEVEL_3_PERCENT', 2.00);

// Часовой пояс
date_default_timezone_set('Europe/Moscow');

// Обработка ошибок
error_reporting(E_ALL);
ini_set('display_errors', 0);
ini_set('log_errors', 1);
ini_set('error_log', 'logs/error.log');

// Автозагрузка классов
spl_autoload_register(function ($class_name) {
    $directories = [
        'classes/',
        'models/',
        'controllers/',
        'helpers/'
    ];
    
    foreach ($directories as $directory) {
        $file = $directory . $class_name . '.php';
        if (file_exists($file)) {
            require_once $file;
            return;
        }
    }
});

// Подключение к базе данных
require_once 'config/database.php';

// Функции безопасности
function sanitize_input($data) {
    $data = trim($data);
    $data = stripslashes($data);
    $data = htmlspecialchars($data, ENT_QUOTES, 'UTF-8');
    return $data;
}

function generate_csrf_token() {
    if (!isset($_SESSION['csrf_token'])) {
        $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
    }
    return $_SESSION['csrf_token'];
}

function verify_csrf_token($token) {
    return isset($_SESSION['csrf_token']) && hash_equals($_SESSION['csrf_token'], $token);
}

function is_logged_in() {
    return isset($_SESSION['user_id']) && !empty($_SESSION['user_id']);
}

function is_admin() {
    return isset($_SESSION['is_admin']) && $_SESSION['is_admin'] === true;
}

function get_user_role() {
    if (!is_logged_in()) {
        return null;
    }
    return isset($_SESSION['role']) ? $_SESSION['role'] : (is_admin() ? 'admin' : 'user');
}

function require_login() {
    if (!is_logged_in()) {
        redirect('login.php');
    }
}

function require_admin() {
    if (!is_logged_in()) {
        redirect('login.php');
    }
    if (!is_admin()) {
        redirect('dashboard.php');
    }
}

function redirect($url) {
    // Проверяем, не начинается ли URL с http/https
    if (!preg_match('/^https?:\/\//', $url)) {
        // Если это относительный URL, добавляем базовый путь
        $base_url = rtrim(SITE_URL, '/');
        if (strpos($url, '/') !== 0) {
            $url = '/' . $url;
        }
        $url = $base_url . $url;
    }

    header("Location: " . $url);
    exit();
}

function format_currency($amount) {
    return number_format($amount, 2, '.', ',') . ' USDT';
}

function generate_referral_code($length = 8) {
    return strtoupper(substr(str_shuffle('ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'), 0, $length));
}

// Проверка режима обслуживания
function check_maintenance_mode() {
    // Здесь можно добавить проверку из базы данных
    if (defined('MAINTENANCE_MODE') && MAINTENANCE_MODE === true) {
        if (!is_admin()) {
            include 'maintenance.php';
            exit();
        }
    }
}
?>
