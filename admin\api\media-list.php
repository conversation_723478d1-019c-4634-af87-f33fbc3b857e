<?php
/**
 * AstroGenix - API для получения списка медиафайлов
 * Используется в редакторе для вставки изображений
 */

require_once '../../config/config.php';

// Проверка авторизации администратора
if (!is_logged_in() || !is_admin()) {
    http_response_code(403);
    exit('Access denied');
}

header('Content-Type: application/json; charset=utf-8');

try {
    $database = new Database();
    $db = $database->getConnection();
    
    // Получение всех изображений
    $query = "SELECT id, filename, original_name, file_path, alt_text, width, height, created_at 
              FROM cms_media 
              WHERE file_type = 'image' 
              ORDER BY created_at DESC 
              LIMIT 100";
    
    $stmt = $db->prepare($query);
    $stmt->execute();
    $images = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Формирование списка для TinyMCE
    $image_list = [];
    
    foreach ($images as $image) {
        $image_list[] = [
            'title' => $image['original_name'] ?: $image['filename'],
            'value' => SITE_URL . $image['file_path'],
            'meta' => [
                'width' => $image['width'],
                'height' => $image['height'],
                'alt' => $image['alt_text'] ?: $image['original_name']
            ]
        ];
    }
    
    // Если нет изображений, добавляем заглушки
    if (empty($image_list)) {
        $image_list[] = [
            'title' => 'Placeholder 400x300',
            'value' => 'https://via.placeholder.com/400x300/22c55e/ffffff?text=AstroGenix',
            'meta' => [
                'width' => 400,
                'height' => 300,
                'alt' => 'Placeholder изображение'
            ]
        ];
        
        $image_list[] = [
            'title' => 'Placeholder 800x400',
            'value' => 'https://via.placeholder.com/800x400/8b5cf6/ffffff?text=AstroGenix',
            'meta' => [
                'width' => 800,
                'height' => 400,
                'alt' => 'Placeholder изображение'
            ]
        ];
    }
    
    echo json_encode($image_list, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
    
} catch (Exception $e) {
    error_log("Media list API error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['error' => 'Internal server error'], JSON_UNESCAPED_UNICODE);
}
?>
