/**
 * AstroGenix - JavaScript для реферальной программы
 * Эко-майнинговая инвестиционная платформа
 */

document.addEventListener('DOMContentLoaded', function() {
    initializeReferrals();
});

function initializeReferrals() {
    // Инициализация копирования ссылки
    initLinkCopy();
    
    // Инициализация кнопок поделиться
    initShareButtons();
    
    // Инициализация переключения уровней
    initLevelTabs();
    
    // Инициализация QR-кода
    initQRCode();
    
    // Инициализация анимаций
    initReferralAnimations();
}

// Копирование реферальной ссылки
function initLinkCopy() {
    const copyBtn = document.getElementById('copyLinkBtn');
    const linkInput = document.getElementById('referralLink');
    
    if (copyBtn && linkInput) {
        copyBtn.addEventListener('click', function() {
            // Выделение текста
            linkInput.select();
            linkInput.setSelectionRange(0, 99999); // Для мобильных устройств
            
            // Копирование в буфер обмена
            try {
                const successful = document.execCommand('copy');
                if (successful) {
                    showCopySuccess();
                } else {
                    // Fallback для современных браузеров
                    navigator.clipboard.writeText(linkInput.value).then(() => {
                        showCopySuccess();
                    }).catch(() => {
                        showCopyError();
                    });
                }
            } catch (err) {
                // Fallback для современных браузеров
                if (navigator.clipboard) {
                    navigator.clipboard.writeText(linkInput.value).then(() => {
                        showCopySuccess();
                    }).catch(() => {
                        showCopyError();
                    });
                } else {
                    showCopyError();
                }
            }
        });
    }
}

function showCopySuccess() {
    const copyBtn = document.getElementById('copyLinkBtn');
    const originalText = copyBtn.innerHTML;
    
    copyBtn.innerHTML = '<i class="fas fa-check"></i> Скопировано!';
    copyBtn.style.background = 'var(--success)';
    
    setTimeout(() => {
        copyBtn.innerHTML = originalText;
        copyBtn.style.background = '';
    }, 2000);
    
    showNotification('Реферальная ссылка скопирована в буфер обмена!', 'success');
}

function showCopyError() {
    showNotification('Не удалось скопировать ссылку. Попробуйте выделить и скопировать вручную.', 'error');
}

// Кнопки поделиться
function initShareButtons() {
    const shareButtons = document.querySelectorAll('.share-btn');
    const referralLink = document.getElementById('referralLink').value;
    
    shareButtons.forEach(button => {
        button.addEventListener('click', function() {
            const platform = this.dataset.platform;
            shareToSocial(platform, referralLink);
        });
    });
}

function shareToSocial(platform, link) {
    const shareText = 'Присоединяйтесь к AstroGenix - эко-майнинговой инвестиционной платформе! Зарабатывайте на зеленых технологиях: ';
    let shareUrl = '';
    
    switch (platform) {
        case 'telegram':
            shareUrl = `https://t.me/share/url?url=${encodeURIComponent(link)}&text=${encodeURIComponent(shareText)}`;
            break;
        case 'vk':
            shareUrl = `https://vk.com/share.php?url=${encodeURIComponent(link)}&title=${encodeURIComponent(shareText)}`;
            break;
        case 'whatsapp':
            shareUrl = `https://wa.me/?text=${encodeURIComponent(shareText + link)}`;
            break;
        default:
            return;
    }
    
    // Открытие в новом окне
    const popup = window.open(shareUrl, 'share', 'width=600,height=400,scrollbars=yes,resizable=yes');
    
    // Анимация кнопки
    const button = document.querySelector(`[data-platform="${platform}"]`);
    button.style.transform = 'scale(0.95)';
    setTimeout(() => {
        button.style.transform = 'scale(1)';
    }, 150);
    
    // Закрытие popup через 30 секунд (если пользователь забыл)
    setTimeout(() => {
        if (popup && !popup.closed) {
            popup.close();
        }
    }, 30000);
}

// Переключение уровней рефералов
function initLevelTabs() {
    const levelTabs = document.querySelectorAll('.level-tab');
    const levelContents = document.querySelectorAll('.referral-level-content');
    
    levelTabs.forEach(tab => {
        tab.addEventListener('click', function() {
            const level = this.dataset.level;
            
            // Удаление активного класса у всех вкладок
            levelTabs.forEach(t => t.classList.remove('active'));
            levelContents.forEach(c => {
                c.style.display = 'none';
                c.classList.remove('active');
            });
            
            // Добавление активного класса к текущей вкладке
            this.classList.add('active');
            
            // Показ соответствующего контента
            const targetContent = document.getElementById(`level-${level}`);
            if (targetContent) {
                targetContent.style.display = 'block';
                targetContent.classList.add('active');
                
                // Анимация появления
                setTimeout(() => {
                    targetContent.style.opacity = '0';
                    targetContent.style.transform = 'translateY(20px)';
                    targetContent.style.transition = 'all 0.3s ease';
                    
                    setTimeout(() => {
                        targetContent.style.opacity = '1';
                        targetContent.style.transform = 'translateY(0)';
                    }, 50);
                }, 10);
            }
        });
    });
}

// QR-код
function initQRCode() {
    const qrBtn = document.getElementById('qrCodeBtn');
    const qrModal = document.getElementById('qrModal');
    const qrModalClose = document.getElementById('qrModalClose');
    const referralLink = document.getElementById('referralLink').value;
    
    if (qrBtn && qrModal) {
        qrBtn.addEventListener('click', function() {
            generateQRCode(referralLink);
            showModal(qrModal);
        });
        
        qrModalClose.addEventListener('click', function() {
            hideModal(qrModal);
        });
        
        // Закрытие по клику на фон
        qrModal.addEventListener('click', function(e) {
            if (e.target === qrModal) {
                hideModal(qrModal);
            }
        });
        
        // Закрытие по Escape
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape' && qrModal.classList.contains('show')) {
                hideModal(qrModal);
            }
        });
    }
}

function generateQRCode(text) {
    const qrContainer = document.getElementById('qrcode');
    
    // Очистка предыдущего QR-кода
    qrContainer.innerHTML = '';
    
    // Генерация нового QR-кода
    QRCode.toCanvas(qrContainer, text, {
        width: 256,
        height: 256,
        colorDark: '#2ECC71',
        colorLight: '#FFFFFF',
        margin: 2,
        errorCorrectionLevel: 'M'
    }, function(error) {
        if (error) {
            console.error('QR Code generation error:', error);
            qrContainer.innerHTML = '<p style="color: var(--error);">Ошибка генерации QR-кода</p>';
        }
    });
}

function showModal(modal) {
    modal.classList.add('show');
    document.body.style.overflow = 'hidden';
}

function hideModal(modal) {
    modal.classList.remove('show');
    document.body.style.overflow = '';
}

// Анимации
function initReferralAnimations() {
    // Анимация статистических карточек
    const statCards = document.querySelectorAll('.referral-stats .stat-card');
    statCards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(30px)';
        
        setTimeout(() => {
            card.style.transition = 'all 0.6s ease';
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, index * 150);
    });
    
    // Анимация карточек рефералов
    const referralCards = document.querySelectorAll('.referral-card');
    const cardObserver = new IntersectionObserver((entries) => {
        entries.forEach((entry, index) => {
            if (entry.isIntersecting) {
                setTimeout(() => {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }, index * 100);
                cardObserver.unobserve(entry.target);
            }
        });
    });
    
    referralCards.forEach(card => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(20px)';
        card.style.transition = 'all 0.5s ease';
        cardObserver.observe(card);
    });
    
    // Анимация комиссий
    const commissionItems = document.querySelectorAll('.commission-item');
    commissionItems.forEach((item, index) => {
        item.style.opacity = '0';
        item.style.transform = 'translateX(-20px)';
        
        setTimeout(() => {
            item.style.transition = 'all 0.4s ease';
            item.style.opacity = '1';
            item.style.transform = 'translateX(0)';
        }, index * 100);
    });
    
    // Анимация информационного баннера
    const infoBanner = document.querySelector('.info-banner');
    if (infoBanner) {
        infoBanner.style.opacity = '0';
        infoBanner.style.transform = 'scale(0.95)';
        
        setTimeout(() => {
            infoBanner.style.transition = 'all 0.8s ease';
            infoBanner.style.opacity = '1';
            infoBanner.style.transform = 'scale(1)';
        }, 200);
    }
}

// Утилиты
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
        <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-triangle' : 'info-circle'}"></i>
        <span>${message}</span>
        <button class="notification-close">
            <i class="fas fa-times"></i>
        </button>
    `;
    
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: ${type === 'success' ? '#28a745' : type === 'error' ? '#dc3545' : '#17a2b8'};
        color: white;
        padding: 16px 20px;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        z-index: 9999;
        display: flex;
        align-items: center;
        gap: 12px;
        max-width: 400px;
        transform: translateX(100%);
        transition: transform 0.3s ease;
    `;
    
    document.body.appendChild(notification);
    
    // Анимация появления
    setTimeout(() => {
        notification.style.transform = 'translateX(0)';
    }, 100);
    
    // Обработчик закрытия
    const closeBtn = notification.querySelector('.notification-close');
    closeBtn.addEventListener('click', () => {
        hideNotification(notification);
    });
    
    // Автоматическое скрытие
    setTimeout(() => {
        hideNotification(notification);
    }, 5000);
}

function hideNotification(notification) {
    notification.style.transform = 'translateX(100%)';
    setTimeout(() => {
        if (notification.parentElement) {
            notification.parentElement.removeChild(notification);
        }
    }, 300);
}

// Обновление статистики в реальном времени
function updateReferralStats() {
    // Здесь можно добавить AJAX запрос для обновления статистики
    console.log('Обновление статистики рефералов...');
}

// Автоматическое обновление каждые 5 минут
setInterval(updateReferralStats, 5 * 60 * 1000);

// Добавление дополнительных стилей
const style = document.createElement('style');
style.textContent = `
    .notification-close {
        background: none;
        border: none;
        color: inherit;
        cursor: pointer;
        padding: 4px;
        border-radius: 4px;
        transition: background 0.2s ease;
    }
    
    .notification-close:hover {
        background: rgba(255, 255, 255, 0.2);
    }
    
    .referral-level-content {
        opacity: 1;
        transform: translateY(0);
    }
    
    .modal {
        opacity: 0;
        transition: opacity 0.3s ease;
    }
    
    .modal.show {
        opacity: 1;
    }
    
    .modal-content {
        transform: scale(0.9);
        transition: transform 0.3s ease;
    }
    
    .modal.show .modal-content {
        transform: scale(1);
    }
    
    @keyframes pulse {
        0% { transform: scale(1); }
        50% { transform: scale(1.05); }
        100% { transform: scale(1); }
    }
    
    .copy-success {
        animation: pulse 0.3s ease;
    }
`;
document.head.appendChild(style);
