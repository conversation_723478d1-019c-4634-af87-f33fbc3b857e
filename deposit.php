<?php
/**
 * AstroGenix - Страница пополнения баланса
 * Эко-майнинговая инвестиционная платформа
 */

require_once 'config/config.php';

// Проверка авторизации
if (!is_logged_in()) {
    redirect('login.php');
}

$errors = [];
$success_message = '';

// Обработка формы пополнения
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!verify_csrf_token($_POST['csrf_token'] ?? '')) {
        $errors[] = 'Ошибка безопасности. Попробуйте еще раз.';
    } else {
        $amount = floatval($_POST['amount'] ?? 0);
        $payment_method = sanitize_input($_POST['payment_method'] ?? '');
        $payment_details = sanitize_input($_POST['payment_details'] ?? '');

        // Валидация
        if ($amount <= 0) {
            $errors[] = 'Сумма должна быть больше нуля.';
        } elseif ($amount < 10) {
            $errors[] = 'Минимальная сумма пополнения: 10 USDT.';
        } elseif ($amount > 10000) {
            $errors[] = 'Максимальная сумма пополнения: 10,000 USDT.';
        }

        if (empty($payment_method)) {
            $errors[] = 'Выберите способ оплаты.';
        }

        if (empty($payment_details) && in_array($payment_method, ['bank_card', 'bank_transfer'])) {
            $errors[] = 'Укажите детали платежа.';
        }

        // Создание запроса на пополнение
        if (empty($errors)) {
            try {
                $database = new Database();
                $db = $database->getConnection();
                $transaction = new Transaction($db);

                if ($transaction->createDepositRequest($_SESSION['user_id'], $amount, $payment_method, $payment_details)) {
                    $success_message = 'Запрос на пополнение создан успешно! Ожидайте обработки администратором.';
                    
                    // Очистка формы
                    $amount = $payment_method = $payment_details = '';
                } else {
                    $errors[] = 'Ошибка при создании запроса. Попробуйте еще раз.';
                }
            } catch (Exception $e) {
                $errors[] = 'Ошибка сервера. Попробуйте позже.';
                error_log("Deposit error: " . $e->getMessage());
            }
        }
    }
}

// Получение последних транзакций пополнения
try {
    $database = new Database();
    $db = $database->getConnection();
    $transaction = new Transaction($db);
    $recent_deposits = $transaction->getUserTransactions($_SESSION['user_id'], 10, 0, 'deposit');
} catch (Exception $e) {
    $recent_deposits = [];
    error_log("Error fetching deposits: " . $e->getMessage());
}

$page_title = 'Пополнение баланса - ' . SITE_NAME;
?>
<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?></title>
    
    <!-- CSS -->
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="assets/css/dashboard.css">
    <link rel="stylesheet" href="assets/css/forms.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body class="dashboard-page">
    <!-- Sidebar -->
    <?php include 'includes/sidebar.php'; ?>

    <!-- Main Content -->
    <main class="main-content">
        <!-- Header -->
        <header class="dashboard-header">
            <div class="header-left">
                <button class="mobile-menu-toggle" id="mobileMenuToggle">
                    <i class="fas fa-bars"></i>
                </button>
                <h1>Пополнение баланса</h1>
            </div>
            <div class="header-right">
                <div class="user-balance">
                    <span class="balance-label">Текущий баланс:</span>
                    <span class="balance-amount"><?php echo format_currency($_SESSION['balance'] ?? 0); ?></span>
                </div>
            </div>
        </header>

        <!-- Content -->
        <div class="dashboard-content">
            <?php if (!empty($errors)): ?>
                <div class="alert alert-error">
                    <i class="fas fa-exclamation-triangle"></i>
                    <ul>
                        <?php foreach ($errors as $error): ?>
                            <li><?php echo htmlspecialchars($error); ?></li>
                        <?php endforeach; ?>
                    </ul>
                </div>
            <?php endif; ?>

            <?php if (!empty($success_message)): ?>
                <div class="alert alert-success">
                    <i class="fas fa-check-circle"></i>
                    <?php echo htmlspecialchars($success_message); ?>
                </div>
            <?php endif; ?>

            <div class="deposit-container">
                <!-- Deposit Form -->
                <div class="deposit-form-section">
                    <div class="card">
                        <div class="card-header">
                            <h3><i class="fas fa-plus-circle"></i> Создать запрос на пополнение</h3>
                            <p>Заполните форму для создания запроса на пополнение баланса</p>
                        </div>
                        <div class="card-body">
                            <form class="deposit-form" method="POST" action="">
                                <input type="hidden" name="csrf_token" value="<?php echo generate_csrf_token(); ?>">
                                
                                <div class="form-group">
                                    <label for="amount">Сумма пополнения (USDT)</label>
                                    <div class="amount-input-group">
                                        <input type="number" id="amount" name="amount"
                                               value="<?php echo htmlspecialchars($amount ?? ''); ?>"
                                               min="10" max="10000" step="0.01" required>
                                        <div class="amount-buttons">
                                            <button type="button" class="amount-btn" data-amount="100">100 USDT</button>
                                            <button type="button" class="amount-btn" data-amount="500">500 USDT</button>
                                            <button type="button" class="amount-btn" data-amount="1000">1,000 USDT</button>
                                            <button type="button" class="amount-btn" data-amount="2500">2,500 USDT</button>
                                        </div>
                                    </div>
                                    <div class="amount-info">
                                        <small>Минимум: 10 USDT | Максимум: 10,000 USDT</small>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label for="payment_method">Способ оплаты</label>
                                    <div class="payment-methods">
                                        <label class="payment-method-option">
                                            <input type="radio" name="payment_method" value="bank_card" required>
                                            <div class="payment-method-card">
                                                <i class="fas fa-credit-card"></i>
                                                <span>Банковская карта</span>
                                                <small>Visa, MasterCard, МИР</small>
                                            </div>
                                        </label>
                                        
                                        <label class="payment-method-option">
                                            <input type="radio" name="payment_method" value="bank_transfer" required>
                                            <div class="payment-method-card">
                                                <i class="fas fa-university"></i>
                                                <span>Банковский перевод</span>
                                                <small>Через банк или онлайн</small>
                                            </div>
                                        </label>
                                        
                                        <label class="payment-method-option">
                                            <input type="radio" name="payment_method" value="qiwi" required>
                                            <div class="payment-method-card">
                                                <i class="fas fa-wallet"></i>
                                                <span>QIWI Кошелек</span>
                                                <small>Быстрый перевод</small>
                                            </div>
                                        </label>
                                        
                                        <label class="payment-method-option">
                                            <input type="radio" name="payment_method" value="yandex_money" required>
                                            <div class="payment-method-card">
                                                <i class="fas fa-ruble-sign"></i>
                                                <span>ЮMoney</span>
                                                <small>Яндекс.Деньги</small>
                                            </div>
                                        </label>
                                        
                                        <label class="payment-method-option">
                                            <input type="radio" name="payment_method" value="crypto" required>
                                            <div class="payment-method-card">
                                                <i class="fab fa-bitcoin"></i>
                                                <span>Криптовалюта</span>
                                                <small>Bitcoin, Ethereum</small>
                                            </div>
                                        </label>
                                        
                                        <label class="payment-method-option">
                                            <input type="radio" name="payment_method" value="sbp" required>
                                            <div class="payment-method-card">
                                                <i class="fas fa-mobile-alt"></i>
                                                <span>СБП</span>
                                                <small>Система быстрых платежей</small>
                                            </div>
                                        </label>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label for="payment_details">Детали платежа</label>
                                    <textarea id="payment_details" name="payment_details" rows="3" 
                                              placeholder="Укажите дополнительную информацию для обработки платежа (номер карты, кошелька и т.д.)"><?php echo htmlspecialchars($payment_details ?? ''); ?></textarea>
                                    <small class="form-help">
                                        <i class="fas fa-info-circle"></i>
                                        Эта информация поможет администратору быстрее обработать ваш запрос
                                    </small>
                                </div>

                                <div class="deposit-summary">
                                    <div class="summary-row">
                                        <span>Сумма к пополнению:</span>
                                        <span class="summary-amount" id="summaryAmount">0 USDT</span>
                                    </div>
                                    <div class="summary-row">
                                        <span>Комиссия:</span>
                                        <span class="summary-fee">0 USDT</span>
                                    </div>
                                    <div class="summary-row total">
                                        <span>Итого к оплате:</span>
                                        <span class="summary-total" id="summaryTotal">0 USDT</span>
                                    </div>
                                </div>

                                <button type="submit" class="btn btn-primary btn-full btn-large">
                                    <i class="fas fa-plus-circle"></i>
                                    Создать запрос на пополнение
                                </button>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- Recent Deposits -->
                <div class="recent-deposits-section">
                    <div class="card">
                        <div class="card-header">
                            <h3><i class="fas fa-history"></i> История пополнений</h3>
                        </div>
                        <div class="card-body">
                            <?php if (empty($recent_deposits)): ?>
                                <div class="empty-state">
                                    <i class="fas fa-inbox"></i>
                                    <p>У вас пока нет запросов на пополнение</p>
                                </div>
                            <?php else: ?>
                                <div class="deposits-list">
                                    <?php foreach ($recent_deposits as $deposit): ?>
                                        <div class="deposit-item">
                                            <div class="deposit-info">
                                                <div class="deposit-amount">
                                                    <?php echo format_currency($deposit['amount']); ?>
                                                </div>
                                                <div class="deposit-date">
                                                    <?php echo date('d.m.Y H:i', strtotime($deposit['created_at'])); ?>
                                                </div>
                                                <?php if (!empty($deposit['description'])): ?>
                                                    <div class="deposit-description">
                                                        <?php echo htmlspecialchars($deposit['description']); ?>
                                                    </div>
                                                <?php endif; ?>
                                            </div>
                                            <div class="deposit-status">
                                                <span class="status-badge status-<?php echo $deposit['status']; ?>">
                                                    <?php 
                                                    $statuses = [
                                                        'pending' => 'Ожидание',
                                                        'approved' => 'Одобрено',
                                                        'rejected' => 'Отклонено',
                                                        'completed' => 'Завершено'
                                                    ];
                                                    echo $statuses[$deposit['status']] ?? $deposit['status'];
                                                    ?>
                                                </span>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                                <div class="card-footer">
                                    <a href="transactions.php?type=deposit" class="btn btn-outline">
                                        Посмотреть все пополнения
                                    </a>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- JavaScript -->
    <script src="assets/js/dashboard.js"></script>
    <script src="assets/js/deposit.js"></script>
</body>
</html>
