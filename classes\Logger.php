<?php
/**
 * AstroGenix - Класс для работы с логами
 * Эко-майнинговая инвестиционная платформа
 */

class Logger {
    private $db;
    
    public function __construct($database) {
        $this->db = $database;
    }
    
    /**
     * Запись лога в базу данных
     */
    public function log($action, $description = null, $user_id = null, $additional_data = null) {
        try {
            // Получение информации о пользователе и запросе
            $user_id = $user_id ?? ($_SESSION['user_id'] ?? null);
            $ip_address = $this->getClientIP();
            $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? null;
            
            // Подготовка дополнительных данных
            if ($additional_data && !is_string($additional_data)) {
                $additional_data = json_encode($additional_data, JSON_UNESCAPED_UNICODE);
            }
            
            $query = "INSERT INTO system_logs (user_id, action, description, ip_address, user_agent, additional_data, created_at) 
                      VALUES (:user_id, :action, :description, :ip_address, :user_agent, :additional_data, NOW())";
            
            $stmt = $this->db->prepare($query);
            $stmt->bindParam(':user_id', $user_id, PDO::PARAM_INT);
            $stmt->bindParam(':action', $action);
            $stmt->bindParam(':description', $description);
            $stmt->bindParam(':ip_address', $ip_address);
            $stmt->bindParam(':user_agent', $user_agent);
            $stmt->bindParam(':additional_data', $additional_data);
            
            return $stmt->execute();
        } catch (Exception $e) {
            error_log("Logger error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Логирование входа пользователя
     */
    public function logLogin($user_id, $success = true, $additional_info = null) {
        $action = $success ? 'user_login_success' : 'user_login_failed';
        $description = $success ? 'Успешный вход в систему' : 'Неудачная попытка входа';
        
        return $this->log($action, $description, $user_id, $additional_info);
    }
    
    /**
     * Логирование выхода пользователя
     */
    public function logLogout($user_id) {
        return $this->log('user_logout', 'Выход из системы', $user_id);
    }
    
    /**
     * Логирование регистрации пользователя
     */
    public function logRegistration($user_id, $email) {
        return $this->log('user_registration', "Регистрация нового пользователя: $email", $user_id);
    }
    
    /**
     * Логирование транзакций
     */
    public function logTransaction($user_id, $transaction_type, $amount, $transaction_id = null) {
        $description = "Транзакция: $transaction_type на сумму " . format_currency($amount);
        $additional_data = [
            'transaction_id' => $transaction_id,
            'type' => $transaction_type,
            'amount' => $amount
        ];
        
        return $this->log('transaction_' . $transaction_type, $description, $user_id, $additional_data);
    }
    
    /**
     * Логирование инвестиций
     */
    public function logInvestment($user_id, $package_name, $amount, $investment_id = null) {
        $description = "Создана инвестиция в пакет '$package_name' на сумму " . format_currency($amount);
        $additional_data = [
            'investment_id' => $investment_id,
            'package_name' => $package_name,
            'amount' => $amount
        ];
        
        return $this->log('investment_created', $description, $user_id, $additional_data);
    }
    
    /**
     * Логирование административных действий
     */
    public function logAdminAction($action, $description, $target_user_id = null, $additional_data = null) {
        $admin_id = $_SESSION['user_id'] ?? null;
        
        if ($target_user_id) {
            $additional_data = array_merge($additional_data ?? [], ['target_user_id' => $target_user_id]);
        }
        
        return $this->log('admin_' . $action, $description, $admin_id, $additional_data);
    }
    
    /**
     * Логирование ошибок безопасности
     */
    public function logSecurityEvent($event_type, $description, $severity = 'medium') {
        $additional_data = [
            'severity' => $severity,
            'event_type' => $event_type,
            'request_uri' => $_SERVER['REQUEST_URI'] ?? null,
            'referer' => $_SERVER['HTTP_REFERER'] ?? null
        ];
        
        return $this->log('security_' . $event_type, $description, null, $additional_data);
    }
    
    /**
     * Получение логов с фильтрацией
     */
    public function getLogs($filters = [], $limit = 50, $offset = 0) {
        try {
            $where_conditions = [];
            $params = [];
            
            // Фильтр по пользователю
            if (!empty($filters['user_id'])) {
                $where_conditions[] = "sl.user_id = :user_id";
                $params[':user_id'] = $filters['user_id'];
            }
            
            // Фильтр по действию
            if (!empty($filters['action'])) {
                $where_conditions[] = "sl.action LIKE :action";
                $params[':action'] = '%' . $filters['action'] . '%';
            }
            
            // Фильтр по IP
            if (!empty($filters['ip_address'])) {
                $where_conditions[] = "sl.ip_address = :ip_address";
                $params[':ip_address'] = $filters['ip_address'];
            }
            
            // Фильтр по дате
            if (!empty($filters['date_from'])) {
                $where_conditions[] = "sl.created_at >= :date_from";
                $params[':date_from'] = $filters['date_from'];
            }
            
            if (!empty($filters['date_to'])) {
                $where_conditions[] = "sl.created_at <= :date_to";
                $params[':date_to'] = $filters['date_to'] . ' 23:59:59';
            }
            
            $where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';
            
            $query = "SELECT sl.*, u.username, u.email 
                      FROM system_logs sl 
                      LEFT JOIN users u ON sl.user_id = u.id 
                      $where_clause 
                      ORDER BY sl.created_at DESC 
                      LIMIT :limit OFFSET :offset";
            
            $stmt = $this->db->prepare($query);
            
            foreach ($params as $key => $value) {
                $stmt->bindValue($key, $value);
            }
            
            $stmt->bindValue(':limit', $limit, PDO::PARAM_INT);
            $stmt->bindValue(':offset', $offset, PDO::PARAM_INT);
            $stmt->execute();
            
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (Exception $e) {
            error_log("Get logs error: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Получение количества логов
     */
    public function getLogsCount($filters = []) {
        try {
            $where_conditions = [];
            $params = [];
            
            // Применение тех же фильтров, что и в getLogs
            if (!empty($filters['user_id'])) {
                $where_conditions[] = "user_id = :user_id";
                $params[':user_id'] = $filters['user_id'];
            }
            
            if (!empty($filters['action'])) {
                $where_conditions[] = "action LIKE :action";
                $params[':action'] = '%' . $filters['action'] . '%';
            }
            
            if (!empty($filters['ip_address'])) {
                $where_conditions[] = "ip_address = :ip_address";
                $params[':ip_address'] = $filters['ip_address'];
            }
            
            if (!empty($filters['date_from'])) {
                $where_conditions[] = "created_at >= :date_from";
                $params[':date_from'] = $filters['date_from'];
            }
            
            if (!empty($filters['date_to'])) {
                $where_conditions[] = "created_at <= :date_to";
                $params[':date_to'] = $filters['date_to'] . ' 23:59:59';
            }
            
            $where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';
            
            $query = "SELECT COUNT(*) as count FROM system_logs $where_clause";
            
            $stmt = $this->db->prepare($query);
            foreach ($params as $key => $value) {
                $stmt->bindValue($key, $value);
            }
            $stmt->execute();
            
            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            return $result['count'] ?? 0;
        } catch (Exception $e) {
            error_log("Get logs count error: " . $e->getMessage());
            return 0;
        }
    }
    
    /**
     * Очистка старых логов
     */
    public function cleanupOldLogs($days = 90) {
        try {
            $query = "DELETE FROM system_logs 
                      WHERE created_at < DATE_SUB(NOW(), INTERVAL :days DAY)";
            
            $stmt = $this->db->prepare($query);
            $stmt->bindParam(':days', $days, PDO::PARAM_INT);
            
            return $stmt->execute();
        } catch (Exception $e) {
            error_log("Cleanup logs error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Получение статистики логов
     */
    public function getLogsStatistics($days = 30) {
        try {
            $query = "SELECT 
                        action,
                        COUNT(*) as count,
                        DATE(created_at) as log_date
                      FROM system_logs 
                      WHERE created_at >= DATE_SUB(NOW(), INTERVAL :days DAY)
                      GROUP BY action, DATE(created_at)
                      ORDER BY log_date DESC, count DESC";
            
            $stmt = $this->db->prepare($query);
            $stmt->bindParam(':days', $days, PDO::PARAM_INT);
            $stmt->execute();
            
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (Exception $e) {
            error_log("Get logs statistics error: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Получение IP-адреса клиента
     */
    private function getClientIP() {
        $ip_keys = ['HTTP_X_FORWARDED_FOR', 'HTTP_X_REAL_IP', 'HTTP_CLIENT_IP', 'REMOTE_ADDR'];
        
        foreach ($ip_keys as $key) {
            if (!empty($_SERVER[$key])) {
                $ip = $_SERVER[$key];
                // Если есть несколько IP (через запятую), берем первый
                if (strpos($ip, ',') !== false) {
                    $ip = trim(explode(',', $ip)[0]);
                }
                // Проверяем, что это валидный IP
                if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                    return $ip;
                }
            }
        }
        
        return $_SERVER['REMOTE_ADDR'] ?? 'unknown';
    }
}
?>
