<?php
/**
 * AstroGenix - Страница пополнения баланса
 * Эко-майнинговая инвестиционная платформа
 */

require_once 'config/config.php';

// Проверка авторизации
if (!is_logged_in()) {
    redirect('login.php');
}

$errors = [];
$success_message = '';

// Обработка формы пополнения
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!verify_csrf_token($_POST['csrf_token'] ?? '')) {
        $errors[] = 'Ошибка безопасности. Попробуйте еще раз.';
    } else {
        $amount = floatval($_POST['amount'] ?? 0);
        $payment_method = sanitize_input($_POST['payment_method'] ?? '');
        $payment_details = sanitize_input($_POST['payment_details'] ?? '');

        // Валидация
        if ($amount <= 0) {
            $errors[] = 'Сумма должна быть больше нуля.';
        } elseif ($amount < 10) {
            $errors[] = 'Минимальная сумма пополнения: 10 USDT.';
        } elseif ($amount > 10000) {
            $errors[] = 'Максимальная сумма пополнения: 10,000 USDT.';
        }

        if (empty($payment_method)) {
            $errors[] = 'Выберите способ оплаты.';
        }

        if (empty($payment_details) && in_array($payment_method, ['bank_card', 'bank_transfer'])) {
            $errors[] = 'Укажите детали платежа.';
        }

        // Создание запроса на пополнение
        if (empty($errors)) {
            try {
                $database = new Database();
                $db = $database->getConnection();
                $transaction = new Transaction($db);

                if ($transaction->createDepositRequest($_SESSION['user_id'], $amount, $payment_method, $payment_details)) {
                    $success_message = 'Запрос на пополнение создан успешно! Ожидайте обработки администратором.';
                    
                    // Очистка формы
                    $amount = $payment_method = $payment_details = '';
                } else {
                    $errors[] = 'Ошибка при создании запроса. Попробуйте еще раз.';
                }
            } catch (Exception $e) {
                $errors[] = 'Ошибка сервера. Попробуйте позже.';
                error_log("Deposit error: " . $e->getMessage());
            }
        }
    }
}

// Получение последних транзакций пополнения
try {
    $database = new Database();
    $db = $database->getConnection();
    $transaction = new Transaction($db);
    $recent_deposits = $transaction->getUserTransactions($_SESSION['user_id'], 10, 0, 'deposit');
} catch (Exception $e) {
    $recent_deposits = [];
    error_log("Error fetching deposits: " . $e->getMessage());
}

$page_title = 'Пополнение баланса - ' . SITE_NAME;
?>
<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?></title>
    
    <!-- CSS -->
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="assets/css/dashboard.css">
    <link rel="stylesheet" href="assets/css/forms.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body class="dashboard-page">
    <!-- Sidebar -->
    <?php include 'includes/sidebar.php'; ?>

    <!-- Main Content -->
    <main class="main-content">
        <!-- Header -->
        <header class="dashboard-header">
            <div class="header-left">
                <button class="mobile-menu-toggle" id="mobileMenuToggle">
                    <i class="fas fa-bars"></i>
                </button>
                <h1>Пополнение баланса</h1>
            </div>
            <div class="header-right">
                <div class="user-balance">
                    <span class="balance-label">Текущий баланс:</span>
                    <span class="balance-amount"><?php echo format_currency($_SESSION['balance'] ?? 0); ?></span>
                </div>
            </div>
        </header>

        <!-- Content -->
        <div class="dashboard-content">
            <?php if (!empty($errors)): ?>
                <div class="alert alert-error">
                    <i class="fas fa-exclamation-triangle"></i>
                    <ul>
                        <?php foreach ($errors as $error): ?>
                            <li><?php echo htmlspecialchars($error); ?></li>
                        <?php endforeach; ?>
                    </ul>
                </div>
            <?php endif; ?>

            <?php if (!empty($success_message)): ?>
                <div class="alert alert-success">
                    <i class="fas fa-check-circle"></i>
                    <?php echo htmlspecialchars($success_message); ?>
                </div>
            <?php endif; ?>

            <div class="deposit-container">
                <!-- Deposit Form -->
                <div class="deposit-form-section">
                    <div class="card">
                        <div class="card-header">
                            <h3><i class="fas fa-plus-circle"></i> Создать запрос на пополнение</h3>
                            <p>Заполните форму для создания запроса на пополнение баланса</p>
                        </div>
                        <div class="card-body">
                            <form class="deposit-form" method="POST" action="">
                                <input type="hidden" name="csrf_token" value="<?php echo generate_csrf_token(); ?>">
                                
                                <div class="form-group">
                                    <label for="amount">Сумма пополнения (USDT)</label>
                                    <div class="amount-input-group">
                                        <input type="number" id="amount" name="amount"
                                               value="<?php echo htmlspecialchars($amount ?? ''); ?>"
                                               min="10" max="10000" step="0.01" required>
                                        <div class="amount-buttons">
                                            <button type="button" class="amount-btn" data-amount="100">100 USDT</button>
                                            <button type="button" class="amount-btn" data-amount="500">500 USDT</button>
                                            <button type="button" class="amount-btn" data-amount="1000">1,000 USDT</button>
                                            <button type="button" class="amount-btn" data-amount="2500">2,500 USDT</button>
                                        </div>
                                    </div>
                                    <div class="amount-info">
                                        <small>Минимум: 10 USDT | Максимум: 10,000 USDT</small>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label for="payment_method">Способ оплаты</label>
                                    <div class="payment-methods crypto-methods">
                                        <label class="payment-method-option crypto-option">
                                            <input type="radio" name="payment_method" value="usdt_trc20" required>
                                            <div class="payment-method-card">
                                                <div class="crypto-icon">
                                                    <img src="assets/images/crypto/usdt.png" alt="USDT" onerror="this.style.display='none'; this.nextElementSibling.style.display='inline-block';">
                                                    <i class="fas fa-coins" style="display: none;"></i>
                                                </div>
                                                <div class="crypto-info">
                                                    <span class="crypto-name">USDT (TRC20)</span>
                                                    <small class="crypto-network">Tron Network</small>
                                                    <div class="crypto-features">
                                                        <span class="feature-badge">Быстро</span>
                                                        <span class="feature-badge">Низкие комиссии</span>
                                                    </div>
                                                </div>
                                            </div>
                                        </label>

                                        <label class="payment-method-option crypto-option">
                                            <input type="radio" name="payment_method" value="usdt_erc20" required>
                                            <div class="payment-method-card">
                                                <div class="crypto-icon">
                                                    <img src="assets/images/crypto/usdt.png" alt="USDT" onerror="this.style.display='none'; this.nextElementSibling.style.display='inline-block';">
                                                    <i class="fas fa-coins" style="display: none;"></i>
                                                </div>
                                                <div class="crypto-info">
                                                    <span class="crypto-name">USDT (ERC20)</span>
                                                    <small class="crypto-network">Ethereum Network</small>
                                                    <div class="crypto-features">
                                                        <span class="feature-badge">Надежно</span>
                                                        <span class="feature-badge">Популярно</span>
                                                    </div>
                                                </div>
                                            </div>
                                        </label>

                                        <label class="payment-method-option crypto-option">
                                            <input type="radio" name="payment_method" value="bitcoin" required>
                                            <div class="payment-method-card">
                                                <div class="crypto-icon">
                                                    <img src="assets/images/crypto/btc.png" alt="Bitcoin" onerror="this.style.display='none'; this.nextElementSibling.style.display='inline-block';">
                                                    <i class="fab fa-bitcoin" style="display: none;"></i>
                                                </div>
                                                <div class="crypto-info">
                                                    <span class="crypto-name">Bitcoin (BTC)</span>
                                                    <small class="crypto-network">Bitcoin Network</small>
                                                    <div class="crypto-features">
                                                        <span class="feature-badge">Классика</span>
                                                        <span class="feature-badge">Безопасно</span>
                                                    </div>
                                                </div>
                                            </div>
                                        </label>

                                        <label class="payment-method-option crypto-option">
                                            <input type="radio" name="payment_method" value="ethereum" required>
                                            <div class="payment-method-card">
                                                <div class="crypto-icon">
                                                    <img src="assets/images/crypto/eth.png" alt="Ethereum" onerror="this.style.display='none'; this.nextElementSibling.style.display='inline-block';">
                                                    <i class="fab fa-ethereum" style="display: none;"></i>
                                                </div>
                                                <div class="crypto-info">
                                                    <span class="crypto-name">Ethereum (ETH)</span>
                                                    <small class="crypto-network">Ethereum Network</small>
                                                    <div class="crypto-features">
                                                        <span class="feature-badge">Смарт-контракты</span>
                                                        <span class="feature-badge">DeFi</span>
                                                    </div>
                                                </div>
                                            </div>
                                        </label>

                                        <label class="payment-method-option crypto-option">
                                            <input type="radio" name="payment_method" value="litecoin" required>
                                            <div class="payment-method-card">
                                                <div class="crypto-icon">
                                                    <img src="assets/images/crypto/ltc.png" alt="Litecoin" onerror="this.style.display='none'; this.nextElementSibling.style.display='inline-block';">
                                                    <i class="fas fa-coins" style="display: none;"></i>
                                                </div>
                                                <div class="crypto-info">
                                                    <span class="crypto-name">Litecoin (LTC)</span>
                                                    <small class="crypto-network">Litecoin Network</small>
                                                    <div class="crypto-features">
                                                        <span class="feature-badge">Быстро</span>
                                                        <span class="feature-badge">Дешево</span>
                                                    </div>
                                                </div>
                                            </div>
                                        </label>

                                        <label class="payment-method-option crypto-option">
                                            <input type="radio" name="payment_method" value="binance_coin" required>
                                            <div class="payment-method-card">
                                                <div class="crypto-icon">
                                                    <img src="assets/images/crypto/bnb.png" alt="BNB" onerror="this.style.display='none'; this.nextElementSibling.style.display='inline-block';">
                                                    <i class="fas fa-coins" style="display: none;"></i>
                                                </div>
                                                <div class="crypto-info">
                                                    <span class="crypto-name">BNB</span>
                                                    <small class="crypto-network">Binance Smart Chain</small>
                                                    <div class="crypto-features">
                                                        <span class="feature-badge">BSC</span>
                                                        <span class="feature-badge">Низкие комиссии</span>
                                                    </div>
                                                </div>
                                            </div>
                                        </label>
                                    </div>
                                </div>

                                <!-- Crypto Wallet Address Section -->
                                <div class="crypto-wallet-section" id="cryptoWalletSection" style="display: none;">
                                    <div class="wallet-info-card">
                                        <div class="wallet-header">
                                            <div class="crypto-selected-icon">
                                                <img id="selectedCryptoIcon" src="" alt="" style="display: none;">
                                                <i id="selectedCryptoIconFallback" class="fas fa-coins" style="display: none;"></i>
                                            </div>
                                            <div class="wallet-title">
                                                <h4 id="selectedCryptoName">Адрес кошелька</h4>
                                                <small id="selectedCryptoNetwork">Сеть</small>
                                            </div>
                                        </div>

                                        <div class="wallet-address-container">
                                            <div class="qr-code-section">
                                                <div class="qr-code-placeholder" id="qrCodeContainer">
                                                    <i class="fas fa-qrcode"></i>
                                                    <span>QR-код будет сгенерирован</span>
                                                </div>
                                            </div>

                                            <div class="address-section">
                                                <label for="walletAddress">Адрес кошелька:</label>
                                                <div class="address-input-group">
                                                    <input type="text" id="walletAddress" readonly
                                                           placeholder="Выберите криптовалюту для получения адреса">
                                                    <button type="button" class="copy-btn" id="copyAddressBtn" onclick="copyAddress()">
                                                        <i class="fas fa-copy"></i>
                                                    </button>
                                                </div>
                                                <small class="address-warning">
                                                    <i class="fas fa-exclamation-triangle"></i>
                                                    Отправляйте только выбранную криптовалюту на этот адрес!
                                                </small>
                                            </div>
                                        </div>

                                        <div class="payment-instructions">
                                            <h5><i class="fas fa-info-circle"></i> Инструкция по оплате:</h5>
                                            <ol id="paymentInstructions">
                                                <li>Скопируйте адрес кошелька или отсканируйте QR-код</li>
                                                <li>Отправьте точную сумму на указанный адрес</li>
                                                <li>Дождитесь подтверждения транзакции в сети</li>
                                                <li>Средства будут зачислены автоматически после подтверждения</li>
                                            </ol>

                                            <div class="network-info" id="networkInfo">
                                                <div class="info-item">
                                                    <span class="info-label">Минимальных подтверждений:</span>
                                                    <span class="info-value" id="minConfirmations">1</span>
                                                </div>
                                                <div class="info-item">
                                                    <span class="info-label">Время зачисления:</span>
                                                    <span class="info-value" id="depositTime">5-30 минут</span>
                                                </div>
                                                <div class="info-item">
                                                    <span class="info-label">Комиссия сети:</span>
                                                    <span class="info-value" id="networkFee">Оплачивается отправителем</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label for="payment_details">Хэш транзакции (необязательно)</label>
                                    <textarea id="payment_details" name="payment_details" rows="2"
                                              placeholder="Вставьте хэш транзакции после отправки средств для ускорения обработки"><?php echo htmlspecialchars($payment_details ?? ''); ?></textarea>
                                    <small class="form-help">
                                        <i class="fas fa-info-circle"></i>
                                        Хэш транзакции поможет быстрее найти ваш платеж в блокчейне
                                    </small>
                                </div>

                                <div class="deposit-summary">
                                    <div class="summary-row">
                                        <span>Сумма к пополнению:</span>
                                        <span class="summary-amount" id="summaryAmount">0 USDT</span>
                                    </div>
                                    <div class="summary-row">
                                        <span>Комиссия:</span>
                                        <span class="summary-fee">0 USDT</span>
                                    </div>
                                    <div class="summary-row total">
                                        <span>Итого к оплате:</span>
                                        <span class="summary-total" id="summaryTotal">0 USDT</span>
                                    </div>
                                </div>

                                <button type="submit" class="btn btn-primary btn-full btn-large">
                                    <i class="fas fa-plus-circle"></i>
                                    Создать запрос на пополнение
                                </button>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- Recent Deposits -->
                <div class="recent-deposits-section">
                    <div class="card">
                        <div class="card-header">
                            <h3><i class="fas fa-history"></i> История пополнений</h3>
                        </div>
                        <div class="card-body">
                            <?php if (empty($recent_deposits)): ?>
                                <div class="empty-state">
                                    <i class="fas fa-inbox"></i>
                                    <p>У вас пока нет запросов на пополнение</p>
                                </div>
                            <?php else: ?>
                                <div class="deposits-list">
                                    <?php foreach ($recent_deposits as $deposit): ?>
                                        <div class="deposit-item">
                                            <div class="deposit-info">
                                                <div class="deposit-amount">
                                                    <?php echo format_currency($deposit['amount']); ?>
                                                </div>
                                                <div class="deposit-date">
                                                    <?php echo date('d.m.Y H:i', strtotime($deposit['created_at'])); ?>
                                                </div>
                                                <?php if (!empty($deposit['description'])): ?>
                                                    <div class="deposit-description">
                                                        <?php echo htmlspecialchars($deposit['description']); ?>
                                                    </div>
                                                <?php endif; ?>
                                            </div>
                                            <div class="deposit-status">
                                                <span class="status-badge status-<?php echo $deposit['status']; ?>">
                                                    <?php 
                                                    $statuses = [
                                                        'pending' => 'Ожидание',
                                                        'approved' => 'Одобрено',
                                                        'rejected' => 'Отклонено',
                                                        'completed' => 'Завершено'
                                                    ];
                                                    echo $statuses[$deposit['status']] ?? $deposit['status'];
                                                    ?>
                                                </span>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                                <div class="card-footer">
                                    <a href="transactions.php?type=deposit" class="btn btn-outline">
                                        Посмотреть все пополнения
                                    </a>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/qrcode@1.5.3/build/qrcode.min.js"></script>
    <script src="assets/js/dashboard.js"></script>
    <script src="assets/js/deposit.js"></script>
</body>
</html>
