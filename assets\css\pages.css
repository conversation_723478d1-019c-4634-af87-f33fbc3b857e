/**
 * AstroGenix - Стили для страниц контента
 * Эко-майнинговая инвестиционная платформа
 */

/* Hero секции для разных страниц */
.features-hero,
.packages-hero,
.about-hero,
.contact-hero,
.support-hero,
.faq-hero,
.terms-hero,
.privacy-hero,
.help-hero {
    background: linear-gradient(135deg, var(--primary-green) 0%, var(--primary-purple) 100%);
    color: white;
    padding: var(--space-20) 0 var(--space-16);
    position: relative;
    overflow: hidden;
}

.features-hero::before,
.packages-hero::before,
.about-hero::before,
.contact-hero::before,
.support-hero::before,
.faq-hero::before,
.terms-hero::before,
.privacy-hero::before,
.help-hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('../images/pattern.svg') repeat;
    opacity: 0.1;
    z-index: 1;
}

.hero-content {
    position: relative;
    z-index: 2;
    text-align: center;
}

/* Мета-информация для документов */
.terms-meta,
.privacy-meta {
    margin-top: var(--space-6);
    padding: var(--space-4);
    background: rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-md);
    backdrop-filter: blur(10px);
}

.terms-meta p,
.privacy-meta p {
    margin: 0;
    font-size: var(--text-sm);
    opacity: 0.9;
}

/* Тарифные планы */
.packages-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: var(--space-8);
    margin-top: var(--space-12);
}

.package-card {
    background: white;
    border-radius: var(--radius-lg);
    padding: var(--space-8);
    box-shadow: var(--shadow-lg);
    border: 2px solid transparent;
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.package-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-xl);
    border-color: var(--primary-green);
}

.package-popular {
    border-color: var(--primary-green);
    transform: scale(1.05);
}

.package-badge {
    position: absolute;
    top: var(--space-4);
    right: var(--space-4);
    background: var(--primary-green);
    color: white;
    padding: var(--space-1) var(--space-3);
    border-radius: var(--radius-full);
    font-size: var(--text-xs);
    font-weight: 600;
    text-transform: uppercase;
}

.package-header {
    text-align: center;
    margin-bottom: var(--space-6);
}

.package-name {
    font-size: var(--text-xl);
    font-weight: 700;
    color: var(--gray-900);
    margin-bottom: var(--space-2);
}

.package-price {
    display: flex;
    align-items: baseline;
    justify-content: center;
    gap: var(--space-1);
    margin-bottom: var(--space-1);
}

.price-amount {
    font-size: 3rem;
    font-weight: 800;
    color: var(--primary-green);
    line-height: 1;
}

.price-currency {
    font-size: var(--text-lg);
    font-weight: 600;
    color: var(--gray-600);
}

.package-period {
    font-size: var(--text-sm);
    color: var(--gray-500);
}

.package-profit {
    text-align: center;
    margin-bottom: var(--space-6);
    padding: var(--space-4);
    background: var(--green-50);
    border-radius: var(--radius-md);
}

.profit-rate {
    display: block;
    font-size: var(--text-2xl);
    font-weight: 800;
    color: var(--primary-green);
    line-height: 1;
}

.profit-period {
    font-size: var(--text-sm);
    color: var(--gray-600);
    margin-top: var(--space-1);
}

.package-features {
    margin-bottom: var(--space-8);
}

.feature-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.feature-list li {
    display: flex;
    align-items: center;
    gap: var(--space-3);
    padding: var(--space-2) 0;
    border-bottom: 1px solid var(--gray-100);
}

.feature-list li:last-child {
    border-bottom: none;
}

.feature-list i {
    color: var(--primary-green);
    font-size: var(--text-sm);
    width: 16px;
    text-align: center;
}

.package-footer {
    text-align: center;
}

/* Калькулятор прибыли */
.calculator-container {
    background: white;
    border-radius: var(--radius-lg);
    padding: var(--space-8);
    box-shadow: var(--shadow-lg);
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--space-8);
    align-items: start;
}

.calculator-form .form-group {
    margin-bottom: var(--space-6);
}

.calculator-form label {
    display: block;
    font-weight: 600;
    color: var(--gray-700);
    margin-bottom: var(--space-2);
}

.calculator-form .form-control {
    width: 100%;
    padding: var(--space-3);
    border: 2px solid var(--gray-200);
    border-radius: var(--radius-md);
    font-size: var(--text-base);
    transition: border-color var(--transition-fast);
}

.calculator-form .form-control:focus {
    outline: none;
    border-color: var(--primary-green);
}

.calculator-results {
    display: flex;
    flex-direction: column;
    gap: var(--space-4);
}

.result-item {
    padding: var(--space-4);
    background: var(--gray-50);
    border-radius: var(--radius-md);
    text-align: center;
}

.result-label {
    font-size: var(--text-sm);
    color: var(--gray-600);
    margin-bottom: var(--space-1);
}

.result-value {
    font-size: var(--text-xl);
    font-weight: 700;
    color: var(--gray-900);
}

.result-value.highlight {
    font-size: var(--text-2xl);
    color: var(--primary-green);
}

/* Условия и гарантии */
.conditions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--space-6);
    margin-top: var(--space-12);
}

.condition-item {
    text-align: center;
    padding: var(--space-6);
    background: white;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
    transition: transform var(--transition-normal);
}

.condition-item:hover {
    transform: translateY(-4px);
}

.condition-icon {
    width: 64px;
    height: 64px;
    background: var(--green-100);
    border-radius: var(--radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto var(--space-4);
}

.condition-icon i {
    font-size: var(--text-2xl);
    color: var(--primary-green);
}

.condition-item h4 {
    font-size: var(--text-lg);
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: var(--space-2);
}

.condition-item p {
    color: var(--gray-600);
    line-height: 1.6;
}

/* Технологии */
.tech-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--space-6);
    margin-top: var(--space-12);
}

.tech-item {
    text-align: center;
    padding: var(--space-6);
}

.tech-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--primary-green), var(--primary-purple));
    border-radius: var(--radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto var(--space-4);
}

.tech-icon i {
    font-size: var(--text-3xl);
    color: white;
}

.tech-item h4 {
    font-size: var(--text-lg);
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: var(--space-2);
}

.tech-item p {
    color: var(--gray-600);
    line-height: 1.6;
}

/* Преимущества */
.advantages-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--space-8);
    margin-top: var(--space-12);
}

.advantage-item {
    position: relative;
    padding: var(--space-6);
    background: white;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
}

.advantage-number {
    position: absolute;
    top: -20px;
    left: var(--space-6);
    width: 40px;
    height: 40px;
    background: var(--primary-green);
    color: white;
    border-radius: var(--radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    font-size: var(--text-lg);
}

.advantage-item h4 {
    font-size: var(--text-lg);
    font-weight: 600;
    color: var(--gray-900);
    margin: var(--space-4) 0 var(--space-2);
}

.advantage-item p {
    color: var(--gray-600);
    line-height: 1.6;
}

/* Ценности */
.values-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: var(--space-8);
    margin-top: var(--space-12);
}

.value-item {
    text-align: center;
    padding: var(--space-8);
    background: white;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
    transition: transform var(--transition-normal);
}

.value-item:hover {
    transform: translateY(-8px);
}

.value-icon {
    width: 80px;
    height: 80px;
    background: var(--green-100);
    border-radius: var(--radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto var(--space-6);
}

.value-icon i {
    font-size: var(--text-3xl);
    color: var(--primary-green);
}

.value-item h3 {
    font-size: var(--text-xl);
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: var(--space-4);
}

.value-item p {
    color: var(--gray-600);
    line-height: 1.6;
}

/* Команда */
.team-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: var(--space-8);
    margin-top: var(--space-12);
}

.team-member {
    text-align: center;
    padding: var(--space-8);
    background: white;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
    transition: transform var(--transition-normal);
}

.team-member:hover {
    transform: translateY(-8px);
}

.member-avatar {
    width: 120px;
    height: 120px;
    background: linear-gradient(135deg, var(--primary-green), var(--primary-purple));
    border-radius: var(--radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto var(--space-6);
}

.member-avatar i {
    font-size: var(--text-4xl);
    color: white;
}

.team-member h4 {
    font-size: var(--text-xl);
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: var(--space-1);
}

.member-position {
    font-size: var(--text-sm);
    color: var(--primary-green);
    font-weight: 600;
    margin-bottom: var(--space-4);
}

.member-bio {
    color: var(--gray-600);
    line-height: 1.6;
}

/* Достижения */
.achievements-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--space-6);
    margin-top: var(--space-12);
}

.achievement-item {
    text-align: center;
    padding: var(--space-6);
    background: white;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
}

.achievement-icon {
    width: 64px;
    height: 64px;
    background: var(--yellow-100);
    border-radius: var(--radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto var(--space-4);
}

.achievement-icon i {
    font-size: var(--text-2xl);
    color: var(--yellow-600);
}

.achievement-item h4 {
    font-size: var(--text-lg);
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: var(--space-2);
}

.achievement-item p {
    color: var(--gray-600);
    line-height: 1.6;
}

/* Партнеры */
.partners-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--space-6);
    margin-top: var(--space-12);
}

.partner-item {
    text-align: center;
    padding: var(--space-6);
    background: white;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
    transition: transform var(--transition-normal);
}

.partner-item:hover {
    transform: translateY(-4px);
}

.partner-logo {
    width: 64px;
    height: 64px;
    background: var(--gray-100);
    border-radius: var(--radius-md);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto var(--space-4);
}

.partner-logo i {
    font-size: var(--text-2xl);
    color: var(--gray-600);
}

.partner-item h4 {
    font-size: var(--text-base);
    font-weight: 600;
    color: var(--gray-900);
}

/* Контакты */
.contact-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--space-12);
    margin-top: var(--space-12);
}

.contact-info h2 {
    font-size: var(--text-2xl);
    font-weight: 700;
    color: var(--gray-900);
    margin-bottom: var(--space-4);
}

.contact-items {
    margin: var(--space-8) 0;
}

.contact-item {
    display: flex;
    gap: var(--space-4);
    padding: var(--space-4) 0;
    border-bottom: 1px solid var(--gray-200);
}

.contact-item:last-child {
    border-bottom: none;
}

.contact-icon {
    width: 48px;
    height: 48px;
    background: var(--green-100);
    border-radius: var(--radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.contact-icon i {
    font-size: var(--text-lg);
    color: var(--primary-green);
}

.contact-details h4 {
    font-size: var(--text-lg);
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: var(--space-1);
}

.contact-details p {
    color: var(--gray-600);
    margin: 0;
    line-height: 1.5;
}

.social-links {
    margin-top: var(--space-8);
}

.social-links h4 {
    font-size: var(--text-lg);
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: var(--space-4);
}

.social-icons {
    display: flex;
    gap: var(--space-3);
}

.social-icon {
    width: 48px;
    height: 48px;
    background: var(--gray-100);
    border-radius: var(--radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--gray-600);
    text-decoration: none;
    transition: all var(--transition-fast);
}

.social-icon:hover {
    background: var(--primary-green);
    color: white;
    transform: translateY(-2px);
}

.contact-form-container {
    background: white;
    border-radius: var(--radius-lg);
    padding: var(--space-8);
    box-shadow: var(--shadow-lg);
}

.contact-form-container h2 {
    font-size: var(--text-2xl);
    font-weight: 700;
    color: var(--gray-900);
    margin-bottom: var(--space-2);
}

.contact-form-container > p {
    color: var(--gray-600);
    margin-bottom: var(--space-6);
}

.contact-form .form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--space-4);
    margin-bottom: var(--space-4);
}

.contact-form .form-group {
    margin-bottom: var(--space-4);
}

.contact-form label {
    display: block;
    font-weight: 600;
    color: var(--gray-700);
    margin-bottom: var(--space-2);
}

.contact-form input,
.contact-form select,
.contact-form textarea {
    width: 100%;
    padding: var(--space-3);
    border: 2px solid var(--gray-200);
    border-radius: var(--radius-md);
    font-size: var(--text-base);
    transition: border-color var(--transition-fast);
}

.contact-form input:focus,
.contact-form select:focus,
.contact-form textarea:focus {
    outline: none;
    border-color: var(--primary-green);
}

.contact-form textarea {
    resize: vertical;
    min-height: 120px;
}

.alert {
    padding: var(--space-4);
    border-radius: var(--radius-md);
    margin-bottom: var(--space-4);
    display: flex;
    align-items: center;
    gap: var(--space-2);
}

.alert-success {
    background: var(--green-50);
    color: var(--green-800);
    border: 1px solid var(--green-200);
}

.alert-error {
    background: var(--red-50);
    color: var(--red-800);
    border: 1px solid var(--red-200);
}

.faq-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--space-6);
    margin-top: var(--space-8);
}

.faq-item {
    background: white;
    border-radius: var(--radius-lg);
    padding: var(--space-6);
    box-shadow: var(--shadow-md);
}

.faq-item h4 {
    font-size: var(--text-lg);
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: var(--space-3);
}

.faq-item p {
    color: var(--gray-600);
    line-height: 1.6;
}

.faq-cta {
    text-align: center;
    margin-top: var(--space-12);
}

.faq-cta p {
    font-size: var(--text-lg);
    color: var(--gray-600);
    margin-bottom: var(--space-4);
}

.offices-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--space-6);
    margin-top: var(--space-12);
}

.office-item {
    text-align: center;
    padding: var(--space-6);
    background: white;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
}

.office-icon {
    width: 64px;
    height: 64px;
    background: var(--blue-100);
    border-radius: var(--radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto var(--space-4);
}

.office-icon i {
    font-size: var(--text-2xl);
    color: var(--blue-600);
}

.office-item h4 {
    font-size: var(--text-lg);
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: var(--space-2);
}

.office-item p {
    color: var(--gray-600);
    line-height: 1.6;
}

/* Поддержка */
.support-options {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: var(--space-8);
    margin-top: var(--space-12);
}

.support-option {
    text-align: center;
    padding: var(--space-8);
    background: white;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
    transition: transform var(--transition-normal);
}

.support-option:hover {
    transform: translateY(-8px);
}

.support-icon {
    width: 80px;
    height: 80px;
    background: var(--blue-100);
    border-radius: var(--radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto var(--space-6);
}

.support-icon i {
    font-size: var(--text-3xl);
    color: var(--blue-600);
}

.support-option h3 {
    font-size: var(--text-xl);
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: var(--space-4);
}

.support-option p {
    color: var(--gray-600);
    line-height: 1.6;
    margin-bottom: var(--space-6);
}

.help-categories {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--space-8);
    margin-top: var(--space-12);
}

.help-category {
    padding: var(--space-6);
    background: white;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
}

.category-icon {
    width: 64px;
    height: 64px;
    background: var(--purple-100);
    border-radius: var(--radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: var(--space-4);
}

.category-icon i {
    font-size: var(--text-2xl);
    color: var(--purple-600);
}

.help-category h4 {
    font-size: var(--text-lg);
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: var(--space-4);
}

.help-category ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.help-category li {
    margin-bottom: var(--space-2);
}

.help-link {
    color: var(--primary-green);
    text-decoration: none;
    font-size: var(--text-sm);
    transition: color var(--transition-fast);
}

.help-link:hover {
    color: var(--primary-purple);
    text-decoration: underline;
}

/* Статус системы */
.system-status {
    background: white;
    border-radius: var(--radius-lg);
    padding: var(--space-8);
    box-shadow: var(--shadow-md);
    margin-top: var(--space-8);
}

.status-item {
    display: flex;
    align-items: center;
    gap: var(--space-4);
    padding: var(--space-4) 0;
    border-bottom: 1px solid var(--gray-200);
}

.status-item:last-child {
    border-bottom: none;
}

.status-indicator {
    width: 12px;
    height: 12px;
    border-radius: var(--radius-full);
    flex-shrink: 0;
}

.status-online {
    background: var(--green-500);
}

.status-warning {
    background: var(--yellow-500);
}

.status-offline {
    background: var(--red-500);
}

.status-info {
    flex: 1;
}

.status-info h4 {
    font-size: var(--text-base);
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: var(--space-1);
}

.status-info p {
    font-size: var(--text-sm);
    color: var(--gray-600);
    margin: 0;
}

.status-badge {
    padding: var(--space-1) var(--space-3);
    border-radius: var(--radius-full);
    font-size: var(--text-xs);
    font-weight: 600;
    text-transform: uppercase;
}

.status-operational {
    background: var(--green-100);
    color: var(--green-800);
}

.status-degraded {
    background: var(--yellow-100);
    color: var(--yellow-800);
}

.status-outage {
    background: var(--red-100);
    color: var(--red-800);
}

.status-footer {
    text-align: center;
    margin-top: var(--space-6);
    padding-top: var(--space-6);
    border-top: 1px solid var(--gray-200);
}

.status-footer p {
    color: var(--gray-600);
    margin-bottom: var(--space-4);
}

.resources-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--space-6);
    margin-top: var(--space-12);
}

.resource-item {
    text-align: center;
    padding: var(--space-6);
    background: white;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
    transition: transform var(--transition-normal);
}

.resource-item:hover {
    transform: translateY(-4px);
}

.resource-icon {
    width: 64px;
    height: 64px;
    background: var(--indigo-100);
    border-radius: var(--radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto var(--space-4);
}

.resource-icon i {
    font-size: var(--text-2xl);
    color: var(--indigo-600);
}

.resource-item h4 {
    font-size: var(--text-lg);
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: var(--space-2);
}

.resource-item p {
    color: var(--gray-600);
    line-height: 1.6;
    margin-bottom: var(--space-4);
}

.emergency-support {
    background: linear-gradient(135deg, var(--red-600), var(--red-700));
    color: white;
    padding: var(--space-12) 0;
    text-align: center;
}

.emergency-content {
    max-width: 600px;
    margin: 0 auto;
}

.emergency-icon {
    width: 80px;
    height: 80px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: var(--radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto var(--space-6);
}

.emergency-icon i {
    font-size: var(--text-3xl);
    color: white;
}

.emergency-content h2 {
    font-size: var(--text-2xl);
    font-weight: 700;
    margin-bottom: var(--space-4);
}

.emergency-content p {
    font-size: var(--text-lg);
    margin-bottom: var(--space-8);
    opacity: 0.9;
}

.emergency-actions {
    display: flex;
    gap: var(--space-4);
    justify-content: center;
    flex-wrap: wrap;
}

/* Адаптивность */
@media (max-width: 768px) {
    .calculator-container {
        grid-template-columns: 1fr;
        gap: var(--space-6);
    }

    .packages-grid {
        grid-template-columns: 1fr;
        gap: var(--space-6);
    }

    .package-popular {
        transform: none;
    }

    .advantages-grid,
    .values-grid,
    .team-grid {
        grid-template-columns: 1fr;
        gap: var(--space-6);
    }

    .price-amount {
        font-size: 2.5rem;
    }

    .contact-grid {
        grid-template-columns: 1fr;
        gap: var(--space-8);
    }

    .contact-form .form-row {
        grid-template-columns: 1fr;
    }

    .emergency-actions {
        flex-direction: column;
        align-items: center;
    }
}

/* FAQ */
.faq-search {
    max-width: 600px;
    margin: 0 auto;
    text-align: center;
}

.search-box {
    position: relative;
    display: inline-block;
    width: 100%;
}

.search-box i {
    position: absolute;
    left: var(--space-4);
    top: 50%;
    transform: translateY(-50%);
    color: var(--gray-400);
    z-index: 1;
}

.search-box input {
    width: 100%;
    padding: var(--space-4) var(--space-4) var(--space-4) var(--space-12);
    border: 2px solid var(--gray-200);
    border-radius: var(--radius-full);
    font-size: var(--text-lg);
    transition: border-color var(--transition-fast);
}

.search-box input:focus {
    outline: none;
    border-color: var(--primary-green);
}

.faq-categories {
    display: flex;
    gap: var(--space-2);
    justify-content: center;
    flex-wrap: wrap;
    margin: var(--space-8) 0;
}

.category-btn {
    padding: var(--space-2) var(--space-4);
    border: 2px solid var(--gray-200);
    background: white;
    color: var(--gray-600);
    border-radius: var(--radius-full);
    font-size: var(--text-sm);
    font-weight: 500;
    cursor: pointer;
    transition: all var(--transition-fast);
}

.category-btn:hover,
.category-btn.active {
    border-color: var(--primary-green);
    background: var(--primary-green);
    color: white;
}

.faq-list {
    max-width: 800px;
    margin: 0 auto;
}

.faq-list .faq-item {
    background: white;
    border-radius: var(--radius-lg);
    margin-bottom: var(--space-4);
    box-shadow: var(--shadow-md);
    overflow: hidden;
    transition: box-shadow var(--transition-fast);
}

.faq-list .faq-item:hover {
    box-shadow: var(--shadow-lg);
}

.faq-question {
    padding: var(--space-6);
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: background-color var(--transition-fast);
}

.faq-question:hover {
    background: var(--gray-50);
}

.faq-question h3 {
    font-size: var(--text-lg);
    font-weight: 600;
    color: var(--gray-900);
    margin: 0;
    flex: 1;
}

.faq-question i {
    color: var(--gray-400);
    transition: transform var(--transition-fast);
}

.faq-answer {
    max-height: 0;
    overflow: hidden;
    transition: max-height var(--transition-normal);
    background: var(--gray-50);
}

.faq-item.active .faq-answer {
    padding: 0 var(--space-6) var(--space-6);
}

.faq-answer p {
    color: var(--gray-600);
    line-height: 1.6;
    margin-bottom: var(--space-3);
}

.faq-answer ol,
.faq-answer ul {
    color: var(--gray-600);
    line-height: 1.6;
    padding-left: var(--space-5);
}

.faq-answer li {
    margin-bottom: var(--space-1);
}

.no-answer-section {
    text-align: center;
    padding: var(--space-12) 0;
}

.no-answer-content h2 {
    font-size: var(--text-2xl);
    font-weight: 700;
    color: var(--gray-900);
    margin-bottom: var(--space-4);
}

.no-answer-content p {
    font-size: var(--text-lg);
    color: var(--gray-600);
    margin-bottom: var(--space-8);
}

.support-actions {
    display: flex;
    gap: var(--space-4);
    justify-content: center;
    flex-wrap: wrap;
}

/* Документы (Terms, Privacy) */
.terms-content,
.privacy-content {
    max-width: 1000px;
    margin: 0 auto;
    display: grid;
    grid-template-columns: 250px 1fr;
    gap: var(--space-8);
    align-items: start;
}

.terms-toc,
.privacy-toc {
    position: sticky;
    top: var(--space-8);
    background: white;
    border-radius: var(--radius-lg);
    padding: var(--space-6);
    box-shadow: var(--shadow-md);
}

.terms-toc h3,
.privacy-toc h3 {
    font-size: var(--text-lg);
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: var(--space-4);
}

.terms-toc ul,
.privacy-toc ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.terms-toc li,
.privacy-toc li {
    margin-bottom: var(--space-2);
}

.terms-toc a,
.privacy-toc a {
    color: var(--gray-600);
    text-decoration: none;
    font-size: var(--text-sm);
    transition: color var(--transition-fast);
}

.terms-toc a:hover,
.privacy-toc a:hover {
    color: var(--primary-green);
}

.terms-text,
.privacy-text {
    background: white;
    border-radius: var(--radius-lg);
    padding: var(--space-8);
    box-shadow: var(--shadow-md);
}

.terms-section,
.privacy-section {
    margin-bottom: var(--space-8);
    padding-bottom: var(--space-6);
    border-bottom: 1px solid var(--gray-200);
}

.terms-section:last-child,
.privacy-section:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.terms-section h2,
.privacy-section h2 {
    font-size: var(--text-xl);
    font-weight: 700;
    color: var(--gray-900);
    margin-bottom: var(--space-4);
}

.terms-section h3,
.privacy-section h3 {
    font-size: var(--text-lg);
    font-weight: 600;
    color: var(--gray-800);
    margin: var(--space-4) 0 var(--space-2);
}

.terms-section p,
.privacy-section p {
    color: var(--gray-600);
    line-height: 1.6;
    margin-bottom: var(--space-3);
}

.terms-section ul,
.privacy-section ul {
    color: var(--gray-600);
    line-height: 1.6;
    padding-left: var(--space-5);
    margin-bottom: var(--space-3);
}

.terms-section li,
.privacy-section li {
    margin-bottom: var(--space-1);
}

.legal-contact {
    background: white;
    border-radius: var(--radius-lg);
    padding: var(--space-8);
    box-shadow: var(--shadow-md);
    text-align: center;
}

.legal-contact h2 {
    font-size: var(--text-2xl);
    font-weight: 700;
    color: var(--gray-900);
    margin-bottom: var(--space-6);
}

.contact-details {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--space-6);
    text-align: left;
}

.contact-details .contact-item {
    padding: var(--space-4);
    background: var(--gray-50);
    border-radius: var(--radius-md);
}

.contact-details .contact-item strong {
    display: block;
    color: var(--gray-900);
    font-weight: 600;
    margin-bottom: var(--space-2);
}

.contact-details .contact-item p {
    color: var(--gray-600);
    margin: 0;
    line-height: 1.5;
}

/* Privacy - управление согласием */
.privacy-summary {
    margin-bottom: var(--space-12);
}

.privacy-summary h2 {
    font-size: var(--text-2xl);
    font-weight: 700;
    color: var(--gray-900);
    margin-bottom: var(--space-6);
    text-align: center;
}

.summary-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--space-6);
}

.summary-item {
    text-align: center;
    padding: var(--space-6);
    background: white;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
}

.summary-icon {
    width: 64px;
    height: 64px;
    background: var(--blue-100);
    border-radius: var(--radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto var(--space-4);
}

.summary-icon i {
    font-size: var(--text-2xl);
    color: var(--blue-600);
}

.summary-item h4 {
    font-size: var(--text-lg);
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: var(--space-2);
}

.summary-item p {
    color: var(--gray-600);
    line-height: 1.6;
}

.consent-management {
    background: white;
    border-radius: var(--radius-lg);
    padding: var(--space-8);
    box-shadow: var(--shadow-md);
    text-align: center;
}

.consent-management h2 {
    font-size: var(--text-2xl);
    font-weight: 700;
    color: var(--gray-900);
    margin-bottom: var(--space-2);
}

.consent-management > p {
    color: var(--gray-600);
    margin-bottom: var(--space-8);
}

.consent-options {
    max-width: 600px;
    margin: 0 auto var(--space-8);
}

.consent-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--space-4);
    border: 1px solid var(--gray-200);
    border-radius: var(--radius-md);
    margin-bottom: var(--space-3);
}

.consent-info {
    text-align: left;
}

.consent-info h4 {
    font-size: var(--text-base);
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: var(--space-1);
}

.consent-info p {
    font-size: var(--text-sm);
    color: var(--gray-600);
    margin: 0;
}

.consent-toggle {
    display: flex;
    align-items: center;
    gap: var(--space-2);
}

.consent-toggle input[type="checkbox"] {
    width: 18px;
    height: 18px;
    accent-color: var(--primary-green);
}

.consent-toggle label {
    font-size: var(--text-sm);
    color: var(--gray-700);
    cursor: pointer;
}

/* Help/База знаний */
.help-search {
    max-width: 600px;
    margin: 0 auto;
    text-align: center;
}

.popular-searches {
    margin-top: var(--space-4);
    font-size: var(--text-sm);
    color: var(--gray-600);
}

.popular-searches span {
    margin-right: var(--space-2);
}

.popular-searches a {
    color: var(--primary-green);
    text-decoration: none;
    margin-right: var(--space-3);
    transition: color var(--transition-fast);
}

.popular-searches a:hover {
    color: var(--primary-purple);
    text-decoration: underline;
}

.help-categories {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--space-6);
    margin-top: var(--space-12);
}

.help-category {
    padding: var(--space-6);
    background: white;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
    cursor: pointer;
    transition: all var(--transition-normal);
    position: relative;
}

.help-category:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
}

.help-category .category-icon {
    width: 64px;
    height: 64px;
    background: var(--purple-100);
    border-radius: var(--radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: var(--space-4);
}

.help-category .category-icon i {
    font-size: var(--text-2xl);
    color: var(--purple-600);
}

.help-category h3 {
    font-size: var(--text-lg);
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: var(--space-2);
}

.help-category p {
    color: var(--gray-600);
    line-height: 1.6;
    margin-bottom: var(--space-4);
}

.article-count {
    position: absolute;
    top: var(--space-4);
    right: var(--space-4);
    background: var(--gray-100);
    color: var(--gray-600);
    padding: var(--space-1) var(--space-2);
    border-radius: var(--radius-full);
    font-size: var(--text-xs);
    font-weight: 500;
}

.popular-articles {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: var(--space-6);
    margin-top: var(--space-12);
}

.article-item {
    display: flex;
    gap: var(--space-4);
    padding: var(--space-6);
    background: white;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
    transition: transform var(--transition-normal);
}

.article-item:hover {
    transform: translateY(-2px);
}

.article-icon {
    width: 48px;
    height: 48px;
    background: var(--green-100);
    border-radius: var(--radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.article-icon i {
    font-size: var(--text-lg);
    color: var(--green-600);
}

.article-content {
    flex: 1;
}

.article-content h4 {
    margin-bottom: var(--space-2);
}

.article-content h4 a {
    color: var(--gray-900);
    text-decoration: none;
    font-size: var(--text-base);
    font-weight: 600;
    transition: color var(--transition-fast);
}

.article-content h4 a:hover {
    color: var(--primary-green);
}

.article-content p {
    color: var(--gray-600);
    font-size: var(--text-sm);
    line-height: 1.5;
    margin-bottom: var(--space-3);
}

.article-meta {
    display: flex;
    gap: var(--space-4);
    font-size: var(--text-xs);
    color: var(--gray-500);
}

.reading-time,
.views {
    display: flex;
    align-items: center;
    gap: var(--space-1);
}

/* Видео-уроки */
.video-tutorials {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--space-6);
    margin-top: var(--space-12);
}

.video-item {
    background: white;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
    overflow: hidden;
    transition: transform var(--transition-normal);
}

.video-item:hover {
    transform: translateY(-4px);
}

.video-thumbnail {
    position: relative;
    height: 180px;
    background: var(--gray-200);
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
}

.video-thumbnail img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.video-thumbnail i {
    position: absolute;
    font-size: var(--text-4xl);
    color: white;
    background: rgba(0, 0, 0, 0.7);
    border-radius: var(--radius-full);
    padding: var(--space-4);
    cursor: pointer;
    transition: transform var(--transition-fast);
}

.video-thumbnail:hover i {
    transform: scale(1.1);
}

.video-content {
    padding: var(--space-6);
}

.video-content h4 {
    font-size: var(--text-lg);
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: var(--space-2);
}

.video-content p {
    color: var(--gray-600);
    line-height: 1.6;
    margin-bottom: var(--space-4);
}

.video-meta {
    display: flex;
    gap: var(--space-4);
    font-size: var(--text-xs);
    color: var(--gray-500);
}

.duration,
.views {
    display: flex;
    align-items: center;
    gap: var(--space-1);
}

/* Быстрые ссылки */
.quick-links {
    background: white;
    border-radius: var(--radius-lg);
    padding: var(--space-8);
    box-shadow: var(--shadow-md);
    text-align: center;
}

.quick-links h2 {
    font-size: var(--text-2xl);
    font-weight: 700;
    color: var(--gray-900);
    margin-bottom: var(--space-8);
}

.links-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--space-8);
    text-align: left;
}

.link-group h4 {
    font-size: var(--text-lg);
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: var(--space-4);
}

.link-group ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.link-group li {
    margin-bottom: var(--space-2);
}

.link-group a {
    color: var(--gray-600);
    text-decoration: none;
    font-size: var(--text-sm);
    transition: color var(--transition-fast);
}

.link-group a:hover {
    color: var(--primary-green);
}

.help-cta {
    text-align: center;
    padding: var(--space-12) 0;
}

.help-cta-content h2 {
    font-size: var(--text-2xl);
    font-weight: 700;
    color: var(--gray-900);
    margin-bottom: var(--space-4);
}

.help-cta-content p {
    font-size: var(--text-lg);
    color: var(--gray-600);
    margin-bottom: var(--space-8);
}

.help-actions {
    display: flex;
    gap: var(--space-4);
    justify-content: center;
    flex-wrap: wrap;
}

/* Адаптивность для новых элементов */
@media (max-width: 768px) {
    .terms-content,
    .privacy-content {
        grid-template-columns: 1fr;
        gap: var(--space-6);
    }

    .terms-toc,
    .privacy-toc {
        position: static;
        order: 2;
    }

    .contact-details {
        grid-template-columns: 1fr;
    }

    .support-actions,
    .help-actions {
        flex-direction: column;
        align-items: center;
    }

    .popular-articles {
        grid-template-columns: 1fr;
    }

    .article-item {
        flex-direction: column;
        text-align: center;
    }

    .video-tutorials {
        grid-template-columns: 1fr;
    }

    .links-grid {
        grid-template-columns: 1fr;
        gap: var(--space-6);
    }
}
