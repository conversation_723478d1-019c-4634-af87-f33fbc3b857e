<?php
/**
 * AstroGenix - Класс пользователя
 * Эко-майнинговая инвестиционная платформа
 */

class User {
    private $conn;
    private $table_name = "users";

    public $id;
    public $username;
    public $email;
    public $password_hash;
    public $first_name;
    public $last_name;
    public $phone;
    public $balance;
    public $total_invested;
    public $total_earned;
    public $referral_code;
    public $referred_by;
    public $email_verified;
    public $email_verification_token;
    public $password_reset_token;
    public $password_reset_expires;
    public $is_active;
    public $is_admin;
    public $created_at;
    public $updated_at;

    public function __construct($db) {
        $this->conn = $db;
    }

    // Регистрация нового пользователя
    public function register() {
        $query = "INSERT INTO " . $this->table_name . " 
                  SET username=:username, email=:email, password_hash=:password_hash, 
                      first_name=:first_name, last_name=:last_name, phone=:phone,
                      referral_code=:referral_code, referred_by=:referred_by,
                      email_verification_token=:email_verification_token";

        $stmt = $this->conn->prepare($query);

        // Очистка данных
        $this->username = htmlspecialchars(strip_tags($this->username));
        $this->email = htmlspecialchars(strip_tags($this->email));
        $this->first_name = htmlspecialchars(strip_tags($this->first_name));
        $this->last_name = htmlspecialchars(strip_tags($this->last_name));
        $this->phone = htmlspecialchars(strip_tags($this->phone));

        // Генерация реферального кода
        if (empty($this->referral_code)) {
            $this->referral_code = $this->generateUniqueReferralCode();
        }

        // Генерация токена верификации email
        $this->email_verification_token = bin2hex(random_bytes(32));

        // Хеширование пароля
        $this->password_hash = password_hash($this->password_hash, PASSWORD_DEFAULT);

        // Привязка параметров
        $stmt->bindParam(":username", $this->username);
        $stmt->bindParam(":email", $this->email);
        $stmt->bindParam(":password_hash", $this->password_hash);
        $stmt->bindParam(":first_name", $this->first_name);
        $stmt->bindParam(":last_name", $this->last_name);
        $stmt->bindParam(":phone", $this->phone);
        $stmt->bindParam(":referral_code", $this->referral_code);
        $stmt->bindParam(":referred_by", $this->referred_by);
        $stmt->bindParam(":email_verification_token", $this->email_verification_token);

        if ($stmt->execute()) {
            $this->id = $this->conn->lastInsertId();
            return true;
        }

        return false;
    }

    // Авторизация пользователя
    public function login($email, $password) {
        $query = "SELECT id, username, email, password_hash, first_name, last_name, 
                         balance, email_verified, is_active, is_admin 
                  FROM " . $this->table_name . " 
                  WHERE email = :email AND is_active = 1";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":email", $email);
        $stmt->execute();

        if ($stmt->rowCount() > 0) {
            $row = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if (password_verify($password, $row['password_hash'])) {
                $this->id = $row['id'];
                $this->username = $row['username'];
                $this->email = $row['email'];
                $this->first_name = $row['first_name'];
                $this->last_name = $row['last_name'];
                $this->balance = $row['balance'];
                $this->email_verified = $row['email_verified'];
                $this->is_active = $row['is_active'];
                $this->is_admin = $row['is_admin'];
                
                return true;
            }
        }

        return false;
    }

    // Получение пользователя по ID
    public function getUserById($id) {
        $query = "SELECT * FROM " . $this->table_name . " WHERE id = :id";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":id", $id);
        $stmt->execute();

        if ($stmt->rowCount() > 0) {
            $row = $stmt->fetch(PDO::FETCH_ASSOC);
            
            $this->id = $row['id'];
            $this->username = $row['username'];
            $this->email = $row['email'];
            $this->first_name = $row['first_name'];
            $this->last_name = $row['last_name'];
            $this->phone = $row['phone'];
            $this->balance = $row['balance'];
            $this->total_invested = $row['total_invested'];
            $this->total_earned = $row['total_earned'];
            $this->referral_code = $row['referral_code'];
            $this->referred_by = $row['referred_by'];
            $this->email_verified = $row['email_verified'];
            $this->is_active = $row['is_active'];
            $this->is_admin = $row['is_admin'];
            $this->created_at = $row['created_at'];
            
            return true;
        }

        return false;
    }

    // Проверка существования email
    public function emailExists($email) {
        $query = "SELECT id FROM " . $this->table_name . " WHERE email = :email";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":email", $email);
        $stmt->execute();

        return $stmt->rowCount() > 0;
    }

    // Проверка существования username
    public function usernameExists($username) {
        $query = "SELECT id FROM " . $this->table_name . " WHERE username = :username";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":username", $username);
        $stmt->execute();

        return $stmt->rowCount() > 0;
    }

    // Генерация уникального реферального кода
    private function generateUniqueReferralCode() {
        do {
            $code = generate_referral_code();
            $query = "SELECT id FROM " . $this->table_name . " WHERE referral_code = :code";
            $stmt = $this->conn->prepare($query);
            $stmt->bindParam(":code", $code);
            $stmt->execute();
        } while ($stmt->rowCount() > 0);

        return $code;
    }

    // Верификация email
    public function verifyEmail($token) {
        $query = "UPDATE " . $this->table_name . " 
                  SET email_verified = 1, email_verification_token = NULL 
                  WHERE email_verification_token = :token";
        
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":token", $token);
        
        return $stmt->execute() && $stmt->rowCount() > 0;
    }

    // Обновление баланса
    public function updateBalance($amount, $operation = 'add') {
        if ($operation === 'add') {
            $query = "UPDATE " . $this->table_name . " SET balance = balance + :amount WHERE id = :id";
        } else {
            $query = "UPDATE " . $this->table_name . " SET balance = balance - :amount WHERE id = :id";
        }

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":amount", $amount);
        $stmt->bindParam(":id", $this->id);

        return $stmt->execute();
    }

    // Получение пользователя по реферальному коду
    public function getUserByReferralCode($code) {
        $query = "SELECT id FROM " . $this->table_name . " WHERE referral_code = :code";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":code", $code);
        $stmt->execute();

        if ($stmt->rowCount() > 0) {
            $row = $stmt->fetch(PDO::FETCH_ASSOC);
            return $row['id'];
        }

        return false;
    }
}
?>
