/**
 * AstroGenix - Стили дашборда
 * Эко-майнинговая инвестиционная платформа
 */

/* Основная структура дашборда */
.dashboard-page {
    display: flex;
    min-height: 100vh;
    background: linear-gradient(135deg, #f8fffe 0%, #f0f9ff 100%);
}

/* Боковая панель */
.sidebar {
    width: 280px;
    background: var(--white);
    box-shadow: var(--shadow-lg);
    display: flex;
    flex-direction: column;
    position: fixed;
    height: 100vh;
    z-index: 1000;
    transition: transform var(--transition-normal);
}

.sidebar-header {
    padding: var(--space-6);
    border-bottom: 1px solid var(--gray-200);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.sidebar-logo {
    display: flex;
    align-items: center;
    gap: var(--space-3);
    font-size: var(--text-xl);
    font-weight: 600;
    color: var(--gray-900);
}

.sidebar-logo img {
    width: 32px;
    height: 32px;
}

.sidebar-toggle {
    display: none;
    background: none;
    border: none;
    color: var(--gray-600);
    font-size: var(--text-lg);
    cursor: pointer;
    padding: var(--space-2);
    border-radius: var(--radius-sm);
    transition: color var(--transition-fast);
}

.sidebar-toggle:hover {
    color: var(--primary-green);
}

/* Навигация */
.sidebar-nav {
    flex: 1;
    padding: var(--space-4) 0;
}

.nav-list {
    list-style: none;
    margin: 0;
    padding: 0;
}

.nav-item {
    margin-bottom: var(--space-1);
}

.nav-link {
    display: flex;
    align-items: center;
    gap: var(--space-3);
    padding: var(--space-3) var(--space-6);
    color: var(--gray-600);
    text-decoration: none;
    transition: all var(--transition-fast);
    position: relative;
}

.nav-link:hover {
    color: var(--primary-green);
    background: rgba(46, 204, 113, 0.05);
}

.nav-item.active .nav-link {
    color: var(--primary-green);
    background: rgba(46, 204, 113, 0.1);
}

.nav-item.active .nav-link::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 3px;
    background: var(--gradient-primary);
}

.nav-link i {
    width: 20px;
    text-align: center;
}

/* Подвал боковой панели */
.sidebar-footer {
    padding: var(--space-6);
    border-top: 1px solid var(--gray-200);
}

.logout-btn {
    display: flex;
    align-items: center;
    gap: var(--space-3);
    padding: var(--space-3) var(--space-4);
    color: var(--error);
    text-decoration: none;
    border-radius: var(--radius-md);
    transition: background var(--transition-fast);
    width: 100%;
}

.logout-btn:hover {
    background: rgba(220, 53, 69, 0.1);
    color: var(--error);
}

/* Основной контент */
.main-content {
    flex: 1;
    margin-left: 280px;
    display: flex;
    flex-direction: column;
    min-height: 100vh;
}

/* Заголовок дашборда */
.dashboard-header {
    background: var(--white);
    padding: var(--space-6);
    box-shadow: var(--shadow-sm);
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: sticky;
    top: 0;
    z-index: 100;
}

.header-left {
    display: flex;
    align-items: center;
    gap: var(--space-4);
}

.mobile-menu-toggle {
    display: none;
    background: none;
    border: none;
    color: var(--gray-600);
    font-size: var(--text-lg);
    cursor: pointer;
    padding: var(--space-2);
    border-radius: var(--radius-sm);
}

.dashboard-header h1 {
    margin: 0;
    font-size: var(--text-2xl);
    color: var(--gray-900);
}

.header-right {
    display: flex;
    align-items: center;
    gap: var(--space-4);
}

.user-balance {
    text-align: right;
}

.balance-label {
    display: block;
    font-size: var(--text-sm);
    color: var(--gray-600);
}

.balance-amount {
    display: block;
    font-size: var(--text-lg);
    font-weight: 600;
    color: var(--primary-green);
}

.user-avatar img {
    width: 40px;
    height: 40px;
    border-radius: var(--radius-full);
    border: 2px solid var(--gray-200);
}

/* Контент дашборда */
.dashboard-content {
    flex: 1;
    padding: var(--space-6);
}

/* Сетка статистики */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--space-6);
    margin-bottom: var(--space-8);
}

.stat-card {
    background: var(--white);
    border-radius: var(--radius-lg);
    padding: var(--space-6);
    box-shadow: var(--shadow-sm);
    display: flex;
    align-items: center;
    gap: var(--space-4);
    transition: transform var(--transition-fast), box-shadow var(--transition-fast);
    position: relative;
    overflow: hidden;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--gradient-primary);
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--text-2xl);
    color: var(--white);
    background: var(--gradient-primary);
}

.balance-card .stat-icon {
    background: linear-gradient(135deg, #2ECC71 0%, #27AE60 100%);
}

.investment-card .stat-icon {
    background: linear-gradient(135deg, #3498DB 0%, #2980B9 100%);
}

.profit-card .stat-icon {
    background: linear-gradient(135deg, #F39C12 0%, #E67E22 100%);
}

.referral-card .stat-icon {
    background: linear-gradient(135deg, #9B59B6 0%, #8E44AD 100%);
}

.stat-content {
    flex: 1;
}

.stat-content h3 {
    margin: 0 0 var(--space-2) 0;
    font-size: var(--text-sm);
    color: var(--gray-600);
    font-weight: 500;
}

.stat-value {
    font-size: var(--text-2xl);
    font-weight: 700;
    color: var(--gray-900);
    margin-bottom: var(--space-2);
}

.stat-change {
    display: flex;
    align-items: center;
    gap: var(--space-1);
    font-size: var(--text-xs);
    font-weight: 500;
}

.stat-change.positive {
    color: var(--success);
}

.stat-change.negative {
    color: var(--error);
}

.stat-subtitle {
    font-size: var(--text-xs);
    color: var(--gray-500);
}

/* Виджет зеленой энергии */
.green-energy-widget {
    background: linear-gradient(135deg, rgba(46, 204, 113, 0.1) 0%, rgba(142, 68, 173, 0.1) 100%);
    border: 2px solid rgba(46, 204, 113, 0.2);
    border-radius: var(--radius-xl);
    padding: var(--space-8);
    margin-bottom: var(--space-8);
    position: relative;
    overflow: hidden;
}

.green-energy-widget::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle, rgba(46, 204, 113, 0.1) 0%, transparent 70%);
    animation: energyPulse 4s ease-in-out infinite;
}

@keyframes energyPulse {
    0%, 100% { transform: scale(1) rotate(0deg); opacity: 0.5; }
    50% { transform: scale(1.2) rotate(180deg); opacity: 0.8; }
}

.energy-header {
    display: flex;
    align-items: center;
    gap: var(--space-4);
    margin-bottom: var(--space-6);
}

.energy-icon {
    width: 60px;
    height: 60px;
    background: var(--gradient-primary);
    border-radius: var(--radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--text-2xl);
    color: var(--white);
    animation: leafSway 3s ease-in-out infinite;
}

@keyframes leafSway {
    0%, 100% { transform: rotate(-5deg); }
    50% { transform: rotate(5deg); }
}

.energy-info h3 {
    margin: 0 0 var(--space-1) 0;
    font-size: var(--text-xl);
    color: var(--gray-900);
}

.energy-info p {
    margin: 0;
    color: var(--gray-600);
    font-size: var(--text-sm);
}

.energy-display {
    text-align: center;
    margin-bottom: var(--space-6);
}

.energy-value {
    font-size: 3rem;
    font-weight: 700;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    display: inline-block;
}

.energy-unit {
    font-size: var(--text-lg);
    color: var(--gray-600);
    margin-left: var(--space-2);
}

.energy-progress {
    position: relative;
}

.progress-bar {
    height: 8px;
    background: rgba(255, 255, 255, 0.3);
    border-radius: var(--radius-full);
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: var(--gradient-primary);
    border-radius: var(--radius-full);
    transition: width var(--transition-slow);
    position: relative;
}

.progress-fill::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.3) 50%, transparent 100%);
    animation: progressShine 2s ease-in-out infinite;
}

@keyframes progressShine {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

.progress-text {
    text-align: center;
    margin-top: var(--space-2);
    font-size: var(--text-sm);
    color: var(--gray-600);
}

/* Сетка дашборда */
.dashboard-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: var(--space-6);
}

/* Виджеты */
.dashboard-widget {
    background: var(--white);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-sm);
    overflow: hidden;
    transition: box-shadow var(--transition-fast);
}

.dashboard-widget:hover {
    box-shadow: var(--shadow-md);
}

.widget-header {
    padding: var(--space-6);
    border-bottom: 1px solid var(--gray-200);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.widget-header h3 {
    margin: 0;
    font-size: var(--text-lg);
    color: var(--gray-900);
}

.widget-link {
    color: var(--primary-green);
    text-decoration: none;
    font-size: var(--text-sm);
    font-weight: 500;
    transition: color var(--transition-fast);
}

.widget-link:hover {
    color: var(--dark-green);
}

.widget-content {
    padding: var(--space-6);
}

/* Пустое состояние */
.empty-state {
    text-align: center;
    padding: var(--space-8);
    color: var(--gray-500);
}

.empty-state i {
    font-size: var(--text-4xl);
    margin-bottom: var(--space-4);
    color: var(--gray-400);
}

.empty-state p {
    margin-bottom: var(--space-4);
}

/* Список инвестиций */
.investments-list {
    display: flex;
    flex-direction: column;
    gap: var(--space-4);
}

.investment-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--space-4);
    border: 1px solid var(--gray-200);
    border-radius: var(--radius-md);
    transition: border-color var(--transition-fast);
}

.investment-item:hover {
    border-color: var(--primary-green);
}

.investment-info h4 {
    margin: 0 0 var(--space-1) 0;
    font-size: var(--text-base);
    color: var(--gray-900);
}

.investment-details {
    display: flex;
    gap: var(--space-3);
    font-size: var(--text-sm);
}

.investment-details .amount {
    color: var(--primary-green);
    font-weight: 600;
}

.investment-details .profit {
    color: var(--gray-600);
}

.investment-progress {
    display: flex;
    align-items: center;
    gap: var(--space-3);
}

.days-left {
    text-align: center;
}

.days-left .days {
    display: block;
    font-size: var(--text-lg);
    font-weight: 600;
    color: var(--primary-green);
}

.days-left .label {
    font-size: var(--text-xs);
    color: var(--gray-500);
}

.days-left .completed {
    font-size: var(--text-sm);
    color: var(--success);
    font-weight: 600;
}

.progress-circle {
    width: 40px;
    height: 40px;
}

.progress-circle svg {
    width: 100%;
    height: 100%;
    transform: rotate(-90deg);
}

/* Список транзакций */
.transactions-list {
    display: flex;
    flex-direction: column;
    gap: var(--space-3);
}

.transaction-item {
    display: flex;
    align-items: center;
    gap: var(--space-3);
    padding: var(--space-3);
    border-radius: var(--radius-md);
    transition: background var(--transition-fast);
}

.transaction-item:hover {
    background: var(--gray-100);
}

.transaction-icon {
    width: 40px;
    height: 40px;
    border-radius: var(--radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
    font-size: var(--text-sm);
}

.transaction-icon.deposit {
    background: var(--success);
}

.transaction-icon.withdrawal {
    background: var(--error);
}

.transaction-icon.investment {
    background: var(--primary-purple);
}

.transaction-icon.profit {
    background: var(--warning);
}

.transaction-icon.referral_bonus {
    background: var(--info);
}

.transaction-info {
    flex: 1;
}

.transaction-type {
    font-weight: 500;
    color: var(--gray-900);
    margin-bottom: var(--space-1);
}

.transaction-date {
    font-size: var(--text-xs);
    color: var(--gray-500);
}

.transaction-amount {
    font-weight: 600;
    margin-right: var(--space-3);
}

.transaction-amount.positive {
    color: var(--success);
}

.transaction-amount.negative {
    color: var(--error);
}

.transaction-status {
    padding: var(--space-1) var(--space-2);
    border-radius: var(--radius-sm);
    font-size: var(--text-xs);
    font-weight: 500;
}

.status-pending {
    background: rgba(255, 193, 7, 0.2);
    color: #856404;
}

.status-approved {
    background: rgba(40, 167, 69, 0.2);
    color: var(--success);
}

.status-rejected {
    background: rgba(220, 53, 69, 0.2);
    color: var(--error);
}

.status-completed {
    background: rgba(23, 162, 184, 0.2);
    color: var(--info);
}

/* Виджет графика */
.chart-widget {
    grid-column: span 2;
}

.chart-controls {
    display: flex;
    gap: var(--space-2);
}

.chart-period {
    padding: var(--space-1) var(--space-3);
    border: 1px solid var(--gray-300);
    background: var(--white);
    color: var(--gray-600);
    border-radius: var(--radius-sm);
    font-size: var(--text-xs);
    cursor: pointer;
    transition: all var(--transition-fast);
}

.chart-period:hover {
    border-color: var(--primary-green);
    color: var(--primary-green);
}

.chart-period.active {
    background: var(--primary-green);
    border-color: var(--primary-green);
    color: var(--white);
}

/* Быстрые действия */
.quick-actions {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: var(--space-4);
}

.quick-action {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--space-2);
    padding: var(--space-4);
    border: 2px solid var(--gray-200);
    border-radius: var(--radius-lg);
    text-decoration: none;
    color: var(--gray-700);
    transition: all var(--transition-fast);
}

.quick-action:hover {
    border-color: var(--primary-green);
    color: var(--primary-green);
    transform: translateY(-2px);
}

.quick-action i {
    font-size: var(--text-2xl);
}

.quick-action span {
    font-size: var(--text-sm);
    font-weight: 500;
    text-align: center;
}

.quick-action.deposit:hover {
    border-color: var(--success);
    color: var(--success);
}

.quick-action.invest:hover {
    border-color: var(--primary-purple);
    color: var(--primary-purple);
}

.quick-action.withdraw:hover {
    border-color: var(--error);
    color: var(--error);
}

.quick-action.referral:hover {
    border-color: var(--info);
    color: var(--info);
}

/* Адаптивность */
@media (max-width: 1200px) {
    .chart-widget {
        grid-column: span 1;
    }

    .dashboard-grid {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .sidebar {
        transform: translateX(-100%);
    }

    .sidebar.open {
        transform: translateX(0);
    }

    .main-content {
        margin-left: 0;
    }

    .mobile-menu-toggle {
        display: block;
    }

    .dashboard-header {
        padding: var(--space-4);
    }

    .dashboard-header h1 {
        font-size: var(--text-xl);
    }

    .dashboard-content {
        padding: var(--space-4);
    }

    .stats-grid {
        grid-template-columns: 1fr;
        gap: var(--space-4);
    }

    .stat-card {
        padding: var(--space-4);
    }

    .green-energy-widget {
        padding: var(--space-6);
    }

    .energy-value {
        font-size: 2rem;
    }

    .dashboard-grid {
        gap: var(--space-4);
    }

    .widget-header {
        padding: var(--space-4);
    }

    .widget-content {
        padding: var(--space-4);
    }

    .investment-item {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--space-3);
    }

    .investment-progress {
        align-self: flex-end;
    }

    .quick-actions {
        grid-template-columns: 1fr;
    }

    .user-balance {
        display: none;
    }
}

@media (max-width: 480px) {
    .dashboard-header {
        flex-direction: column;
        gap: var(--space-3);
        align-items: flex-start;
    }

    .header-right {
        align-self: flex-end;
    }

    .stat-card {
        flex-direction: column;
        text-align: center;
    }

    .stat-icon {
        width: 50px;
        height: 50px;
        font-size: var(--text-xl);
    }

    .energy-header {
        flex-direction: column;
        text-align: center;
    }

    .transaction-item {
        padding: var(--space-2);
    }

    .transaction-icon {
        width: 32px;
        height: 32px;
        font-size: var(--text-xs);
    }
}
