<?php
/**
 * AstroGenix - API для лидербордов
 * Обновление и получение данных рейтингов
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST');
header('Access-Control-Allow-Headers: Content-Type');

session_start();
require_once '../config/config.php';
require_once '../classes/Leaderboards.php';

// Проверка авторизации
if (!isset($_SESSION['user_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'error' => 'Unauthorized']);
    exit();
}

try {
    $database = new Database();
    $db = $database->getConnection();
    $leaderboards = new Leaderboards($db);
    
    $method = $_SERVER['REQUEST_METHOD'];
    
    if ($method === 'GET') {
        handleGetRequest($leaderboards);
    } elseif ($method === 'POST') {
        handlePostRequest($leaderboards);
    } else {
        http_response_code(405);
        echo json_encode(['success' => false, 'error' => 'Method not allowed']);
    }
    
} catch (Exception $e) {
    error_log("Leaderboard API error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['success' => false, 'error' => 'Internal server error']);
}

/**
 * Обработка GET запросов
 */
function handleGetRequest($leaderboards) {
    $action = $_GET['action'] ?? 'get_leaderboard';
    
    switch ($action) {
        case 'get_leaderboard':
            getLeaderboard($leaderboards);
            break;
            
        case 'get_user_ranking':
            getUserRanking($leaderboards);
            break;
            
        case 'get_ranking_types':
            getRankingTypes($leaderboards);
            break;
            
        case 'get_ranking_history':
            getRankingHistory($leaderboards);
            break;
            
        default:
            http_response_code(400);
            echo json_encode(['success' => false, 'error' => 'Invalid action']);
    }
}

/**
 * Обработка POST запросов
 */
function handlePostRequest($leaderboards) {
    $input = json_decode(file_get_contents('php://input'), true);
    $action = $input['action'] ?? '';
    
    switch ($action) {
        case 'update_rankings':
            updateRankings($leaderboards, $input);
            break;
            
        case 'refresh_leaderboard':
            refreshLeaderboard($leaderboards, $input);
            break;
            
        default:
            http_response_code(400);
            echo json_encode(['success' => false, 'error' => 'Invalid action']);
    }
}

/**
 * Получение лидерборда
 */
function getLeaderboard($leaderboards) {
    $type = $_GET['type'] ?? 'referrals';
    $limit = intval($_GET['limit'] ?? 50);
    $refresh = intval($_GET['refresh'] ?? 0);
    $user_id = $_SESSION['user_id'];
    
    // Валидация параметров
    $valid_types = ['referrals', 'investments', 'earnings', 'activity', 'eco_impact'];
    if (!in_array($type, $valid_types)) {
        http_response_code(400);
        echo json_encode(['success' => false, 'error' => 'Invalid ranking type']);
        return;
    }
    
    if ($limit < 1 || $limit > 500) {
        http_response_code(400);
        echo json_encode(['success' => false, 'error' => 'Invalid limit']);
        return;
    }
    
    // Принудительное обновление рейтингов
    if ($refresh) {
        $leaderboards->updateUserRankings($user_id);
    }
    
    // Получение данных
    $leaderboard = $leaderboards->getLeaderboard($type, $limit, $user_id);
    $ranking_types = $leaderboards->getRankingTypes();
    
    // Поиск данных текущего типа
    $type_data = array_filter($ranking_types, function($rt) use ($type) {
        return $rt['ranking_type'] === $type;
    });
    $type_data = reset($type_data);
    
    echo json_encode([
        'success' => true,
        'leaderboard' => $leaderboard,
        'typeData' => $type_data,
        'total_count' => count($leaderboard),
        'current_type' => $type,
        'current_limit' => $limit
    ]);
}

/**
 * Получение рейтинга пользователя
 */
function getUserRanking($leaderboards) {
    $user_id = $_SESSION['user_id'];
    $type = $_GET['type'] ?? null;
    
    $ranking = $leaderboards->getUserRanking($user_id, $type);
    
    echo json_encode([
        'success' => true,
        'ranking' => $ranking
    ]);
}

/**
 * Получение типов рейтингов
 */
function getRankingTypes($leaderboards) {
    $types = $leaderboards->getRankingTypes();
    
    echo json_encode([
        'success' => true,
        'types' => $types
    ]);
}

/**
 * Получение истории рейтинга
 */
function getRankingHistory($leaderboards) {
    $user_id = $_SESSION['user_id'];
    $type = $_GET['type'] ?? 'referrals';
    $period = $_GET['period'] ?? 'monthly';
    $limit = intval($_GET['limit'] ?? 12);
    
    $history = $leaderboards->getUserRankingHistory($user_id, $type, $period, $limit);
    
    echo json_encode([
        'success' => true,
        'history' => $history,
        'type' => $type,
        'period' => $period
    ]);
}

/**
 * Обновление рейтингов
 */
function updateRankings($leaderboards, $input) {
    $user_id = $_SESSION['user_id'];
    
    $result = $leaderboards->updateUserRankings($user_id);
    
    if ($result) {
        // Получение обновленных данных
        $rankings = $leaderboards->getUserRanking($user_id);
        
        echo json_encode([
            'success' => true,
            'message' => 'Рейтинги обновлены',
            'rankings' => $rankings
        ]);
    } else {
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'error' => 'Ошибка обновления рейтингов'
        ]);
    }
}

/**
 * Принудительное обновление лидерборда
 */
function refreshLeaderboard($leaderboards, $input) {
    $type = $input['type'] ?? 'referrals';
    $limit = intval($input['limit'] ?? 50);
    $user_id = $_SESSION['user_id'];
    
    // Обновление рейтингов пользователя
    $leaderboards->updateUserRankings($user_id);
    
    // Получение обновленного лидерборда
    $leaderboard = $leaderboards->getLeaderboard($type, $limit, $user_id);
    
    echo json_encode([
        'success' => true,
        'leaderboard' => $leaderboard,
        'message' => 'Лидерборд обновлен'
    ]);
}

/**
 * Логирование API запросов
 */
function logApiRequest($action, $params = []) {
    $log_data = [
        'timestamp' => date('Y-m-d H:i:s'),
        'user_id' => $_SESSION['user_id'] ?? null,
        'action' => $action,
        'params' => $params,
        'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
        'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown'
    ];
    
    $log_file = '../logs/api_leaderboard.log';
    $log_dir = dirname($log_file);
    
    if (!is_dir($log_dir)) {
        mkdir($log_dir, 0755, true);
    }
    
    file_put_contents($log_file, json_encode($log_data) . PHP_EOL, FILE_APPEND | LOCK_EX);
}

/**
 * Валидация параметров
 */
function validateParameters($required_params, $provided_params) {
    $missing = [];
    
    foreach ($required_params as $param) {
        if (!isset($provided_params[$param]) || empty($provided_params[$param])) {
            $missing[] = $param;
        }
    }
    
    return $missing;
}

/**
 * Ограничение частоты запросов
 */
function checkRateLimit($user_id, $action, $limit_per_minute = 60) {
    $cache_key = "rate_limit_{$user_id}_{$action}";
    $cache_file = "../cache/{$cache_key}.txt";
    
    if (!is_dir('../cache')) {
        mkdir('../cache', 0755, true);
    }
    
    $current_time = time();
    $requests = [];
    
    // Чтение существующих запросов
    if (file_exists($cache_file)) {
        $content = file_get_contents($cache_file);
        $requests = $content ? json_decode($content, true) : [];
    }
    
    // Фильтрация запросов за последнюю минуту
    $requests = array_filter($requests, function($timestamp) use ($current_time) {
        return ($current_time - $timestamp) < 60;
    });
    
    // Проверка лимита
    if (count($requests) >= $limit_per_minute) {
        http_response_code(429);
        echo json_encode([
            'success' => false,
            'error' => 'Rate limit exceeded',
            'retry_after' => 60
        ]);
        exit();
    }
    
    // Добавление текущего запроса
    $requests[] = $current_time;
    file_put_contents($cache_file, json_encode($requests), LOCK_EX);
}

// Применение ограничения частоты запросов
if (isset($_SESSION['user_id'])) {
    $action = $_GET['action'] ?? $_POST['action'] ?? 'unknown';
    checkRateLimit($_SESSION['user_id'], $action);
    logApiRequest($action, array_merge($_GET, $_POST));
}
?>
