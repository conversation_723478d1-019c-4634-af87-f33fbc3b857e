/**
 * AstroGenix - JavaScript для страницы инвестиций
 * Эко-майнинговая инвестиционная платформа
 */

document.addEventListener('DOMContentLoaded', function() {
    initializeInvestments();
});

function initializeInvestments() {
    // Инициализация калькуляторов пакетов
    initPackageCalculators();
    
    // Инициализация кнопок инвестирования
    initInvestButtons();
    
    // Инициализация модального окна
    initInvestmentModal();
    
    // Инициализация анимаций
    initInvestmentAnimations();
}

// Калькуляторы пакетов
function initPackageCalculators() {
    const packageCards = document.querySelectorAll('.package-card');
    
    packageCards.forEach(card => {
        const amountInput = card.querySelector('.investment-amount');
        const dailyProfitElement = card.querySelector('.daily-profit');
        const totalProfitElement = card.querySelector('.total-profit');
        const totalReturnElement = card.querySelector('.total-return');
        
        if (!amountInput) return;
        
        const profitPercent = parseFloat(card.dataset.profitPercent || amountInput.dataset.profitPercent || 0);
        const duration = parseInt(card.dataset.duration || amountInput.dataset.duration || 30);
        
        function updateCalculations() {
            const amount = parseFloat(amountInput.value) || 0;
            const dailyProfit = (amount * profitPercent) / 100;
            const totalProfit = dailyProfit * duration;
            const totalReturn = amount + totalProfit;
            
            dailyProfitElement.textContent = formatCurrency(dailyProfit);
            totalProfitElement.textContent = formatCurrency(totalProfit);
            totalReturnElement.textContent = formatCurrency(totalReturn);
            
            // Анимация обновления
            [dailyProfitElement, totalProfitElement, totalReturnElement].forEach(element => {
                element.style.color = 'var(--primary-green)';
                element.style.transform = 'scale(1.1)';
                setTimeout(() => {
                    element.style.color = '';
                    element.style.transform = 'scale(1)';
                }, 300);
            });
        }
        
        // Обновление при изменении суммы
        amountInput.addEventListener('input', updateCalculations);
        
        // Первоначальный расчет
        updateCalculations();
        
        // Валидация суммы
        amountInput.addEventListener('blur', function() {
            const minAmount = parseFloat(this.getAttribute('min'));
            const maxAmount = parseFloat(this.getAttribute('max'));
            const value = parseFloat(this.value);
            
            if (value < minAmount) {
                this.value = minAmount;
                updateCalculations();
                showFieldWarning(this, `Минимальная сумма: ${formatCurrency(minAmount)}`);
            } else if (value > maxAmount) {
                this.value = maxAmount;
                updateCalculations();
                showFieldWarning(this, `Максимальная сумма: ${formatCurrency(maxAmount)}`);
            }
        });
    });
}

// Кнопки инвестирования
function initInvestButtons() {
    const investButtons = document.querySelectorAll('.invest-btn');
    
    investButtons.forEach(button => {
        button.addEventListener('click', function() {
            const packageId = this.dataset.packageId;
            const packageCard = this.closest('.package-card');
            const amountInput = packageCard.querySelector('.investment-amount');
            const amount = parseFloat(amountInput.value);
            
            // Валидация
            const minAmount = parseFloat(this.dataset.minAmount);
            const maxAmount = parseFloat(this.dataset.maxAmount);
            
            if (!amount || amount < minAmount || amount > maxAmount) {
                showNotification(`Сумма должна быть от ${formatCurrency(minAmount)} до ${formatCurrency(maxAmount)}`, 'error');
                return;
            }
            
            // Открытие модального окна
            openInvestmentModal({
                packageId: packageId,
                packageName: packageCard.querySelector('h3').textContent,
                amount: amount,
                profitPercent: parseFloat(this.dataset.profitPercent),
                duration: parseInt(this.dataset.duration),
                minAmount: minAmount,
                maxAmount: maxAmount
            });
        });
    });
}

// Модальное окно инвестиции
function initInvestmentModal() {
    const modal = document.getElementById('investmentModal');
    const modalClose = document.getElementById('modalClose');
    const modalCancel = document.getElementById('modalCancel');
    const modalAmount = document.getElementById('modalAmount');
    
    // Закрытие модального окна
    [modalClose, modalCancel].forEach(element => {
        element.addEventListener('click', closeInvestmentModal);
    });
    
    // Закрытие по клику на фон
    modal.addEventListener('click', function(e) {
        if (e.target === modal) {
            closeInvestmentModal();
        }
    });
    
    // Закрытие по Escape
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape' && modal.classList.contains('show')) {
            closeInvestmentModal();
        }
    });
    
    // Обновление расчетов в модальном окне
    modalAmount.addEventListener('input', updateModalCalculations);
}

function openInvestmentModal(packageData) {
    const modal = document.getElementById('investmentModal');
    const packageInfo = document.getElementById('selectedPackageInfo');
    const packageIdInput = document.getElementById('modalPackageId');
    const amountInput = document.getElementById('modalAmount');
    
    // Заполнение данных
    packageIdInput.value = packageData.packageId;
    amountInput.value = packageData.amount;
    amountInput.setAttribute('min', packageData.minAmount);
    amountInput.setAttribute('max', packageData.maxAmount);
    
    // Информация о пакете
    packageInfo.innerHTML = `
        <div class="package-info">
            <h4>${packageData.packageName}</h4>
            <div class="package-details">
                <span>Доходность: ${packageData.profitPercent}% в день</span>
                <span>Срок: ${packageData.duration} дней</span>
                <span>Лимиты: ${formatCurrency(packageData.minAmount)} - ${formatCurrency(packageData.maxAmount)}</span>
            </div>
        </div>
    `;
    
    // Сохранение данных для расчетов
    modal.dataset.profitPercent = packageData.profitPercent;
    modal.dataset.duration = packageData.duration;
    
    // Обновление расчетов
    updateModalCalculations();
    
    // Показ модального окна
    modal.classList.add('show');
    document.body.style.overflow = 'hidden';
    
    // Фокус на поле суммы
    setTimeout(() => {
        amountInput.focus();
        amountInput.select();
    }, 300);
}

function closeInvestmentModal() {
    const modal = document.getElementById('investmentModal');
    modal.classList.remove('show');
    document.body.style.overflow = '';
}

function updateModalCalculations() {
    const modal = document.getElementById('investmentModal');
    const amountInput = document.getElementById('modalAmount');
    const summaryDiv = document.getElementById('modalSummary');
    
    const amount = parseFloat(amountInput.value) || 0;
    const profitPercent = parseFloat(modal.dataset.profitPercent) || 0;
    const duration = parseInt(modal.dataset.duration) || 30;
    
    const dailyProfit = (amount * profitPercent) / 100;
    const totalProfit = dailyProfit * duration;
    const totalReturn = amount + totalProfit;
    
    summaryDiv.innerHTML = `
        <div class="summary-row">
            <span>Сумма инвестиции:</span>
            <span class="summary-amount">${formatCurrency(amount)}</span>
        </div>
        <div class="summary-row">
            <span>Ежедневная прибыль:</span>
            <span class="summary-daily">${formatCurrency(dailyProfit)}</span>
        </div>
        <div class="summary-row">
            <span>Общая прибыль за ${duration} дней:</span>
            <span class="summary-profit">${formatCurrency(totalProfit)}</span>
        </div>
        <div class="summary-row total">
            <span>Итого к получению:</span>
            <span class="summary-total">${formatCurrency(totalReturn)}</span>
        </div>
        <div class="summary-note">
            <i class="fas fa-info-circle"></i>
            <span>Прибыль начисляется ежедневно автоматически</span>
        </div>
    `;
}

// Анимации
function initInvestmentAnimations() {
    // Анимация появления карточек пакетов
    const packageCards = document.querySelectorAll('.package-card');
    const observer = new IntersectionObserver((entries) => {
        entries.forEach((entry, index) => {
            if (entry.isIntersecting) {
                setTimeout(() => {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }, index * 100);
                observer.unobserve(entry.target);
            }
        });
    });
    
    packageCards.forEach(card => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(30px)';
        card.style.transition = 'all 0.6s ease';
        observer.observe(card);
    });
    
    // Анимация статистических карточек
    const statCards = document.querySelectorAll('.investment-stats .stat-card');
    statCards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(20px)';
        
        setTimeout(() => {
            card.style.transition = 'all 0.6s ease';
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, index * 150);
    });
    
    // Анимация прогресс-баров
    const progressBars = document.querySelectorAll('.progress-fill');
    const progressObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const width = entry.target.style.width;
                entry.target.style.width = '0%';
                setTimeout(() => {
                    entry.target.style.width = width;
                }, 500);
                progressObserver.unobserve(entry.target);
            }
        });
    });
    
    progressBars.forEach(bar => {
        progressObserver.observe(bar);
    });
}

// Утилиты
function formatCurrency(amount) {
    return new Intl.NumberFormat('en-US', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
    }).format(amount) + ' USDT';
}

function showFieldWarning(field, message) {
    // Удаление существующего предупреждения
    const existingWarning = field.parentElement.querySelector('.field-warning');
    if (existingWarning) {
        existingWarning.remove();
    }
    
    // Добавление нового предупреждения
    const warningElement = document.createElement('div');
    warningElement.className = 'field-warning';
    warningElement.style.cssText = `
        color: var(--warning);
        font-size: var(--text-xs);
        margin-top: var(--space-1);
        display: flex;
        align-items: center;
        gap: var(--space-1);
    `;
    warningElement.innerHTML = `<i class="fas fa-exclamation-triangle"></i> ${message}`;
    
    field.parentElement.appendChild(warningElement);
    
    // Автоматическое скрытие через 3 секунды
    setTimeout(() => {
        if (warningElement.parentElement) {
            warningElement.remove();
        }
    }, 3000);
}

function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
        <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-triangle' : 'info-circle'}"></i>
        <span>${message}</span>
        <button class="notification-close">
            <i class="fas fa-times"></i>
        </button>
    `;
    
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: ${type === 'success' ? '#28a745' : type === 'error' ? '#dc3545' : '#17a2b8'};
        color: white;
        padding: 16px 20px;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        z-index: 9999;
        display: flex;
        align-items: center;
        gap: 12px;
        max-width: 400px;
        transform: translateX(100%);
        transition: transform 0.3s ease;
    `;
    
    document.body.appendChild(notification);
    
    // Анимация появления
    setTimeout(() => {
        notification.style.transform = 'translateX(0)';
    }, 100);
    
    // Обработчик закрытия
    const closeBtn = notification.querySelector('.notification-close');
    closeBtn.addEventListener('click', () => {
        hideNotification(notification);
    });
    
    // Автоматическое скрытие
    setTimeout(() => {
        hideNotification(notification);
    }, 5000);
}

function hideNotification(notification) {
    notification.style.transform = 'translateX(100%)';
    setTimeout(() => {
        if (notification.parentElement) {
            notification.parentElement.removeChild(notification);
        }
    }, 300);
}

// Добавление дополнительных стилей
const style = document.createElement('style');
style.textContent = `
    .package-info h4 {
        margin: 0 0 var(--space-2) 0;
        color: var(--gray-900);
    }
    
    .package-details {
        display: flex;
        flex-direction: column;
        gap: var(--space-1);
        font-size: var(--text-sm);
        color: var(--gray-600);
    }
    
    .summary-row {
        display: flex;
        justify-content: space-between;
        margin-bottom: var(--space-2);
        padding: var(--space-2) 0;
    }
    
    .summary-row.total {
        border-top: 1px solid var(--gray-300);
        padding-top: var(--space-3);
        font-weight: 600;
        font-size: var(--text-lg);
        color: var(--primary-green);
    }
    
    .summary-note {
        display: flex;
        align-items: center;
        gap: var(--space-2);
        margin-top: var(--space-3);
        padding: var(--space-3);
        background: rgba(46, 204, 113, 0.1);
        border-radius: var(--radius-md);
        font-size: var(--text-sm);
        color: var(--primary-green);
    }
    
    .notification-close {
        background: none;
        border: none;
        color: inherit;
        cursor: pointer;
        padding: 4px;
        border-radius: 4px;
        transition: background 0.2s ease;
    }
    
    .notification-close:hover {
        background: rgba(255, 255, 255, 0.2);
    }
`;
document.head.appendChild(style);
