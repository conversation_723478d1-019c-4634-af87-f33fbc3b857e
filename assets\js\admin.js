/**
 * AstroGenix - JavaScript для административной панели
 * Эко-майнинговая инвестиционная платформа
 */

document.addEventListener('DOMContentLoaded', function() {
    initializeAdmin();
});

function initializeAdmin() {
    // Инициализация графиков
    initAdminCharts();
    
    // Инициализация обработки транзакций
    initTransactionActions();
    
    // Инициализация модального окна
    initTransactionModal();
    
    // Инициализация автообновления
    initAutoRefresh();
    
    // Инициализация анимаций
    initAdminAnimations();
}

// Графики административной панели
function initAdminCharts() {
    initUsersChart();
    initFinanceChart();
}

function initUsersChart() {
    const canvas = document.getElementById('usersChart');
    if (!canvas) return;
    
    const ctx = canvas.getContext('2d');
    
    // Генерация данных для демонстрации
    const generateUsersData = (days) => {
        const data = [];
        const labels = [];
        const today = new Date();
        
        for (let i = days - 1; i >= 0; i--) {
            const date = new Date(today);
            date.setDate(date.getDate() - i);
            
            labels.push(date.toLocaleDateString('ru-RU', { 
                month: 'short', 
                day: 'numeric' 
            }));
            
            // Генерация данных с трендом роста
            const baseValue = Math.floor(Math.random() * 10) + 5;
            data.push(baseValue);
        }
        
        return { labels, data };
    };
    
    let currentPeriod = 7;
    let usersChartInstance = null;
    
    const createUsersChart = (period) => {
        const chartData = generateUsersData(period);
        
        if (usersChartInstance) {
            usersChartInstance.destroy();
        }
        
        usersChartInstance = new Chart(ctx, {
            type: 'line',
            data: {
                labels: chartData.labels,
                datasets: [{
                    label: 'Новые пользователи',
                    data: chartData.data,
                    borderColor: 'rgb(52, 152, 219)',
                    backgroundColor: 'rgba(52, 152, 219, 0.1)',
                    borderWidth: 3,
                    fill: true,
                    tension: 0.4,
                    pointBackgroundColor: 'rgb(52, 152, 219)',
                    pointBorderColor: '#fff',
                    pointBorderWidth: 2,
                    pointRadius: 5
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    x: {
                        grid: {
                            display: false
                        },
                        ticks: {
                            color: '#6C757D'
                        }
                    },
                    y: {
                        grid: {
                            color: 'rgba(0, 0, 0, 0.1)'
                        },
                        ticks: {
                            color: '#6C757D',
                            stepSize: 1
                        }
                    }
                }
            }
        });
    };
    
    // Инициализация графика
    createUsersChart(currentPeriod);
    
    // Обработчики кнопок периода
    const periodButtons = document.querySelectorAll('.chart-period');
    periodButtons.forEach(button => {
        button.addEventListener('click', function() {
            periodButtons.forEach(btn => btn.classList.remove('active'));
            this.classList.add('active');
            
            const period = parseInt(this.dataset.period);
            currentPeriod = period;
            createUsersChart(period);
        });
    });
}

function initFinanceChart() {
    const canvas = document.getElementById('financeChart');
    if (!canvas) return;
    
    const ctx = canvas.getContext('2d');
    
    new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: ['Депозиты', 'Инвестиции', 'Выводы', 'Прибыль'],
            datasets: [{
                data: [45, 30, 15, 10],
                backgroundColor: [
                    'rgba(46, 204, 113, 0.8)',
                    'rgba(52, 152, 219, 0.8)',
                    'rgba(231, 76, 60, 0.8)',
                    'rgba(241, 196, 15, 0.8)'
                ],
                borderColor: [
                    'rgb(46, 204, 113)',
                    'rgb(52, 152, 219)',
                    'rgb(231, 76, 60)',
                    'rgb(241, 196, 15)'
                ],
                borderWidth: 2
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom',
                    labels: {
                        padding: 20,
                        usePointStyle: true
                    }
                }
            }
        }
    });
}

// Обработка транзакций
function initTransactionActions() {
    const approveButtons = document.querySelectorAll('.approve-btn');
    const rejectButtons = document.querySelectorAll('.reject-btn');
    
    approveButtons.forEach(button => {
        button.addEventListener('click', function() {
            const transactionId = this.dataset.id;
            openTransactionModal(transactionId, 'approve');
        });
    });
    
    rejectButtons.forEach(button => {
        button.addEventListener('click', function() {
            const transactionId = this.dataset.id;
            openTransactionModal(transactionId, 'reject');
        });
    });
}

// Модальное окно транзакций
function initTransactionModal() {
    const modal = document.getElementById('transactionModal');
    const modalClose = document.getElementById('modalClose');
    const modalCancel = document.getElementById('modalCancel');
    const transactionForm = document.getElementById('transactionForm');
    
    if (!modal) return;
    
    // Закрытие модального окна
    [modalClose, modalCancel].forEach(element => {
        element.addEventListener('click', closeTransactionModal);
    });
    
    // Закрытие по клику на фон
    modal.addEventListener('click', function(e) {
        if (e.target === modal) {
            closeTransactionModal();
        }
    });
    
    // Закрытие по Escape
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape' && modal.classList.contains('show')) {
            closeTransactionModal();
        }
    });
    
    // Обработка формы
    transactionForm.addEventListener('submit', function(e) {
        e.preventDefault();
        processTransaction();
    });
}

function openTransactionModal(transactionId, action) {
    const modal = document.getElementById('transactionModal');
    const modalTitle = document.getElementById('modalTitle');
    const modalSubmit = document.getElementById('modalSubmit');
    const transactionIdInput = document.getElementById('transactionId');
    const transactionActionInput = document.getElementById('transactionAction');
    const adminNoteInput = document.getElementById('adminNote');
    
    // Заполнение данных
    transactionIdInput.value = transactionId;
    transactionActionInput.value = action;
    adminNoteInput.value = '';
    
    // Настройка заголовка и кнопки
    if (action === 'approve') {
        modalTitle.textContent = 'Одобрить транзакцию';
        modalSubmit.textContent = 'Одобрить';
        modalSubmit.className = 'btn btn-success';
        adminNoteInput.placeholder = 'Комментарий к одобрению (необязательно)';
    } else {
        modalTitle.textContent = 'Отклонить транзакцию';
        modalSubmit.textContent = 'Отклонить';
        modalSubmit.className = 'btn btn-error';
        adminNoteInput.placeholder = 'Укажите причину отклонения';
        adminNoteInput.required = true;
    }
    
    // Показ модального окна
    modal.classList.add('show');
    document.body.style.overflow = 'hidden';
    
    // Фокус на поле комментария
    setTimeout(() => {
        adminNoteInput.focus();
    }, 300);
}

function closeTransactionModal() {
    const modal = document.getElementById('transactionModal');
    modal.classList.remove('show');
    document.body.style.overflow = '';
    
    // Сброс формы
    document.getElementById('transactionForm').reset();
    document.getElementById('adminNote').required = false;
}

function processTransaction() {
    const transactionId = document.getElementById('transactionId').value;
    const action = document.getElementById('transactionAction').value;
    const adminNote = document.getElementById('adminNote').value;
    
    // Показ загрузки
    const submitButton = document.getElementById('modalSubmit');
    const originalText = submitButton.textContent;
    submitButton.disabled = true;
    submitButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Обработка...';
    
    // Симуляция AJAX запроса
    setTimeout(() => {
        // Здесь должен быть реальный AJAX запрос
        console.log('Processing transaction:', { transactionId, action, adminNote });
        
        // Закрытие модального окна
        closeTransactionModal();
        
        // Показ уведомления
        const actionText = action === 'approve' ? 'одобрена' : 'отклонена';
        showNotification(`Транзакция успешно ${actionText}!`, 'success');
        
        // Удаление строки из таблицы
        const row = document.querySelector(`[data-id="${transactionId}"]`).closest('tr');
        if (row) {
            row.style.opacity = '0';
            setTimeout(() => {
                row.remove();
            }, 300);
        }
        
        // Восстановление кнопки
        submitButton.disabled = false;
        submitButton.textContent = originalText;
        
    }, 1500);
}

// Автообновление данных
function initAutoRefresh() {
    // Обновление каждые 30 секунд
    setInterval(() => {
        refreshAdminData();
    }, 30000);
    
    // Обновление при фокусе на вкладке
    document.addEventListener('visibilitychange', () => {
        if (!document.hidden) {
            refreshAdminData();
        }
    });
}

function refreshAdminData() {
    // Здесь можно добавить AJAX запросы для обновления данных
    console.log('Обновление данных административной панели...');
    
    // Обновление счетчиков в боковой панели
    updateSidebarCounters();
}

function updateSidebarCounters() {
    // Симуляция обновления счетчиков
    const badges = document.querySelectorAll('.nav-badge');
    badges.forEach(badge => {
        badge.style.animation = 'pulse 0.5s ease';
        setTimeout(() => {
            badge.style.animation = '';
        }, 500);
    });
}

// Анимации
function initAdminAnimations() {
    // Анимация статистических карточек
    const statCards = document.querySelectorAll('.admin-stat-card');
    statCards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(30px)';
        
        setTimeout(() => {
            card.style.transition = 'all 0.6s ease';
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, index * 100);
    });
    
    // Анимация таблиц
    const tableCards = document.querySelectorAll('.admin-table-card');
    const tableObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
            }
        });
    });
    
    tableCards.forEach(card => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(20px)';
        card.style.transition = 'all 0.5s ease';
        tableObserver.observe(card);
    });
    
    // Анимация строк таблицы
    const tableRows = document.querySelectorAll('.admin-table tbody tr');
    tableRows.forEach((row, index) => {
        row.style.opacity = '0';
        row.style.transform = 'translateX(-20px)';
        
        setTimeout(() => {
            row.style.transition = 'all 0.3s ease';
            row.style.opacity = '1';
            row.style.transform = 'translateX(0)';
        }, index * 50);
    });
}

// Утилиты
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
        <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-triangle' : 'info-circle'}"></i>
        <span>${message}</span>
        <button class="notification-close">
            <i class="fas fa-times"></i>
        </button>
    `;
    
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: ${type === 'success' ? '#28a745' : type === 'error' ? '#dc3545' : '#17a2b8'};
        color: white;
        padding: 16px 20px;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        z-index: 9999;
        display: flex;
        align-items: center;
        gap: 12px;
        max-width: 400px;
        transform: translateX(100%);
        transition: transform 0.3s ease;
    `;
    
    document.body.appendChild(notification);
    
    // Анимация появления
    setTimeout(() => {
        notification.style.transform = 'translateX(0)';
    }, 100);
    
    // Обработчик закрытия
    const closeBtn = notification.querySelector('.notification-close');
    closeBtn.addEventListener('click', () => {
        hideNotification(notification);
    });
    
    // Автоматическое скрытие
    setTimeout(() => {
        hideNotification(notification);
    }, 5000);
}

function hideNotification(notification) {
    notification.style.transform = 'translateX(100%)';
    setTimeout(() => {
        if (notification.parentElement) {
            notification.parentElement.removeChild(notification);
        }
    }, 300);
}

// Экспорт данных
function exportData(type, format = 'csv') {
    console.log(`Экспорт данных: ${type} в формате ${format}`);
    showNotification(`Экспорт ${type} начат. Файл будет загружен автоматически.`, 'info');
}

// Добавление дополнительных стилей
const style = document.createElement('style');
style.textContent = `
    .notification-close {
        background: none;
        border: none;
        color: inherit;
        cursor: pointer;
        padding: 4px;
        border-radius: 4px;
        transition: background 0.2s ease;
    }
    
    .notification-close:hover {
        background: rgba(255, 255, 255, 0.2);
    }
    
    @keyframes pulse {
        0% { transform: scale(1); }
        50% { transform: scale(1.1); }
        100% { transform: scale(1); }
    }
    
    .modal {
        opacity: 0;
        transition: opacity 0.3s ease;
    }
    
    .modal.show {
        opacity: 1;
    }
    
    .modal-content {
        transform: scale(0.9);
        transition: transform 0.3s ease;
    }
    
    .modal.show .modal-content {
        transform: scale(1);
    }
`;
document.head.appendChild(style);
