<?php
/**
 * AstroGenix - Шаблон страницы
 * Унифицированный шаблон для всех страниц
 */

// Проверка сессии
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Подключение конфигурации и функций
require_once 'config/config.php';
require_once 'config/functions.php';

// Настройки по умолчанию
$page_title = $page_title ?? 'AstroGenix - Эко-майнинговая инвестиционная платформа';
$page_description = $page_description ?? 'Инвестируйте в будущее с AstroGenix - эко-майнинговой платформой, объединяющей прибыльные инвестиции с заботой об окружающей среде.';
$page_keywords = $page_keywords ?? 'инвестиции, майнинг, экология, криптовалюта, USDT, прибыль';
$page_image = $page_image ?? 'assets/images/og-image.jpg';
$canonical_url = $canonical_url ?? 'https://' . $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI'];

// Дополнительные CSS файлы
$additional_css = $additional_css ?? [];

// Дополнительные JS файлы
$additional_js = $additional_js ?? [];

// Проверка авторизации для защищенных страниц
if (isset($require_auth) && $require_auth && !is_logged_in()) {
    header('Location: login.php');
    exit();
}

// Проверка прав администратора
if (isset($require_admin) && $require_admin && !is_admin()) {
    header('Location: dashboard.php');
    exit();
}

// CSRF токен для форм
$csrf_token = generate_csrf_token();
?>
<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($page_title); ?></title>
    
    <!-- SEO Meta Tags -->
    <meta name="description" content="<?php echo htmlspecialchars($page_description); ?>">
    <meta name="keywords" content="<?php echo htmlspecialchars($page_keywords); ?>">
    <meta name="author" content="AstroGenix">
    <meta name="robots" content="index, follow">
    <link rel="canonical" href="<?php echo htmlspecialchars($canonical_url); ?>">
    
    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="<?php echo htmlspecialchars($page_title); ?>">
    <meta property="og:description" content="<?php echo htmlspecialchars($page_description); ?>">
    <meta property="og:image" content="<?php echo htmlspecialchars($page_image); ?>">
    <meta property="og:url" content="<?php echo htmlspecialchars($canonical_url); ?>">
    <meta property="og:type" content="website">
    <meta property="og:site_name" content="AstroGenix">
    
    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="<?php echo htmlspecialchars($page_title); ?>">
    <meta name="twitter:description" content="<?php echo htmlspecialchars($page_description); ?>">
    <meta name="twitter:image" content="<?php echo htmlspecialchars($page_image); ?>">
    
    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="assets/images/favicon.svg">
    <link rel="icon" type="image/png" href="assets/images/favicon.png">
    <link rel="apple-touch-icon" href="assets/images/apple-touch-icon.png">
    
    <!-- Preload Critical Resources -->
    <link rel="preload" href="assets/css/style.css" as="style">
    <link rel="preload" href="assets/js/main.js" as="script">
    <link rel="preload" href="assets/fonts/inter.woff2" as="font" type="font/woff2" crossorigin>
    
    <!-- CSS -->
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="assets/css/unified-components.css">
    <link rel="stylesheet" href="assets/css/animations.css">
    
    <!-- Additional CSS -->
    <?php foreach ($additional_css as $css): ?>
        <link rel="stylesheet" href="<?php echo htmlspecialchars($css); ?>">
    <?php endforeach; ?>
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <!-- Structured Data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "Organization",
        "name": "AstroGenix",
        "url": "<?php echo htmlspecialchars($canonical_url); ?>",
        "logo": "<?php echo 'https://' . $_SERVER['HTTP_HOST']; ?>/assets/images/logo.svg",
        "description": "<?php echo htmlspecialchars($page_description); ?>",
        "sameAs": [
            "https://t.me/astrogenix_official",
            "https://twitter.com/astrogenix",
            "https://discord.gg/astrogenix"
        ]
    }
    </script>
    
    <!-- Security Headers -->
    <meta http-equiv="X-Content-Type-Options" content="nosniff">
    <meta http-equiv="X-Frame-Options" content="DENY">
    <meta http-equiv="X-XSS-Protection" content="1; mode=block">
    <meta http-equiv="Referrer-Policy" content="strict-origin-when-cross-origin">
    
    <!-- Theme Color -->
    <meta name="theme-color" content="#2ECC71">
    <meta name="msapplication-TileColor" content="#2ECC71">
    
    <!-- Preloader -->
    <?php if (!isset($disable_preloader) || !$disable_preloader): ?>
    <style>
        .preloader {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #2ECC71 0%, #8E44AD 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 99999;
            transition: opacity 0.5s ease;
        }
        
        .preloader-content {
            text-align: center;
            color: white;
        }
        
        .preloader-logo {
            width: 80px;
            height: 80px;
            margin-bottom: 20px;
            animation: pulse 2s infinite;
        }
        
        .preloader-text {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 20px;
        }
        
        .loading-progress {
            width: 200px;
            height: 4px;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 2px;
            overflow: hidden;
        }
        
        .loading-bar {
            height: 100%;
            background: white;
            border-radius: 2px;
            width: 0%;
            transition: width 0.3s ease;
        }
        
        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }
    </style>
    <?php endif; ?>
</head>
<body class="<?php echo $body_class ?? ''; ?>">
    <!-- Preloader -->
    <?php if (!isset($disable_preloader) || !$disable_preloader): ?>
    <div class="preloader" id="preloader">
        <div class="preloader-content">
            <img src="assets/images/logo.svg" alt="AstroGenix" class="preloader-logo">
            <div class="preloader-text">AstroGenix</div>
            <div class="loading-progress">
                <div class="loading-bar" id="loading-bar"></div>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <!-- Skip to Content -->
    <a href="#main-content" class="skip-to-content">Перейти к содержимому</a>

    <!-- Header Navigation -->
    <?php if (!isset($hide_navigation) || !$hide_navigation): ?>
    <header class="header">
        <nav class="navbar">
            <div class="container">
                <div class="nav-brand">
                    <a href="<?php echo is_logged_in() ? 'dashboard.php' : 'index.php'; ?>" class="brand-link">
                        <img src="assets/images/logo.svg" alt="AstroGenix" class="logo">
                        <span class="brand-text">AstroGenix</span>
                    </a>
                </div>
                
                <?php if (is_logged_in()): ?>
                    <!-- Авторизованное меню -->
                    <div class="nav-menu">
                        <a href="dashboard.php" class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'dashboard.php' ? 'active' : ''; ?>">
                            <i class="fas fa-tachometer-alt"></i>
                            Дашборд
                        </a>
                        <a href="investments.php" class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'investments.php' ? 'active' : ''; ?>">
                            <i class="fas fa-chart-line"></i>
                            Инвестиции
                        </a>
                        <a href="investment-portfolio.php" class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'investment-portfolio.php' ? 'active' : ''; ?>">
                            <i class="fas fa-briefcase"></i>
                            Портфель
                        </a>
                        <a href="leaderboards.php" class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'leaderboards.php' ? 'active' : ''; ?>">
                            <i class="fas fa-trophy"></i>
                            Рейтинги
                        </a>
                        <a href="financial-operations.php" class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'financial-operations.php' ? 'active' : ''; ?>">
                            <i class="fas fa-wallet"></i>
                            Финансы
                        </a>
                        <a href="referrals.php" class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'referrals.php' ? 'active' : ''; ?>">
                            <i class="fas fa-users"></i>
                            Рефералы
                        </a>
                        
                        <!-- Пользовательское меню -->
                        <div class="user-menu dropdown">
                            <button class="user-menu-toggle dropdown-toggle">
                                <div class="user-avatar">
                                    <?php $current_user = get_current_user(); ?>
                                    <?php if ($current_user && $current_user['avatar']): ?>
                                        <img src="<?php echo htmlspecialchars($current_user['avatar']); ?>" alt="Avatar">
                                    <?php else: ?>
                                        <i class="fas fa-user"></i>
                                    <?php endif; ?>
                                </div>
                                <span class="user-name"><?php echo htmlspecialchars($current_user['username'] ?? 'Пользователь'); ?></span>
                                <i class="fas fa-chevron-down"></i>
                            </button>
                            <div class="dropdown-menu">
                                <a href="profile.php" class="dropdown-item">
                                    <i class="fas fa-user-circle"></i>
                                    Профиль
                                </a>
                                <a href="settings.php" class="dropdown-item">
                                    <i class="fas fa-cog"></i>
                                    Настройки
                                </a>
                                <div class="dropdown-divider"></div>
                                <a href="logout.php" class="dropdown-item">
                                    <i class="fas fa-sign-out-alt"></i>
                                    Выйти
                                </a>
                            </div>
                        </div>
                    </div>
                <?php else: ?>
                    <!-- Гостевое меню -->
                    <div class="nav-menu">
                        <a href="features.php" class="nav-link">Возможности</a>
                        <a href="packages.php" class="nav-link">Тарифы</a>
                        <a href="about.php" class="nav-link">О нас</a>
                        <a href="contact.php" class="nav-link">Контакты</a>
                        <a href="login.php" class="btn btn-outline">Войти</a>
                        <a href="register.php" class="btn btn-primary">Регистрация</a>
                    </div>
                <?php endif; ?>
                
                <!-- Мобильное меню -->
                <button class="mobile-menu-toggle" id="mobile-menu-toggle">
                    <span></span>
                    <span></span>
                    <span></span>
                </button>
            </div>
        </nav>
        
        <!-- Мобильная навигация -->
        <div class="mobile-nav" id="mobile-nav">
            <div class="mobile-nav-content">
                <?php if (is_logged_in()): ?>
                    <a href="dashboard.php" class="mobile-nav-link">
                        <i class="fas fa-tachometer-alt"></i>
                        Дашборд
                    </a>
                    <a href="investments.php" class="mobile-nav-link">
                        <i class="fas fa-chart-line"></i>
                        Инвестиции
                    </a>
                    <a href="investment-portfolio.php" class="mobile-nav-link">
                        <i class="fas fa-briefcase"></i>
                        Портфель
                    </a>
                    <a href="leaderboards.php" class="mobile-nav-link">
                        <i class="fas fa-trophy"></i>
                        Рейтинги
                    </a>
                    <a href="financial-operations.php" class="mobile-nav-link">
                        <i class="fas fa-wallet"></i>
                        Финансы
                    </a>
                    <a href="referrals.php" class="mobile-nav-link">
                        <i class="fas fa-users"></i>
                        Рефералы
                    </a>
                    <div class="mobile-nav-divider"></div>
                    <a href="profile.php" class="mobile-nav-link">
                        <i class="fas fa-user-circle"></i>
                        Профиль
                    </a>
                    <a href="settings.php" class="mobile-nav-link">
                        <i class="fas fa-cog"></i>
                        Настройки
                    </a>
                    <a href="logout.php" class="mobile-nav-link">
                        <i class="fas fa-sign-out-alt"></i>
                        Выйти
                    </a>
                <?php else: ?>
                    <a href="features.php" class="mobile-nav-link">Возможности</a>
                    <a href="packages.php" class="mobile-nav-link">Тарифы</a>
                    <a href="about.php" class="mobile-nav-link">О нас</a>
                    <a href="contact.php" class="mobile-nav-link">Контакты</a>
                    <div class="mobile-nav-divider"></div>
                    <a href="login.php" class="mobile-nav-link">Войти</a>
                    <a href="register.php" class="mobile-nav-link">Регистрация</a>
                <?php endif; ?>
            </div>
        </div>
    </header>
    <?php endif; ?>

    <!-- Main Content -->
    <main class="main-content" id="main-content">
        <?php
        // Здесь будет содержимое страницы
        if (isset($page_content)) {
            echo $page_content;
        }
        ?>
    </main>

    <!-- Footer -->
    <?php if (!isset($hide_footer) || !$hide_footer): ?>
        <?php include 'includes/footer.php'; ?>
    <?php endif; ?>

    <!-- JavaScript -->
    <script>
        // Глобальные переменные
        window.CSRF_TOKEN = '<?php echo $csrf_token; ?>';
        window.USER_ID = <?php echo is_logged_in() ? $_SESSION['user_id'] : 'null'; ?>;
        window.IS_LOGGED_IN = <?php echo is_logged_in() ? 'true' : 'false'; ?>;
        window.BASE_URL = '<?php echo 'https://' . $_SERVER['HTTP_HOST']; ?>';
    </script>
    
    <!-- Core JavaScript -->
    <script src="assets/js/main.js"></script>
    
    <!-- Additional JavaScript -->
    <?php foreach ($additional_js as $js): ?>
        <script src="<?php echo htmlspecialchars($js); ?>"></script>
    <?php endforeach; ?>

    <!-- Back to Top Button -->
    <button class="back-to-top" id="back-to-top" title="Наверх">
        <i class="fas fa-chevron-up"></i>
    </button>

    <!-- Notification Container -->
    <div id="notification-container"></div>
</body>
</html>
