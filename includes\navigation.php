<?php
/**
 * AstroGenix - Навигационное меню
 * Эко-майнинговая инвестиционная платформа
 */

// Определение текущей страницы для подсветки активного пункта меню
$current_page = basename($_SERVER['PHP_SELF'], '.php');
?>
    <!-- Header Navigation -->
    <header class="header">
        <nav class="navbar">
            <div class="container">
                <div class="nav-brand">
                    <a href="<?php echo is_logged_in() ? 'dashboard.php' : 'index.php'; ?>" class="brand-link">
                        <img src="assets/images/logo.svg" alt="AstroGenix" class="logo">
                        <span class="brand-text">AstroGenix</span>
                    </a>
                </div>

                <?php if (is_logged_in()): ?>
                    <!-- Авторизованное меню -->
                    <div class="nav-menu">
                        <a href="dashboard.php" class="nav-link <?php echo $current_page == 'dashboard' ? 'active' : ''; ?>">
                            <i class="fas fa-tachometer-alt"></i>
                            Дашборд
                        </a>
                        <a href="investments.php" class="nav-link <?php echo $current_page == 'investments' ? 'active' : ''; ?>">
                            <i class="fas fa-chart-line"></i>
                            Инвестиции
                        </a>
                        <a href="investment-portfolio.php" class="nav-link <?php echo $current_page == 'investment-portfolio' ? 'active' : ''; ?>">
                            <i class="fas fa-briefcase"></i>
                            Портфель
                        </a>
                        <a href="leaderboards.php" class="nav-link <?php echo $current_page == 'leaderboards' ? 'active' : ''; ?>">
                            <i class="fas fa-trophy"></i>
                            Рейтинги
                        </a>
                        <a href="financial-operations.php" class="nav-link <?php echo $current_page == 'financial-operations' ? 'active' : ''; ?>">
                            <i class="fas fa-wallet"></i>
                            Финансы
                        </a>
                        <a href="referrals.php" class="nav-link <?php echo $current_page == 'referrals' ? 'active' : ''; ?>">
                            <i class="fas fa-users"></i>
                            Рефералы
                        </a>

                        <!-- Пользовательское меню -->
                        <div class="user-menu dropdown">
                            <button class="user-menu-toggle dropdown-toggle">
                                <div class="user-avatar">
                                    <?php if (isset($_SESSION['avatar']) && $_SESSION['avatar']): ?>
                                        <img src="<?php echo htmlspecialchars($_SESSION['avatar']); ?>" alt="Avatar">
                                    <?php else: ?>
                                        <i class="fas fa-user"></i>
                                    <?php endif; ?>
                                </div>
                                <span class="user-name"><?php echo htmlspecialchars($_SESSION['username'] ?? 'Пользователь'); ?></span>
                                <i class="fas fa-chevron-down"></i>
                            </button>
                            <div class="dropdown-menu">
                                <a href="profile.php" class="dropdown-item">
                                    <i class="fas fa-user-circle"></i>
                                    Профиль
                                </a>
                                <a href="settings.php" class="dropdown-item">
                                    <i class="fas fa-cog"></i>
                                    Настройки
                                </a>
                                <?php if (is_admin()): ?>
                                    <div class="dropdown-divider"></div>
                                    <a href="admin/dashboard.php" class="dropdown-item">
                                        <i class="fas fa-shield-alt"></i>
                                        Админ-панель
                                    </a>
                                <?php endif; ?>
                                <div class="dropdown-divider"></div>
                                <a href="logout.php" class="dropdown-item">
                                    <i class="fas fa-sign-out-alt"></i>
                                    Выйти
                                </a>
                            </div>
                        </div>
                    </div>
                <?php else: ?>
                    <!-- Гостевое меню -->
                    <div class="nav-menu">
                        <a href="index.php" class="nav-link <?php echo $current_page == 'index' ? 'active' : ''; ?>">Главная</a>
                        <a href="features.php" class="nav-link <?php echo $current_page == 'features' ? 'active' : ''; ?>">Возможности</a>
                        <a href="packages.php" class="nav-link <?php echo $current_page == 'packages' ? 'active' : ''; ?>">Тарифы</a>
                        <a href="about.php" class="nav-link <?php echo $current_page == 'about' ? 'active' : ''; ?>">О нас</a>
                        <a href="contact.php" class="nav-link <?php echo $current_page == 'contact' ? 'active' : ''; ?>">Контакты</a>
                        <a href="support.php" class="nav-link <?php echo $current_page == 'support' ? 'active' : ''; ?>">Поддержка</a>
                        <a href="login.php" class="btn btn-outline">Войти</a>
                        <a href="register.php" class="btn btn-primary">Регистрация</a>
                    </div>
                <?php endif; ?>

                <!-- Мобильное меню -->
                <div class="mobile-menu-toggle">
                    <i class="fas fa-bars"></i>
                </div>
            </div>
        </nav>
    </header>
