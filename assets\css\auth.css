/**
 * AstroGenix - Стили аутентификации
 * Эко-майнинговая инвестиционная платформа
 */

/* Страница аутентификации */
.auth-page {
    min-height: 100vh;
    background: var(--gradient-primary);
    position: relative;
    overflow: hidden;
}

.auth-container {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: var(--space-4);
    position: relative;
    z-index: 2;
}

.auth-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: 
        radial-gradient(circle at 20% 80%, rgba(46, 204, 113, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(142, 68, 173, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(88, 214, 141, 0.2) 0%, transparent 50%);
    animation: backgroundShift 20s ease-in-out infinite;
}

@keyframes backgroundShift {
    0%, 100% { transform: scale(1) rotate(0deg); }
    50% { transform: scale(1.1) rotate(5deg); }
}

.auth-content {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: var(--radius-xl);
    padding: var(--space-8);
    width: 100%;
    max-width: 480px;
    box-shadow: var(--shadow-xl);
    border: 1px solid rgba(255, 255, 255, 0.2);
    animation: slideUp 0.6s ease-out;
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Заголовок аутентификации */
.auth-header {
    text-align: center;
    margin-bottom: var(--space-8);
}

.auth-logo {
    display: inline-flex;
    align-items: center;
    gap: var(--space-3);
    margin-bottom: var(--space-6);
    text-decoration: none;
    color: var(--gray-900);
    font-size: var(--text-xl);
    font-weight: 600;
}

.auth-logo img {
    width: 40px;
    height: 40px;
}

.auth-header h1 {
    font-size: var(--text-3xl);
    margin-bottom: var(--space-2);
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.auth-header p {
    color: var(--gray-600);
    margin-bottom: 0;
}

/* Форма аутентификации */
.auth-form {
    margin-bottom: var(--space-6);
}

.input-with-icon {
    position: relative;
}

.input-with-icon i {
    position: absolute;
    left: var(--space-3);
    top: 50%;
    transform: translateY(-50%);
    color: var(--gray-500);
    z-index: 1;
}

.input-with-icon input {
    padding-left: var(--space-10);
}

.password-input {
    position: relative;
}

.password-toggle {
    position: absolute;
    right: var(--space-3);
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: var(--gray-500);
    cursor: pointer;
    padding: var(--space-2);
    border-radius: var(--radius-sm);
    transition: color var(--transition-fast);
}

.password-toggle:hover {
    color: var(--primary-green);
}

/* Опции формы */
.form-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--space-6);
}

.checkbox-label {
    display: flex;
    align-items: center;
    gap: var(--space-2);
    cursor: pointer;
    font-size: var(--text-sm);
    color: var(--gray-600);
}

.checkbox-label input[type="checkbox"] {
    width: auto;
    margin: 0;
}

.checkmark {
    width: 18px;
    height: 18px;
    border: 2px solid var(--gray-300);
    border-radius: var(--radius-sm);
    position: relative;
    transition: all var(--transition-fast);
}

.checkbox-label input[type="checkbox"] {
    position: absolute;
    opacity: 0;
    cursor: pointer;
}

.checkbox-label input[type="checkbox"]:checked + .checkmark {
    background: var(--gradient-primary);
    border-color: var(--primary-green);
}

.checkbox-label input[type="checkbox"]:checked + .checkmark::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 12px;
    font-weight: bold;
}

.forgot-link {
    font-size: var(--text-sm);
    color: var(--primary-green);
    text-decoration: none;
    transition: color var(--transition-fast);
}

.forgot-link:hover {
    color: var(--dark-green);
}

/* Разделитель */
.auth-divider {
    text-align: center;
    margin: var(--space-6) 0;
    position: relative;
}

.auth-divider::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 1px;
    background: var(--gray-300);
}

.auth-divider span {
    background: rgba(255, 255, 255, 0.95);
    padding: 0 var(--space-4);
    color: var(--gray-500);
    font-size: var(--text-sm);
}

/* Социальные кнопки */
.social-login {
    display: flex;
    flex-direction: column;
    gap: var(--space-3);
    margin-bottom: var(--space-6);
}

.btn-social {
    background: var(--white);
    color: var(--gray-700);
    border: 2px solid var(--gray-300);
    justify-content: flex-start;
    padding-left: var(--space-4);
}

.btn-social:hover {
    border-color: var(--primary-green);
    color: var(--primary-green);
}

.btn-google:hover {
    border-color: #db4437;
    color: #db4437;
}

.btn-vk:hover {
    border-color: #4c75a3;
    color: #4c75a3;
}

/* Подвал аутентификации */
.auth-footer {
    text-align: center;
    color: var(--gray-600);
    font-size: var(--text-sm);
}

.auth-footer a {
    color: var(--primary-green);
    font-weight: 500;
    text-decoration: none;
}

.auth-footer a:hover {
    color: var(--dark-green);
}

/* Анимация энергетических сфер */
.energy-orbs {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 1;
}

.energy-orb {
    position: absolute;
    width: 60px;
    height: 60px;
    background: radial-gradient(circle, rgba(46, 204, 113, 0.3) 0%, transparent 70%);
    border-radius: 50%;
    animation: floatOrb 15s infinite linear;
}

.energy-orb:nth-child(1) {
    top: 10%;
    left: 10%;
    animation-delay: 0s;
    animation-duration: 20s;
}

.energy-orb:nth-child(2) {
    top: 20%;
    right: 15%;
    animation-delay: -5s;
    animation-duration: 25s;
}

.energy-orb:nth-child(3) {
    bottom: 30%;
    left: 20%;
    animation-delay: -10s;
    animation-duration: 18s;
}

.energy-orb:nth-child(4) {
    bottom: 20%;
    right: 10%;
    animation-delay: -15s;
    animation-duration: 22s;
}

.energy-orb:nth-child(5) {
    top: 50%;
    left: 50%;
    animation-delay: -8s;
    animation-duration: 30s;
}

@keyframes floatOrb {
    0% {
        transform: translate(0, 0) scale(1);
        opacity: 0.3;
    }
    25% {
        transform: translate(30px, -30px) scale(1.2);
        opacity: 0.6;
    }
    50% {
        transform: translate(-20px, -60px) scale(0.8);
        opacity: 0.4;
    }
    75% {
        transform: translate(-40px, -20px) scale(1.1);
        opacity: 0.7;
    }
    100% {
        transform: translate(0, 0) scale(1);
        opacity: 0.3;
    }
}

/* Эко-частицы */
.eco-particles {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: 
        radial-gradient(2px 2px at 20px 30px, rgba(46, 204, 113, 0.3), transparent),
        radial-gradient(2px 2px at 40px 70px, rgba(142, 68, 173, 0.3), transparent),
        radial-gradient(1px 1px at 90px 40px, rgba(88, 214, 141, 0.4), transparent),
        radial-gradient(1px 1px at 130px 80px, rgba(187, 143, 206, 0.4), transparent);
    background-repeat: repeat;
    background-size: 150px 100px;
    animation: particleFloat 20s linear infinite;
}

@keyframes particleFloat {
    0% { transform: translate(0, 0); }
    100% { transform: translate(-150px, -100px); }
}

/* Адаптивность */
@media (max-width: 768px) {
    .auth-content {
        padding: var(--space-6);
        margin: var(--space-4);
    }
    
    .auth-header h1 {
        font-size: var(--text-2xl);
    }
    
    .social-login {
        flex-direction: column;
    }
    
    .form-options {
        flex-direction: column;
        gap: var(--space-3);
        align-items: flex-start;
    }
}

@media (max-width: 480px) {
    .auth-content {
        padding: var(--space-4);
    }
    
    .energy-orb {
        width: 40px;
        height: 40px;
    }
}
