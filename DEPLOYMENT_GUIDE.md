# Руководство по развертыванию AstroGenix

## Подготовка к развертыванию

### 1. Резервное копирование
```bash
# Создание бэкапа базы данных
mysqldump -u username -p database_name > backup_$(date +%Y%m%d_%H%M%S).sql

# Создание бэкапа файлов
tar -czf files_backup_$(date +%Y%m%d_%H%M%S).tar.gz /path/to/website/
```

### 2. Проверка требований сервера
- PHP 7.4 или выше
- MySQL 5.7 или выше
- Apache/Nginx с mod_rewrite
- SSL сертификат
- Достаточно места на диске (минимум 1GB)

## Пошаговое развертывание

### Шаг 1: Загрузка файлов
```bash
# Загрузка новых файлов на сервер
scp -r ./new_files/ user@server:/path/to/website/

# Или через FTP/SFTP
# Загрузить все новые файлы:
# - features.php
# - packages.php
# - about.php
# - contact.php
# - support.php
# - faq.php
# - terms.php
# - privacy.php
# - help.php
# - assets/css/pages.css
# - migration_to_usdt.sql
```

### Шаг 2: Обновление существующих файлов
Обновить следующие файлы с изменениями:
- `index.php` - убраны пакеты, обновлена навигация
- `login.php` - исправлен дизайн поля пароля
- `config/config.php` - обновлена функция форматирования валюты
- `includes/footer.php` - добавлены новые ссылки и бейджи
- `assets/css/style.css` - добавлены стили для футера
- `assets/css/animations.css` - новые анимации
- `assets/js/*.js` - обновлено форматирование валюты
- `deposit.php` и `withdraw.php` - изменены лимиты USDT
- `cron/daily_profits.php` - обновлено логирование

### Шаг 3: Миграция базы данных
```sql
-- Выполнить миграцию (ОСТОРОЖНО!)
-- Сначала на тестовой базе данных
mysql -u username -p database_name < migration_to_usdt.sql

-- Проверить результат
SELECT * FROM investment_packages;
SELECT * FROM system_settings WHERE setting_key = 'default_currency';
```

### Шаг 4: Настройка прав доступа
```bash
# Установка правильных прав доступа
chmod 644 *.php
chmod 644 assets/css/*.css
chmod 644 assets/js/*.js
chmod 755 assets/images/
chmod 600 config/config.php  # Конфиденциальный файл
```

### Шаг 5: Проверка конфигурации веб-сервера

#### Apache (.htaccess)
```apache
RewriteEngine On
RewriteCond %{HTTPS} off
RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]

# Кэширование статических файлов
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType image/png "access plus 1 year"
    ExpiresByType image/jpg "access plus 1 year"
    ExpiresByType image/jpeg "access plus 1 year"
    ExpiresByType image/gif "access plus 1 year"
    ExpiresByType image/svg+xml "access plus 1 year"
</IfModule>
```

#### Nginx
```nginx
server {
    listen 80;
    server_name yourdomain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl;
    server_name yourdomain.com;
    
    ssl_certificate /path/to/certificate.crt;
    ssl_certificate_key /path/to/private.key;
    
    root /path/to/website;
    index index.php;
    
    location ~ \.php$ {
        fastcgi_pass unix:/var/run/php/php7.4-fpm.sock;
        fastcgi_index index.php;
        include fastcgi_params;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
    }
    
    location ~* \.(css|js|png|jpg|jpeg|gif|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}
```

## Тестирование после развертывания

### 1. Функциональное тестирование
```bash
# Проверка доступности страниц
curl -I https://yourdomain.com/features.php
curl -I https://yourdomain.com/packages.php
curl -I https://yourdomain.com/about.php
curl -I https://yourdomain.com/contact.php
curl -I https://yourdomain.com/support.php
curl -I https://yourdomain.com/faq.php
curl -I https://yourdomain.com/terms.php
curl -I https://yourdomain.com/privacy.php
curl -I https://yourdomain.com/help.php

# Все должны возвращать HTTP 200
```

### 2. Проверка базы данных
```sql
-- Проверка миграции
SELECT COUNT(*) FROM investment_packages; -- Должно быть 3
SELECT setting_value FROM system_settings WHERE setting_key = 'default_currency'; -- Должно быть 'USDT'

-- Проверка пользовательских данных
SELECT AVG(balance) FROM users WHERE balance > 0; -- Проверить разумность сумм
```

### 3. Проверка производительности
- Время загрузки страниц < 3 секунд
- Размер CSS/JS файлов оптимизирован
- Изображения сжаты

## Мониторинг и логирование

### 1. Настройка логирования ошибок
```php
// В config/config.php добавить:
ini_set('log_errors', 1);
ini_set('error_log', '/path/to/logs/php_errors.log');
```

### 2. Мониторинг доступности
Настроить мониторинг для проверки:
- Доступности сайта
- Времени отклика
- Ошибок 404/500
- Использования ресурсов сервера

### 3. Аналитика
- Google Analytics
- Яндекс.Метрика
- Внутренняя аналитика платформы

## Откат изменений (если необходимо)

### 1. Откат файлов
```bash
# Восстановление из бэкапа
tar -xzf files_backup_YYYYMMDD_HHMMSS.tar.gz -C /path/to/website/
```

### 2. Откат базы данных
```bash
# Восстановление базы данных
mysql -u username -p database_name < backup_YYYYMMDD_HHMMSS.sql
```

## Контрольный список развертывания

### Перед развертыванием
- [ ] Создан бэкап базы данных
- [ ] Создан бэкап файлов
- [ ] Протестировано на тестовом сервере
- [ ] Проверены права доступа к файлам
- [ ] Уведомлены пользователи о техническом обслуживании

### Во время развертывания
- [ ] Загружены все новые файлы
- [ ] Обновлены существующие файлы
- [ ] Выполнена миграция базы данных
- [ ] Настроены права доступа
- [ ] Проверена конфигурация веб-сервера

### После развертывания
- [ ] Проверена доступность всех страниц
- [ ] Протестированы формы
- [ ] Проверена корректность отображения валюты
- [ ] Протестирована мобильная версия
- [ ] Проверены анимации в разных браузерах
- [ ] Настроен мониторинг
- [ ] Обновлена документация

## Поддержка и обслуживание

### Регулярные задачи
- Мониторинг логов ошибок
- Проверка производительности
- Обновление контента
- Резервное копирование

### Обновления безопасности
- Регулярное обновление PHP
- Обновление SSL сертификатов
- Мониторинг уязвимостей
- Аудит безопасности

## Контакты для поддержки

- **Техническая поддержка**: <EMAIL>
- **Экстренная связь**: +7 (800) 123-45-67
- **Документация**: help.astrogenix.com

---

**Важно**: Всегда тестируйте изменения на тестовом сервере перед развертыванием в продакшн!
