/**
 * AstroGenix - Унифицированные компоненты
 * Единая система компонентов для всех страниц
 */

/* ========================================
   МОДАЛЬНЫЕ ОКНА
   ======================================== */

.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(5px);
    z-index: 9999;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.modal.show {
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 1;
}

.modal-content {
    background: var(--white);
    border-radius: var(--radius-xl);
    max-width: 500px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: var(--shadow-2xl);
    transform: scale(0.9);
    transition: transform 0.3s ease;
}

.modal.show .modal-content {
    transform: scale(1);
}

.modal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--space-6);
    border-bottom: 1px solid var(--gray-200);
}

.modal-header h3 {
    margin: 0;
    color: var(--dark);
    font-size: var(--text-xl);
    font-weight: 600;
}

.modal-close {
    background: none;
    border: none;
    font-size: var(--text-2xl);
    color: var(--gray-500);
    cursor: pointer;
    padding: var(--space-2);
    border-radius: var(--radius-md);
    transition: var(--transition-colors);
}

.modal-close:hover {
    background: var(--gray-100);
    color: var(--gray-700);
}

.modal-body {
    padding: var(--space-6);
}

.modal-footer {
    display: flex;
    gap: var(--space-3);
    justify-content: flex-end;
    padding: var(--space-6);
    border-top: 1px solid var(--gray-200);
}

/* ========================================
   УВЕДОМЛЕНИЯ
   ======================================== */

.notification {
    position: fixed;
    top: var(--space-6);
    right: var(--space-6);
    background: var(--white);
    border-radius: var(--radius-lg);
    padding: var(--space-4);
    box-shadow: var(--shadow-xl);
    border-left: 4px solid var(--primary-green);
    max-width: 400px;
    z-index: 10000;
    transform: translateX(100%);
    transition: transform 0.3s ease;
}

.notification.show {
    transform: translateX(0);
}

.notification-success {
    border-left-color: var(--success-color);
}

.notification-warning {
    border-left-color: var(--warning-color);
}

.notification-error {
    border-left-color: var(--error-color);
}

.notification-info {
    border-left-color: var(--info-color);
}

.notification-content {
    display: flex;
    align-items: flex-start;
    gap: var(--space-3);
}

.notification-icon {
    flex-shrink: 0;
    width: 20px;
    height: 20px;
    margin-top: 2px;
}

.notification-text {
    flex: 1;
}

.notification-title {
    font-weight: 600;
    color: var(--dark);
    margin-bottom: var(--space-1);
}

.notification-message {
    color: var(--gray-600);
    font-size: var(--text-sm);
}

.notification-close {
    background: none;
    border: none;
    color: var(--gray-400);
    cursor: pointer;
    padding: var(--space-1);
    border-radius: var(--radius-sm);
    transition: var(--transition-colors);
}

.notification-close:hover {
    color: var(--gray-600);
    background: var(--gray-100);
}

/* ========================================
   ЗАГРУЗЧИКИ
   ======================================== */

.loader {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 2px solid var(--gray-300);
    border-radius: 50%;
    border-top-color: var(--primary-green);
    animation: spin 1s ease-in-out infinite;
}

.loader-lg {
    width: 40px;
    height: 40px;
    border-width: 4px;
}

.loader-sm {
    width: 16px;
    height: 16px;
    border-width: 2px;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 100;
}

/* ========================================
   ТАБЛИЦЫ
   ======================================== */

.table-container {
    background: var(--white);
    border-radius: var(--radius-xl);
    overflow: hidden;
    box-shadow: var(--shadow-lg);
    border: 1px solid var(--gray-200);
}

.table {
    width: 100%;
    border-collapse: collapse;
}

.table th {
    background: var(--gray-50);
    padding: var(--space-4);
    text-align: left;
    font-weight: 600;
    color: var(--gray-700);
    font-size: var(--text-sm);
    border-bottom: 1px solid var(--gray-200);
}

.table td {
    padding: var(--space-4);
    border-bottom: 1px solid var(--gray-200);
    color: var(--gray-700);
}

.table tr:last-child td {
    border-bottom: none;
}

.table tr:hover {
    background: var(--gray-50);
}

.table-striped tr:nth-child(even) {
    background: var(--gray-50);
}

/* ========================================
   БЕЙДЖИ И СТАТУСЫ
   ======================================== */

.badge {
    display: inline-flex;
    align-items: center;
    padding: var(--space-1) var(--space-3);
    border-radius: var(--radius-full);
    font-size: var(--text-xs);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.badge-primary {
    background: rgba(46, 204, 113, 0.1);
    color: var(--primary-green);
}

.badge-success {
    background: var(--success-light);
    color: var(--success-color);
}

.badge-warning {
    background: var(--warning-light);
    color: var(--warning-color);
}

.badge-error {
    background: var(--error-light);
    color: var(--error-color);
}

.badge-info {
    background: var(--info-light);
    color: var(--info-color);
}

.badge-gray {
    background: var(--gray-100);
    color: var(--gray-600);
}

/* ========================================
   ПРОГРЕСС-БАРЫ
   ======================================== */

.progress {
    width: 100%;
    height: 8px;
    background: var(--gray-200);
    border-radius: var(--radius-full);
    overflow: hidden;
}

.progress-bar {
    height: 100%;
    background: var(--gradient-primary);
    border-radius: var(--radius-full);
    transition: width 0.3s ease;
    position: relative;
}

.progress-bar::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

.progress-lg {
    height: 12px;
}

.progress-sm {
    height: 6px;
}

/* ========================================
   АККОРДЕОНЫ
   ======================================== */

.accordion {
    border: 1px solid var(--gray-200);
    border-radius: var(--radius-lg);
    overflow: hidden;
}

.accordion-item {
    border-bottom: 1px solid var(--gray-200);
}

.accordion-item:last-child {
    border-bottom: none;
}

.accordion-header {
    background: var(--white);
    padding: var(--space-4);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: space-between;
    transition: var(--transition-colors);
}

.accordion-header:hover {
    background: var(--gray-50);
}

.accordion-title {
    font-weight: 600;
    color: var(--dark);
    margin: 0;
}

.accordion-icon {
    color: var(--gray-500);
    transition: transform 0.3s ease;
}

.accordion-item.active .accordion-icon {
    transform: rotate(180deg);
}

.accordion-content {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease;
}

.accordion-item.active .accordion-content {
    max-height: 500px;
}

.accordion-body {
    padding: var(--space-4);
    background: var(--gray-50);
    color: var(--gray-700);
    line-height: 1.6;
}

/* ========================================
   ВКЛАДКИ
   ======================================== */

.tabs {
    border-bottom: 1px solid var(--gray-200);
}

.tabs-nav {
    display: flex;
    gap: var(--space-1);
}

.tab-button {
    background: none;
    border: none;
    padding: var(--space-3) var(--space-4);
    color: var(--gray-600);
    font-weight: 500;
    cursor: pointer;
    border-bottom: 2px solid transparent;
    transition: var(--transition-colors);
}

.tab-button:hover {
    color: var(--primary-green);
}

.tab-button.active {
    color: var(--primary-green);
    border-bottom-color: var(--primary-green);
}

.tab-content {
    padding: var(--space-6) 0;
}

.tab-pane {
    display: none;
}

.tab-pane.active {
    display: block;
    animation: fadeIn 0.3s ease;
}

/* ========================================
   ДРОПДАУНЫ
   ======================================== */

.dropdown {
    position: relative;
    display: inline-block;
}

.dropdown-toggle {
    background: var(--white);
    border: 2px solid var(--gray-300);
    border-radius: var(--radius-lg);
    padding: var(--space-3) var(--space-4);
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: var(--space-2);
    transition: var(--transition-colors);
}

.dropdown-toggle:hover {
    border-color: var(--primary-green);
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    min-width: 200px;
    background: var(--white);
    border: 1px solid var(--gray-200);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-lg);
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.3s ease;
}

.dropdown.active .dropdown-menu {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dropdown-item {
    display: block;
    padding: var(--space-3) var(--space-4);
    color: var(--gray-700);
    text-decoration: none;
    transition: var(--transition-colors);
}

.dropdown-item:hover {
    background: var(--gray-50);
    color: var(--primary-green);
}

.dropdown-divider {
    height: 1px;
    background: var(--gray-200);
    margin: var(--space-2) 0;
}
