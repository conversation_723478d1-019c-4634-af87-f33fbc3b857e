<?php
/**
 * AstroGenix - Компонент уведомлений
 * Эко-майнинговая инвестиционная платформа
 */

// Проверка авторизации
if (!is_logged_in()) {
    return;
}

require_once 'classes/Notification.php';

try {
    $database = new Database();
    $db = $database->getConnection();
    $notification = new Notification($db);
    
    $user_notifications = $notification->getUserNotifications($_SESSION['user_id'], 5);
    $unread_count = $notification->getUnreadCount($_SESSION['user_id']);
    
} catch (Exception $e) {
    error_log("Notifications component error: " . $e->getMessage());
    $user_notifications = [];
    $unread_count = 0;
}
?>

<!-- Notifications Dropdown -->
<div class="notifications-dropdown" id="notificationsDropdown">
    <button class="notifications-trigger" id="notificationsTrigger">
        <i class="fas fa-bell"></i>
        <?php if ($unread_count > 0): ?>
            <span class="notifications-badge" id="notificationsBadge"><?php echo $unread_count; ?></span>
        <?php endif; ?>
    </button>
    
    <div class="notifications-panel" id="notificationsPanel">
        <div class="notifications-header">
            <h4>Уведомления</h4>
            <div class="notifications-actions">
                <?php if ($unread_count > 0): ?>
                    <button class="mark-all-read-btn" id="markAllReadBtn" title="Отметить все как прочитанные">
                        <i class="fas fa-check-double"></i>
                    </button>
                <?php endif; ?>
                <button class="close-notifications-btn" id="closeNotificationsBtn" title="Закрыть">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        
        <div class="notifications-list" id="notificationsList">
            <?php if (empty($user_notifications)): ?>
                <div class="notifications-empty">
                    <i class="fas fa-bell-slash"></i>
                    <p>У вас пока нет уведомлений</p>
                </div>
            <?php else: ?>
                <?php foreach ($user_notifications as $notif): ?>
                    <div class="notification-item <?php echo $notif['is_read'] ? 'read' : 'unread'; ?>" 
                         data-id="<?php echo $notif['id']; ?>">
                        <div class="notification-icon notification-<?php echo $notif['type']; ?>">
                            <?php
                            $icons = [
                                'info' => 'fas fa-info-circle',
                                'success' => 'fas fa-check-circle',
                                'warning' => 'fas fa-exclamation-triangle',
                                'error' => 'fas fa-times-circle'
                            ];
                            ?>
                            <i class="<?php echo $icons[$notif['type']] ?? 'fas fa-bell'; ?>"></i>
                        </div>
                        
                        <div class="notification-content">
                            <div class="notification-title">
                                <?php echo htmlspecialchars($notif['title']); ?>
                            </div>
                            <div class="notification-message">
                                <?php echo htmlspecialchars($notif['message']); ?>
                            </div>
                            <div class="notification-time">
                                <?php echo time_ago($notif['created_at']); ?>
                            </div>
                        </div>
                        
                        <div class="notification-actions">
                            <?php if (!$notif['is_read']): ?>
                                <button class="mark-read-btn" data-id="<?php echo $notif['id']; ?>" title="Отметить как прочитанное">
                                    <i class="fas fa-check"></i>
                                </button>
                            <?php endif; ?>
                            
                            <?php if ($notif['action_url']): ?>
                                <a href="<?php echo htmlspecialchars($notif['action_url']); ?>" 
                                   class="notification-action-btn" title="Перейти">
                                    <i class="fas fa-external-link-alt"></i>
                                </a>
                            <?php endif; ?>
                            
                            <button class="delete-notification-btn" data-id="<?php echo $notif['id']; ?>" title="Удалить">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>
                <?php endforeach; ?>
            <?php endif; ?>
        </div>
        
        <?php if (!empty($user_notifications)): ?>
            <div class="notifications-footer">
                <a href="notifications.php" class="view-all-notifications">
                    Посмотреть все уведомления
                </a>
            </div>
        <?php endif; ?>
    </div>
</div>

<style>
/* Notifications Styles */
.notifications-dropdown {
    position: relative;
    display: inline-block;
}

.notifications-trigger {
    position: relative;
    background: none;
    border: none;
    color: var(--gray-600);
    font-size: 20px;
    padding: var(--space-2);
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.3s ease;
}

.notifications-trigger:hover {
    background: var(--gray-100);
    color: var(--primary-green);
}

.notifications-badge {
    position: absolute;
    top: 0;
    right: 0;
    background: var(--error-color);
    color: var(--white);
    font-size: 10px;
    font-weight: 600;
    padding: 2px 6px;
    border-radius: 10px;
    min-width: 18px;
    text-align: center;
    line-height: 1.2;
}

.notifications-panel {
    position: absolute;
    top: 100%;
    right: 0;
    width: 350px;
    max-height: 500px;
    background: var(--white);
    border: 1px solid var(--gray-300);
    border-radius: var(--radius-lg);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
    z-index: 1000;
    display: none;
    overflow: hidden;
}

.notifications-panel.show {
    display: block;
    animation: slideDown 0.3s ease-out;
}

.notifications-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--space-4);
    border-bottom: 1px solid var(--gray-200);
    background: var(--gray-50);
}

.notifications-header h4 {
    margin: 0;
    font-size: var(--text-lg);
    color: var(--dark);
}

.notifications-actions {
    display: flex;
    gap: var(--space-2);
}

.notifications-actions button {
    background: none;
    border: none;
    color: var(--gray-600);
    padding: var(--space-1);
    border-radius: var(--radius-sm);
    cursor: pointer;
    transition: all 0.3s ease;
}

.notifications-actions button:hover {
    background: var(--gray-200);
    color: var(--primary-green);
}

.notifications-list {
    max-height: 400px;
    overflow-y: auto;
}

.notifications-empty {
    text-align: center;
    padding: var(--space-8);
    color: var(--gray-500);
}

.notifications-empty i {
    font-size: 48px;
    margin-bottom: var(--space-4);
    opacity: 0.5;
}

.notification-item {
    display: flex;
    align-items: flex-start;
    gap: var(--space-3);
    padding: var(--space-4);
    border-bottom: 1px solid var(--gray-100);
    transition: all 0.3s ease;
    cursor: pointer;
}

.notification-item:hover {
    background: var(--gray-50);
}

.notification-item.unread {
    background: linear-gradient(90deg, rgba(34, 197, 94, 0.05) 0%, transparent 100%);
    border-left: 3px solid var(--primary-green);
}

.notification-icon {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.notification-info {
    background: var(--info-color);
    color: var(--white);
}

.notification-success {
    background: var(--success-color);
    color: var(--white);
}

.notification-warning {
    background: var(--warning-color);
    color: var(--white);
}

.notification-error {
    background: var(--error-color);
    color: var(--white);
}

.notification-content {
    flex: 1;
    min-width: 0;
}

.notification-title {
    font-weight: 600;
    color: var(--dark);
    margin-bottom: var(--space-1);
    font-size: var(--text-sm);
}

.notification-message {
    color: var(--gray-600);
    font-size: var(--text-sm);
    line-height: 1.4;
    margin-bottom: var(--space-1);
}

.notification-time {
    color: var(--gray-500);
    font-size: var(--text-xs);
}

.notification-actions {
    display: flex;
    flex-direction: column;
    gap: var(--space-1);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.notification-item:hover .notification-actions {
    opacity: 1;
}

.notification-actions button,
.notification-actions a {
    background: none;
    border: none;
    color: var(--gray-500);
    padding: var(--space-1);
    border-radius: var(--radius-sm);
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 12px;
    text-decoration: none;
    display: flex;
    align-items: center;
    justify-content: center;
}

.notification-actions button:hover,
.notification-actions a:hover {
    background: var(--gray-200);
    color: var(--primary-green);
}

.notifications-footer {
    padding: var(--space-3);
    border-top: 1px solid var(--gray-200);
    background: var(--gray-50);
    text-align: center;
}

.view-all-notifications {
    color: var(--primary-green);
    text-decoration: none;
    font-size: var(--text-sm);
    font-weight: 500;
    transition: color 0.3s ease;
}

.view-all-notifications:hover {
    color: var(--primary-green-dark);
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@media (max-width: 768px) {
    .notifications-panel {
        width: 300px;
        right: -50px;
    }
}

@media (max-width: 480px) {
    .notifications-panel {
        width: 280px;
        right: -100px;
    }
}
</style>

<script>
// Notifications JavaScript
document.addEventListener('DOMContentLoaded', function() {
    initNotifications();
});

function initNotifications() {
    const trigger = document.getElementById('notificationsTrigger');
    const panel = document.getElementById('notificationsPanel');
    const closeBtn = document.getElementById('closeNotificationsBtn');
    const markAllReadBtn = document.getElementById('markAllReadBtn');
    
    // Toggle notifications panel
    if (trigger) {
        trigger.addEventListener('click', function(e) {
            e.stopPropagation();
            panel.classList.toggle('show');
        });
    }
    
    // Close notifications panel
    if (closeBtn) {
        closeBtn.addEventListener('click', function() {
            panel.classList.remove('show');
        });
    }
    
    // Close panel when clicking outside
    document.addEventListener('click', function(e) {
        if (!panel.contains(e.target) && !trigger.contains(e.target)) {
            panel.classList.remove('show');
        }
    });
    
    // Mark all as read
    if (markAllReadBtn) {
        markAllReadBtn.addEventListener('click', function() {
            markAllNotificationsAsRead();
        });
    }
    
    // Individual notification actions
    document.querySelectorAll('.mark-read-btn').forEach(btn => {
        btn.addEventListener('click', function(e) {
            e.stopPropagation();
            const notificationId = this.dataset.id;
            markNotificationAsRead(notificationId);
        });
    });
    
    document.querySelectorAll('.delete-notification-btn').forEach(btn => {
        btn.addEventListener('click', function(e) {
            e.stopPropagation();
            const notificationId = this.dataset.id;
            deleteNotification(notificationId);
        });
    });
}

function markNotificationAsRead(notificationId) {
    fetch('api/notifications.php', {
        method: 'PUT',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            id: notificationId,
            action: 'mark_read'
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const notificationItem = document.querySelector(`[data-id="${notificationId}"]`);
            if (notificationItem) {
                notificationItem.classList.remove('unread');
                notificationItem.classList.add('read');
                
                const markReadBtn = notificationItem.querySelector('.mark-read-btn');
                if (markReadBtn) {
                    markReadBtn.remove();
                }
            }
            
            updateNotificationsBadge();
        }
    })
    .catch(error => {
        console.error('Error marking notification as read:', error);
    });
}

function markAllNotificationsAsRead() {
    fetch('api/notifications.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            action: 'mark_all_read'
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            document.querySelectorAll('.notification-item.unread').forEach(item => {
                item.classList.remove('unread');
                item.classList.add('read');
                
                const markReadBtn = item.querySelector('.mark-read-btn');
                if (markReadBtn) {
                    markReadBtn.remove();
                }
            });
            
            const markAllReadBtn = document.getElementById('markAllReadBtn');
            if (markAllReadBtn) {
                markAllReadBtn.remove();
            }
            
            updateNotificationsBadge();
        }
    })
    .catch(error => {
        console.error('Error marking all notifications as read:', error);
    });
}

function deleteNotification(notificationId) {
    if (!confirm('Удалить это уведомление?')) {
        return;
    }
    
    fetch('api/notifications.php', {
        method: 'DELETE',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            id: notificationId
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const notificationItem = document.querySelector(`[data-id="${notificationId}"]`);
            if (notificationItem) {
                notificationItem.remove();
            }
            
            updateNotificationsBadge();
        }
    })
    .catch(error => {
        console.error('Error deleting notification:', error);
    });
}

function updateNotificationsBadge() {
    fetch('api/notifications.php?action=count')
    .then(response => response.json())
    .then(data => {
        const badge = document.getElementById('notificationsBadge');
        if (data.unread_count > 0) {
            if (badge) {
                badge.textContent = data.unread_count;
            } else {
                const trigger = document.getElementById('notificationsTrigger');
                const newBadge = document.createElement('span');
                newBadge.className = 'notifications-badge';
                newBadge.id = 'notificationsBadge';
                newBadge.textContent = data.unread_count;
                trigger.appendChild(newBadge);
            }
        } else {
            if (badge) {
                badge.remove();
            }
        }
    })
    .catch(error => {
        console.error('Error updating notifications badge:', error);
    });
}
</script>
