<?php
/**
 * AstroGenix - Финансовые операции
 * Централизованное управление всеми финансовыми операциями
 */

session_start();
require_once 'config/config.php';
require_once 'classes/FinancialOperations.php';

// Проверка авторизации
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit();
}

$database = new Database();
$db = $database->getConnection();
$financial = new FinancialOperations($db);

// Получение данных пользователя
$user_balance = $financial->getUserBalance($_SESSION['user_id']);
$financial_stats = $financial->getFinancialStats($_SESSION['user_id']);
$payment_methods = $financial->getPaymentMethods();
$recent_transactions = $financial->getFinancialHistory($_SESSION['user_id'], null, 10);

// Обработка AJAX запросов
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    header('Content-Type: application/json');
    
    switch ($_POST['action']) {
        case 'create_withdrawal':
            $result = $financial->createWithdrawalRequest(
                $_SESSION['user_id'],
                floatval($_POST['amount']),
                $_POST['payment_method'],
                json_decode($_POST['payment_details'], true)
            );
            echo json_encode($result);
            exit();
            
        case 'create_deposit':
            $result = $financial->createDepositRequest(
                $_SESSION['user_id'],
                floatval($_POST['amount']),
                $_POST['payment_method'],
                json_decode($_POST['payment_details'] ?? '{}', true)
            );
            echo json_encode($result);
            exit();
            
        case 'get_transactions':
            $type = $_POST['type'] ?? null;
            $limit = intval($_POST['limit'] ?? 20);
            $offset = intval($_POST['offset'] ?? 0);
            
            $transactions = $financial->getFinancialHistory($_SESSION['user_id'], $type, $limit, $offset);
            echo json_encode(['success' => true, 'transactions' => $transactions]);
            exit();
    }
}

$page_title = 'Финансовые операции - AstroGenix';
?>
<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?></title>
    <meta name="description" content="Управление финансовыми операциями на платформе AstroGenix">
    
    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="assets/images/favicon.svg">
    
    <!-- Стили -->
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="assets/css/financial-operations.css">
    <link rel="stylesheet" href="assets/css/animations.css">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <!-- QR Code Generator -->
    <script src="https://cdn.jsdelivr.net/npm/qrcode@1.5.3/build/qrcode.min.js"></script>
</head>
<body>
    <?php include 'includes/header.php'; ?>

    <main class="main-content">
        <!-- Hero секция -->
        <section class="hero-section financial-hero">
            <div class="container">
                <div class="hero-content animate-fade-in">
                    <h1 class="hero-title">
                        Финансовые <span class="gradient-text">операции</span>
                    </h1>
                    <p class="hero-subtitle">
                        Управляйте своими финансами: пополняйте баланс, выводите средства, 
                        отслеживайте историю операций и анализируйте статистику.
                    </p>
                </div>
            </div>
        </section>

        <div class="container">
            <!-- Баланс и статистика -->
            <section class="section balance-overview">
                <div class="balance-grid">
                    <div class="balance-card main-balance">
                        <div class="balance-icon">
                            <i class="fas fa-wallet"></i>
                        </div>
                        <div class="balance-content">
                            <div class="balance-label">Доступный баланс</div>
                            <div class="balance-value"><?php echo number_format($user_balance['balance'], 2); ?> USDT</div>
                            <div class="balance-actions">
                                <button class="btn btn-primary" id="deposit-btn">
                                    <i class="fas fa-plus"></i>
                                    Пополнить
                                </button>
                                <button class="btn btn-secondary" id="withdraw-btn">
                                    <i class="fas fa-minus"></i>
                                    Вывести
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <div class="balance-card">
                        <div class="balance-icon invested">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <div class="balance-content">
                            <div class="balance-label">Инвестировано</div>
                            <div class="balance-value"><?php echo number_format($user_balance['total_invested'], 2); ?> USDT</div>
                        </div>
                    </div>
                    
                    <div class="balance-card">
                        <div class="balance-icon earned">
                            <i class="fas fa-coins"></i>
                        </div>
                        <div class="balance-content">
                            <div class="balance-label">Заработано</div>
                            <div class="balance-value"><?php echo number_format($user_balance['total_earned'], 2); ?> USDT</div>
                        </div>
                    </div>
                    
                    <div class="balance-card">
                        <div class="balance-icon pending">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div class="balance-content">
                            <div class="balance-label">В обработке</div>
                            <div class="balance-value"><?php echo number_format($user_balance['pending_withdrawals'], 2); ?> USDT</div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Статистика операций -->
            <section class="section operations-stats">
                <div class="section-header">
                    <h2 class="section-title">Статистика за 30 дней</h2>
                </div>
                
                <div class="stats-grid">
                    <div class="stat-card deposits">
                        <div class="stat-icon">
                            <i class="fas fa-arrow-down"></i>
                        </div>
                        <div class="stat-content">
                            <div class="stat-value"><?php echo $financial_stats['deposits_count']; ?></div>
                            <div class="stat-label">Пополнений</div>
                            <div class="stat-amount"><?php echo number_format($financial_stats['total_deposits'], 2); ?> USDT</div>
                        </div>
                    </div>
                    
                    <div class="stat-card withdrawals">
                        <div class="stat-icon">
                            <i class="fas fa-arrow-up"></i>
                        </div>
                        <div class="stat-content">
                            <div class="stat-value"><?php echo $financial_stats['withdrawals_count']; ?></div>
                            <div class="stat-label">Выводов</div>
                            <div class="stat-amount"><?php echo number_format($financial_stats['total_withdrawals'], 2); ?> USDT</div>
                        </div>
                    </div>
                    
                    <div class="stat-card investments">
                        <div class="stat-icon">
                            <i class="fas fa-chart-pie"></i>
                        </div>
                        <div class="stat-content">
                            <div class="stat-value"><?php echo $financial_stats['investments_count']; ?></div>
                            <div class="stat-label">Инвестиций</div>
                            <div class="stat-amount"><?php echo number_format($financial_stats['total_investments'], 2); ?> USDT</div>
                        </div>
                    </div>
                    
                    <div class="stat-card profits">
                        <div class="stat-icon">
                            <i class="fas fa-trophy"></i>
                        </div>
                        <div class="stat-content">
                            <div class="stat-value"><?php echo $financial_stats['profits_count']; ?></div>
                            <div class="stat-label">Начислений прибыли</div>
                            <div class="stat-amount"><?php echo number_format($financial_stats['total_profits'], 2); ?> USDT</div>
                        </div>
                    </div>
                </div>
                
                <div class="stats-summary">
                    <div class="summary-item">
                        <span class="summary-label">Чистый поток:</span>
                        <span class="summary-value <?php echo $financial_stats['net_flow'] >= 0 ? 'positive' : 'negative'; ?>">
                            <?php echo ($financial_stats['net_flow'] >= 0 ? '+' : '') . number_format($financial_stats['net_flow'], 2); ?> USDT
                        </span>
                    </div>
                    <div class="summary-item">
                        <span class="summary-label">Маржа прибыли:</span>
                        <span class="summary-value positive"><?php echo $financial_stats['profit_margin']; ?>%</span>
                    </div>
                </div>
            </section>

            <!-- Быстрые операции -->
            <section class="section quick-operations">
                <div class="section-header">
                    <h2 class="section-title">Быстрые операции</h2>
                </div>
                
                <div class="operations-grid">
                    <div class="operation-card deposit-card">
                        <div class="operation-icon">
                            <i class="fas fa-plus-circle"></i>
                        </div>
                        <div class="operation-content">
                            <h3>Пополнение баланса</h3>
                            <p>Пополните баланс через криптовалюты или банковские карты</p>
                            <div class="quick-amounts">
                                <button class="amount-btn" data-amount="50">50 USDT</button>
                                <button class="amount-btn" data-amount="100">100 USDT</button>
                                <button class="amount-btn" data-amount="500">500 USDT</button>
                                <button class="amount-btn" data-amount="1000">1000 USDT</button>
                            </div>
                            <button class="btn btn-primary btn-block" id="custom-deposit-btn">
                                Пополнить другую сумму
                            </button>
                        </div>
                    </div>
                    
                    <div class="operation-card withdraw-card">
                        <div class="operation-icon">
                            <i class="fas fa-minus-circle"></i>
                        </div>
                        <div class="operation-content">
                            <h3>Вывод средств</h3>
                            <p>Выведите средства на криптокошелек или банковскую карту</p>
                            <div class="withdrawal-info">
                                <div class="info-item">
                                    <span class="info-label">Минимум:</span>
                                    <span class="info-value">10 USDT</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">Комиссия:</span>
                                    <span class="info-value">2-5%</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">Время:</span>
                                    <span class="info-value">1-24 часа</span>
                                </div>
                            </div>
                            <button class="btn btn-secondary btn-block" id="custom-withdraw-btn">
                                Создать запрос на вывод
                            </button>
                        </div>
                    </div>
                </div>
            </section>

            <!-- История операций -->
            <section class="section transactions-history">
                <div class="section-header">
                    <h2 class="section-title">История операций</h2>
                    <div class="history-controls">
                        <select id="transaction-filter" class="form-control">
                            <option value="">Все операции</option>
                            <option value="deposit">Пополнения</option>
                            <option value="withdrawal">Выводы</option>
                            <option value="investment">Инвестиции</option>
                            <option value="profit">Прибыль</option>
                            <option value="referral_bonus">Реферальные бонусы</option>
                        </select>
                        <button class="btn btn-secondary" id="export-history">
                            <i class="fas fa-download"></i>
                            Экспорт
                        </button>
                    </div>
                </div>
                
                <div class="transactions-container">
                    <div class="transactions-list" id="transactions-list">
                        <?php foreach ($recent_transactions as $transaction): ?>
                            <div class="transaction-item transaction-<?php echo $transaction['type']; ?>">
                                <div class="transaction-icon">
                                    <i class="fas <?php 
                                        echo $transaction['type'] === 'deposit' ? 'fa-arrow-down' : 
                                            ($transaction['type'] === 'withdrawal' ? 'fa-arrow-up' : 
                                            ($transaction['type'] === 'investment' ? 'fa-chart-line' : 
                                            ($transaction['type'] === 'profit' ? 'fa-coins' : 'fa-gift')));
                                    ?>"></i>
                                </div>
                                <div class="transaction-content">
                                    <div class="transaction-title">
                                        <?php 
                                        $titles = [
                                            'deposit' => 'Пополнение',
                                            'withdrawal' => 'Вывод средств',
                                            'investment' => 'Инвестиция',
                                            'profit' => 'Прибыль',
                                            'referral_bonus' => 'Реферальный бонус'
                                        ];
                                        echo $titles[$transaction['type']] ?? ucfirst($transaction['type']);
                                        ?>
                                    </div>
                                    <div class="transaction-description"><?php echo htmlspecialchars($transaction['description']); ?></div>
                                    <div class="transaction-date"><?php echo date('d.m.Y H:i', strtotime($transaction['created_at'])); ?></div>
                                </div>
                                <div class="transaction-amount">
                                    <span class="amount <?php echo in_array($transaction['type'], ['deposit', 'profit', 'referral_bonus']) ? 'positive' : 'negative'; ?>">
                                        <?php echo in_array($transaction['type'], ['deposit', 'profit', 'referral_bonus']) ? '+' : '-'; ?>
                                        <?php echo number_format($transaction['amount'], 2); ?> USDT
                                    </span>
                                    <span class="status status-<?php echo $transaction['status']; ?>">
                                        <?php 
                                        $statuses = [
                                            'pending' => 'В обработке',
                                            'completed' => 'Завершено',
                                            'failed' => 'Отклонено',
                                            'cancelled' => 'Отменено'
                                        ];
                                        echo $statuses[$transaction['status']] ?? ucfirst($transaction['status']);
                                        ?>
                                    </span>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                    
                    <div class="load-more-container">
                        <button class="btn btn-outline" id="load-more-transactions">
                            <i class="fas fa-plus"></i>
                            Загрузить еще
                        </button>
                    </div>
                </div>
            </section>
        </div>
    </main>

    <!-- Модальные окна -->
    <!-- Модальное окно пополнения -->
    <div id="deposit-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Пополнение баланса</h3>
                <button class="modal-close">&times;</button>
            </div>
            <div class="modal-body">
                <form id="deposit-form">
                    <div class="form-group">
                        <label>Сумма пополнения</label>
                        <div class="input-group">
                            <input type="number" id="deposit-amount" class="form-control" min="5" step="0.01" required>
                            <span class="input-suffix">USDT</span>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label>Способ пополнения</label>
                        <div class="payment-methods">
                            <?php foreach ($payment_methods as $method): ?>
                                <?php if (in_array($method['type'], ['deposit', 'both'])): ?>
                                    <label class="payment-method">
                                        <input type="radio" name="payment_method" value="<?php echo $method['method_code']; ?>" required>
                                        <div class="method-card">
                                            <div class="method-icon">
                                                <i class="<?php echo $method['icon']; ?>"></i>
                                            </div>
                                            <div class="method-info">
                                                <div class="method-name"><?php echo $method['name']; ?></div>
                                                <div class="method-description"><?php echo $method['description']; ?></div>
                                            </div>
                                        </div>
                                    </label>
                                <?php endif; ?>
                            <?php endforeach; ?>
                        </div>
                    </div>
                    
                    <div class="form-actions">
                        <button type="button" class="btn btn-secondary" data-modal-close>Отмена</button>
                        <button type="submit" class="btn btn-primary">Создать запрос</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Модальное окно вывода -->
    <div id="withdrawal-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Вывод средств</h3>
                <button class="modal-close">&times;</button>
            </div>
            <div class="modal-body">
                <form id="withdrawal-form">
                    <div class="form-group">
                        <label>Сумма вывода</label>
                        <div class="input-group">
                            <input type="number" id="withdrawal-amount" class="form-control" min="10" step="0.01" required>
                            <span class="input-suffix">USDT</span>
                        </div>
                        <div class="form-help">
                            Доступно для вывода: <?php echo number_format($user_balance['balance'], 2); ?> USDT
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label>Способ вывода</label>
                        <div class="payment-methods">
                            <?php foreach ($payment_methods as $method): ?>
                                <?php if (in_array($method['type'], ['withdrawal', 'both'])): ?>
                                    <label class="payment-method">
                                        <input type="radio" name="payment_method" value="<?php echo $method['method_code']; ?>" required>
                                        <div class="method-card">
                                            <div class="method-icon">
                                                <i class="<?php echo $method['icon']; ?>"></i>
                                            </div>
                                            <div class="method-info">
                                                <div class="method-name"><?php echo $method['name']; ?></div>
                                                <div class="method-description"><?php echo $method['description']; ?></div>
                                            </div>
                                        </div>
                                    </label>
                                <?php endif; ?>
                            <?php endforeach; ?>
                        </div>
                    </div>
                    
                    <div class="form-group" id="payment-details-group" style="display: none;">
                        <label>Реквизиты для вывода</label>
                        <textarea id="payment-details" class="form-control" rows="3" placeholder="Введите адрес кошелька или реквизиты карты"></textarea>
                    </div>
                    
                    <div class="withdrawal-summary" id="withdrawal-summary" style="display: none;">
                        <div class="summary-row">
                            <span>Сумма вывода:</span>
                            <span id="summary-amount">0 USDT</span>
                        </div>
                        <div class="summary-row">
                            <span>Комиссия:</span>
                            <span id="summary-fee">0 USDT</span>
                        </div>
                        <div class="summary-row total">
                            <span>К получению:</span>
                            <span id="summary-net">0 USDT</span>
                        </div>
                    </div>
                    
                    <div class="form-actions">
                        <button type="button" class="btn btn-secondary" data-modal-close>Отмена</button>
                        <button type="submit" class="btn btn-primary">Создать запрос</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <?php include 'includes/footer.php'; ?>

    <!-- JavaScript -->
    <script>
        // Передача данных в JavaScript
        window.financialData = {
            balance: <?php echo $user_balance['balance']; ?>,
            paymentMethods: <?php echo json_encode($payment_methods); ?>
        };
    </script>
    <script src="assets/js/main.js"></script>
    <script src="assets/js/financial-operations.js"></script>
</body>
</html>
