<?php
/**
 * AstroGenix - Боковая панель навигации
 * Эко-майнинговая инвестиционная платформа
 */

// Определение текущей страницы для подсветки активного пункта меню
$current_page = basename($_SERVER['PHP_SELF'], '.php');
?>

<aside class="sidebar">
    <div class="sidebar-header">
        <div class="sidebar-logo">
            <img src="assets/images/logo.svg" alt="AstroGenix">
            <span>AstroGenix</span>
        </div>
        <button class="sidebar-toggle" id="sidebarToggle">
            <i class="fas fa-bars"></i>
        </button>
    </div>
    
    <nav class="sidebar-nav">
        <ul class="nav-list">
            <li class="nav-item <?php echo $current_page === 'dashboard' ? 'active' : ''; ?>">
                <a href="dashboard.php" class="nav-link">
                    <i class="fas fa-tachometer-alt"></i>
                    <span>Панель управления</span>
                </a>
            </li>
            <li class="nav-item <?php echo $current_page === 'investments' ? 'active' : ''; ?>">
                <a href="investments.php" class="nav-link">
                    <i class="fas fa-chart-line"></i>
                    <span>Инвестиции</span>
                </a>
            </li>
            <li class="nav-item <?php echo $current_page === 'transactions' ? 'active' : ''; ?>">
                <a href="transactions.php" class="nav-link">
                    <i class="fas fa-exchange-alt"></i>
                    <span>Транзакции</span>
                </a>
            </li>
            <li class="nav-item <?php echo $current_page === 'referrals' ? 'active' : ''; ?>">
                <a href="referrals.php" class="nav-link">
                    <i class="fas fa-users"></i>
                    <span>Рефералы</span>
                </a>
            </li>
            <li class="nav-item <?php echo $current_page === 'deposit' ? 'active' : ''; ?>">
                <a href="deposit.php" class="nav-link">
                    <i class="fas fa-plus-circle"></i>
                    <span>Пополнить</span>
                </a>
            </li>
            <li class="nav-item <?php echo $current_page === 'withdraw' ? 'active' : ''; ?>">
                <a href="withdraw.php" class="nav-link">
                    <i class="fas fa-minus-circle"></i>
                    <span>Вывести</span>
                </a>
            </li>
            <li class="nav-item <?php echo $current_page === 'profile' ? 'active' : ''; ?>">
                <a href="profile.php" class="nav-link">
                    <i class="fas fa-user"></i>
                    <span>Профиль</span>
                </a>
            </li>
            <li class="nav-item <?php echo $current_page === 'support' ? 'active' : ''; ?>">
                <a href="support.php" class="nav-link">
                    <i class="fas fa-headset"></i>
                    <span>Поддержка</span>
                </a>
            </li>
        </ul>
    </nav>
    
    <div class="sidebar-footer">
        <a href="logout.php" class="logout-btn">
            <i class="fas fa-sign-out-alt"></i>
            <span>Выйти</span>
        </a>
    </div>
</aside>
