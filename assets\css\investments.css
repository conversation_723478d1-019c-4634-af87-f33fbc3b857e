/**
 * AstroGenix - Стили страницы инвестиций
 * Эко-майнинговая инвестиционная платформа
 */

/* Статистика инвестиций */
.investment-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--space-6);
    margin-bottom: var(--space-8);
}

/* Секция пакетов */
.packages-section {
    margin-bottom: var(--space-8);
}

.section-header {
    text-align: center;
    margin-bottom: var(--space-8);
}

.section-header h2 {
    margin-bottom: var(--space-2);
    color: var(--gray-900);
}

.section-header p {
    color: var(--gray-600);
    font-size: var(--text-lg);
    margin: 0;
}

.packages-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: var(--space-6);
}

/* Карточки пакетов */
.package-card {
    background: var(--white);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-sm);
    overflow: hidden;
    transition: all var(--transition-normal);
    position: relative;
    border: 2px solid transparent;
}

.package-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-xl);
    border-color: var(--primary-green);
}

.package-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--gradient-primary);
}

.package-header {
    padding: var(--space-6);
    text-align: center;
    background: linear-gradient(135deg, rgba(46, 204, 113, 0.05) 0%, rgba(142, 68, 173, 0.05) 100%);
}

.package-header h3 {
    margin: 0 0 var(--space-3) 0;
    font-size: var(--text-2xl);
    color: var(--gray-900);
}

.package-profit {
    font-size: var(--text-4xl);
    font-weight: 700;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    line-height: 1;
}

.package-profit span {
    display: block;
    font-size: var(--text-sm);
    color: var(--gray-600);
    margin-top: var(--space-1);
}

.package-body {
    padding: var(--space-6);
}

.package-description {
    color: var(--gray-700);
    margin-bottom: var(--space-6);
    text-align: center;
    font-size: var(--text-sm);
}

.package-details {
    margin-bottom: var(--space-6);
}

.detail-item {
    display: flex;
    align-items: center;
    gap: var(--space-3);
    margin-bottom: var(--space-3);
    padding: var(--space-3);
    background: var(--gray-100);
    border-radius: var(--radius-md);
}

.detail-item i {
    color: var(--primary-green);
    width: 20px;
    text-align: center;
}

.detail-item span {
    color: var(--gray-700);
    font-size: var(--text-sm);
}

/* Калькулятор пакета */
.package-calculator {
    background: var(--gray-50);
    border-radius: var(--radius-lg);
    padding: var(--space-4);
    margin-bottom: var(--space-4);
}

.package-calculator label {
    display: block;
    margin-bottom: var(--space-2);
    font-weight: 500;
    color: var(--gray-700);
}

.investment-amount {
    width: 100%;
    padding: var(--space-3);
    border: 2px solid var(--gray-300);
    border-radius: var(--radius-md);
    font-size: var(--text-base);
    transition: border-color var(--transition-fast);
}

.investment-amount:focus {
    outline: none;
    border-color: var(--primary-green);
}

.calculator-results {
    margin-top: var(--space-4);
}

.result-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: var(--space-2);
    font-size: var(--text-sm);
}

.result-item.total {
    border-top: 1px solid var(--gray-300);
    padding-top: var(--space-2);
    font-weight: 600;
    font-size: var(--text-base);
    color: var(--primary-green);
}

.package-footer {
    padding: var(--space-6);
    background: var(--gray-50);
}

/* Секция пользовательских инвестиций */
.user-investments-section {
    margin-bottom: var(--space-8);
}

.user-investments-section .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    text-align: left;
    margin-bottom: var(--space-6);
}

.view-all-link {
    color: var(--primary-green);
    text-decoration: none;
    font-weight: 500;
    transition: color var(--transition-fast);
}

.view-all-link:hover {
    color: var(--dark-green);
}

/* Список инвестиций */
.investments-list {
    display: grid;
    gap: var(--space-4);
}

.investment-item {
    background: var(--white);
    border-radius: var(--radius-lg);
    padding: var(--space-6);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--gray-200);
    transition: all var(--transition-fast);
}

.investment-item:hover {
    box-shadow: var(--shadow-md);
    border-color: var(--primary-green);
}

.investment-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--space-4);
}

.investment-header h4 {
    margin: 0;
    color: var(--gray-900);
}

.investment-status {
    padding: var(--space-1) var(--space-3);
    border-radius: var(--radius-full);
    font-size: var(--text-xs);
    font-weight: 500;
}

.status-active {
    background: rgba(40, 167, 69, 0.2);
    color: var(--success);
}

.status-completed {
    background: rgba(23, 162, 184, 0.2);
    color: var(--info);
}

.status-cancelled {
    background: rgba(220, 53, 69, 0.2);
    color: var(--error);
}

.investment-details {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--space-3);
    margin-bottom: var(--space-4);
}

.detail-row {
    display: flex;
    justify-content: space-between;
    padding: var(--space-2) 0;
    border-bottom: 1px solid var(--gray-200);
}

.detail-row span:first-child {
    color: var(--gray-600);
    font-size: var(--text-sm);
}

.detail-row span:last-child {
    font-weight: 500;
    color: var(--gray-900);
}

.detail-row .amount {
    color: var(--primary-green);
    font-weight: 600;
}

.detail-row .profit {
    color: var(--warning);
    font-weight: 600;
}

.detail-row .earned {
    color: var(--success);
    font-weight: 600;
}

/* Прогресс инвестиции */
.investment-progress {
    margin-top: var(--space-4);
}

.progress-info {
    display: flex;
    justify-content: space-between;
    margin-bottom: var(--space-2);
    font-size: var(--text-sm);
    color: var(--gray-600);
}

.progress-bar {
    height: 8px;
    background: var(--gray-200);
    border-radius: var(--radius-full);
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: var(--gradient-primary);
    border-radius: var(--radius-full);
    transition: width var(--transition-slow);
    position: relative;
}

.progress-fill::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.3) 50%, transparent 100%);
    animation: progressShine 2s ease-in-out infinite;
}

/* Модальное окно */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    opacity: 0;
    transition: opacity var(--transition-normal);
}

.modal.show {
    display: flex;
    opacity: 1;
}

.modal-content {
    background: var(--white);
    border-radius: var(--radius-xl);
    width: 90%;
    max-width: 500px;
    max-height: 90vh;
    overflow-y: auto;
    transform: scale(0.9);
    transition: transform var(--transition-normal);
}

.modal.show .modal-content {
    transform: scale(1);
}

.modal-header {
    padding: var(--space-6);
    border-bottom: 1px solid var(--gray-200);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    margin: 0;
    color: var(--gray-900);
}

.modal-close {
    background: none;
    border: none;
    color: var(--gray-500);
    font-size: var(--text-lg);
    cursor: pointer;
    padding: var(--space-2);
    border-radius: var(--radius-sm);
    transition: color var(--transition-fast);
}

.modal-close:hover {
    color: var(--gray-700);
}

.modal-body {
    padding: var(--space-6);
}

.selected-package {
    background: var(--gray-100);
    border-radius: var(--radius-md);
    padding: var(--space-4);
    margin-bottom: var(--space-4);
}

.investment-summary {
    background: var(--gray-50);
    border-radius: var(--radius-lg);
    padding: var(--space-4);
    margin: var(--space-4) 0;
}

.modal-actions {
    display: flex;
    gap: var(--space-3);
    justify-content: flex-end;
    margin-top: var(--space-6);
}

/* Адаптивность */
@media (max-width: 768px) {
    .packages-grid {
        grid-template-columns: 1fr;
    }
    
    .investment-stats {
        grid-template-columns: 1fr;
    }
    
    .investment-details {
        grid-template-columns: 1fr;
    }
    
    .user-investments-section .section-header {
        flex-direction: column;
        gap: var(--space-3);
        align-items: flex-start;
    }
    
    .modal-content {
        width: 95%;
        margin: var(--space-4);
    }
    
    .modal-actions {
        flex-direction: column;
    }
}

@media (max-width: 480px) {
    .package-card {
        margin: 0 var(--space-2);
    }
    
    .package-header,
    .package-body,
    .package-footer {
        padding: var(--space-4);
    }
    
    .investment-item {
        padding: var(--space-4);
    }
    
    .investment-header {
        flex-direction: column;
        gap: var(--space-2);
        align-items: flex-start;
    }
}
