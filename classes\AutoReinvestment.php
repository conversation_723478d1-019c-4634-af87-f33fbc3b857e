<?php
/**
 * AstroGenix - Система автоматического реинвестирования
 * Управление автоматическим реинвестированием прибыли
 */

class AutoReinvestment {
    private $db;
    private $enhanced_investment;
    
    public function __construct($database) {
        $this->db = $database;
        require_once 'EnhancedInvestment.php';
        $this->enhanced_investment = new EnhancedInvestment($database);
    }
    
    /**
     * Получение настроек автореинвестирования пользователя
     */
    public function getUserSettings($user_id) {
        try {
            $query = "SELECT * FROM auto_reinvestment_settings WHERE user_id = :user_id";
            $stmt = $this->db->prepare($query);
            $stmt->bindParam(':user_id', $user_id, PDO::PARAM_INT);
            $stmt->execute();
            
            $settings = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$settings) {
                // Создание настроек по умолчанию
                return $this->createDefaultSettings($user_id);
            }
            
            return $settings;
        } catch (Exception $e) {
            error_log("Get user auto-reinvestment settings error: " . $e->getMessage());
            return null;
        }
    }
    
    /**
     * Обновление настроек автореинвестирования
     */
    public function updateUserSettings($user_id, $settings) {
        try {
            $query = "INSERT INTO auto_reinvestment_settings 
                      (user_id, is_enabled, min_amount, preferred_package_id, reinvest_percent, max_investments_per_day) 
                      VALUES 
                      (:user_id, :is_enabled, :min_amount, :preferred_package_id, :reinvest_percent, :max_investments_per_day)
                      ON DUPLICATE KEY UPDATE
                      is_enabled = :is_enabled,
                      min_amount = :min_amount,
                      preferred_package_id = :preferred_package_id,
                      reinvest_percent = :reinvest_percent,
                      max_investments_per_day = :max_investments_per_day,
                      updated_at = NOW()";
            
            $stmt = $this->db->prepare($query);
            $stmt->bindParam(':user_id', $user_id, PDO::PARAM_INT);
            $stmt->bindParam(':is_enabled', $settings['is_enabled'], PDO::PARAM_BOOL);
            $stmt->bindParam(':min_amount', $settings['min_amount']);
            $stmt->bindParam(':preferred_package_id', $settings['preferred_package_id'], PDO::PARAM_INT);
            $stmt->bindParam(':reinvest_percent', $settings['reinvest_percent']);
            $stmt->bindParam(':max_investments_per_day', $settings['max_investments_per_day'], PDO::PARAM_INT);
            
            return $stmt->execute();
        } catch (Exception $e) {
            error_log("Update auto-reinvestment settings error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Обработка автоматического реинвестирования для пользователя
     */
    public function processUserReinvestment($user_id) {
        try {
            // Получение настроек
            $settings = $this->getUserSettings($user_id);
            
            if (!$settings || !$settings['is_enabled']) {
                return ['success' => true, 'message' => 'Автореинвестирование отключено'];
            }
            
            // Проверка лимита инвестиций за день
            $today_investments = $this->getTodayInvestmentsCount($user_id);
            if ($today_investments >= $settings['max_investments_per_day']) {
                return ['success' => true, 'message' => 'Достигнут дневной лимит инвестиций'];
            }
            
            // Получение доступной прибыли для реинвестирования
            $available_profit = $this->getAvailableProfitForReinvestment($user_id, $settings['reinvest_percent']);
            
            if ($available_profit < $settings['min_amount']) {
                return ['success' => true, 'message' => 'Недостаточно прибыли для реинвестирования'];
            }
            
            // Выбор пакета для реинвестирования
            $package_id = $this->selectReinvestmentPackage($user_id, $available_profit, $settings['preferred_package_id']);
            
            if (!$package_id) {
                return ['success' => false, 'error' => 'Не найден подходящий пакет для реинвестирования'];
            }
            
            // Создание реинвестиции
            $result = $this->enhanced_investment->createEnhancedInvestment(
                $user_id, 
                $package_id, 
                $available_profit,
                ['auto_reinvest' => true, 'reinvest_percent' => $settings['reinvest_percent']]
            );
            
            if ($result['success']) {
                // Логирование реинвестирования
                $this->logReinvestment($user_id, $result['investment_id'], $available_profit, $package_id);
                
                // Создание уведомления
                $this->createReinvestmentNotification($user_id, $result['investment_id'], $available_profit);
                
                return [
                    'success' => true,
                    'investment_id' => $result['investment_id'],
                    'amount' => $available_profit,
                    'message' => 'Автореинвестирование выполнено успешно'
                ];
            } else {
                return ['success' => false, 'error' => $result['error']];
            }
            
        } catch (Exception $e) {
            error_log("Process user reinvestment error: " . $e->getMessage());
            return ['success' => false, 'error' => 'Ошибка обработки автореинвестирования'];
        }
    }
    
    /**
     * Массовая обработка автореинвестирования для всех пользователей
     */
    public function processAllUsersReinvestment() {
        try {
            $processed = 0;
            $successful = 0;
            $errors = [];
            
            // Получение пользователей с включенным автореинвестированием
            $query = "SELECT DISTINCT user_id FROM auto_reinvestment_settings WHERE is_enabled = 1";
            $stmt = $this->db->prepare($query);
            $stmt->execute();
            $users = $stmt->fetchAll(PDO::FETCH_COLUMN);
            
            foreach ($users as $user_id) {
                $processed++;
                $result = $this->processUserReinvestment($user_id);
                
                if ($result['success']) {
                    if (isset($result['investment_id'])) {
                        $successful++;
                    }
                } else {
                    $errors[] = "Пользователь {$user_id}: " . $result['error'];
                }
            }
            
            return [
                'success' => true,
                'processed' => $processed,
                'successful' => $successful,
                'errors' => $errors
            ];
            
        } catch (Exception $e) {
            error_log("Process all users reinvestment error: " . $e->getMessage());
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }
    
    /**
     * Получение статистики автореинвестирования пользователя
     */
    public function getUserReinvestmentStats($user_id) {
        try {
            $query = "SELECT 
                        COUNT(*) as total_reinvestments,
                        SUM(amount) as total_reinvested,
                        AVG(amount) as avg_reinvestment,
                        MAX(amount) as max_reinvestment,
                        MIN(amount) as min_reinvestment,
                        COUNT(CASE WHEN DATE(created_at) >= DATE_SUB(CURDATE(), INTERVAL 30 DAY) THEN 1 END) as last_30_days
                      FROM user_investments 
                      WHERE user_id = :user_id AND auto_reinvest = 1";
            
            $stmt = $this->db->prepare($query);
            $stmt->bindParam(':user_id', $user_id, PDO::PARAM_INT);
            $stmt->execute();
            
            return $stmt->fetch(PDO::FETCH_ASSOC);
        } catch (Exception $e) {
            error_log("Get user reinvestment stats error: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Создание настроек по умолчанию
     */
    private function createDefaultSettings($user_id) {
        try {
            $default_settings = [
                'user_id' => $user_id,
                'is_enabled' => false,
                'min_amount' => 50.00,
                'preferred_package_id' => null,
                'reinvest_percent' => 50.00,
                'max_investments_per_day' => 1
            ];
            
            $query = "INSERT INTO auto_reinvestment_settings 
                      (user_id, is_enabled, min_amount, preferred_package_id, reinvest_percent, max_investments_per_day) 
                      VALUES 
                      (:user_id, :is_enabled, :min_amount, :preferred_package_id, :reinvest_percent, :max_investments_per_day)";
            
            $stmt = $this->db->prepare($query);
            $stmt->bindParam(':user_id', $user_id, PDO::PARAM_INT);
            $stmt->bindParam(':is_enabled', $default_settings['is_enabled'], PDO::PARAM_BOOL);
            $stmt->bindParam(':min_amount', $default_settings['min_amount']);
            $stmt->bindParam(':preferred_package_id', $default_settings['preferred_package_id'], PDO::PARAM_INT);
            $stmt->bindParam(':reinvest_percent', $default_settings['reinvest_percent']);
            $stmt->bindParam(':max_investments_per_day', $default_settings['max_investments_per_day'], PDO::PARAM_INT);
            
            if ($stmt->execute()) {
                return $default_settings;
            }
            
            return null;
        } catch (Exception $e) {
            error_log("Create default auto-reinvestment settings error: " . $e->getMessage());
            return null;
        }
    }
    
    /**
     * Получение количества инвестиций за сегодня
     */
    private function getTodayInvestmentsCount($user_id) {
        try {
            $query = "SELECT COUNT(*) FROM user_investments 
                      WHERE user_id = :user_id AND DATE(created_at) = CURDATE() AND auto_reinvest = 1";
            $stmt = $this->db->prepare($query);
            $stmt->bindParam(':user_id', $user_id, PDO::PARAM_INT);
            $stmt->execute();
            
            return $stmt->fetchColumn();
        } catch (Exception $e) {
            error_log("Get today investments count error: " . $e->getMessage());
            return 0;
        }
    }
    
    /**
     * Получение доступной прибыли для реинвестирования
     */
    private function getAvailableProfitForReinvestment($user_id, $reinvest_percent) {
        try {
            // Получение накопленной прибыли, которая еще не была реинвестирована
            $query = "SELECT SUM(amount) as total_profit 
                      FROM transactions 
                      WHERE user_id = :user_id 
                      AND type = 'profit' 
                      AND status = 'completed'
                      AND id NOT IN (
                          SELECT transaction_id FROM reinvestment_log 
                          WHERE user_id = :user_id AND transaction_id IS NOT NULL
                      )";
            
            $stmt = $this->db->prepare($query);
            $stmt->bindParam(':user_id', $user_id, PDO::PARAM_INT);
            $stmt->execute();
            
            $total_profit = $stmt->fetchColumn() ?: 0;
            
            // Расчет суммы для реинвестирования
            return ($total_profit * $reinvest_percent) / 100;
        } catch (Exception $e) {
            error_log("Get available profit for reinvestment error: " . $e->getMessage());
            return 0;
        }
    }
    
    /**
     * Выбор пакета для реинвестирования
     */
    private function selectReinvestmentPackage($user_id, $amount, $preferred_package_id = null) {
        try {
            // Если указан предпочтительный пакет, проверяем его
            if ($preferred_package_id) {
                $query = "SELECT id FROM investment_packages 
                          WHERE id = :package_id AND is_active = 1 
                          AND min_amount <= :amount AND max_amount >= :amount";
                $stmt = $this->db->prepare($query);
                $stmt->bindParam(':package_id', $preferred_package_id, PDO::PARAM_INT);
                $stmt->bindParam(':amount', $amount);
                $stmt->execute();
                
                if ($stmt->fetchColumn()) {
                    return $preferred_package_id;
                }
            }
            
            // Поиск подходящего пакета
            $query = "SELECT id FROM investment_packages 
                      WHERE is_active = 1 
                      AND min_amount <= :amount AND max_amount >= :amount
                      ORDER BY daily_profit_percent DESC, min_amount ASC
                      LIMIT 1";
            
            $stmt = $this->db->prepare($query);
            $stmt->bindParam(':amount', $amount);
            $stmt->execute();
            
            return $stmt->fetchColumn();
        } catch (Exception $e) {
            error_log("Select reinvestment package error: " . $e->getMessage());
            return null;
        }
    }
    
    /**
     * Логирование реинвестирования
     */
    private function logReinvestment($user_id, $investment_id, $amount, $package_id) {
        try {
            // Создание таблицы логов если не существует
            $create_table_query = "CREATE TABLE IF NOT EXISTS reinvestment_log (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_id INT NOT NULL,
                investment_id INT NOT NULL,
                amount DECIMAL(15,2) NOT NULL,
                package_id INT NOT NULL,
                transaction_id INT DEFAULT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
                FOREIGN KEY (investment_id) REFERENCES user_investments(id) ON DELETE CASCADE,
                FOREIGN KEY (package_id) REFERENCES investment_packages(id) ON DELETE CASCADE,
                INDEX idx_user_id (user_id),
                INDEX idx_created_at (created_at)
            )";
            
            $this->db->exec($create_table_query);
            
            // Добавление записи в лог
            $query = "INSERT INTO reinvestment_log 
                      (user_id, investment_id, amount, package_id) 
                      VALUES 
                      (:user_id, :investment_id, :amount, :package_id)";
            
            $stmt = $this->db->prepare($query);
            $stmt->bindParam(':user_id', $user_id, PDO::PARAM_INT);
            $stmt->bindParam(':investment_id', $investment_id, PDO::PARAM_INT);
            $stmt->bindParam(':amount', $amount);
            $stmt->bindParam(':package_id', $package_id, PDO::PARAM_INT);
            
            return $stmt->execute();
        } catch (Exception $e) {
            error_log("Log reinvestment error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Создание уведомления о реинвестировании
     */
    private function createReinvestmentNotification($user_id, $investment_id, $amount) {
        try {
            $query = "INSERT INTO investment_notifications 
                      (user_id, investment_id, notification_type, title, message) 
                      VALUES 
                      (:user_id, :investment_id, 'reinvestment_made', :title, :message)";
            
            $title = 'Автоматическое реинвестирование';
            $message = "Выполнено автоматическое реинвестирование на сумму {$amount} USDT";
            
            $stmt = $this->db->prepare($query);
            $stmt->bindParam(':user_id', $user_id, PDO::PARAM_INT);
            $stmt->bindParam(':investment_id', $investment_id, PDO::PARAM_INT);
            $stmt->bindParam(':title', $title);
            $stmt->bindParam(':message', $message);
            
            return $stmt->execute();
        } catch (Exception $e) {
            error_log("Create reinvestment notification error: " . $e->getMessage());
            return false;
        }
    }
}
?>
