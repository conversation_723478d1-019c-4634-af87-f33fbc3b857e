<?php
/**
 * AstroGenix - Страница входа
 * Эко-майнинговая инвестиционная платформа
 */

require_once 'config/config.php';

// Если пользователь уже авторизован, перенаправляем в дашборд
if (is_logged_in()) {
    redirect('dashboard.php');
}

$errors = [];
$success_message = '';

// Обработка формы входа
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!verify_csrf_token($_POST['csrf_token'] ?? '')) {
        $errors[] = 'Ошибка безопасности. Попробуйте еще раз.';
    } else {
        $email = sanitize_input($_POST['email'] ?? '');
        $password = $_POST['password'] ?? '';
        $remember_me = isset($_POST['remember_me']);

        // Валидация
        if (empty($email)) {
            $errors[] = 'Email обязателен для заполнения.';
        } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            $errors[] = 'Некорректный формат email.';
        }

        if (empty($password)) {
            $errors[] = 'Пароль обязателен для заполнения.';
        }

        // Авторизация
        if (empty($errors)) {
            try {
                $database = new Database();
                $db = $database->getConnection();
                $user = new User($db);

                if ($user->login($email, $password)) {
                    // Проверка верификации email
                    if (!$user->email_verified) {
                        $errors[] = 'Пожалуйста, подтвердите ваш email перед входом в систему.';
                    } else {
                        // Создание сессии
                        $_SESSION['user_id'] = $user->id;
                        $_SESSION['username'] = $user->username;
                        $_SESSION['email'] = $user->email;
                        $_SESSION['first_name'] = $user->first_name;
                        $_SESSION['last_name'] = $user->last_name;
                        $_SESSION['is_admin'] = $user->is_admin;
                        $_SESSION['balance'] = $user->balance;

                        // Запоминание пользователя
                        if ($remember_me) {
                            $token = bin2hex(random_bytes(32));
                            setcookie('remember_token', $token, time() + (30 * 24 * 60 * 60), '/', '', false, true);
                            // Здесь можно сохранить токен в базе данных
                        }

                        // Перенаправление
                        if ($user->is_admin) {
                            redirect('admin/dashboard.php');
                        } else {
                            redirect('dashboard.php');
                        }
                    }
                } else {
                    $errors[] = 'Неверный email или пароль.';
                }
            } catch (Exception $e) {
                $errors[] = 'Ошибка сервера. Попробуйте позже.';
                error_log("Login error: " . $e->getMessage());
            }
        }
    }
}

// Обработка верификации email
if (isset($_GET['verify']) && !empty($_GET['token'])) {
    try {
        $database = new Database();
        $db = $database->getConnection();
        $user = new User($db);
        
        if ($user->verifyEmail($_GET['token'])) {
            $success_message = 'Email успешно подтвержден! Теперь вы можете войти в систему.';
        } else {
            $errors[] = 'Неверный или истекший токен верификации.';
        }
    } catch (Exception $e) {
        $errors[] = 'Ошибка при верификации email.';
        error_log("Email verification error: " . $e->getMessage());
    }
}

$page_title = 'Вход - ' . SITE_NAME;
?>
<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?></title>
    
    <!-- CSS -->
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="assets/css/auth.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body class="auth-page">
    <div class="auth-container">
        <div class="auth-background">
            <div class="eco-particles"></div>
        </div>
        
        <div class="auth-content">
            <div class="auth-header">
                <a href="index.php" class="auth-logo">
                    <img src="assets/images/logo.png" alt="AstroGenix">
                    <span>AstroGenix</span>
                </a>
                <h1>Добро пожаловать</h1>
                <p>Войдите в свой аккаунт для управления инвестициями</p>
            </div>

            <?php if (!empty($errors)): ?>
                <div class="alert alert-error">
                    <i class="fas fa-exclamation-triangle"></i>
                    <ul>
                        <?php foreach ($errors as $error): ?>
                            <li><?php echo htmlspecialchars($error); ?></li>
                        <?php endforeach; ?>
                    </ul>
                </div>
            <?php endif; ?>

            <?php if (!empty($success_message)): ?>
                <div class="alert alert-success">
                    <i class="fas fa-check-circle"></i>
                    <?php echo htmlspecialchars($success_message); ?>
                </div>
            <?php endif; ?>

            <form class="auth-form" method="POST" action="">
                <input type="hidden" name="csrf_token" value="<?php echo generate_csrf_token(); ?>">
                
                <div class="form-group">
                    <label for="email">Email</label>
                    <div class="input-with-icon">
                        <i class="fas fa-envelope"></i>
                        <input type="email" id="email" name="email" 
                               value="<?php echo htmlspecialchars($email ?? ''); ?>" 
                               placeholder="Введите ваш email" required>
                    </div>
                </div>

                <div class="form-group">
                    <label for="password">Пароль</label>
                    <div class="input-with-icon">
                        <i class="fas fa-lock"></i>
                        <input type="password" id="password" name="password" 
                               placeholder="Введите ваш пароль" required>
                        <button type="button" class="password-toggle" onclick="togglePassword('password')">
                            <i class="fas fa-eye"></i>
                        </button>
                    </div>
                </div>

                <div class="form-options">
                    <label class="checkbox-label">
                        <input type="checkbox" name="remember_me">
                        <span class="checkmark"></span>
                        Запомнить меня
                    </label>
                    <a href="forgot-password.php" class="forgot-link">Забыли пароль?</a>
                </div>

                <button type="submit" class="btn btn-primary btn-full">
                    <i class="fas fa-sign-in-alt"></i>
                    Войти
                </button>
            </form>

            <div class="auth-divider">
                <span>или</span>
            </div>

            <div class="social-login">
                <button class="btn btn-social btn-google">
                    <i class="fab fa-google"></i>
                    Войти через Google
                </button>
                <button class="btn btn-social btn-vk">
                    <i class="fab fa-vk"></i>
                    Войти через VK
                </button>
            </div>

            <div class="auth-footer">
                <p>Нет аккаунта? <a href="register.php">Зарегистрироваться</a></p>
            </div>
        </div>

        <!-- Green Energy Animation -->
        <div class="energy-orbs">
            <div class="energy-orb"></div>
            <div class="energy-orb"></div>
            <div class="energy-orb"></div>
            <div class="energy-orb"></div>
            <div class="energy-orb"></div>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="assets/js/auth.js"></script>
</body>
</html>
