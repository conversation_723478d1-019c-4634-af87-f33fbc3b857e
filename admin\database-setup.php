<?php
/**
 * AstroGenix - Настройка базы данных
 * Эко-майнинговая инвестиционная платформа
 */

require_once '../config/config.php';

// Проверка авторизации и прав администратора
require_admin();

// Инициализация переменных
$error_message = '';
$success_message = '';
$setup_results = [];

try {
    $database = new Database();
    $db = $database->getConnection();
    
    // Обработка запросов на настройку
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        if (!verify_csrf_token($_POST['csrf_token'] ?? '')) {
            $error_message = 'Ошибка безопасности. Попробуйте еще раз.';
        } else {
            $action = sanitize_input($_POST['action'] ?? '');
            
            switch ($action) {
                case 'check_tables':
                    $setup_results = checkDatabaseTables($db);
                    $success_message = "Проверка таблиц базы данных завершена.";
                    break;
                    
                case 'create_tables':
                    $setup_results = createDatabaseTables($db);
                    $success_message = "Создание таблиц базы данных завершено.";
                    break;
                    
                case 'insert_sample_data':
                    $setup_results = insertSampleData($db);
                    $success_message = "Вставка демо-данных завершена.";
                    break;
                    
                case 'reset_database':
                    if (isset($_POST['confirm_reset']) && $_POST['confirm_reset'] === 'yes') {
                        $setup_results = resetDatabase($db);
                        $success_message = "База данных сброшена и пересоздана.";
                    } else {
                        $error_message = "Подтверждение сброса не получено.";
                    }
                    break;
                    
                case 'optimize_database':
                    $setup_results = optimizeDatabase($db);
                    $success_message = "Оптимизация базы данных завершена.";
                    break;
            }
        }
    }
    
} catch (Exception $e) {
    error_log("Database setup error: " . $e->getMessage());
    $error_message = "Ошибка настройки базы данных: " . $e->getMessage();
}

// Функция проверки таблиц
function checkDatabaseTables($db) {
    $results = [];
    
    $required_tables = [
        'users', 'investment_packages', 'user_investments', 'transactions',
        'referral_commissions', 'green_energy_stats', 'system_settings',
        'notifications', 'support_tickets', 'support_ticket_replies',
        'system_logs', 'user_sessions'
    ];
    
    foreach ($required_tables as $table) {
        $query = "SHOW TABLES LIKE :table";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':table', $table);
        $stmt->execute();
        
        $exists = $stmt->rowCount() > 0;
        
        if ($exists) {
            // Получение количества записей
            $count_query = "SELECT COUNT(*) as count FROM `$table`";
            $count_stmt = $db->prepare($count_query);
            $count_stmt->execute();
            $count = $count_stmt->fetch(PDO::FETCH_ASSOC)['count'];
            
            $results[$table] = [
                'exists' => true,
                'records' => $count,
                'status' => 'OK'
            ];
        } else {
            $results[$table] = [
                'exists' => false,
                'records' => 0,
                'status' => 'MISSING'
            ];
        }
    }
    
    return $results;
}

// Функция создания таблиц
function createDatabaseTables($db) {
    $results = [];
    $sql_file = '../config/database_init.sql';
    
    if (!file_exists($sql_file)) {
        throw new Exception("SQL файл не найден: $sql_file");
    }
    
    $sql_content = file_get_contents($sql_file);
    $statements = explode(';', $sql_content);
    
    $created_count = 0;
    $error_count = 0;
    
    foreach ($statements as $statement) {
        $statement = trim($statement);
        if (empty($statement) || strpos($statement, '--') === 0) {
            continue;
        }
        
        try {
            $db->exec($statement);
            $created_count++;
        } catch (PDOException $e) {
            $error_count++;
            $results['errors'][] = $e->getMessage();
        }
    }
    
    $results['summary'] = [
        'executed_statements' => $created_count,
        'errors' => $error_count,
        'status' => $error_count === 0 ? 'SUCCESS' : 'PARTIAL'
    ];
    
    return $results;
}

// Функция вставки демо-данных
function insertSampleData($db) {
    $results = [];
    
    // Создание дополнительных тестовых пользователей
    $sample_users = [
        ['demo1', '<EMAIL>', 'Демо', 'Пользователь 1', 500.00],
        ['demo2', '<EMAIL>', 'Демо', 'Пользователь 2', 750.00],
        ['demo3', '<EMAIL>', 'Демо', 'Пользователь 3', 1200.00]
    ];
    
    $user_count = 0;
    foreach ($sample_users as $user_data) {
        $password = password_hash('demo123', PASSWORD_DEFAULT);
        $referral_code = 'DEMO' . str_pad($user_count + 1, 3, '0', STR_PAD_LEFT);
        
        $query = "INSERT IGNORE INTO users (username, email, password, first_name, last_name, balance, is_active, email_verified, referral_code) 
                  VALUES (?, ?, ?, ?, ?, ?, 1, 1, ?)";
        
        $stmt = $db->prepare($query);
        if ($stmt->execute([$user_data[0], $user_data[1], $password, $user_data[2], $user_data[3], $user_data[4], $referral_code])) {
            $user_count++;
        }
    }
    
    // Создание демо-инвестиций
    $investment_count = 0;
    $users_query = "SELECT id FROM users WHERE username LIKE 'demo%' LIMIT 3";
    $users_stmt = $db->prepare($users_query);
    $users_stmt->execute();
    $demo_users = $users_stmt->fetchAll(PDO::FETCH_COLUMN);
    
    foreach ($demo_users as $user_id) {
        $package_id = rand(1, 4);
        $amount = rand(100, 1000);
        $start_date = date('Y-m-d H:i:s', time() - rand(1, 30) * 24 * 60 * 60);
        $end_date = date('Y-m-d H:i:s', strtotime($start_date) + 30 * 24 * 60 * 60);
        
        $query = "INSERT IGNORE INTO user_investments (user_id, package_id, amount, daily_profit, start_date, end_date, status) 
                  VALUES (?, ?, ?, ?, ?, ?, 'active')";
        
        $daily_profit = $amount * 0.02; // 2% в день
        $stmt = $db->prepare($query);
        if ($stmt->execute([$user_id, $package_id, $amount, $daily_profit, $start_date, $end_date])) {
            $investment_count++;
        }
    }
    
    // Создание демо-транзакций
    $transaction_count = 0;
    $transaction_types = ['deposit', 'withdrawal', 'profit'];
    
    foreach ($demo_users as $user_id) {
        for ($i = 0; $i < 3; $i++) {
            $type = $transaction_types[array_rand($transaction_types)];
            $amount = rand(10, 500);
            $status = rand(0, 1) ? 'completed' : 'pending';
            
            $query = "INSERT INTO transactions (user_id, type, amount, status, description) 
                      VALUES (?, ?, ?, ?, ?)";
            
            $description = "Демо-транзакция: $type";
            $stmt = $db->prepare($query);
            if ($stmt->execute([$user_id, $type, $amount, $status, $description])) {
                $transaction_count++;
            }
        }
    }
    
    // Создание записей зеленой энергии
    $energy_count = 0;
    foreach ($demo_users as $user_id) {
        $energy_generated = rand(50, 200);
        
        $query = "INSERT IGNORE INTO green_energy_stats (user_id, energy_generated, co2_saved, trees_planted) 
                  VALUES (?, ?, ?, ?)";
        
        $co2_saved = $energy_generated * 0.5;
        $trees_planted = intval($energy_generated / 10);
        
        $stmt = $db->prepare($query);
        if ($stmt->execute([$user_id, $energy_generated, $co2_saved, $trees_planted])) {
            $energy_count++;
        }
    }
    
    $results = [
        'users_created' => $user_count,
        'investments_created' => $investment_count,
        'transactions_created' => $transaction_count,
        'energy_records_created' => $energy_count,
        'status' => 'SUCCESS'
    ];
    
    return $results;
}

// Функция сброса базы данных
function resetDatabase($db) {
    $results = [];
    
    // Получение списка всех таблиц
    $tables_query = "SHOW TABLES";
    $tables_stmt = $db->prepare($tables_query);
    $tables_stmt->execute();
    $tables = $tables_stmt->fetchAll(PDO::FETCH_COLUMN);
    
    // Отключение проверки внешних ключей
    $db->exec("SET FOREIGN_KEY_CHECKS = 0");
    
    $dropped_count = 0;
    foreach ($tables as $table) {
        try {
            $db->exec("DROP TABLE IF EXISTS `$table`");
            $dropped_count++;
        } catch (PDOException $e) {
            $results['errors'][] = "Ошибка удаления таблицы $table: " . $e->getMessage();
        }
    }
    
    // Включение проверки внешних ключей
    $db->exec("SET FOREIGN_KEY_CHECKS = 1");
    
    // Пересоздание таблиц
    $create_results = createDatabaseTables($db);
    
    $results['dropped_tables'] = $dropped_count;
    $results['create_results'] = $create_results;
    $results['status'] = 'SUCCESS';
    
    return $results;
}

// Функция оптимизации базы данных
function optimizeDatabase($db) {
    $results = [];
    
    // Получение списка всех таблиц
    $tables_query = "SHOW TABLES";
    $tables_stmt = $db->prepare($tables_query);
    $tables_stmt->execute();
    $tables = $tables_stmt->fetchAll(PDO::FETCH_COLUMN);
    
    $optimized_count = 0;
    foreach ($tables as $table) {
        try {
            $db->exec("OPTIMIZE TABLE `$table`");
            $optimized_count++;
        } catch (PDOException $e) {
            $results['errors'][] = "Ошибка оптимизации таблицы $table: " . $e->getMessage();
        }
    }
    
    // Анализ таблиц
    $analyzed_count = 0;
    foreach ($tables as $table) {
        try {
            $db->exec("ANALYZE TABLE `$table`");
            $analyzed_count++;
        } catch (PDOException $e) {
            $results['errors'][] = "Ошибка анализа таблицы $table: " . $e->getMessage();
        }
    }
    
    $results['optimized_tables'] = $optimized_count;
    $results['analyzed_tables'] = $analyzed_count;
    $results['status'] = 'SUCCESS';
    
    return $results;
}

$page_title = 'Настройка базы данных - Админ-панель';
?>
<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?></title>
    
    <!-- CSS -->
    <link rel="stylesheet" href="../assets/css/style.css">
    <link rel="stylesheet" href="../assets/css/dashboard.css">
    <link rel="stylesheet" href="../assets/css/admin.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body class="dashboard-page admin-page">
    <!-- Admin Sidebar -->
    <?php include 'includes/admin-sidebar.php'; ?>

    <!-- Main Content -->
    <main class="main-content">
        <!-- Header -->
        <header class="dashboard-header">
            <div class="header-left">
                <button class="mobile-menu-toggle" id="mobileMenuToggle">
                    <i class="fas fa-bars"></i>
                </button>
                <h1>Настройка базы данных</h1>
            </div>
        </header>

        <!-- Dashboard Content -->
        <div class="dashboard-content">
            <?php if (!empty($error_message)): ?>
                <div class="alert alert-error">
                    <i class="fas fa-exclamation-triangle"></i>
                    <?php echo htmlspecialchars($error_message); ?>
                </div>
            <?php endif; ?>

            <?php if (!empty($success_message)): ?>
                <div class="alert alert-success">
                    <i class="fas fa-check-circle"></i>
                    <?php echo htmlspecialchars($success_message); ?>
                </div>
            <?php endif; ?>

            <!-- Database Setup Controls -->
            <div class="database-setup-grid">
                <!-- Check Tables -->
                <div class="setup-card">
                    <div class="card-header">
                        <h3><i class="fas fa-search"></i> Проверка таблиц</h3>
                    </div>
                    <div class="card-body">
                        <p>Проверка существования всех необходимых таблиц в базе данных.</p>
                        <form method="POST" class="setup-form">
                            <input type="hidden" name="csrf_token" value="<?php echo generate_csrf_token(); ?>">
                            <input type="hidden" name="action" value="check_tables">
                            
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-search"></i>
                                Проверить таблицы
                            </button>
                        </form>
                    </div>
                </div>

                <!-- Create Tables -->
                <div class="setup-card">
                    <div class="card-header">
                        <h3><i class="fas fa-plus"></i> Создание таблиц</h3>
                    </div>
                    <div class="card-body">
                        <p>Создание всех необходимых таблиц и вставка базовых данных.</p>
                        <form method="POST" class="setup-form">
                            <input type="hidden" name="csrf_token" value="<?php echo generate_csrf_token(); ?>">
                            <input type="hidden" name="action" value="create_tables">
                            
                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-plus"></i>
                                Создать таблицы
                            </button>
                        </form>
                    </div>
                </div>

                <!-- Insert Sample Data -->
                <div class="setup-card">
                    <div class="card-header">
                        <h3><i class="fas fa-database"></i> Демо-данные</h3>
                    </div>
                    <div class="card-body">
                        <p>Вставка демонстрационных данных для тестирования платформы.</p>
                        <form method="POST" class="setup-form">
                            <input type="hidden" name="csrf_token" value="<?php echo generate_csrf_token(); ?>">
                            <input type="hidden" name="action" value="insert_sample_data">
                            
                            <button type="submit" class="btn btn-info">
                                <i class="fas fa-database"></i>
                                Добавить демо-данные
                            </button>
                        </form>
                    </div>
                </div>

                <!-- Optimize Database -->
                <div class="setup-card">
                    <div class="card-header">
                        <h3><i class="fas fa-tachometer-alt"></i> Оптимизация</h3>
                    </div>
                    <div class="card-body">
                        <p>Оптимизация и анализ таблиц для улучшения производительности.</p>
                        <form method="POST" class="setup-form">
                            <input type="hidden" name="csrf_token" value="<?php echo generate_csrf_token(); ?>">
                            <input type="hidden" name="action" value="optimize_database">
                            
                            <button type="submit" class="btn btn-warning">
                                <i class="fas fa-tachometer-alt"></i>
                                Оптимизировать БД
                            </button>
                        </form>
                    </div>
                </div>

                <!-- Reset Database -->
                <div class="setup-card danger-card">
                    <div class="card-header">
                        <h3><i class="fas fa-exclamation-triangle"></i> Сброс базы данных</h3>
                    </div>
                    <div class="card-body">
                        <p><strong>ВНИМАНИЕ!</strong> Это действие удалит все данные и пересоздаст базу данных.</p>
                        <form method="POST" class="setup-form" id="resetForm">
                            <input type="hidden" name="csrf_token" value="<?php echo generate_csrf_token(); ?>">
                            <input type="hidden" name="action" value="reset_database">
                            <input type="hidden" name="confirm_reset" value="yes">
                            
                            <div class="form-group">
                                <label>
                                    <input type="checkbox" id="confirmReset" required>
                                    Я понимаю, что все данные будут удалены
                                </label>
                            </div>
                            
                            <button type="submit" class="btn btn-danger" disabled id="resetButton">
                                <i class="fas fa-exclamation-triangle"></i>
                                Сбросить базу данных
                            </button>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Setup Results -->
            <?php if (!empty($setup_results)): ?>
                <div class="setup-results">
                    <div class="card">
                        <div class="card-header">
                            <h3><i class="fas fa-chart-bar"></i> Результаты операции</h3>
                        </div>
                        <div class="card-body">
                            <?php if (isset($setup_results['summary'])): ?>
                                <div class="results-summary">
                                    <h4>Сводка:</h4>
                                    <ul>
                                        <li>Выполнено операций: <?php echo $setup_results['summary']['executed_statements'] ?? 0; ?></li>
                                        <li>Ошибок: <?php echo $setup_results['summary']['errors'] ?? 0; ?></li>
                                        <li>Статус: <?php echo $setup_results['summary']['status'] ?? 'UNKNOWN'; ?></li>
                                    </ul>
                                </div>
                            <?php endif; ?>
                            
                            <div class="results-details">
                                <h4>Подробности:</h4>
                                <pre><?php echo json_encode($setup_results, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE); ?></pre>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </main>

    <!-- JavaScript -->
    <script src="../assets/js/admin.js"></script>
    <script>
        // Подтверждение сброса базы данных
        document.getElementById('confirmReset').addEventListener('change', function() {
            document.getElementById('resetButton').disabled = !this.checked;
        });
        
        // Подтверждение перед опасными операциями
        document.querySelectorAll('.setup-form').forEach(form => {
            form.addEventListener('submit', function(e) {
                const action = this.querySelector('input[name="action"]').value;
                
                if (action === 'reset_database') {
                    if (!confirm('ВЫ УВЕРЕНЫ? Это действие НЕОБРАТИМО удалит все данные!')) {
                        e.preventDefault();
                    }
                } else if (action === 'create_tables') {
                    if (!confirm('Создать таблицы базы данных? Существующие данные могут быть изменены.')) {
                        e.preventDefault();
                    }
                }
            });
        });
    </script>
</body>
</html>
