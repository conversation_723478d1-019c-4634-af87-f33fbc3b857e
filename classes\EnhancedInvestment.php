<?php
/**
 * AstroGenix - Расширенная система инвестирования
 * Улучшенный класс для работы с инвестициями
 */

class EnhancedInvestment {
    private $db;
    private $table_name = "user_investments";
    private $packages_table = "investment_packages";
    
    public function __construct($database) {
        $this->db = $database;
    }
    
    /**
     * Получение всех инвестиционных пакетов с расширенной информацией
     */
    public function getAllPackages($featured_only = false, $package_type = null) {
        try {
            $where_conditions = ["is_active = 1"];
            $params = [];
            
            if ($featured_only) {
                $where_conditions[] = "is_featured = 1";
            }
            
            if ($package_type) {
                $where_conditions[] = "package_type = :package_type";
                $params[':package_type'] = $package_type;
            }
            
            $where_clause = implode(' AND ', $where_conditions);
            
            $query = "SELECT *, 
                      CASE 
                          WHEN package_type = 'compound' THEN 'Компаундный'
                          WHEN package_type = 'fixed' THEN 'Фиксированный'
                          WHEN package_type = 'variable' THEN 'Переменный'
                          WHEN package_type = 'vip' THEN 'VIP'
                          ELSE 'Простой'
                      END as type_name,
                      CASE 
                          WHEN risk_level = 'low' THEN 'Низкий'
                          WHEN risk_level = 'medium' THEN 'Средний'
                          WHEN risk_level = 'high' THEN 'Высокий'
                          WHEN risk_level = 'very_high' THEN 'Очень высокий'
                          ELSE 'Средний'
                      END as risk_name
                      FROM {$this->packages_table} 
                      WHERE {$where_clause} 
                      ORDER BY sort_order ASC, is_featured DESC, daily_profit_percent ASC";
            
            $stmt = $this->db->prepare($query);
            
            foreach ($params as $key => $value) {
                $stmt->bindValue($key, $value);
            }
            
            $stmt->execute();
            $packages = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            // Декодирование JSON полей
            foreach ($packages as &$package) {
                $package['features'] = json_decode($package['features'], true) ?: [];
                $package['requirements'] = json_decode($package['requirements'], true) ?: [];
            }
            
            return $packages;
        } catch (Exception $e) {
            error_log("Get packages error: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Создание инвестиции с расширенными возможностями
     */
    public function createEnhancedInvestment($user_id, $package_id, $amount, $options = []) {
        try {
            $this->db->beginTransaction();
            
            // Получение информации о пакете
            $package = $this->getPackageById($package_id);
            if (!$package) {
                throw new Exception("Инвестиционный пакет не найден");
            }
            
            // Проверка лимитов пользователя
            if (!$this->checkInvestmentLimits($user_id, $amount)) {
                throw new Exception("Превышены лимиты инвестирования");
            }
            
            // Проверка требований пакета
            if (!$this->checkPackageRequirements($user_id, $package)) {
                throw new Exception("Не выполнены требования для данного пакета");
            }
            
            // Проверка баланса
            $user_balance = $this->getUserBalance($user_id);
            if ($user_balance < $amount) {
                throw new Exception("Недостаточно средств на балансе");
            }
            
            // Расчет параметров инвестиции
            $daily_profit = $this->calculateDailyProfit($package, $amount);
            $start_date = date('Y-m-d');
            $end_date = date('Y-m-d', strtotime("+{$package['duration_days']} days"));
            
            // Настройки автореинвестирования
            $auto_reinvest = $options['auto_reinvest'] ?? false;
            $reinvest_percent = $options['reinvest_percent'] ?? 0;
            
            // Создание инвестиции
            $insert_query = "INSERT INTO {$this->table_name} 
                            (user_id, package_id, amount, daily_profit, start_date, end_date, 
                             status, investment_type, auto_reinvest, reinvest_percent, 
                             last_profit_date, next_profit_date, created_at) 
                            VALUES 
                            (:user_id, :package_id, :amount, :daily_profit, :start_date, :end_date, 
                             'active', :investment_type, :auto_reinvest, :reinvest_percent, 
                             :start_date, :next_profit_date, NOW())";
            
            $next_profit_date = $this->calculateNextProfitDate($start_date, $package['compound_frequency']);
            
            $stmt = $this->db->prepare($insert_query);
            $stmt->bindParam(':user_id', $user_id, PDO::PARAM_INT);
            $stmt->bindParam(':package_id', $package_id, PDO::PARAM_INT);
            $stmt->bindParam(':amount', $amount);
            $stmt->bindParam(':daily_profit', $daily_profit);
            $stmt->bindParam(':start_date', $start_date);
            $stmt->bindParam(':end_date', $end_date);
            $stmt->bindParam(':investment_type', $package['package_type']);
            $stmt->bindParam(':auto_reinvest', $auto_reinvest, PDO::PARAM_BOOL);
            $stmt->bindParam(':reinvest_percent', $reinvest_percent);
            $stmt->bindParam(':next_profit_date', $next_profit_date);
            
            if (!$stmt->execute()) {
                throw new Exception("Ошибка создания инвестиции");
            }
            
            $investment_id = $this->db->lastInsertId();
            
            // Списание средств с баланса
            $this->updateUserBalance($user_id, -$amount);
            $this->updateUserInvestmentStats($user_id, $amount, 0);
            
            // Создание транзакции
            $this->createInvestmentTransaction($user_id, $amount, $package['name'], 'investment');
            
            // Обработка реферальных бонусов
            $this->processReferralBonuses($user_id, $amount);
            
            // Создание уведомления
            $this->createInvestmentNotification(
                $user_id, 
                $investment_id, 
                'investment_created',
                'Инвестиция создана',
                "Создана инвестиция в пакет \"{$package['name']}\" на сумму {$amount} USDT"
            );
            
            // Обновление экологической статистики
            $this->updateGreenEnergyStats($user_id, $amount);
            
            $this->db->commit();
            
            return [
                'success' => true,
                'investment_id' => $investment_id,
                'daily_profit' => $daily_profit,
                'end_date' => $end_date
            ];
            
        } catch (Exception $e) {
            $this->db->rollback();
            error_log("Enhanced investment creation error: " . $e->getMessage());
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Расчет ежедневной прибыли с учетом типа пакета
     */
    private function calculateDailyProfit($package, $amount) {
        $base_profit = ($amount * $package['daily_profit_percent']) / 100;
        
        switch ($package['package_type']) {
            case 'variable':
                // Для переменного пакета добавляем случайность
                $variation = rand(-10, 20) / 100; // от -10% до +20%
                $profit = $base_profit * (1 + $variation);
                
                // Ограничиваем минимальными и максимальными значениями
                $min_profit = ($amount * $package['min_profit_percent']) / 100;
                $max_profit = ($amount * $package['max_profit_percent']) / 100;
                
                return max($min_profit, min($max_profit, $profit));
                
            case 'compound':
                // Для компаундного пакета базовая прибыль
                return $base_profit;
                
            case 'fixed':
                // Для фиксированного пакета точная прибыль
                return $base_profit;
                
            case 'vip':
                // Для VIP пакета добавляем бонус
                $bonus = ($amount * $package['bonus_percent']) / 100;
                return $base_profit + $bonus;
                
            default:
                return $base_profit;
        }
    }
    
    /**
     * Расчет следующей даты прибыли
     */
    private function calculateNextProfitDate($current_date, $frequency) {
        switch ($frequency) {
            case 'weekly':
                return date('Y-m-d', strtotime($current_date . ' +1 week'));
            case 'monthly':
                return date('Y-m-d', strtotime($current_date . ' +1 month'));
            default: // daily
                return date('Y-m-d', strtotime($current_date . ' +1 day'));
        }
    }
    
    /**
     * Проверка лимитов инвестирования
     */
    private function checkInvestmentLimits($user_id, $amount) {
        try {
            // Получение лимитов пользователя
            $limits_query = "SELECT * FROM investment_limits WHERE user_id = :user_id";
            $limits_stmt = $this->db->prepare($limits_query);
            $limits_stmt->bindParam(':user_id', $user_id, PDO::PARAM_INT);
            $limits_stmt->execute();
            $limits = $limits_stmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$limits) {
                // Создание лимитов по умолчанию
                $this->createDefaultLimits($user_id);
                return true; // Первая инвестиция всегда разрешена
            }
            
            // Проверка дневного лимита
            $daily_invested_query = "SELECT COALESCE(SUM(amount), 0) as daily_total 
                                    FROM {$this->table_name} 
                                    WHERE user_id = :user_id AND DATE(created_at) = CURDATE()";
            $daily_stmt = $this->db->prepare($daily_invested_query);
            $daily_stmt->bindParam(':user_id', $user_id, PDO::PARAM_INT);
            $daily_stmt->execute();
            $daily_total = $daily_stmt->fetch(PDO::FETCH_ASSOC)['daily_total'];
            
            if (($daily_total + $amount) > $limits['daily_investment_limit']) {
                return false;
            }
            
            // Проверка месячного лимита
            $monthly_invested_query = "SELECT COALESCE(SUM(amount), 0) as monthly_total 
                                      FROM {$this->table_name} 
                                      WHERE user_id = :user_id 
                                      AND created_at >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)";
            $monthly_stmt = $this->db->prepare($monthly_invested_query);
            $monthly_stmt->bindParam(':user_id', $user_id, PDO::PARAM_INT);
            $monthly_stmt->execute();
            $monthly_total = $monthly_stmt->fetch(PDO::FETCH_ASSOC)['monthly_total'];
            
            if (($monthly_total + $amount) > $limits['monthly_investment_limit']) {
                return false;
            }
            
            // Проверка количества активных инвестиций
            $active_count_query = "SELECT COUNT(*) as active_count 
                                  FROM {$this->table_name} 
                                  WHERE user_id = :user_id AND status = 'active'";
            $active_stmt = $this->db->prepare($active_count_query);
            $active_stmt->bindParam(':user_id', $user_id, PDO::PARAM_INT);
            $active_stmt->execute();
            $active_count = $active_stmt->fetch(PDO::FETCH_ASSOC)['active_count'];
            
            if ($active_count >= $limits['max_active_investments']) {
                return false;
            }
            
            return true;
            
        } catch (Exception $e) {
            error_log("Check investment limits error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Проверка требований пакета
     */
    private function checkPackageRequirements($user_id, $package) {
        try {
            $requirements = json_decode($package['requirements'], true);
            
            if (empty($requirements)) {
                return true; // Нет требований
            }
            
            // Получение статистики пользователя
            $user_stats = $this->getUserInvestmentStats($user_id);
            
            foreach ($requirements as $requirement) {
                switch ($requirement) {
                    case 'VIP статус':
                        if (!$this->hasVipStatus($user_id)) {
                            return false;
                        }
                        break;
                        
                    case 'Минимум 3 завершенные инвестиции':
                        if ($user_stats['completed_investments'] < 3) {
                            return false;
                        }
                        break;
                        
                    case 'Минимум 50,000 USDT оборота':
                        if ($user_stats['total_invested'] < 50000) {
                            return false;
                        }
                        break;
                        
                    case 'Подтверждение личности':
                        if (!$this->isIdentityVerified($user_id)) {
                            return false;
                        }
                        break;
                }
            }
            
            return true;
            
        } catch (Exception $e) {
            error_log("Check package requirements error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Получение детальной аналитики инвестиций пользователя
     */
    public function getUserInvestmentAnalytics($user_id) {
        try {
            $query = "SELECT 
                        COUNT(*) as total_investments,
                        COUNT(CASE WHEN status = 'active' THEN 1 END) as active_investments,
                        COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_investments,
                        COALESCE(SUM(amount), 0) as total_invested,
                        COALESCE(SUM(total_earned), 0) as total_earned,
                        COALESCE(SUM(compound_earnings), 0) as total_compound_earnings,
                        COALESCE(AVG(daily_profit), 0) as avg_daily_profit,
                        COALESCE(MAX(amount), 0) as largest_investment,
                        COUNT(CASE WHEN auto_reinvest = 1 THEN 1 END) as auto_reinvest_count,
                        COUNT(CASE WHEN investment_type = 'compound' THEN 1 END) as compound_investments
                      FROM {$this->table_name} 
                      WHERE user_id = :user_id";
            
            $stmt = $this->db->prepare($query);
            $stmt->bindParam(':user_id', $user_id, PDO::PARAM_INT);
            $stmt->execute();
            $analytics = $stmt->fetch(PDO::FETCH_ASSOC);
            
            // Расчет дополнительных метрик
            if ($analytics['total_invested'] > 0) {
                $analytics['roi_percent'] = round(($analytics['total_earned'] / $analytics['total_invested']) * 100, 2);
            } else {
                $analytics['roi_percent'] = 0;
            }
            
            // Получение статистики по типам пакетов
            $types_query = "SELECT 
                              ip.package_type,
                              COUNT(*) as count,
                              SUM(ui.amount) as total_amount,
                              SUM(ui.total_earned) as total_earned
                            FROM {$this->table_name} ui
                            JOIN {$this->packages_table} ip ON ui.package_id = ip.id
                            WHERE ui.user_id = :user_id
                            GROUP BY ip.package_type";
            
            $types_stmt = $this->db->prepare($types_query);
            $types_stmt->bindParam(':user_id', $user_id, PDO::PARAM_INT);
            $types_stmt->execute();
            $analytics['by_type'] = $types_stmt->fetchAll(PDO::FETCH_ASSOC);
            
            return $analytics;
            
        } catch (Exception $e) {
            error_log("Get user investment analytics error: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Создание уведомления об инвестиции
     */
    private function createInvestmentNotification($user_id, $investment_id, $type, $title, $message) {
        try {
            $query = "INSERT INTO investment_notifications 
                      (user_id, investment_id, notification_type, title, message) 
                      VALUES 
                      (:user_id, :investment_id, :type, :title, :message)";
            
            $stmt = $this->db->prepare($query);
            $stmt->bindParam(':user_id', $user_id, PDO::PARAM_INT);
            $stmt->bindParam(':investment_id', $investment_id, PDO::PARAM_INT);
            $stmt->bindParam(':type', $type);
            $stmt->bindParam(':title', $title);
            $stmt->bindParam(':message', $message);
            
            return $stmt->execute();
        } catch (Exception $e) {
            error_log("Create investment notification error: " . $e->getMessage());
            return false;
        }
    }
    
    // Вспомогательные методы (заглушки для методов, которые должны быть реализованы)
    private function getPackageById($package_id) {
        $query = "SELECT * FROM {$this->packages_table} WHERE id = :id AND is_active = 1";
        $stmt = $this->db->prepare($query);
        $stmt->bindParam(':id', $package_id, PDO::PARAM_INT);
        $stmt->execute();
        $package = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($package) {
            $package['features'] = json_decode($package['features'], true) ?: [];
            $package['requirements'] = json_decode($package['requirements'], true) ?: [];
        }
        
        return $package;
    }
    
    private function getUserBalance($user_id) {
        $query = "SELECT balance FROM users WHERE id = :id";
        $stmt = $this->db->prepare($query);
        $stmt->bindParam(':id', $user_id, PDO::PARAM_INT);
        $stmt->execute();
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        return $result ? $result['balance'] : 0;
    }
    
    private function updateUserBalance($user_id, $amount) {
        $query = "UPDATE users SET balance = balance + :amount WHERE id = :id";
        $stmt = $this->db->prepare($query);
        $stmt->bindParam(':amount', $amount);
        $stmt->bindParam(':id', $user_id, PDO::PARAM_INT);
        return $stmt->execute();
    }
    
    private function updateUserInvestmentStats($user_id, $invested, $earned) {
        $query = "UPDATE users SET total_invested = total_invested + :invested, total_earned = total_earned + :earned WHERE id = :id";
        $stmt = $this->db->prepare($query);
        $stmt->bindParam(':invested', $invested);
        $stmt->bindParam(':earned', $earned);
        $stmt->bindParam(':id', $user_id, PDO::PARAM_INT);
        return $stmt->execute();
    }
    
    private function createInvestmentTransaction($user_id, $amount, $package_name, $type) {
        $query = "INSERT INTO transactions (user_id, type, amount, status, description) VALUES (:user_id, :type, :amount, 'completed', :description)";
        $stmt = $this->db->prepare($query);
        $stmt->bindParam(':user_id', $user_id, PDO::PARAM_INT);
        $stmt->bindParam(':type', $type);
        $stmt->bindParam(':amount', $amount);
        $description = "Инвестиция в пакет: " . $package_name;
        $stmt->bindParam(':description', $description);
        return $stmt->execute();
    }
    
    private function processReferralBonuses($user_id, $amount) {
        // Реализация реферальных бонусов
        return true;
    }
    
    private function updateGreenEnergyStats($user_id, $amount) {
        // Реализация обновления экологической статистики
        return true;
    }
    
    private function createDefaultLimits($user_id) {
        $query = "INSERT INTO investment_limits (user_id) VALUES (:user_id)";
        $stmt = $this->db->prepare($query);
        $stmt->bindParam(':user_id', $user_id, PDO::PARAM_INT);
        return $stmt->execute();
    }
    
    private function hasVipStatus($user_id) {
        // Проверка VIP статуса
        return false;
    }
    
    private function isIdentityVerified($user_id) {
        // Проверка верификации личности
        return true;
    }
    
    private function getUserInvestmentStats($user_id) {
        $query = "SELECT 
                    COUNT(*) as total_investments,
                    COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_investments,
                    COALESCE(SUM(amount), 0) as total_invested
                  FROM {$this->table_name} 
                  WHERE user_id = :user_id";
        
        $stmt = $this->db->prepare($query);
        $stmt->bindParam(':user_id', $user_id, PDO::PARAM_INT);
        $stmt->execute();
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }
}
?>
