<?php
/**
 * AstroGenix - Управление страницами CMS
 * Система управления контентом
 */

require_once '../config/config.php';
require_once '../classes/CMS.php';

// Проверка авторизации и прав администратора
require_admin();

// Инициализация переменных
$cms = new CMS(new Database());
$error_message = '';
$success_message = '';
$pages = [];

// Параметры фильтрации и пагинации
$status_filter = sanitize_input($_GET['status'] ?? '');
$search = sanitize_input($_GET['search'] ?? '');
$page = max(1, intval($_GET['page'] ?? 1));
$per_page = 20;
$offset = ($page - 1) * $per_page;

try {
    $database = new Database();
    $db = $database->getConnection();
    
    // Обработка действий
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        if (!verify_csrf_token($_POST['csrf_token'] ?? '')) {
            $error_message = 'Ошибка безопасности. Попробуйте еще раз.';
        } else {
            $action = sanitize_input($_POST['action'] ?? '');
            
            switch ($action) {
                case 'delete_page':
                    $page_id = intval($_POST['page_id'] ?? 0);
                    if ($page_id > 0) {
                        $delete_query = "UPDATE cms_pages SET status = 'archived' WHERE id = :id";
                        $delete_stmt = $db->prepare($delete_query);
                        $delete_stmt->bindParam(':id', $page_id, PDO::PARAM_INT);
                        
                        if ($delete_stmt->execute()) {
                            $success_message = 'Страница успешно архивирована.';
                        } else {
                            $error_message = 'Ошибка при архивировании страницы.';
                        }
                    }
                    break;
                    
                case 'bulk_action':
                    $bulk_action = sanitize_input($_POST['bulk_action'] ?? '');
                    $selected_pages = $_POST['selected_pages'] ?? [];
                    
                    if (!empty($selected_pages) && !empty($bulk_action)) {
                        $placeholders = str_repeat('?,', count($selected_pages) - 1) . '?';
                        
                        switch ($bulk_action) {
                            case 'publish':
                                $bulk_query = "UPDATE cms_pages SET status = 'published' WHERE id IN ($placeholders)";
                                break;
                            case 'draft':
                                $bulk_query = "UPDATE cms_pages SET status = 'draft' WHERE id IN ($placeholders)";
                                break;
                            case 'archive':
                                $bulk_query = "UPDATE cms_pages SET status = 'archived' WHERE id IN ($placeholders)";
                                break;
                        }
                        
                        if (isset($bulk_query)) {
                            $bulk_stmt = $db->prepare($bulk_query);
                            if ($bulk_stmt->execute($selected_pages)) {
                                $success_message = "Массовое действие выполнено для " . count($selected_pages) . " страниц.";
                            } else {
                                $error_message = 'Ошибка при выполнении массового действия.';
                            }
                        }
                    }
                    break;
            }
        }
    }
    
    // Построение запроса для получения страниц
    $where_conditions = [];
    $params = [];
    
    if (!empty($status_filter)) {
        $where_conditions[] = "p.status = :status";
        $params[':status'] = $status_filter;
    }
    
    if (!empty($search)) {
        $where_conditions[] = "(p.title LIKE :search OR p.content LIKE :search OR p.slug LIKE :search)";
        $params[':search'] = '%' . $search . '%';
    }
    
    $where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';
    
    // Получение страниц
    $pages_query = "SELECT p.*, u.username as author_name 
                    FROM cms_pages p 
                    LEFT JOIN users u ON p.author_id = u.id 
                    $where_clause 
                    ORDER BY p.created_at DESC 
                    LIMIT :limit OFFSET :offset";
    
    $pages_stmt = $db->prepare($pages_query);
    
    foreach ($params as $key => $value) {
        $pages_stmt->bindValue($key, $value);
    }
    
    $pages_stmt->bindValue(':limit', $per_page, PDO::PARAM_INT);
    $pages_stmt->bindValue(':offset', $offset, PDO::PARAM_INT);
    $pages_stmt->execute();
    
    $pages = $pages_stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Подсчет общего количества страниц
    $count_query = "SELECT COUNT(*) as total FROM cms_pages p $where_clause";
    $count_stmt = $db->prepare($count_query);
    
    foreach ($params as $key => $value) {
        $count_stmt->bindValue($key, $value);
    }
    
    $count_stmt->execute();
    $total_pages = $count_stmt->fetch(PDO::FETCH_ASSOC)['total'];
    $total_pages_count = ceil($total_pages / $per_page);
    
} catch (Exception $e) {
    error_log("CMS pages error: " . $e->getMessage());
    $error_message = "Ошибка загрузки страниц. Попробуйте обновить страницу.";
}

$page_title = 'Управление страницами - CMS';
?>
<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?></title>
    
    <!-- CSS -->
    <link rel="stylesheet" href="../assets/css/style.css">
    <link rel="stylesheet" href="../assets/css/dashboard.css">
    <link rel="stylesheet" href="../assets/css/admin.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body class="dashboard-page admin-page">
    <!-- Admin Sidebar -->
    <?php include 'includes/admin-sidebar.php'; ?>

    <!-- Main Content -->
    <main class="main-content">
        <!-- Header -->
        <header class="dashboard-header">
            <div class="header-left">
                <button class="mobile-menu-toggle" id="mobileMenuToggle">
                    <i class="fas fa-bars"></i>
                </button>
                <h1>Управление страницами</h1>
            </div>
            <div class="header-right">
                <a href="cms-page-edit.php" class="btn btn-primary">
                    <i class="fas fa-plus"></i>
                    Создать страницу
                </a>
            </div>
        </header>

        <!-- Dashboard Content -->
        <div class="dashboard-content">
            <?php if (!empty($error_message)): ?>
                <div class="alert alert-error">
                    <i class="fas fa-exclamation-triangle"></i>
                    <?php echo htmlspecialchars($error_message); ?>
                </div>
            <?php endif; ?>

            <?php if (!empty($success_message)): ?>
                <div class="alert alert-success">
                    <i class="fas fa-check-circle"></i>
                    <?php echo htmlspecialchars($success_message); ?>
                </div>
            <?php endif; ?>

            <!-- Filters and Search -->
            <div class="cms-controls">
                <div class="admin-filters">
                    <form method="GET" class="filters-form">
                        <div class="filter-group">
                            <input type="text" name="search" value="<?php echo htmlspecialchars($search); ?>" 
                                   placeholder="Поиск по названию, содержимому..." class="filter-input">
                        </div>
                        <div class="filter-group">
                            <select name="status" class="filter-input">
                                <option value="">Все статусы</option>
                                <option value="published" <?php echo $status_filter === 'published' ? 'selected' : ''; ?>>Опубликовано</option>
                                <option value="draft" <?php echo $status_filter === 'draft' ? 'selected' : ''; ?>>Черновик</option>
                                <option value="archived" <?php echo $status_filter === 'archived' ? 'selected' : ''; ?>>Архив</option>
                            </select>
                        </div>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search"></i>
                            Найти
                        </button>
                        <a href="cms-pages.php" class="btn btn-outline">
                            <i class="fas fa-times"></i>
                            Сбросить
                        </a>
                    </form>
                </div>
            </div>

            <!-- Pages Table -->
            <div class="admin-table-card">
                <div class="table-header">
                    <h3><i class="fas fa-file-alt"></i> Страницы сайта</h3>
                    <div class="table-stats">
                        Всего: <?php echo number_format($total_pages); ?>
                    </div>
                </div>
                
                <?php if (empty($pages)): ?>
                    <div class="empty-state">
                        <i class="fas fa-file-alt"></i>
                        <p>Страницы не найдены</p>
                        <a href="cms-page-edit.php" class="btn btn-primary">
                            <i class="fas fa-plus"></i>
                            Создать первую страницу
                        </a>
                    </div>
                <?php else: ?>
                    <form method="POST" id="bulkForm">
                        <input type="hidden" name="csrf_token" value="<?php echo generate_csrf_token(); ?>">
                        <input type="hidden" name="action" value="bulk_action">
                        
                        <div class="bulk-actions">
                            <select name="bulk_action" id="bulkAction">
                                <option value="">Массовые действия</option>
                                <option value="publish">Опубликовать</option>
                                <option value="draft">В черновики</option>
                                <option value="archive">Архивировать</option>
                            </select>
                            <button type="submit" class="btn btn-outline" disabled id="bulkSubmit">
                                Применить
                            </button>
                        </div>
                        
                        <div class="table-container">
                            <table class="admin-table cms-pages-table">
                                <thead>
                                    <tr>
                                        <th width="40">
                                            <input type="checkbox" id="selectAll">
                                        </th>
                                        <th>Название</th>
                                        <th>Slug</th>
                                        <th>Статус</th>
                                        <th>Автор</th>
                                        <th>Дата создания</th>
                                        <th>Действия</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($pages as $page): ?>
                                        <tr>
                                            <td>
                                                <input type="checkbox" name="selected_pages[]" value="<?php echo $page['id']; ?>" class="page-checkbox">
                                            </td>
                                            <td>
                                                <div class="page-title">
                                                    <strong><?php echo htmlspecialchars($page['title']); ?></strong>
                                                    <?php if ($page['is_homepage']): ?>
                                                        <span class="badge badge-info">Главная</span>
                                                    <?php endif; ?>
                                                    <?php if (!$page['show_in_menu']): ?>
                                                        <span class="badge badge-secondary">Скрыта из меню</span>
                                                    <?php endif; ?>
                                                </div>
                                                <?php if ($page['excerpt']): ?>
                                                    <small class="page-excerpt"><?php echo htmlspecialchars(substr($page['excerpt'], 0, 100)); ?>...</small>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <code class="page-slug"><?php echo htmlspecialchars($page['slug']); ?></code>
                                            </td>
                                            <td>
                                                <span class="status-badge status-<?php echo $page['status']; ?>">
                                                    <?php
                                                    $statuses = [
                                                        'published' => 'Опубликовано',
                                                        'draft' => 'Черновик',
                                                        'archived' => 'Архив'
                                                    ];
                                                    echo $statuses[$page['status']] ?? $page['status'];
                                                    ?>
                                                </span>
                                            </td>
                                            <td>
                                                <?php echo htmlspecialchars($page['author_name'] ?? 'Неизвестно'); ?>
                                            </td>
                                            <td>
                                                <?php echo date('d.m.Y H:i', strtotime($page['created_at'])); ?>
                                            </td>
                                            <td>
                                                <div class="action-buttons">
                                                    <?php if ($page['status'] === 'published'): ?>
                                                        <a href="../<?php echo $page['slug']; ?>" target="_blank" 
                                                           class="btn btn-small btn-outline" title="Просмотр">
                                                            <i class="fas fa-eye"></i>
                                                        </a>
                                                    <?php endif; ?>
                                                    
                                                    <a href="cms-page-edit.php?id=<?php echo $page['id']; ?>" 
                                                       class="btn btn-small btn-primary" title="Редактировать">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    
                                                    <button type="button" class="btn btn-small btn-danger" 
                                                            onclick="deletePage(<?php echo $page['id']; ?>)" title="Удалить">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </form>
                    
                    <!-- Pagination -->
                    <?php if ($total_pages_count > 1): ?>
                        <div class="pagination">
                            <?php if ($page > 1): ?>
                                <a href="?page=<?php echo $page - 1; ?>&<?php echo http_build_query(['status' => $status_filter, 'search' => $search]); ?>" 
                                   class="pagination-btn">
                                    <i class="fas fa-chevron-left"></i>
                                </a>
                            <?php endif; ?>
                            
                            <?php for ($i = max(1, $page - 2); $i <= min($total_pages_count, $page + 2); $i++): ?>
                                <a href="?page=<?php echo $i; ?>&<?php echo http_build_query(['status' => $status_filter, 'search' => $search]); ?>" 
                                   class="pagination-btn <?php echo $i === $page ? 'active' : ''; ?>">
                                    <?php echo $i; ?>
                                </a>
                            <?php endfor; ?>
                            
                            <?php if ($page < $total_pages_count): ?>
                                <a href="?page=<?php echo $page + 1; ?>&<?php echo http_build_query(['status' => $status_filter, 'search' => $search]); ?>" 
                                   class="pagination-btn">
                                    <i class="fas fa-chevron-right"></i>
                                </a>
                            <?php endif; ?>
                        </div>
                    <?php endif; ?>
                <?php endif; ?>
            </div>
        </div>
    </main>

    <!-- Delete Confirmation Modal -->
    <div id="deleteModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Подтверждение удаления</h3>
                <button class="modal-close" onclick="closeDeleteModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <p>Вы уверены, что хотите архивировать эту страницу? Она будет скрыта с сайта, но останется в базе данных.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-outline" onclick="closeDeleteModal()">Отмена</button>
                <form method="POST" style="display: inline;">
                    <input type="hidden" name="csrf_token" value="<?php echo generate_csrf_token(); ?>">
                    <input type="hidden" name="action" value="delete_page">
                    <input type="hidden" name="page_id" id="deletePageId">
                    <button type="submit" class="btn btn-danger">Архивировать</button>
                </form>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="../assets/js/admin.js"></script>
    <script>
        // Массовые действия
        document.addEventListener('DOMContentLoaded', function() {
            const selectAll = document.getElementById('selectAll');
            const pageCheckboxes = document.querySelectorAll('.page-checkbox');

            if (selectAll) {
                selectAll.addEventListener('change', function() {
                    pageCheckboxes.forEach(checkbox => {
                        checkbox.checked = this.checked;
                    });
                    updateBulkActions();
                });
            }

            pageCheckboxes.forEach(checkbox => {
                checkbox.addEventListener('change', updateBulkActions);
            });

            function updateBulkActions() {
                const checkedBoxes = document.querySelectorAll('.page-checkbox:checked');
                const bulkSubmit = document.getElementById('bulkSubmit');
                const bulkAction = document.getElementById('bulkAction');

                if (bulkSubmit && bulkAction) {
                    bulkSubmit.disabled = checkedBoxes.length === 0 || bulkAction.value === '';
                }
            }

            const bulkAction = document.getElementById('bulkAction');
            if (bulkAction) {
                bulkAction.addEventListener('change', updateBulkActions);
            }

            // Подтверждение массовых действий
            const bulkForm = document.getElementById('bulkForm');
            if (bulkForm) {
                bulkForm.addEventListener('submit', function(e) {
                    const checkedBoxes = document.querySelectorAll('.page-checkbox:checked');
                    const action = document.getElementById('bulkAction').value;

                    if (checkedBoxes.length === 0) {
                        e.preventDefault();
                        alert('Выберите страницы для выполнения действия.');
                        return;
                    }

                    const actionNames = {
                        'publish': 'опубликовать',
                        'draft': 'перевести в черновики',
                        'archive': 'архивировать'
                    };

                    if (!confirm(`Вы уверены, что хотите ${actionNames[action]} ${checkedBoxes.length} страниц?`)) {
                        e.preventDefault();
                    }
                });
            }
        });

        // Удаление страницы
        function deletePage(pageId) {
            document.getElementById('deletePageId').value = pageId;
            document.getElementById('deleteModal').style.display = 'block';
        }

        function closeDeleteModal() {
            document.getElementById('deleteModal').style.display = 'none';
        }

        // Закрытие модального окна по клику вне его
        window.addEventListener('click', function(e) {
            const modal = document.getElementById('deleteModal');
            if (e.target === modal) {
                closeDeleteModal();
            }
        });
    </script>
</body>
</html>
