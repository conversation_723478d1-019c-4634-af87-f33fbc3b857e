/**
 * AstroGenix - Анимации для главной страницы
 * Эко-майнинговая инвестиционная платформа
 */

document.addEventListener('DOMContentLoaded', function() {
    initLandingAnimations();
});

function initLandingAnimations() {
    // Инициализация анимаций при прокрутке
    initScrollAnimations();
    
    // Инициализация счетчиков статистики
    initStatCounters();
    
    // Инициализация живого счетчика энергии
    initLiveEnergyCounter();
    
    // Инициализация анимаций частиц
    initParticleAnimations();
    
    // Инициализация мобильного меню
    initMobileMenu();
    
    // Инициализация загрузки пакетов
    loadInvestmentPackages();
}

// Анимации при прокрутке
function initScrollAnimations() {
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('visible');
            }
        });
    }, observerOptions);

    // Наблюдение за элементами с анимацией
    const animatedElements = document.querySelectorAll('.animate-on-scroll');
    animatedElements.forEach(el => observer.observe(el));
}

// Счетчики статистики
function initStatCounters() {
    const statNumbers = document.querySelectorAll('.stat-number');
    
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const element = entry.target;
                const finalValue = getStatValue(element.id);
                animateCounter(element, 0, finalValue, 2000);
                observer.unobserve(element);
            }
        });
    }, { threshold: 0.5 });

    statNumbers.forEach(stat => observer.observe(stat));
}

// Получение значений статистики
function getStatValue(statId) {
    const values = {
        'total-invested': 284750,
        'active-users': 15420,
        'green-energy': 847250
    };
    return values[statId] || 0;
}

// Анимация счетчика
function animateCounter(element, start, end, duration) {
    const startTime = performance.now();
    const suffix = getCounterSuffix(element.id);
    
    function updateCounter(currentTime) {
        const elapsed = currentTime - startTime;
        const progress = Math.min(elapsed / duration, 1);
        
        const current = start + (end - start) * easeOutCubic(progress);
        const formattedValue = formatCounterValue(current, element.id);
        
        element.textContent = formattedValue + suffix;
        
        if (progress < 1) {
            requestAnimationFrame(updateCounter);
        }
    }
    
    requestAnimationFrame(updateCounter);
}

// Форматирование значений счетчика
function formatCounterValue(value, statId) {
    if (statId === 'total-invested') {
        return Math.floor(value).toLocaleString('en-US') + ' USDT';
    } else if (statId === 'green-energy') {
        return Math.floor(value).toLocaleString('en-US');
    } else {
        return Math.floor(value).toLocaleString('en-US');
    }
}

// Получение суффикса для счетчика
function getCounterSuffix(statId) {
    const suffixes = {
        'total-invested': '',
        'active-users': '',
        'green-energy': ''
    };
    return suffixes[statId] || '';
}

// Живой счетчик энергии
function initLiveEnergyCounter() {
    const energyCounter = document.getElementById('live-energy-counter');
    if (!energyCounter) return;
    
    let currentValue = 0;
    const targetIncrement = 0.5; // кВт⋅ч в секунду
    
    function updateEnergyCounter() {
        currentValue += targetIncrement;
        energyCounter.textContent = currentValue.toFixed(1);
        
        // Добавляем небольшую случайность
        const randomDelay = 800 + Math.random() * 400;
        setTimeout(updateEnergyCounter, randomDelay);
    }
    
    // Начальное значение
    currentValue = 15420.5;
    energyCounter.textContent = currentValue.toFixed(1);
    
    // Запуск счетчика
    setTimeout(updateEnergyCounter, 1000);
}

// Анимации частиц
function initParticleAnimations() {
    const particleContainers = document.querySelectorAll('.eco-particles');
    
    particleContainers.forEach(container => {
        // Создание дополнительных частиц
        createFloatingParticles(container);
    });
}

// Создание плавающих частиц
function createFloatingParticles(container) {
    const particleCount = 15;
    
    for (let i = 0; i < particleCount; i++) {
        const particle = document.createElement('div');
        particle.className = 'floating-particle';
        
        const size = Math.random() * 4 + 2;
        const x = Math.random() * 100;
        const y = Math.random() * 100;
        const duration = Math.random() * 10 + 10;
        const delay = Math.random() * 5;
        
        particle.style.cssText = `
            position: absolute;
            width: ${size}px;
            height: ${size}px;
            background: radial-gradient(circle, rgba(46, 204, 113, 0.6) 0%, transparent 70%);
            border-radius: 50%;
            left: ${x}%;
            top: ${y}%;
            animation: floatParticle ${duration}s ease-in-out infinite ${delay}s;
            pointer-events: none;
        `;
        
        container.appendChild(particle);
    }
}

// Мобильное меню
function initMobileMenu() {
    const mobileToggle = document.querySelector('.mobile-menu-toggle');
    const navMenu = document.querySelector('.nav-menu');
    
    if (!mobileToggle || !navMenu) return;
    
    mobileToggle.addEventListener('click', function() {
        navMenu.classList.toggle('active');
        this.classList.toggle('active');
        
        // Изменение иконки
        const icon = this.querySelector('i');
        if (navMenu.classList.contains('active')) {
            icon.className = 'fas fa-times';
        } else {
            icon.className = 'fas fa-bars';
        }
    });
    
    // Закрытие меню при клике на ссылку
    const navLinks = navMenu.querySelectorAll('.nav-link');
    navLinks.forEach(link => {
        link.addEventListener('click', () => {
            navMenu.classList.remove('active');
            mobileToggle.classList.remove('active');
            mobileToggle.querySelector('i').className = 'fas fa-bars';
        });
    });
}

// Загрузка инвестиционных пакетов
function loadInvestmentPackages() {
    const packagesContainer = document.getElementById('packages-container');
    if (!packagesContainer) return;
    
    // Данные пакетов (в реальном проекте загружались бы с сервера)
    const packages = [
        {
            name: 'Стартовый',
            price: '1,000',
            period: 'минимум',
            profit: '1.5%',
            duration: '30 дней',
            features: [
                'Ежедневные выплаты',
                'Минимальный риск',
                'Поддержка 24/7',
                'Мобильное приложение'
            ],
            featured: false
        },
        {
            name: 'Профессиональный',
            price: '10,000',
            period: 'минимум',
            profit: '2.5%',
            duration: '60 дней',
            features: [
                'Ежедневные выплаты',
                'Персональный менеджер',
                'Приоритетная поддержка',
                'Аналитические отчеты',
                'Реферальные бонусы'
            ],
            featured: true
        },
        {
            name: 'Премиум',
            price: '50,000',
            period: 'минимум',
            profit: '3.5%',
            duration: '90 дней',
            features: [
                'Максимальная прибыль',
                'VIP поддержка',
                'Эксклюзивные проекты',
                'Страхование депозита',
                'Персональная стратегия'
            ],
            featured: false
        }
    ];
    
    // Создание HTML для пакетов
    packagesContainer.innerHTML = packages.map(pkg => `
        <div class="package-card ${pkg.featured ? 'featured' : ''} animate-on-scroll">
            <div class="package-header">
                <h3 class="package-name">${pkg.name}</h3>
                <div class="package-price">${pkg.price} ₽</div>
                <div class="package-period">${pkg.period}</div>
            </div>
            <div class="package-body">
                <div class="package-profit">
                    <span class="profit-value">${pkg.profit}</span>
                    <span class="profit-label">ежедневно</span>
                </div>
                <div class="package-duration">
                    <i class="fas fa-clock"></i>
                    Срок: ${pkg.duration}
                </div>
                <ul class="package-features">
                    ${pkg.features.map(feature => `
                        <li><i class="fas fa-check"></i> ${feature}</li>
                    `).join('')}
                </ul>
            </div>
            <div class="package-footer">
                <a href="register.php" class="btn btn-primary btn-full">
                    Начать инвестировать
                </a>
            </div>
        </div>
    `).join('');
    
    // Добавление стилей для пакетов
    addPackageStyles();
}

// Добавление стилей для пакетов
function addPackageStyles() {
    const style = document.createElement('style');
    style.textContent = `
        .package-profit {
            text-align: center;
            margin-bottom: var(--space-4);
            padding: var(--space-4);
            background: var(--gray-100);
            border-radius: var(--radius-md);
        }
        
        .profit-value {
            font-size: var(--text-3xl);
            font-weight: 700;
            color: var(--primary-green);
            display: block;
        }
        
        .profit-label {
            font-size: var(--text-sm);
            color: var(--gray-600);
        }
        
        .package-duration {
            display: flex;
            align-items: center;
            gap: var(--space-2);
            margin-bottom: var(--space-6);
            color: var(--gray-600);
            font-size: var(--text-sm);
        }
        
        .package-duration i {
            color: var(--primary-green);
        }
    `;
    document.head.appendChild(style);
}

// Функция плавности анимации
function easeOutCubic(t) {
    return 1 - Math.pow(1 - t, 3);
}

// CSS анимация для плавающих частиц
const particleStyle = document.createElement('style');
particleStyle.textContent = `
    @keyframes floatParticle {
        0%, 100% {
            transform: translate(0, 0) scale(1);
            opacity: 0.3;
        }
        25% {
            transform: translate(20px, -20px) scale(1.2);
            opacity: 0.7;
        }
        50% {
            transform: translate(-10px, -40px) scale(0.8);
            opacity: 0.5;
        }
        75% {
            transform: translate(-30px, -10px) scale(1.1);
            opacity: 0.8;
        }
    }
    
    @media (max-width: 768px) {
        .nav-menu.active {
            display: flex;
            flex-direction: column;
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: rgba(255, 255, 255, 0.98);
            backdrop-filter: blur(20px);
            padding: var(--space-6);
            box-shadow: var(--shadow-lg);
            border-radius: 0 0 var(--radius-lg) var(--radius-lg);
        }
        
        .nav-menu {
            display: none;
        }
        
        .mobile-menu-toggle.active {
            color: var(--primary-green);
        }
    }
`;
document.head.appendChild(particleStyle);
