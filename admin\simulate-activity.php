<?php
/**
 * AstroGenix - Симуляция активности платформы
 * Расширенные инструменты имитации активности
 */

require_once '../config/config.php';
require_once '../classes/FakeDataGenerator.php';

// Проверка авторизации и прав администратора
require_admin();

// Инициализация переменных
$error_message = '';
$success_message = '';
$simulation_results = [];

try {
    $database = new Database();
    $db = $database->getConnection();
    $fake_generator = new FakeDataGenerator($db);
    
    // Обработка запросов на симуляцию
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        if (!verify_csrf_token($_POST['csrf_token'] ?? '')) {
            $error_message = 'Ошибка безопасности. Попробуйте еще раз.';
        } else {
            $action = sanitize_input($_POST['action'] ?? '');
            
            switch ($action) {
                case 'generate_fake_users':
                    $count = intval($_POST['user_count'] ?? 10);
                    $balance_type = sanitize_input($_POST['balance_type'] ?? 'random');

                    if ($count > 0 && $count <= 100) {
                        $result = $fake_generator->createMultipleUsers($count, ['balance_type' => $balance_type]);

                        if ($result['total_created'] > 0) {
                            $success_message = "Создано пользователей: {$result['total_created']}";
                            if ($result['total_errors'] > 0) {
                                $success_message .= " (ошибок: {$result['total_errors']})";
                            }
                            $simulation_results['users'] = $result;
                        } else {
                            $error_message = 'Не удалось создать пользователей: ' . implode(', ', $result['errors']);
                        }
                    } else {
                        $error_message = 'Количество пользователей должно быть от 1 до 100.';
                    }
                    break;

                case 'simulate_transactions':
                    $count = intval($_POST['transaction_count'] ?? 20);
                    $transaction_types = $_POST['transaction_types'] ?? ['deposit', 'withdrawal', 'investment'];

                    if ($count > 0 && $count <= 200) {
                        $result = $this->simulateTransactions($db, $count, $transaction_types);

                        if ($result['success']) {
                            $success_message = "Создано транзакций: {$result['created']}";
                            $simulation_results['transactions'] = $result;
                        } else {
                            $error_message = $result['error'];
                        }
                    } else {
                        $error_message = 'Количество транзакций должно быть от 1 до 200.';
                    }
                    break;

                case 'simulate_investments':
                    $count = intval($_POST['investment_count'] ?? 15);

                    if ($count > 0 && $count <= 100) {
                        $result = $this->simulateInvestments($db, $count);

                        if ($result['success']) {
                            $success_message = "Создано инвестиций: {$result['created']}";
                            $simulation_results['investments'] = $result;
                        } else {
                            $error_message = $result['error'];
                        }
                    } else {
                        $error_message = 'Количество инвестиций должно быть от 1 до 100.';
                    }
                    break;

                case 'update_platform_stats':
                    $result = $this->updatePlatformStats($db);

                    if ($result['success']) {
                        $success_message = 'Статистика платформы обновлена.';
                        $simulation_results['stats'] = $result;
                    } else {
                        $error_message = $result['error'];
                    }
                    break;

                case 'auto_activity':
                    $duration = intval($_POST['duration'] ?? 60); // минуты
                    $intensity = sanitize_input($_POST['intensity'] ?? 'medium');

                    $result = $this->startAutoActivity($db, $fake_generator, $duration, $intensity);

                    if ($result['success']) {
                        $success_message = "Автоматическая активность запущена на {$duration} минут.";
                        $simulation_results['auto_activity'] = $result;
                    } else {
                        $error_message = $result['error'];
                    }
                    break;

                case 'simulate_users':
                    $count = intval($_POST['user_count'] ?? 5);
                    $simulation_results = simulateUsers($db, $count);
                    $success_message = "Создано {$count} новых пользователей.";
                    break;
                    
                case 'simulate_transactions':
                    $count = intval($_POST['transaction_count'] ?? 10);
                    $simulation_results = simulateTransactions($db, $count);
                    $success_message = "Создано {$count} новых транзакций.";
                    break;
                    
                case 'simulate_investments':
                    $count = intval($_POST['investment_count'] ?? 5);
                    $simulation_results = simulateInvestments($db, $count);
                    $success_message = "Создано {$count} новых инвестиций.";
                    break;
                    
                case 'simulate_profits':
                    $simulation_results = simulateProfits($db);
                    $success_message = "Начислена прибыль по активным инвестициям.";
                    break;
                    
                case 'simulate_full_activity':
                    $users = simulateUsers($db, 3);
                    $transactions = simulateTransactions($db, 8);
                    $investments = simulateInvestments($db, 4);
                    $profits = simulateProfits($db);
                    
                    $simulation_results = [
                        'users' => $users,
                        'transactions' => $transactions,
                        'investments' => $investments,
                        'profits' => $profits
                    ];
                    $success_message = "Выполнена полная симуляция активности платформы.";
                    break;
            }
        }
    }
    
} catch (Exception $e) {
    error_log("Simulation error: " . $e->getMessage());
    $error_message = "Ошибка симуляции: " . $e->getMessage();
}

// Функция симуляции пользователей
function simulateUsers($db, $count) {
    $results = [];
    $fake_names = [
        ['Александр', 'Иванов'], ['Мария', 'Петрова'], ['Дмитрий', 'Сидоров'],
        ['Елена', 'Козлова'], ['Андрей', 'Морозов'], ['Ольга', 'Волкова'],
        ['Сергей', 'Соколов'], ['Анна', 'Лебедева'], ['Михаил', 'Новиков'],
        ['Татьяна', 'Федорова'], ['Владимир', 'Кузнецов'], ['Наталья', 'Попова']
    ];
    
    for ($i = 0; $i < $count; $i++) {
        $name = $fake_names[array_rand($fake_names)];
        $username = strtolower($name[0] . '_' . $name[1] . '_' . rand(100, 999));
        $email = $username . '@example.com';
        $password = password_hash('demo123', PASSWORD_DEFAULT);
        $balance = rand(0, 1000);
        
        $query = "INSERT INTO users (username, email, password, first_name, last_name, balance, is_active, email_verified, created_at) 
                  VALUES (:username, :email, :password, :first_name, :last_name, :balance, 1, 1, NOW())";
        
        $stmt = $db->prepare($query);
        $stmt->bindParam(':username', $username);
        $stmt->bindParam(':email', $email);
        $stmt->bindParam(':password', $password);
        $stmt->bindParam(':first_name', $name[0]);
        $stmt->bindParam(':last_name', $name[1]);
        $stmt->bindParam(':balance', $balance);
        
        if ($stmt->execute()) {
            $user_id = $db->lastInsertId();
            $results[] = [
                'id' => $user_id,
                'username' => $username,
                'email' => $email,
                'name' => $name[0] . ' ' . $name[1],
                'balance' => $balance
            ];
        }
    }
    
    return $results;
}

// Функция симуляции транзакций
function simulateTransactions($db, $count) {
    $results = [];
    
    // Получение случайных пользователей
    $users_query = "SELECT id FROM users WHERE is_active = 1 ORDER BY RAND() LIMIT " . ($count * 2);
    $users_stmt = $db->prepare($users_query);
    $users_stmt->execute();
    $users = $users_stmt->fetchAll(PDO::FETCH_COLUMN);
    
    if (empty($users)) {
        return $results;
    }
    
    $transaction_types = ['deposit', 'withdrawal', 'profit', 'referral_bonus'];
    $statuses = ['completed', 'pending'];
    
    for ($i = 0; $i < $count; $i++) {
        $user_id = $users[array_rand($users)];
        $type = $transaction_types[array_rand($transaction_types)];
        $amount = rand(10, 500);
        $status = $statuses[array_rand($statuses)];
        
        // Для выводов делаем меньшие суммы
        if ($type === 'withdrawal') {
            $amount = rand(10, 100);
        }
        
        $query = "INSERT INTO transactions (user_id, type, amount, status, description, created_at) 
                  VALUES (:user_id, :type, :amount, :status, :description, NOW() - INTERVAL RAND() * 7 DAY)";
        
        $description = "Симулированная транзакция: " . $type;
        
        $stmt = $db->prepare($query);
        $stmt->bindParam(':user_id', $user_id, PDO::PARAM_INT);
        $stmt->bindParam(':type', $type);
        $stmt->bindParam(':amount', $amount);
        $stmt->bindParam(':status', $status);
        $stmt->bindParam(':description', $description);
        
        if ($stmt->execute()) {
            $results[] = [
                'id' => $db->lastInsertId(),
                'user_id' => $user_id,
                'type' => $type,
                'amount' => $amount,
                'status' => $status
            ];
        }
    }
    
    return $results;
}

// Функция симуляции инвестиций
function simulateInvestments($db, $count) {
    $results = [];
    
    // Получение пользователей и пакетов
    $users_query = "SELECT id FROM users WHERE is_active = 1 ORDER BY RAND() LIMIT " . ($count * 2);
    $users_stmt = $db->prepare($users_query);
    $users_stmt->execute();
    $users = $users_stmt->fetchAll(PDO::FETCH_COLUMN);
    
    $packages_query = "SELECT id, min_amount, max_amount, duration_days FROM investment_packages WHERE is_active = 1";
    $packages_stmt = $db->prepare($packages_query);
    $packages_stmt->execute();
    $packages = $packages_stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($users) || empty($packages)) {
        return $results;
    }
    
    for ($i = 0; $i < $count; $i++) {
        $user_id = $users[array_rand($users)];
        $package = $packages[array_rand($packages)];
        $amount = rand($package['min_amount'], min($package['max_amount'], 1000));
        
        $start_date = date('Y-m-d H:i:s', time() - rand(0, 7 * 24 * 60 * 60));
        $end_date = date('Y-m-d H:i:s', strtotime($start_date) + ($package['duration_days'] * 24 * 60 * 60));
        
        $query = "INSERT INTO user_investments (user_id, package_id, amount, start_date, end_date, status, created_at) 
                  VALUES (:user_id, :package_id, :amount, :start_date, :end_date, 'active', :created_at)";
        
        $stmt = $db->prepare($query);
        $stmt->bindParam(':user_id', $user_id, PDO::PARAM_INT);
        $stmt->bindParam(':package_id', $package['id'], PDO::PARAM_INT);
        $stmt->bindParam(':amount', $amount);
        $stmt->bindParam(':start_date', $start_date);
        $stmt->bindParam(':end_date', $end_date);
        $stmt->bindParam(':created_at', $start_date);
        
        if ($stmt->execute()) {
            $results[] = [
                'id' => $db->lastInsertId(),
                'user_id' => $user_id,
                'package_id' => $package['id'],
                'amount' => $amount,
                'start_date' => $start_date,
                'end_date' => $end_date
            ];
        }
    }
    
    return $results;
}

// Функция симуляции прибыли
function simulateProfits($db) {
    $results = [];
    
    // Получение активных инвестиций
    $query = "SELECT ui.*, ip.daily_profit_percent 
              FROM user_investments ui 
              JOIN investment_packages ip ON ui.package_id = ip.id 
              WHERE ui.status = 'active' AND ui.end_date > NOW()";
    
    $stmt = $db->prepare($query);
    $stmt->execute();
    $investments = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    foreach ($investments as $investment) {
        $daily_profit = ($investment['amount'] * $investment['daily_profit_percent']) / 100;
        
        // Обновление общей прибыли
        $update_query = "UPDATE user_investments 
                        SET total_earned = total_earned + :profit 
                        WHERE id = :id";
        
        $update_stmt = $db->prepare($update_query);
        $update_stmt->bindParam(':profit', $daily_profit);
        $update_stmt->bindParam(':id', $investment['id'], PDO::PARAM_INT);
        $update_stmt->execute();
        
        // Создание транзакции прибыли
        $transaction_query = "INSERT INTO transactions (user_id, type, amount, status, description, created_at) 
                             VALUES (:user_id, 'profit', :amount, 'completed', 'Ежедневная прибыль', NOW())";
        
        $transaction_stmt = $db->prepare($transaction_query);
        $transaction_stmt->bindParam(':user_id', $investment['user_id'], PDO::PARAM_INT);
        $transaction_stmt->bindParam(':amount', $daily_profit);
        $transaction_stmt->execute();
        
        // Обновление баланса пользователя
        $balance_query = "UPDATE users SET balance = balance + :profit WHERE id = :user_id";
        $balance_stmt = $db->prepare($balance_query);
        $balance_stmt->bindParam(':profit', $daily_profit);
        $balance_stmt->bindParam(':user_id', $investment['user_id'], PDO::PARAM_INT);
        $balance_stmt->execute();
        
        $results[] = [
            'investment_id' => $investment['id'],
            'user_id' => $investment['user_id'],
            'profit' => $daily_profit
        ];
    }
    
    return $results;
}

$page_title = 'Симуляция активности - Админ-панель';
?>
<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?></title>
    
    <!-- CSS -->
    <link rel="stylesheet" href="../assets/css/style.css">
    <link rel="stylesheet" href="../assets/css/dashboard.css">
    <link rel="stylesheet" href="../assets/css/admin.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body class="dashboard-page admin-page">
    <!-- Admin Sidebar -->
    <?php include 'includes/admin-sidebar.php'; ?>

    <!-- Main Content -->
    <main class="main-content">
        <!-- Header -->
        <header class="dashboard-header">
            <div class="header-left">
                <button class="mobile-menu-toggle" id="mobileMenuToggle">
                    <i class="fas fa-bars"></i>
                </button>
                <h1>Симуляция активности платформы</h1>
            </div>
        </header>

        <!-- Dashboard Content -->
        <div class="dashboard-content">
            <?php if (!empty($error_message)): ?>
                <div class="alert alert-error">
                    <i class="fas fa-exclamation-triangle"></i>
                    <?php echo htmlspecialchars($error_message); ?>
                </div>
            <?php endif; ?>

            <?php if (!empty($success_message)): ?>
                <div class="alert alert-success">
                    <i class="fas fa-check-circle"></i>
                    <?php echo htmlspecialchars($success_message); ?>
                </div>
            <?php endif; ?>

            <!-- Simulation Controls -->
            <div class="simulation-grid">
                <!-- Generate Fake Users -->
                <div class="simulation-card">
                    <div class="card-header">
                        <h3><i class="fas fa-user-plus"></i> Генератор пользователей</h3>
                    </div>
                    <div class="card-body">
                        <p>Создание реалистичных пользователей с различными профилями и балансами.</p>

                        <form method="POST" class="simulation-form">
                            <input type="hidden" name="csrf_token" value="<?php echo generate_csrf_token(); ?>">
                            <input type="hidden" name="action" value="generate_fake_users">

                            <div class="form-group">
                                <label for="user_count">Количество пользователей:</label>
                                <input type="number" id="user_count" name="user_count" value="10" min="1" max="100" required>
                            </div>

                            <div class="form-group">
                                <label for="balance_type">Тип баланса:</label>
                                <select id="balance_type" name="balance_type">
                                    <option value="random">Случайный</option>
                                    <option value="newbie">Новички (0-50 USDT)</option>
                                    <option value="active">Активные (50-500 USDT)</option>
                                    <option value="vip">VIP (500-5000 USDT)</option>
                                    <option value="whale">Киты (5000+ USDT)</option>
                                </select>
                            </div>

                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-user-plus"></i>
                                Создать пользователей
                            </button>
                        </form>
                    </div>
                </div>

                <!-- Simulate Transactions -->
                <div class="simulation-card">
                    <div class="card-header">
                        <h3><i class="fas fa-exchange-alt"></i> Симуляция транзакций</h3>
                    </div>
                    <div class="card-body">
                        <p>Автоматическое создание различных типов транзакций для имитации активности.</p>

                        <form method="POST" class="simulation-form">
                            <input type="hidden" name="csrf_token" value="<?php echo generate_csrf_token(); ?>">
                            <input type="hidden" name="action" value="simulate_transactions">

                            <div class="form-group">
                                <label for="transaction_count">Количество транзакций:</label>
                                <input type="number" id="transaction_count" name="transaction_count" value="20" min="1" max="200" required>
                            </div>

                            <div class="form-group">
                                <label>Типы транзакций:</label>
                                <div class="checkbox-group">
                                    <label class="checkbox-label">
                                        <input type="checkbox" name="transaction_types[]" value="deposit" checked>
                                        <span class="checkbox-custom"></span>
                                        Депозиты
                                    </label>
                                    <label class="checkbox-label">
                                        <input type="checkbox" name="transaction_types[]" value="withdrawal" checked>
                                        <span class="checkbox-custom"></span>
                                        Выводы
                                    </label>
                                    <label class="checkbox-label">
                                        <input type="checkbox" name="transaction_types[]" value="investment" checked>
                                        <span class="checkbox-custom"></span>
                                        Инвестиции
                                    </label>
                                    <label class="checkbox-label">
                                        <input type="checkbox" name="transaction_types[]" value="profit" checked>
                                        <span class="checkbox-custom"></span>
                                        Прибыль
                                    </label>
                                </div>
                            </div>

                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-exchange-alt"></i>
                                Создать транзакции
                            </button>
                        </form>
                    </div>
                </div>

                <!-- Simulate Investments -->
                <div class="simulation-card">
                    <div class="card-header">
                        <h3><i class="fas fa-chart-line"></i> Симуляция инвестиций</h3>
                    </div>
                    <div class="card-body">
                        <p>Создание активных инвестиций с автоматическими начислениями прибыли.</p>

                        <form method="POST" class="simulation-form">
                            <input type="hidden" name="csrf_token" value="<?php echo generate_csrf_token(); ?>">
                            <input type="hidden" name="action" value="simulate_investments">

                            <div class="form-group">
                                <label for="investment_count">Количество инвестиций:</label>
                                <input type="number" id="investment_count" name="investment_count" value="15" min="1" max="100" required>
                            </div>

                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-chart-line"></i>
                                Создать инвестиции
                            </button>
                        </form>
                    </div>
                </div>

                <!-- Update Platform Stats -->
                <div class="simulation-card">
                    <div class="card-header">
                        <h3><i class="fas fa-chart-bar"></i> Обновление статистики</h3>
                    </div>
                    <div class="card-body">
                        <p>Обновление общей статистики платформы и экологических показателей.</p>

                        <form method="POST" class="simulation-form">
                            <input type="hidden" name="csrf_token" value="<?php echo generate_csrf_token(); ?>">
                            <input type="hidden" name="action" value="update_platform_stats">

                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-sync-alt"></i>
                                Обновить статистику
                            </button>
                        </form>
                    </div>
                </div>

                <!-- Auto Activity -->
                <div class="simulation-card">
                    <div class="card-header">
                        <h3><i class="fas fa-robot"></i> Автоматическая активность</h3>
                    </div>
                    <div class="card-body">
                        <p>Запуск автоматического создания пользователей и активности в фоновом режиме.</p>

                        <form method="POST" class="simulation-form">
                            <input type="hidden" name="csrf_token" value="<?php echo generate_csrf_token(); ?>">
                            <input type="hidden" name="action" value="auto_activity">

                            <div class="form-group">
                                <label for="duration">Длительность (минуты):</label>
                                <input type="number" id="duration" name="duration" value="60" min="5" max="1440" required>
                            </div>

                            <div class="form-group">
                                <label for="intensity">Интенсивность:</label>
                                <select id="intensity" name="intensity">
                                    <option value="low">Низкая (2 польз./час)</option>
                                    <option value="medium" selected>Средняя (5 польз./час)</option>
                                    <option value="high">Высокая (10 польз./час)</option>
                                </select>
                            </div>

                            <button type="submit" class="btn btn-warning">
                                <i class="fas fa-play"></i>
                                Запустить автоактивность
                            </button>
                        </form>
                    </div>
                </div>

                <!-- Simulate Users (старая версия) -->
                <div class="simulation-card">
                    <div class="card-header">
                        <h3><i class="fas fa-users"></i> Симуляция пользователей</h3>
                    </div>
                    <div class="card-body">
                        <p>Создание фиктивных пользователей для демонстрации активности платформы.</p>
                        <form method="POST" class="simulation-form">
                            <input type="hidden" name="csrf_token" value="<?php echo generate_csrf_token(); ?>">
                            <input type="hidden" name="action" value="simulate_users">
                            
                            <div class="form-group">
                                <label for="user_count">Количество пользователей:</label>
                                <input type="number" id="user_count" name="user_count" value="5" min="1" max="50" required>
                            </div>
                            
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-user-plus"></i>
                                Создать пользователей
                            </button>
                        </form>
                    </div>
                </div>

                <!-- Simulate Transactions -->
                <div class="simulation-card">
                    <div class="card-header">
                        <h3><i class="fas fa-exchange-alt"></i> Симуляция транзакций</h3>
                    </div>
                    <div class="card-body">
                        <p>Создание случайных транзакций (пополнения, выводы, прибыль).</p>
                        <form method="POST" class="simulation-form">
                            <input type="hidden" name="csrf_token" value="<?php echo generate_csrf_token(); ?>">
                            <input type="hidden" name="action" value="simulate_transactions">
                            
                            <div class="form-group">
                                <label for="transaction_count">Количество транзакций:</label>
                                <input type="number" id="transaction_count" name="transaction_count" value="10" min="1" max="100" required>
                            </div>
                            
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-plus"></i>
                                Создать транзакции
                            </button>
                        </form>
                    </div>
                </div>

                <!-- Simulate Investments -->
                <div class="simulation-card">
                    <div class="card-header">
                        <h3><i class="fas fa-chart-line"></i> Симуляция инвестиций</h3>
                    </div>
                    <div class="card-body">
                        <p>Создание активных инвестиций для существующих пользователей.</p>
                        <form method="POST" class="simulation-form">
                            <input type="hidden" name="csrf_token" value="<?php echo generate_csrf_token(); ?>">
                            <input type="hidden" name="action" value="simulate_investments">
                            
                            <div class="form-group">
                                <label for="investment_count">Количество инвестиций:</label>
                                <input type="number" id="investment_count" name="investment_count" value="5" min="1" max="50" required>
                            </div>
                            
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-chart-line"></i>
                                Создать инвестиции
                            </button>
                        </form>
                    </div>
                </div>

                <!-- Simulate Profits -->
                <div class="simulation-card">
                    <div class="card-header">
                        <h3><i class="fas fa-coins"></i> Начисление прибыли</h3>
                    </div>
                    <div class="card-body">
                        <p>Начисление ежедневной прибыли по всем активным инвестициям.</p>
                        <form method="POST" class="simulation-form">
                            <input type="hidden" name="csrf_token" value="<?php echo generate_csrf_token(); ?>">
                            <input type="hidden" name="action" value="simulate_profits">
                            
                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-coins"></i>
                                Начислить прибыль
                            </button>
                        </form>
                    </div>
                </div>

                <!-- Full Activity Simulation -->
                <div class="simulation-card full-width">
                    <div class="card-header">
                        <h3><i class="fas fa-magic"></i> Полная симуляция активности</h3>
                    </div>
                    <div class="card-body">
                        <p>Комплексная симуляция: создание пользователей, транзакций, инвестиций и начисление прибыли.</p>
                        <form method="POST" class="simulation-form">
                            <input type="hidden" name="csrf_token" value="<?php echo generate_csrf_token(); ?>">
                            <input type="hidden" name="action" value="simulate_full_activity">
                            
                            <button type="submit" class="btn btn-primary btn-large">
                                <i class="fas fa-magic"></i>
                                Запустить полную симуляцию
                            </button>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Simulation Results -->
            <?php if (!empty($simulation_results)): ?>
                <div class="simulation-results">
                    <div class="card">
                        <div class="card-header">
                            <h3><i class="fas fa-chart-bar"></i> Результаты симуляции</h3>
                        </div>
                        <div class="card-body">
                            <pre><?php echo json_encode($simulation_results, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE); ?></pre>
                        </div>
                    </div>
                </div>
            <?php endif; ?>

            <!-- Results Display -->
            <?php if (!empty($simulation_results)): ?>
                <div class="simulation-results">
                    <h4><i class="fas fa-chart-bar"></i> Результаты симуляции</h4>

                    <div class="results-grid">
                        <?php if (isset($simulation_results['users'])): ?>
                            <div class="result-item">
                                <span class="result-value"><?php echo $simulation_results['users']['total_created']; ?></span>
                                <span class="result-label">Создано пользователей</span>
                            </div>
                        <?php endif; ?>

                        <?php if (isset($simulation_results['transactions'])): ?>
                            <div class="result-item">
                                <span class="result-value"><?php echo $simulation_results['transactions']['created']; ?></span>
                                <span class="result-label">Создано транзакций</span>
                            </div>
                        <?php endif; ?>

                        <?php if (isset($simulation_results['investments'])): ?>
                            <div class="result-item">
                                <span class="result-value"><?php echo $simulation_results['investments']['created']; ?></span>
                                <span class="result-label">Создано инвестиций</span>
                            </div>
                        <?php endif; ?>

                        <?php if (isset($simulation_results['stats'])): ?>
                            <div class="result-item">
                                <span class="result-value"><?php echo number_format($simulation_results['stats']['total_energy']); ?></span>
                                <span class="result-label">кВт⋅ч энергии</span>
                            </div>
                            <div class="result-item">
                                <span class="result-value"><?php echo number_format($simulation_results['stats']['total_co2_saved'], 1); ?></span>
                                <span class="result-label">кг CO₂ сохранено</span>
                            </div>
                            <div class="result-item">
                                <span class="result-value"><?php echo number_format($simulation_results['stats']['total_trees']); ?></span>
                                <span class="result-label">деревьев посажено</span>
                            </div>
                        <?php endif; ?>

                        <?php if (isset($simulation_results['auto_activity'])): ?>
                            <div class="result-item">
                                <span class="result-value"><?php echo $simulation_results['auto_activity']['duration']; ?></span>
                                <span class="result-label">минут активности</span>
                            </div>
                            <div class="result-item">
                                <span class="result-value"><?php echo ucfirst($simulation_results['auto_activity']['intensity']); ?></span>
                                <span class="result-label">интенсивность</span>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </main>

    <!-- JavaScript -->
    <script src="../assets/js/admin.js"></script>
    <script>
        // Подтверждение перед симуляцией
        document.querySelectorAll('.simulation-form').forEach(form => {
            form.addEventListener('submit', function(e) {
                const action = this.querySelector('input[name="action"]').value;
                let message = 'Вы уверены, что хотите выполнить эту симуляцию?';
                
                if (action === 'simulate_full_activity') {
                    message = 'Это создаст множество тестовых данных. Продолжить?';
                }
                
                if (!confirm(message)) {
                    e.preventDefault();
                }
            });
        });
    </script>
</body>
</html>

<?php
/**
 * Методы для симуляции различных типов активности
 */

/**
 * Симуляция транзакций
 */
function simulateTransactions($db, $count, $types) {
    try {
        $created = 0;
        $errors = [];

        // Получение списка пользователей
        $users_query = "SELECT id FROM users WHERE is_active = 1 ORDER BY RAND() LIMIT " . ($count * 2);
        $users_stmt = $db->prepare($users_query);
        $users_stmt->execute();
        $users = $users_stmt->fetchAll(PDO::FETCH_COLUMN);

        if (empty($users)) {
            return ['success' => false, 'error' => 'Нет активных пользователей для создания транзакций'];
        }

        for ($i = 0; $i < $count; $i++) {
            $user_id = $users[array_rand($users)];
            $type = $types[array_rand($types)];

            // Генерация суммы в зависимости от типа
            switch ($type) {
                case 'deposit':
                    $amount = rand(10, 1000);
                    $status = rand(1, 100) <= 85 ? 'completed' : 'pending';
                    break;
                case 'withdrawal':
                    $amount = rand(5, 500);
                    $status = rand(1, 100) <= 70 ? 'completed' : 'pending';
                    break;
                case 'investment':
                    $amount = rand(50, 2000);
                    $status = 'completed';
                    break;
                case 'profit':
                    $amount = rand(1, 100);
                    $status = 'completed';
                    break;
                default:
                    $amount = rand(10, 500);
                    $status = 'completed';
            }

            // Генерация даты (последние 30 дней)
            $created_at = date('Y-m-d H:i:s', rand(strtotime('-30 days'), time()));

            // Создание транзакции
            $insert_query = "INSERT INTO transactions
                            (user_id, type, amount, status, description, created_at)
                            VALUES
                            (:user_id, :type, :amount, :status, :description, :created_at)";

            $description = "Симулированная транзакция: " . ucfirst($type);

            $insert_stmt = $db->prepare($insert_query);
            $insert_stmt->bindParam(':user_id', $user_id, PDO::PARAM_INT);
            $insert_stmt->bindParam(':type', $type);
            $insert_stmt->bindParam(':amount', $amount);
            $insert_stmt->bindParam(':status', $status);
            $insert_stmt->bindParam(':description', $description);
            $insert_stmt->bindParam(':created_at', $created_at);

            if ($insert_stmt->execute()) {
                $created++;

                // Обновление баланса пользователя для завершенных транзакций
                if ($status === 'completed') {
                    $balance_change = 0;

                    switch ($type) {
                        case 'deposit':
                        case 'profit':
                            $balance_change = $amount;
                            break;
                        case 'withdrawal':
                        case 'investment':
                            $balance_change = -$amount;
                            break;
                    }

                    if ($balance_change != 0) {
                        $update_balance_query = "UPDATE users SET balance = balance + :change WHERE id = :user_id";
                        $update_balance_stmt = $db->prepare($update_balance_query);
                        $update_balance_stmt->bindParam(':change', $balance_change);
                        $update_balance_stmt->bindParam(':user_id', $user_id, PDO::PARAM_INT);
                        $update_balance_stmt->execute();
                    }
                }
            } else {
                $errors[] = "Ошибка создания транзакции #" . ($i + 1);
            }
        }

        return [
            'success' => true,
            'created' => $created,
            'errors' => $errors,
            'types_used' => $types
        ];

    } catch (Exception $e) {
        return ['success' => false, 'error' => 'Ошибка симуляции транзакций: ' . $e->getMessage()];
    }
}

/**
 * Симуляция инвестиций
 */
function simulateInvestments($db, $count) {
    try {
        $created = 0;
        $errors = [];

        // Получение пользователей с достаточным балансом
        $users_query = "SELECT id, balance FROM users WHERE is_active = 1 AND balance >= 50 ORDER BY RAND() LIMIT " . ($count * 2);
        $users_stmt = $db->prepare($users_query);
        $users_stmt->execute();
        $users = $users_stmt->fetchAll(PDO::FETCH_ASSOC);

        if (empty($users)) {
            return ['success' => false, 'error' => 'Нет пользователей с достаточным балансом для инвестиций'];
        }

        // Получение инвестиционных пакетов
        $packages_query = "SELECT * FROM investment_packages WHERE is_active = 1";
        $packages_stmt = $db->prepare($packages_query);
        $packages_stmt->execute();
        $packages = $packages_stmt->fetchAll(PDO::FETCH_ASSOC);

        if (empty($packages)) {
            return ['success' => false, 'error' => 'Нет активных инвестиционных пакетов'];
        }

        for ($i = 0; $i < $count; $i++) {
            $user = $users[array_rand($users)];
            $package = $packages[array_rand($packages)];

            // Генерация суммы инвестиции
            $min_amount = max($package['min_amount'], 50);
            $max_amount = min($package['max_amount'], $user['balance']);

            if ($min_amount > $max_amount) {
                continue; // Пропускаем если недостаточно средств
            }

            $amount = rand($min_amount, $max_amount);

            // Расчет параметров инвестиции
            $daily_profit = ($amount * $package['daily_profit_percent']) / 100;
            $start_date = date('Y-m-d');
            $end_date = date('Y-m-d', strtotime("+{$package['duration_days']} days"));

            // Создание инвестиции
            $insert_query = "INSERT INTO user_investments
                            (user_id, package_id, amount, daily_profit, start_date, end_date, status)
                            VALUES
                            (:user_id, :package_id, :amount, :daily_profit, :start_date, :end_date, 'active')";

            $insert_stmt = $db->prepare($insert_query);
            $insert_stmt->bindParam(':user_id', $user['id'], PDO::PARAM_INT);
            $insert_stmt->bindParam(':package_id', $package['id'], PDO::PARAM_INT);
            $insert_stmt->bindParam(':amount', $amount);
            $insert_stmt->bindParam(':daily_profit', $daily_profit);
            $insert_stmt->bindParam(':start_date', $start_date);
            $insert_stmt->bindParam(':end_date', $end_date);

            if ($insert_stmt->execute()) {
                $created++;

                // Списание суммы с баланса пользователя
                $update_balance_query = "UPDATE users SET balance = balance - :amount, total_invested = total_invested + :amount WHERE id = :user_id";
                $update_balance_stmt = $db->prepare($update_balance_query);
                $update_balance_stmt->bindParam(':amount', $amount);
                $update_balance_stmt->bindParam(':user_id', $user['id'], PDO::PARAM_INT);
                $update_balance_stmt->execute();

                // Создание транзакции
                $transaction_query = "INSERT INTO transactions
                                     (user_id, type, amount, status, description)
                                     VALUES
                                     (:user_id, 'investment', :amount, 'completed', :description)";

                $description = "Инвестиция в пакет: " . $package['name'];

                $transaction_stmt = $db->prepare($transaction_query);
                $transaction_stmt->bindParam(':user_id', $user['id'], PDO::PARAM_INT);
                $transaction_stmt->bindParam(':amount', $amount);
                $transaction_stmt->bindParam(':description', $description);
                $transaction_stmt->execute();

            } else {
                $errors[] = "Ошибка создания инвестиции #" . ($i + 1);
            }
        }

        return [
            'success' => true,
            'created' => $created,
            'errors' => $errors
        ];

    } catch (Exception $e) {
        return ['success' => false, 'error' => 'Ошибка симуляции инвестиций: ' . $e->getMessage()];
    }
}

/**
 * Обновление статистики платформы
 */
function updatePlatformStats($db) {
    try {
        // Обновление общей статистики зеленой энергии
        $total_users_query = "SELECT COUNT(*) as total FROM users WHERE is_active = 1";
        $total_users_stmt = $db->prepare($total_users_query);
        $total_users_stmt->execute();
        $total_users = $total_users_stmt->fetch(PDO::FETCH_ASSOC)['total'];

        // Генерация реалистичной статистики
        $total_energy = $total_users * rand(50, 200);
        $total_co2_saved = round($total_energy * 0.4, 2);
        $total_trees = $total_users * rand(1, 10);

        // Обновление или создание записи общей статистики
        $update_stats_query = "INSERT INTO green_energy_stats
                              (user_id, energy_generated, co2_saved, trees_planted, updated_at)
                              VALUES
                              (0, :energy, :co2, :trees, NOW())
                              ON DUPLICATE KEY UPDATE
                              energy_generated = :energy, co2_saved = :co2, trees_planted = :trees, updated_at = NOW()";

        $update_stats_stmt = $db->prepare($update_stats_query);
        $update_stats_stmt->bindParam(':energy', $total_energy);
        $update_stats_stmt->bindParam(':co2', $total_co2_saved);
        $update_stats_stmt->bindParam(':trees', $total_trees, PDO::PARAM_INT);
        $update_stats_stmt->execute();

        return [
            'success' => true,
            'total_users' => $total_users,
            'total_energy' => $total_energy,
            'total_co2_saved' => $total_co2_saved,
            'total_trees' => $total_trees
        ];

    } catch (Exception $e) {
        return ['success' => false, 'error' => 'Ошибка обновления статистики: ' . $e->getMessage()];
    }
}

/**
 * Запуск автоматической активности
 */
function startAutoActivity($db, $fake_generator, $duration, $intensity) {
    try {
        // Настройки интенсивности
        $settings = [
            'low' => ['users_per_hour' => 2, 'transactions_per_hour' => 5, 'investments_per_hour' => 1],
            'medium' => ['users_per_hour' => 5, 'transactions_per_hour' => 15, 'investments_per_hour' => 3],
            'high' => ['users_per_hour' => 10, 'transactions_per_hour' => 30, 'investments_per_hour' => 8]
        ];

        $config = $settings[$intensity] ?? $settings['medium'];

        // Сохранение настроек автоактивности в системные настройки
        $auto_activity_config = [
            'enabled' => true,
            'duration' => $duration,
            'intensity' => $intensity,
            'config' => $config,
            'start_time' => time(),
            'end_time' => time() + ($duration * 60)
        ];

        $save_config_query = "INSERT INTO system_settings
                             (setting_key, setting_value)
                             VALUES
                             ('auto_activity_config', :config)
                             ON DUPLICATE KEY UPDATE
                             setting_value = :config";

        $save_config_stmt = $db->prepare($save_config_query);
        $save_config_stmt->bindParam(':config', json_encode($auto_activity_config));
        $save_config_stmt->execute();

        return [
            'success' => true,
            'duration' => $duration,
            'intensity' => $intensity,
            'config' => $config,
            'message' => 'Автоматическая активность настроена. Используйте cron job для выполнения.'
        ];

    } catch (Exception $e) {
        return ['success' => false, 'error' => 'Ошибка запуска автоактивности: ' . $e->getMessage()];
    }
}
?>
