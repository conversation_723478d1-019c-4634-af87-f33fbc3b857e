<?php
/**
 * AstroGenix - Экспорт данных
 * Эко-майнинговая инвестиционная платформа
 */

require_once '../config/config.php';

// Проверка авторизации и прав администратора
require_admin();

// Получение типа экспорта
$export_type = sanitize_input($_GET['type'] ?? '');
$format = sanitize_input($_GET['format'] ?? 'csv');

if (empty($export_type)) {
    http_response_code(400);
    die('Не указан тип экспорта');
}

try {
    $database = new Database();
    $db = $database->getConnection();
    
    switch ($export_type) {
        case 'users':
            exportUsers($db, $format);
            break;
            
        case 'transactions':
            exportTransactions($db, $format);
            break;
            
        case 'investments':
            exportInvestments($db, $format);
            break;
            
        case 'logs':
            exportLogs($db, $format);
            break;
            
        default:
            http_response_code(400);
            die('Неизвестный тип экспорта');
    }
    
} catch (Exception $e) {
    error_log("Export error: " . $e->getMessage());
    http_response_code(500);
    die('Ошибка экспорта данных');
}

function exportUsers($db, $format) {
    // Получение параметров фильтрации
    $search = sanitize_input($_GET['search'] ?? '');
    $status_filter = sanitize_input($_GET['status'] ?? '');
    
    // Построение запроса
    $where_conditions = [];
    $params = [];
    
    if (!empty($search)) {
        $where_conditions[] = "(username LIKE :search OR email LIKE :search OR first_name LIKE :search OR last_name LIKE :search)";
        $params[':search'] = '%' . $search . '%';
    }
    
    if ($status_filter === 'active') {
        $where_conditions[] = "is_active = 1";
    } elseif ($status_filter === 'inactive') {
        $where_conditions[] = "is_active = 0";
    } elseif ($status_filter === 'admin') {
        $where_conditions[] = "is_admin = 1";
    }
    
    $where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';
    
    $query = "SELECT id, username, email, first_name, last_name, balance, total_invested, total_earned,
                     is_admin, is_active, email_verified, country, phone, 
                     (SELECT COUNT(*) FROM users WHERE referred_by = u.id) as referrals_count,
                     created_at, last_login
              FROM users u 
              $where_clause 
              ORDER BY created_at DESC";
    
    $stmt = $db->prepare($query);
    foreach ($params as $key => $value) {
        $stmt->bindValue($key, $value);
    }
    $stmt->execute();
    $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Заголовки для экспорта
    $headers = [
        'ID', 'Имя пользователя', 'Email', 'Имя', 'Фамилия', 'Баланс', 'Всего инвестировано', 
        'Всего заработано', 'Администратор', 'Активен', 'Email подтвержден', 'Страна', 
        'Телефон', 'Рефералов', 'Дата регистрации', 'Последний вход'
    ];
    
    if ($format === 'csv') {
        exportToCSV($users, $headers, 'users_' . date('Y-m-d_H-i-s') . '.csv', function($user) {
            return [
                $user['id'],
                $user['username'],
                $user['email'],
                $user['first_name'],
                $user['last_name'],
                $user['balance'],
                $user['total_invested'],
                $user['total_earned'],
                $user['is_admin'] ? 'Да' : 'Нет',
                $user['is_active'] ? 'Да' : 'Нет',
                $user['email_verified'] ? 'Да' : 'Нет',
                $user['country'] ?? '',
                $user['phone'] ?? '',
                $user['referrals_count'],
                $user['created_at'],
                $user['last_login'] ?? ''
            ];
        });
    } else {
        exportToJSON($users, 'users_' . date('Y-m-d_H-i-s') . '.json');
    }
}

function exportTransactions($db, $format) {
    // Получение параметров фильтрации
    $type_filter = sanitize_input($_GET['type'] ?? '');
    $status_filter = sanitize_input($_GET['status'] ?? '');
    
    $where_conditions = [];
    $params = [];
    
    if (!empty($type_filter)) {
        $where_conditions[] = "t.type = :type";
        $params[':type'] = $type_filter;
    }
    
    if (!empty($status_filter)) {
        $where_conditions[] = "t.status = :status";
        $params[':status'] = $status_filter;
    }
    
    $where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';
    
    $query = "SELECT t.id, t.type, t.amount, t.fee, t.status, t.payment_method, 
                     t.transaction_hash, t.description, t.created_at, t.processed_at,
                     u.username, u.email, u.first_name, u.last_name,
                     admin.username as admin_username
              FROM transactions t 
              JOIN users u ON t.user_id = u.id 
              LEFT JOIN users admin ON t.processed_by = admin.id
              $where_clause 
              ORDER BY t.created_at DESC";
    
    $stmt = $db->prepare($query);
    foreach ($params as $key => $value) {
        $stmt->bindValue($key, $value);
    }
    $stmt->execute();
    $transactions = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $headers = [
        'ID', 'Тип', 'Сумма', 'Комиссия', 'Статус', 'Способ оплаты', 'Хэш транзакции',
        'Описание', 'Пользователь', 'Email пользователя', 'Дата создания', 'Дата обработки', 'Обработал'
    ];
    
    if ($format === 'csv') {
        exportToCSV($transactions, $headers, 'transactions_' . date('Y-m-d_H-i-s') . '.csv', function($transaction) {
            return [
                $transaction['id'],
                $transaction['type'],
                $transaction['amount'],
                $transaction['fee'],
                $transaction['status'],
                $transaction['payment_method'] ?? '',
                $transaction['transaction_hash'] ?? '',
                $transaction['description'] ?? '',
                $transaction['first_name'] . ' ' . $transaction['last_name'],
                $transaction['email'],
                $transaction['created_at'],
                $transaction['processed_at'] ?? '',
                $transaction['admin_username'] ?? ''
            ];
        });
    } else {
        exportToJSON($transactions, 'transactions_' . date('Y-m-d_H-i-s') . '.json');
    }
}

function exportInvestments($db, $format) {
    $query = "SELECT ui.id, ui.amount, ui.daily_profit, ui.total_earned, ui.start_date, 
                     ui.end_date, ui.status, ui.created_at,
                     u.username, u.email, u.first_name, u.last_name,
                     ip.name as package_name, ip.daily_profit_percent, ip.duration_days
              FROM user_investments ui 
              JOIN users u ON ui.user_id = u.id 
              JOIN investment_packages ip ON ui.package_id = ip.id
              ORDER BY ui.created_at DESC";
    
    $stmt = $db->prepare($query);
    $stmt->execute();
    $investments = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $headers = [
        'ID', 'Сумма инвестиции', 'Ежедневная прибыль', 'Всего заработано', 'Дата начала',
        'Дата окончания', 'Статус', 'Пользователь', 'Email пользователя', 'Пакет',
        'Процент прибыли', 'Длительность (дни)', 'Дата создания'
    ];
    
    if ($format === 'csv') {
        exportToCSV($investments, $headers, 'investments_' . date('Y-m-d_H-i-s') . '.csv', function($investment) {
            return [
                $investment['id'],
                $investment['amount'],
                $investment['daily_profit'],
                $investment['total_earned'],
                $investment['start_date'],
                $investment['end_date'],
                $investment['status'],
                $investment['first_name'] . ' ' . $investment['last_name'],
                $investment['email'],
                $investment['package_name'],
                $investment['daily_profit_percent'],
                $investment['duration_days'],
                $investment['created_at']
            ];
        });
    } else {
        exportToJSON($investments, 'investments_' . date('Y-m-d_H-i-s') . '.json');
    }
}

function exportLogs($db, $format) {
    // Получение параметров фильтрации из URL
    $filters = [
        'action' => sanitize_input($_GET['action'] ?? ''),
        'ip_address' => sanitize_input($_GET['ip_address'] ?? ''),
        'date_from' => sanitize_input($_GET['date_from'] ?? ''),
        'date_to' => sanitize_input($_GET['date_to'] ?? '')
    ];
    
    $where_conditions = [];
    $params = [];
    
    if (!empty($filters['action'])) {
        $where_conditions[] = "sl.action LIKE :action";
        $params[':action'] = '%' . $filters['action'] . '%';
    }
    
    if (!empty($filters['ip_address'])) {
        $where_conditions[] = "sl.ip_address = :ip_address";
        $params[':ip_address'] = $filters['ip_address'];
    }
    
    if (!empty($filters['date_from'])) {
        $where_conditions[] = "sl.created_at >= :date_from";
        $params[':date_from'] = $filters['date_from'];
    }
    
    if (!empty($filters['date_to'])) {
        $where_conditions[] = "sl.created_at <= :date_to";
        $params[':date_to'] = $filters['date_to'] . ' 23:59:59';
    }
    
    $where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';
    
    $query = "SELECT sl.id, sl.action, sl.description, sl.ip_address, sl.user_agent, 
                     sl.additional_data, sl.created_at,
                     u.username, u.email
              FROM system_logs sl 
              LEFT JOIN users u ON sl.user_id = u.id 
              $where_clause 
              ORDER BY sl.created_at DESC 
              LIMIT 10000";
    
    $stmt = $db->prepare($query);
    foreach ($params as $key => $value) {
        $stmt->bindValue($key, $value);
    }
    $stmt->execute();
    $logs = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $headers = [
        'ID', 'Действие', 'Описание', 'IP адрес', 'User Agent', 'Пользователь', 
        'Email пользователя', 'Дополнительные данные', 'Дата создания'
    ];
    
    if ($format === 'csv') {
        exportToCSV($logs, $headers, 'logs_' . date('Y-m-d_H-i-s') . '.csv', function($log) {
            return [
                $log['id'],
                $log['action'],
                $log['description'] ?? '',
                $log['ip_address'],
                $log['user_agent'] ?? '',
                $log['username'] ?? 'Система',
                $log['email'] ?? '',
                $log['additional_data'] ?? '',
                $log['created_at']
            ];
        });
    } else {
        exportToJSON($logs, 'logs_' . date('Y-m-d_H-i-s') . '.json');
    }
}

function exportToCSV($data, $headers, $filename, $row_formatter = null) {
    header('Content-Type: text/csv; charset=utf-8');
    header('Content-Disposition: attachment; filename="' . $filename . '"');
    header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
    header('Expires: 0');
    
    $output = fopen('php://output', 'w');
    
    // BOM для корректного отображения UTF-8 в Excel
    fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF));
    
    // Заголовки
    fputcsv($output, $headers, ';');
    
    // Данные
    foreach ($data as $row) {
        if ($row_formatter) {
            $formatted_row = $row_formatter($row);
        } else {
            $formatted_row = array_values($row);
        }
        fputcsv($output, $formatted_row, ';');
    }
    
    fclose($output);
    exit;
}

function exportToJSON($data, $filename) {
    header('Content-Type: application/json; charset=utf-8');
    header('Content-Disposition: attachment; filename="' . $filename . '"');
    header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
    header('Expires: 0');
    
    echo json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
    exit;
}
?>
