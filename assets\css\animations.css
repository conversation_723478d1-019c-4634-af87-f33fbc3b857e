/**
 * AstroGenix - Анимации и эффекты
 * Эко-майнинговая инвестиционная платформа
 */

/* Базовые анимации появления */
@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInDown {
    from {
        opacity: 0;
        transform: translateY(-30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideLeft {
    from {
        opacity: 0;
        transform: translateX(50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideRight {
    from {
        opacity: 0;
        transform: translateX(-50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes scaleIn {
    from {
        opacity: 0;
        transform: scale(0.8);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

/* Анимации для главной страницы */
.animate-fade-in {
    animation: fadeIn 1s ease-out;
}

.animate-fade-in-delay {
    animation: fadeIn 1s ease-out 0.3s both;
}

.animate-slide-up {
    animation: slideUp 0.8s ease-out 0.5s both;
}

.animate-slide-up-delay {
    animation: slideUp 0.8s ease-out 0.8s both;
}

.animate-on-scroll {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.6s ease-out;
}

.animate-on-scroll.visible {
    opacity: 1;
    transform: translateY(0);
}

/* Анимации счетчиков */
@keyframes countUp {
    from {
        transform: translateY(20px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

.stat-number {
    animation: countUp 0.8s ease-out;
}

/* Анимации энергетических эффектов */
@keyframes energyPulse {
    0%, 100% {
        transform: scale(1);
        opacity: 0.7;
    }
    50% {
        transform: scale(1.1);
        opacity: 1;
    }
}

@keyframes energyWave {
    0% {
        transform: scale(0.8);
        opacity: 1;
    }
    100% {
        transform: scale(2);
        opacity: 0;
    }
}

@keyframes energyFloat {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-10px);
    }
}

.energy-icon {
    animation: energyPulse 2s ease-in-out infinite;
}

.energy-wave {
    position: absolute;
    width: 100px;
    height: 100px;
    border: 2px solid var(--primary-green);
    border-radius: 50%;
    opacity: 0;
    animation: energyWave 3s ease-out infinite;
}

.energy-wave:nth-child(1) {
    animation-delay: 0s;
}

.energy-wave:nth-child(2) {
    animation-delay: 1s;
}

.energy-wave:nth-child(3) {
    animation-delay: 2s;
}

.energy-animation {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Анимации частиц */
@keyframes particleFloat {
    0% {
        transform: translate(0, 0) rotate(0deg);
        opacity: 0.3;
    }
    25% {
        transform: translate(20px, -20px) rotate(90deg);
        opacity: 0.7;
    }
    50% {
        transform: translate(-10px, -40px) rotate(180deg);
        opacity: 0.5;
    }
    75% {
        transform: translate(-30px, -10px) rotate(270deg);
        opacity: 0.8;
    }
    100% {
        transform: translate(0, 0) rotate(360deg);
        opacity: 0.3;
    }
}

.eco-particles {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: 
        radial-gradient(3px 3px at 25px 35px, rgba(46, 204, 113, 0.4), transparent),
        radial-gradient(2px 2px at 50px 75px, rgba(142, 68, 173, 0.3), transparent),
        radial-gradient(1px 1px at 95px 45px, rgba(88, 214, 141, 0.5), transparent),
        radial-gradient(2px 2px at 135px 85px, rgba(187, 143, 206, 0.4), transparent),
        radial-gradient(1px 1px at 175px 25px, rgba(46, 204, 113, 0.3), transparent);
    background-repeat: repeat;
    background-size: 200px 120px;
    animation: particleFloat 25s linear infinite;
    pointer-events: none;
}

/* Анимации кнопок */
@keyframes buttonPulse {
    0% {
        box-shadow: 0 0 0 0 rgba(46, 204, 113, 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(46, 204, 113, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(46, 204, 113, 0);
    }
}

.btn-primary:hover {
    animation: buttonPulse 1.5s infinite;
}

/* Анимации карточек */
@keyframes cardHover {
    from {
        transform: translateY(0);
        box-shadow: var(--shadow-sm);
    }
    to {
        transform: translateY(-5px);
        box-shadow: var(--shadow-lg);
    }
}

.feature-card:hover {
    animation: cardHover 0.3s ease-out forwards;
}

/* Анимации загрузки */
@keyframes spin {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

.loading-spinner {
    animation: spin 1s linear infinite;
}

/* Анимации градиентов */
@keyframes gradientShift {
    0% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
    100% {
        background-position: 0% 50%;
    }
}

.text-gradient {
    background: linear-gradient(-45deg, var(--primary-green), var(--primary-purple), var(--light-green), var(--light-purple));
    background-size: 400% 400%;
    animation: gradientShift 3s ease infinite;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Анимации при наведении */
.hover-scale {
    transition: transform 0.3s ease;
}

.hover-scale:hover {
    transform: scale(1.05);
}

.hover-glow {
    transition: all 0.3s ease;
}

.hover-glow:hover {
    box-shadow: 0 0 20px rgba(46, 204, 113, 0.3);
}

/* Анимации для мобильных устройств */
@media (max-width: 768px) {
    .animate-fade-in,
    .animate-fade-in-delay,
    .animate-slide-up,
    .animate-slide-up-delay {
        animation-duration: 0.6s;
    }
    
    .eco-particles {
        background-size: 150px 100px;
        animation-duration: 20s;
    }
}

/* Отключение анимаций для пользователей с ограниченными возможностями */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}
