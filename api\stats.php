<?php
/**
 * AstroGenix - API для получения статистики
 * Эко-майнинговая инвестиционная платформа
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET');
header('Access-Control-Allow-Headers: Content-Type');

require_once '../config/config.php';
require_once '../config/database.php';

try {
    $database = new Database();
    $db = $database->getConnection();
    
    // Получение общей статистики
    $stats_query = "SELECT 
        (SELECT COUNT(*) FROM users WHERE is_active = 1) as total_users,
        (SELECT COALESCE(SUM(amount), 0) FROM user_investments WHERE status = 'active') as total_invested,
        (SELECT COALESCE(SUM(amount), 0) FROM transactions WHERE type = 'profit' AND status = 'completed') as total_profits,
        (SELECT COUNT(*) FROM user_investments WHERE status = 'active') as active_investments";
    
    $stmt = $db->prepare($stats_query);
    $stmt->execute();
    $stats = $stmt->fetch(PDO::FETCH_ASSOC);
    
    // Расчет зеленой энергии на основе инвестиций
    // Предполагаем, что каждый доллар инвестиций генерирует 2.5 кВт⋅ч зеленой энергии
    $green_energy = ($stats['total_invested'] ?? 0) * 2.5;
    
    // Добавление случайного роста для имитации реального времени
    $time_factor = time() % 3600; // Обновление каждый час
    $random_growth = sin($time_factor / 600) * 100; // Плавное изменение
    
    $response = [
        'success' => true,
        'data' => [
            'total_invested' => number_format($stats['total_invested'] ?? 284750, 0, '.', ','),
            'total_invested_raw' => $stats['total_invested'] ?? 284750,
            'active_users' => number_format(($stats['total_users'] ?? 15420) + floor($random_growth), 0, '.', ','),
            'active_users_raw' => ($stats['total_users'] ?? 15420) + floor($random_growth),
            'green_energy' => number_format($green_energy + $random_growth * 10, 1, '.', ','),
            'green_energy_raw' => $green_energy + $random_growth * 10,
            'total_profits' => number_format($stats['total_profits'] ?? 847250, 0, '.', ','),
            'total_profits_raw' => $stats['total_profits'] ?? 847250,
            'active_investments' => $stats['active_investments'] ?? 1250,
            'timestamp' => time(),
            'last_update' => date('Y-m-d H:i:s')
        ]
    ];
    
    echo json_encode($response);
    
} catch (Exception $e) {
    // В случае ошибки возвращаем статические данные
    $response = [
        'success' => false,
        'error' => 'Database connection failed',
        'data' => [
            'total_invested' => '284,750',
            'total_invested_raw' => 284750,
            'active_users' => '15,420',
            'active_users_raw' => 15420,
            'green_energy' => '847,250.0',
            'green_energy_raw' => 847250,
            'total_profits' => '847,250',
            'total_profits_raw' => 847250,
            'active_investments' => 1250,
            'timestamp' => time(),
            'last_update' => date('Y-m-d H:i:s')
        ]
    ];
    
    echo json_encode($response);
}
?>
