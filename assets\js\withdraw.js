/**
 * AstroGenix - JavaScript для страницы вывода средств
 * Эко-майнинговая инвестиционная платформа
 */

document.addEventListener('DOMContentLoaded', function() {
    initializeWithdraw();
});

function initializeWithdraw() {
    // Инициализация кнопок быстрого выбора суммы
    initAmountButtons();
    
    // Инициализация обновления итоговой суммы
    initSummaryUpdate();
    
    // Инициализация валидации формы
    initWithdrawValidation();
    
    // Инициализация способов вывода
    initPaymentMethods();
}

// Кнопки быстрого выбора суммы
function initAmountButtons() {
    const amountInput = document.getElementById('amount');
    const amountButtons = document.querySelectorAll('.amount-btn');
    
    amountButtons.forEach(button => {
        button.addEventListener('click', function() {
            const amount = parseFloat(this.dataset.amount);
            const maxAmount = parseFloat(amountInput.getAttribute('max'));
            
            // Проверка максимальной суммы
            const finalAmount = Math.min(amount, maxAmount);
            amountInput.value = finalAmount;
            
            // Удаление активного класса у всех кнопок
            amountButtons.forEach(btn => btn.classList.remove('active'));
            
            // Добавление активного класса к текущей кнопке
            this.classList.add('active');
            
            // Обновление итоговой суммы
            updateSummary();
            
            // Анимация
            amountInput.style.transform = 'scale(1.05)';
            amountInput.style.borderColor = 'var(--primary-green)';
            setTimeout(() => {
                amountInput.style.transform = 'scale(1)';
                amountInput.style.borderColor = '';
            }, 300);
        });
    });
    
    // Сброс активного состояния при ручном вводе
    amountInput.addEventListener('input', function() {
        amountButtons.forEach(btn => btn.classList.remove('active'));
        updateSummary();
    });
}

// Обновление итоговой суммы
function initSummaryUpdate() {
    const amountInput = document.getElementById('amount');
    
    amountInput.addEventListener('input', updateSummary);
    
    // Обновление при изменении способа вывода
    const paymentMethods = document.querySelectorAll('input[name="payment_method"]');
    paymentMethods.forEach(method => {
        method.addEventListener('change', updateSummary);
    });
    
    // Первоначальное обновление
    updateSummary();
}

function updateSummary() {
    const amountInput = document.getElementById('amount');
    const summaryAmount = document.getElementById('summaryAmount');
    const summaryFee = document.getElementById('summaryFee');
    const summaryTotal = document.getElementById('summaryTotal');
    
    const amount = parseFloat(amountInput.value) || 0;
    const selectedPaymentMethod = document.querySelector('input[name="payment_method"]:checked');
    
    // Расчет комиссии в зависимости от способа вывода
    let feePercent = 0;
    if (selectedPaymentMethod) {
        const paymentType = selectedPaymentMethod.value;
        switch (paymentType) {
            case 'bank_card':
                feePercent = 0.02; // 2%
                break;
            case 'bank_transfer':
                feePercent = 0.01; // 1%
                break;
            case 'qiwi':
            case 'yandex_money':
                feePercent = 0.03; // 3%
                break;
            case 'crypto':
                feePercent = 0.01; // 1%
                break;
            case 'sbp':
                feePercent = 0; // 0%
                break;
            default:
                feePercent = 0.02; // 2% по умолчанию
        }
    }
    
    const fee = amount * feePercent;
    const total = amount - fee; // При выводе комиссия вычитается из суммы
    
    // Обновление отображения
    summaryAmount.textContent = formatCurrency(amount);
    summaryFee.textContent = formatCurrency(fee);
    summaryTotal.textContent = formatCurrency(Math.max(0, total));
    
    // Анимация изменения
    [summaryAmount, summaryFee, summaryTotal].forEach(element => {
        element.style.transform = 'scale(1.1)';
        element.style.color = 'var(--primary-green)';
        setTimeout(() => {
            element.style.transform = 'scale(1)';
            element.style.color = '';
        }, 300);
    });
    
    // Предупреждение о высокой комиссии
    if (feePercent > 0.02) {
        showFeeWarning(feePercent);
    } else {
        hideFeeWarning();
    }
}

// Валидация формы
function initWithdrawValidation() {
    const form = document.querySelector('.withdrawal-form');
    const amountInput = document.getElementById('amount');
    const paymentDetailsTextarea = document.getElementById('payment_details');
    
    form.addEventListener('submit', function(e) {
        if (!validateWithdrawForm()) {
            e.preventDefault();
        }
    });
    
    // Валидация суммы в реальном времени
    amountInput.addEventListener('blur', function() {
        validateAmount(this);
    });
    
    amountInput.addEventListener('input', function() {
        clearFieldError(this);
        
        // Ограничение ввода максимальной суммой
        const maxAmount = parseFloat(this.getAttribute('max'));
        const value = parseFloat(this.value);
        if (value > maxAmount) {
            this.value = maxAmount;
            showFieldError(this, `Максимальная сумма: ${formatCurrency(maxAmount)}`);
        }
    });
    
    // Автоматическое заполнение placeholder для реквизитов
    const paymentMethods = document.querySelectorAll('input[name="payment_method"]');
    paymentMethods.forEach(method => {
        method.addEventListener('change', function() {
            updatePaymentDetailsPlaceholder(this.value);
        });
    });
}

function validateWithdrawForm() {
    let isValid = true;
    
    // Валидация суммы
    const amountInput = document.getElementById('amount');
    if (!validateAmount(amountInput)) {
        isValid = false;
    }
    
    // Валидация способа вывода
    const selectedPaymentMethod = document.querySelector('input[name="payment_method"]:checked');
    if (!selectedPaymentMethod) {
        showFormError('Выберите способ вывода');
        isValid = false;
    }
    
    // Валидация реквизитов
    const paymentDetailsTextarea = document.getElementById('payment_details');
    if (!paymentDetailsTextarea.value.trim()) {
        showFieldError(paymentDetailsTextarea, 'Укажите реквизиты для вывода');
        isValid = false;
    } else if (paymentDetailsTextarea.value.trim().length < 10) {
        showFieldError(paymentDetailsTextarea, 'Реквизиты должны содержать минимум 10 символов');
        isValid = false;
    }
    
    return isValid;
}

function validateAmount(input) {
    const value = parseFloat(input.value);
    const maxAmount = parseFloat(input.getAttribute('max'));
    
    clearFieldError(input);
    
    if (!value || value <= 0) {
        showFieldError(input, 'Введите корректную сумму');
        return false;
    }
    
    if (value < 10) {
        showFieldError(input, 'Минимальная сумма вывода: 10 ₽');
        return false;
    }
    
    if (value > maxAmount) {
        showFieldError(input, `Недостаточно средств. Доступно: ${formatCurrency(maxAmount)}`);
        return false;
    }
    
    if (value > 50000) {
        showFieldError(input, 'Максимальная сумма вывода: 50,000 ₽');
        return false;
    }
    
    return true;
}

// Способы вывода
function initPaymentMethods() {
    const paymentOptions = document.querySelectorAll('.payment-method-option');
    
    paymentOptions.forEach(option => {
        option.addEventListener('click', function() {
            const radio = this.querySelector('input[type="radio"]');
            radio.checked = true;
            
            // Удаление активного класса у всех опций
            paymentOptions.forEach(opt => opt.classList.remove('active'));
            
            // Добавление активного класса к выбранной опции
            this.classList.add('active');
            
            // Обновление итоговой суммы
            updateSummary();
            
            // Обновление placeholder для реквизитов
            updatePaymentDetailsPlaceholder(radio.value);
            
            // Анимация
            const card = this.querySelector('.payment-method-card');
            card.style.transform = 'scale(0.95)';
            setTimeout(() => {
                card.style.transform = 'scale(1)';
            }, 150);
        });
    });
}

function updatePaymentDetailsPlaceholder(paymentMethod) {
    const paymentDetailsTextarea = document.getElementById('payment_details');
    
    const placeholders = {
        'bank_card': 'Номер карты: 1234 5678 9012 3456\nИмя держателя: IVAN PETROV\nБанк: Сбербанк',
        'bank_transfer': 'Номер счета: 40817810123456789012\nБИК банка: *********\nИмя получателя: Иванов Иван Иванович\nБанк: ПАО Сбербанк',
        'qiwi': 'Номер QIWI кошелька: +***********\nИли номер кошелька: ***********',
        'yandex_money': 'Номер ЮMoney кошелька: ***************\nИли привязанный номер телефона: +***********',
        'crypto': 'Bitcoin адрес: **********************************\nИли Ethereum адрес: ******************************************',
        'sbp': 'Номер телефона: +***********\nБанк: Сбербанк\nИмя получателя: Иванов Иван'
    };
    
    paymentDetailsTextarea.placeholder = placeholders[paymentMethod] || 'Укажите точные реквизиты для вывода средств';
    
    // Анимация изменения
    paymentDetailsTextarea.style.borderColor = 'var(--primary-green)';
    setTimeout(() => {
        paymentDetailsTextarea.style.borderColor = '';
    }, 1000);
}

// Предупреждения о комиссии
function showFeeWarning(feePercent) {
    let warning = document.querySelector('.fee-warning');
    if (!warning) {
        warning = document.createElement('div');
        warning.className = 'fee-warning';
        warning.style.cssText = `
            background: rgba(255, 193, 7, 0.1);
            border: 1px solid rgba(255, 193, 7, 0.3);
            color: #856404;
            padding: var(--space-3);
            border-radius: var(--radius-md);
            margin: var(--space-3) 0;
            display: flex;
            align-items: center;
            gap: var(--space-2);
            font-size: var(--text-sm);
        `;
        
        const summaryElement = document.querySelector('.withdrawal-summary');
        summaryElement.parentNode.insertBefore(warning, summaryElement);
    }
    
    warning.innerHTML = `
        <i class="fas fa-exclamation-triangle"></i>
        <span>Внимание! Комиссия за вывод составляет ${(feePercent * 100).toFixed(1)}%. Рассмотрите другие способы вывода с меньшей комиссией.</span>
    `;
}

function hideFeeWarning() {
    const warning = document.querySelector('.fee-warning');
    if (warning) {
        warning.remove();
    }
}

// Утилиты
function formatCurrency(amount) {
    return new Intl.NumberFormat('ru-RU', {
        style: 'currency',
        currency: 'RUB',
        minimumFractionDigits: 0,
        maximumFractionDigits: 2
    }).format(amount);
}

function showFieldError(field, message) {
    field.classList.add('error');
    
    // Удаление существующего сообщения об ошибке
    const existingError = field.parentElement.querySelector('.field-error');
    if (existingError) {
        existingError.remove();
    }
    
    // Добавление нового сообщения об ошибке
    const errorElement = document.createElement('div');
    errorElement.className = 'field-error';
    errorElement.style.cssText = `
        color: var(--error);
        font-size: var(--text-xs);
        margin-top: var(--space-1);
        display: flex;
        align-items: center;
        gap: var(--space-1);
    `;
    errorElement.innerHTML = `<i class="fas fa-exclamation-triangle"></i> ${message}`;
    
    field.parentElement.appendChild(errorElement);
}

function clearFieldError(field) {
    field.classList.remove('error');
    const errorElement = field.parentElement.querySelector('.field-error');
    if (errorElement) {
        errorElement.remove();
    }
}

function showFormError(message) {
    // Создание общего уведомления об ошибке
    const alert = document.createElement('div');
    alert.className = 'alert alert-error';
    alert.innerHTML = `
        <i class="fas fa-exclamation-triangle"></i>
        ${message}
    `;
    
    // Вставка в начало контента
    const content = document.querySelector('.dashboard-content');
    content.insertBefore(alert, content.firstChild);
    
    // Прокрутка к ошибке
    alert.scrollIntoView({ behavior: 'smooth', block: 'center' });
    
    // Автоматическое скрытие через 5 секунд
    setTimeout(() => {
        if (alert.parentElement) {
            alert.style.opacity = '0';
            setTimeout(() => {
                alert.remove();
            }, 300);
        }
    }, 5000);
}

// Добавление стилей для страницы вывода
const style = document.createElement('style');
style.textContent = `
    .withdrawal-container {
        display: grid;
        grid-template-columns: 2fr 1fr;
        gap: var(--space-6);
    }
    
    .withdrawal-info {
        margin-bottom: var(--space-6);
    }
    
    .info-card {
        background: linear-gradient(135deg, rgba(23, 162, 184, 0.1) 0%, rgba(46, 204, 113, 0.1) 100%);
        border: 1px solid rgba(23, 162, 184, 0.2);
        border-radius: var(--radius-lg);
        padding: var(--space-6);
        display: flex;
        gap: var(--space-4);
    }
    
    .info-icon {
        width: 60px;
        height: 60px;
        background: var(--info);
        border-radius: var(--radius-full);
        display: flex;
        align-items: center;
        justify-content: center;
        color: var(--white);
        font-size: var(--text-2xl);
        flex-shrink: 0;
    }
    
    .info-content h3 {
        margin: 0 0 var(--space-3) 0;
        color: var(--gray-900);
    }
    
    .info-content ul {
        margin: 0;
        padding-left: var(--space-4);
        color: var(--gray-700);
    }
    
    .info-content li {
        margin-bottom: var(--space-1);
    }
    
    .withdrawal-summary {
        background: var(--gray-100);
        border-radius: var(--radius-lg);
        padding: var(--space-4);
        margin: var(--space-6) 0;
    }
    
    .summary-row {
        display: flex;
        justify-content: space-between;
        margin-bottom: var(--space-2);
    }
    
    .summary-row.total {
        border-top: 1px solid var(--gray-300);
        padding-top: var(--space-2);
        font-weight: 600;
        font-size: var(--text-lg);
        color: var(--primary-green);
    }
    
    .withdrawal-item {
        border: 1px solid var(--gray-200);
        border-radius: var(--radius-md);
        padding: var(--space-4);
        margin-bottom: var(--space-3);
        transition: border-color var(--transition-fast);
    }
    
    .withdrawal-item:hover {
        border-color: var(--primary-green);
    }
    
    .withdrawal-item:last-child {
        margin-bottom: 0;
    }
    
    .withdrawal-info {
        margin-bottom: var(--space-2);
    }
    
    .withdrawal-amount {
        font-size: var(--text-lg);
        font-weight: 600;
        color: var(--error);
        margin-bottom: var(--space-1);
    }
    
    .withdrawal-date {
        font-size: var(--text-sm);
        color: var(--gray-600);
        margin-bottom: var(--space-1);
    }
    
    .withdrawal-description {
        font-size: var(--text-sm);
        color: var(--gray-700);
        margin-bottom: var(--space-1);
    }
    
    .admin-note {
        font-size: var(--text-sm);
        color: var(--error);
        background: rgba(220, 53, 69, 0.1);
        padding: var(--space-2);
        border-radius: var(--radius-sm);
        border-left: 3px solid var(--error);
    }
    
    .status-badge {
        padding: var(--space-1) var(--space-2);
        border-radius: var(--radius-sm);
        font-size: var(--text-xs);
        font-weight: 500;
    }
    
    @media (max-width: 768px) {
        .withdrawal-container {
            grid-template-columns: 1fr;
        }
        
        .info-card {
            flex-direction: column;
            text-align: center;
        }
        
        .info-icon {
            align-self: center;
        }
    }
`;
document.head.appendChild(style);
