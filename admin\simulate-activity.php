<?php
/**
 * AstroGenix - Симуляция активности платформы
 * Эко-майнинговая инвестиционная платформа
 */

require_once '../config/config.php';

// Проверка авторизации и прав администратора
require_admin();

// Инициализация переменных
$error_message = '';
$success_message = '';
$simulation_results = [];

try {
    $database = new Database();
    $db = $database->getConnection();
    
    // Обработка запросов на симуляцию
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        if (!verify_csrf_token($_POST['csrf_token'] ?? '')) {
            $error_message = 'Ошибка безопасности. Попробуйте еще раз.';
        } else {
            $action = sanitize_input($_POST['action'] ?? '');
            
            switch ($action) {
                case 'simulate_users':
                    $count = intval($_POST['user_count'] ?? 5);
                    $simulation_results = simulateUsers($db, $count);
                    $success_message = "Создано {$count} новых пользователей.";
                    break;
                    
                case 'simulate_transactions':
                    $count = intval($_POST['transaction_count'] ?? 10);
                    $simulation_results = simulateTransactions($db, $count);
                    $success_message = "Создано {$count} новых транзакций.";
                    break;
                    
                case 'simulate_investments':
                    $count = intval($_POST['investment_count'] ?? 5);
                    $simulation_results = simulateInvestments($db, $count);
                    $success_message = "Создано {$count} новых инвестиций.";
                    break;
                    
                case 'simulate_profits':
                    $simulation_results = simulateProfits($db);
                    $success_message = "Начислена прибыль по активным инвестициям.";
                    break;
                    
                case 'simulate_full_activity':
                    $users = simulateUsers($db, 3);
                    $transactions = simulateTransactions($db, 8);
                    $investments = simulateInvestments($db, 4);
                    $profits = simulateProfits($db);
                    
                    $simulation_results = [
                        'users' => $users,
                        'transactions' => $transactions,
                        'investments' => $investments,
                        'profits' => $profits
                    ];
                    $success_message = "Выполнена полная симуляция активности платформы.";
                    break;
            }
        }
    }
    
} catch (Exception $e) {
    error_log("Simulation error: " . $e->getMessage());
    $error_message = "Ошибка симуляции: " . $e->getMessage();
}

// Функция симуляции пользователей
function simulateUsers($db, $count) {
    $results = [];
    $fake_names = [
        ['Александр', 'Иванов'], ['Мария', 'Петрова'], ['Дмитрий', 'Сидоров'],
        ['Елена', 'Козлова'], ['Андрей', 'Морозов'], ['Ольга', 'Волкова'],
        ['Сергей', 'Соколов'], ['Анна', 'Лебедева'], ['Михаил', 'Новиков'],
        ['Татьяна', 'Федорова'], ['Владимир', 'Кузнецов'], ['Наталья', 'Попова']
    ];
    
    for ($i = 0; $i < $count; $i++) {
        $name = $fake_names[array_rand($fake_names)];
        $username = strtolower($name[0] . '_' . $name[1] . '_' . rand(100, 999));
        $email = $username . '@example.com';
        $password = password_hash('demo123', PASSWORD_DEFAULT);
        $balance = rand(0, 1000);
        
        $query = "INSERT INTO users (username, email, password, first_name, last_name, balance, is_active, email_verified, created_at) 
                  VALUES (:username, :email, :password, :first_name, :last_name, :balance, 1, 1, NOW())";
        
        $stmt = $db->prepare($query);
        $stmt->bindParam(':username', $username);
        $stmt->bindParam(':email', $email);
        $stmt->bindParam(':password', $password);
        $stmt->bindParam(':first_name', $name[0]);
        $stmt->bindParam(':last_name', $name[1]);
        $stmt->bindParam(':balance', $balance);
        
        if ($stmt->execute()) {
            $user_id = $db->lastInsertId();
            $results[] = [
                'id' => $user_id,
                'username' => $username,
                'email' => $email,
                'name' => $name[0] . ' ' . $name[1],
                'balance' => $balance
            ];
        }
    }
    
    return $results;
}

// Функция симуляции транзакций
function simulateTransactions($db, $count) {
    $results = [];
    
    // Получение случайных пользователей
    $users_query = "SELECT id FROM users WHERE is_active = 1 ORDER BY RAND() LIMIT " . ($count * 2);
    $users_stmt = $db->prepare($users_query);
    $users_stmt->execute();
    $users = $users_stmt->fetchAll(PDO::FETCH_COLUMN);
    
    if (empty($users)) {
        return $results;
    }
    
    $transaction_types = ['deposit', 'withdrawal', 'profit', 'referral_bonus'];
    $statuses = ['completed', 'pending'];
    
    for ($i = 0; $i < $count; $i++) {
        $user_id = $users[array_rand($users)];
        $type = $transaction_types[array_rand($transaction_types)];
        $amount = rand(10, 500);
        $status = $statuses[array_rand($statuses)];
        
        // Для выводов делаем меньшие суммы
        if ($type === 'withdrawal') {
            $amount = rand(10, 100);
        }
        
        $query = "INSERT INTO transactions (user_id, type, amount, status, description, created_at) 
                  VALUES (:user_id, :type, :amount, :status, :description, NOW() - INTERVAL RAND() * 7 DAY)";
        
        $description = "Симулированная транзакция: " . $type;
        
        $stmt = $db->prepare($query);
        $stmt->bindParam(':user_id', $user_id, PDO::PARAM_INT);
        $stmt->bindParam(':type', $type);
        $stmt->bindParam(':amount', $amount);
        $stmt->bindParam(':status', $status);
        $stmt->bindParam(':description', $description);
        
        if ($stmt->execute()) {
            $results[] = [
                'id' => $db->lastInsertId(),
                'user_id' => $user_id,
                'type' => $type,
                'amount' => $amount,
                'status' => $status
            ];
        }
    }
    
    return $results;
}

// Функция симуляции инвестиций
function simulateInvestments($db, $count) {
    $results = [];
    
    // Получение пользователей и пакетов
    $users_query = "SELECT id FROM users WHERE is_active = 1 ORDER BY RAND() LIMIT " . ($count * 2);
    $users_stmt = $db->prepare($users_query);
    $users_stmt->execute();
    $users = $users_stmt->fetchAll(PDO::FETCH_COLUMN);
    
    $packages_query = "SELECT id, min_amount, max_amount, duration_days FROM investment_packages WHERE is_active = 1";
    $packages_stmt = $db->prepare($packages_query);
    $packages_stmt->execute();
    $packages = $packages_stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($users) || empty($packages)) {
        return $results;
    }
    
    for ($i = 0; $i < $count; $i++) {
        $user_id = $users[array_rand($users)];
        $package = $packages[array_rand($packages)];
        $amount = rand($package['min_amount'], min($package['max_amount'], 1000));
        
        $start_date = date('Y-m-d H:i:s', time() - rand(0, 7 * 24 * 60 * 60));
        $end_date = date('Y-m-d H:i:s', strtotime($start_date) + ($package['duration_days'] * 24 * 60 * 60));
        
        $query = "INSERT INTO user_investments (user_id, package_id, amount, start_date, end_date, status, created_at) 
                  VALUES (:user_id, :package_id, :amount, :start_date, :end_date, 'active', :created_at)";
        
        $stmt = $db->prepare($query);
        $stmt->bindParam(':user_id', $user_id, PDO::PARAM_INT);
        $stmt->bindParam(':package_id', $package['id'], PDO::PARAM_INT);
        $stmt->bindParam(':amount', $amount);
        $stmt->bindParam(':start_date', $start_date);
        $stmt->bindParam(':end_date', $end_date);
        $stmt->bindParam(':created_at', $start_date);
        
        if ($stmt->execute()) {
            $results[] = [
                'id' => $db->lastInsertId(),
                'user_id' => $user_id,
                'package_id' => $package['id'],
                'amount' => $amount,
                'start_date' => $start_date,
                'end_date' => $end_date
            ];
        }
    }
    
    return $results;
}

// Функция симуляции прибыли
function simulateProfits($db) {
    $results = [];
    
    // Получение активных инвестиций
    $query = "SELECT ui.*, ip.daily_profit_percent 
              FROM user_investments ui 
              JOIN investment_packages ip ON ui.package_id = ip.id 
              WHERE ui.status = 'active' AND ui.end_date > NOW()";
    
    $stmt = $db->prepare($query);
    $stmt->execute();
    $investments = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    foreach ($investments as $investment) {
        $daily_profit = ($investment['amount'] * $investment['daily_profit_percent']) / 100;
        
        // Обновление общей прибыли
        $update_query = "UPDATE user_investments 
                        SET total_earned = total_earned + :profit 
                        WHERE id = :id";
        
        $update_stmt = $db->prepare($update_query);
        $update_stmt->bindParam(':profit', $daily_profit);
        $update_stmt->bindParam(':id', $investment['id'], PDO::PARAM_INT);
        $update_stmt->execute();
        
        // Создание транзакции прибыли
        $transaction_query = "INSERT INTO transactions (user_id, type, amount, status, description, created_at) 
                             VALUES (:user_id, 'profit', :amount, 'completed', 'Ежедневная прибыль', NOW())";
        
        $transaction_stmt = $db->prepare($transaction_query);
        $transaction_stmt->bindParam(':user_id', $investment['user_id'], PDO::PARAM_INT);
        $transaction_stmt->bindParam(':amount', $daily_profit);
        $transaction_stmt->execute();
        
        // Обновление баланса пользователя
        $balance_query = "UPDATE users SET balance = balance + :profit WHERE id = :user_id";
        $balance_stmt = $db->prepare($balance_query);
        $balance_stmt->bindParam(':profit', $daily_profit);
        $balance_stmt->bindParam(':user_id', $investment['user_id'], PDO::PARAM_INT);
        $balance_stmt->execute();
        
        $results[] = [
            'investment_id' => $investment['id'],
            'user_id' => $investment['user_id'],
            'profit' => $daily_profit
        ];
    }
    
    return $results;
}

$page_title = 'Симуляция активности - Админ-панель';
?>
<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?></title>
    
    <!-- CSS -->
    <link rel="stylesheet" href="../assets/css/style.css">
    <link rel="stylesheet" href="../assets/css/dashboard.css">
    <link rel="stylesheet" href="../assets/css/admin.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body class="dashboard-page admin-page">
    <!-- Admin Sidebar -->
    <?php include 'includes/admin-sidebar.php'; ?>

    <!-- Main Content -->
    <main class="main-content">
        <!-- Header -->
        <header class="dashboard-header">
            <div class="header-left">
                <button class="mobile-menu-toggle" id="mobileMenuToggle">
                    <i class="fas fa-bars"></i>
                </button>
                <h1>Симуляция активности платформы</h1>
            </div>
        </header>

        <!-- Dashboard Content -->
        <div class="dashboard-content">
            <?php if (!empty($error_message)): ?>
                <div class="alert alert-error">
                    <i class="fas fa-exclamation-triangle"></i>
                    <?php echo htmlspecialchars($error_message); ?>
                </div>
            <?php endif; ?>

            <?php if (!empty($success_message)): ?>
                <div class="alert alert-success">
                    <i class="fas fa-check-circle"></i>
                    <?php echo htmlspecialchars($success_message); ?>
                </div>
            <?php endif; ?>

            <!-- Simulation Controls -->
            <div class="simulation-grid">
                <!-- Simulate Users -->
                <div class="simulation-card">
                    <div class="card-header">
                        <h3><i class="fas fa-users"></i> Симуляция пользователей</h3>
                    </div>
                    <div class="card-body">
                        <p>Создание фиктивных пользователей для демонстрации активности платформы.</p>
                        <form method="POST" class="simulation-form">
                            <input type="hidden" name="csrf_token" value="<?php echo generate_csrf_token(); ?>">
                            <input type="hidden" name="action" value="simulate_users">
                            
                            <div class="form-group">
                                <label for="user_count">Количество пользователей:</label>
                                <input type="number" id="user_count" name="user_count" value="5" min="1" max="50" required>
                            </div>
                            
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-user-plus"></i>
                                Создать пользователей
                            </button>
                        </form>
                    </div>
                </div>

                <!-- Simulate Transactions -->
                <div class="simulation-card">
                    <div class="card-header">
                        <h3><i class="fas fa-exchange-alt"></i> Симуляция транзакций</h3>
                    </div>
                    <div class="card-body">
                        <p>Создание случайных транзакций (пополнения, выводы, прибыль).</p>
                        <form method="POST" class="simulation-form">
                            <input type="hidden" name="csrf_token" value="<?php echo generate_csrf_token(); ?>">
                            <input type="hidden" name="action" value="simulate_transactions">
                            
                            <div class="form-group">
                                <label for="transaction_count">Количество транзакций:</label>
                                <input type="number" id="transaction_count" name="transaction_count" value="10" min="1" max="100" required>
                            </div>
                            
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-plus"></i>
                                Создать транзакции
                            </button>
                        </form>
                    </div>
                </div>

                <!-- Simulate Investments -->
                <div class="simulation-card">
                    <div class="card-header">
                        <h3><i class="fas fa-chart-line"></i> Симуляция инвестиций</h3>
                    </div>
                    <div class="card-body">
                        <p>Создание активных инвестиций для существующих пользователей.</p>
                        <form method="POST" class="simulation-form">
                            <input type="hidden" name="csrf_token" value="<?php echo generate_csrf_token(); ?>">
                            <input type="hidden" name="action" value="simulate_investments">
                            
                            <div class="form-group">
                                <label for="investment_count">Количество инвестиций:</label>
                                <input type="number" id="investment_count" name="investment_count" value="5" min="1" max="50" required>
                            </div>
                            
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-chart-line"></i>
                                Создать инвестиции
                            </button>
                        </form>
                    </div>
                </div>

                <!-- Simulate Profits -->
                <div class="simulation-card">
                    <div class="card-header">
                        <h3><i class="fas fa-coins"></i> Начисление прибыли</h3>
                    </div>
                    <div class="card-body">
                        <p>Начисление ежедневной прибыли по всем активным инвестициям.</p>
                        <form method="POST" class="simulation-form">
                            <input type="hidden" name="csrf_token" value="<?php echo generate_csrf_token(); ?>">
                            <input type="hidden" name="action" value="simulate_profits">
                            
                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-coins"></i>
                                Начислить прибыль
                            </button>
                        </form>
                    </div>
                </div>

                <!-- Full Activity Simulation -->
                <div class="simulation-card full-width">
                    <div class="card-header">
                        <h3><i class="fas fa-magic"></i> Полная симуляция активности</h3>
                    </div>
                    <div class="card-body">
                        <p>Комплексная симуляция: создание пользователей, транзакций, инвестиций и начисление прибыли.</p>
                        <form method="POST" class="simulation-form">
                            <input type="hidden" name="csrf_token" value="<?php echo generate_csrf_token(); ?>">
                            <input type="hidden" name="action" value="simulate_full_activity">
                            
                            <button type="submit" class="btn btn-primary btn-large">
                                <i class="fas fa-magic"></i>
                                Запустить полную симуляцию
                            </button>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Simulation Results -->
            <?php if (!empty($simulation_results)): ?>
                <div class="simulation-results">
                    <div class="card">
                        <div class="card-header">
                            <h3><i class="fas fa-chart-bar"></i> Результаты симуляции</h3>
                        </div>
                        <div class="card-body">
                            <pre><?php echo json_encode($simulation_results, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE); ?></pre>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </main>

    <!-- JavaScript -->
    <script src="../assets/js/admin.js"></script>
    <script>
        // Подтверждение перед симуляцией
        document.querySelectorAll('.simulation-form').forEach(form => {
            form.addEventListener('submit', function(e) {
                const action = this.querySelector('input[name="action"]').value;
                let message = 'Вы уверены, что хотите выполнить эту симуляцию?';
                
                if (action === 'simulate_full_activity') {
                    message = 'Это создаст множество тестовых данных. Продолжить?';
                }
                
                if (!confirm(message)) {
                    e.preventDefault();
                }
            });
        });
    </script>
</body>
</html>
