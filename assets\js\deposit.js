/**
 * AstroGenix - JavaScript для страницы пополнения
 * Эко-майнинговая инвестиционная платформа
 */

document.addEventListener('DOMContentLoaded', function() {
    initializeDeposit();
});

function initializeDeposit() {
    // Инициализация кнопок быстрого выбора суммы
    initAmountButtons();
    
    // Инициализация обновления итоговой суммы
    initSummaryUpdate();
    
    // Инициализация валидации формы
    initDepositValidation();
    
    // Инициализация способов оплаты
    initPaymentMethods();
}

// Кнопки быстрого выбора суммы
function initAmountButtons() {
    const amountInput = document.getElementById('amount');
    const amountButtons = document.querySelectorAll('.amount-btn');
    
    amountButtons.forEach(button => {
        button.addEventListener('click', function() {
            const amount = this.dataset.amount;
            amountInput.value = amount;
            
            // Удаление активного класса у всех кнопок
            amountButtons.forEach(btn => btn.classList.remove('active'));
            
            // Добавление активного класса к текущей кнопке
            this.classList.add('active');
            
            // Обновление итоговой суммы
            updateSummary();
            
            // Анимация
            amountInput.style.transform = 'scale(1.05)';
            setTimeout(() => {
                amountInput.style.transform = 'scale(1)';
            }, 200);
        });
    });
    
    // Сброс активного состояния при ручном вводе
    amountInput.addEventListener('input', function() {
        amountButtons.forEach(btn => btn.classList.remove('active'));
        updateSummary();
    });
}

// Обновление итоговой суммы
function initSummaryUpdate() {
    const amountInput = document.getElementById('amount');
    
    amountInput.addEventListener('input', updateSummary);
    
    // Обновление при изменении способа оплаты
    const paymentMethods = document.querySelectorAll('input[name="payment_method"]');
    paymentMethods.forEach(method => {
        method.addEventListener('change', updateSummary);
    });
    
    // Первоначальное обновление
    updateSummary();
}

function updateSummary() {
    const amountInput = document.getElementById('amount');
    const summaryAmount = document.getElementById('summaryAmount');
    const summaryTotal = document.getElementById('summaryTotal');
    const summaryFee = document.querySelector('.summary-fee');
    
    const amount = parseFloat(amountInput.value) || 0;
    const selectedPaymentMethod = document.querySelector('input[name="payment_method"]:checked');
    
    // Расчет комиссии в зависимости от способа оплаты
    let fee = 0;
    if (selectedPaymentMethod) {
        const paymentType = selectedPaymentMethod.value;
        switch (paymentType) {
            case 'bank_card':
                fee = amount * 0.03; // 3%
                break;
            case 'crypto':
                fee = amount * 0.01; // 1%
                break;
            case 'qiwi':
            case 'yandex_money':
                fee = amount * 0.02; // 2%
                break;
            default:
                fee = 0;
        }
    }
    
    const total = amount + fee;
    
    // Обновление отображения
    summaryAmount.textContent = formatCurrency(amount);
    summaryFee.textContent = formatCurrency(fee);
    summaryTotal.textContent = formatCurrency(total);
    
    // Анимация изменения
    [summaryAmount, summaryFee, summaryTotal].forEach(element => {
        element.style.transform = 'scale(1.1)';
        element.style.color = 'var(--primary-green)';
        setTimeout(() => {
            element.style.transform = 'scale(1)';
            element.style.color = '';
        }, 300);
    });
}

// Валидация формы
function initDepositValidation() {
    const form = document.querySelector('.deposit-form');
    const amountInput = document.getElementById('amount');
    const paymentDetailsTextarea = document.getElementById('payment_details');
    
    form.addEventListener('submit', function(e) {
        if (!validateDepositForm()) {
            e.preventDefault();
        }
    });
    
    // Валидация суммы в реальном времени
    amountInput.addEventListener('blur', function() {
        validateAmount(this);
    });
    
    amountInput.addEventListener('input', function() {
        clearFieldError(this);
        
        // Ограничение ввода
        const value = parseFloat(this.value);
        if (value > 10000) {
            this.value = 10000;
        }
    });
    
    // Автоматическое заполнение деталей платежа
    const paymentMethods = document.querySelectorAll('input[name="payment_method"]');
    paymentMethods.forEach(method => {
        method.addEventListener('change', function() {
            updatePaymentDetailsPlaceholder(this.value);
        });
    });
}

function validateDepositForm() {
    let isValid = true;
    
    // Валидация суммы
    const amountInput = document.getElementById('amount');
    if (!validateAmount(amountInput)) {
        isValid = false;
    }
    
    // Валидация способа оплаты
    const selectedPaymentMethod = document.querySelector('input[name="payment_method"]:checked');
    if (!selectedPaymentMethod) {
        showFormError('Выберите способ оплаты');
        isValid = false;
    }
    
    // Валидация деталей платежа для определенных способов
    const paymentDetailsTextarea = document.getElementById('payment_details');
    if (selectedPaymentMethod && ['bank_card', 'bank_transfer'].includes(selectedPaymentMethod.value)) {
        if (!paymentDetailsTextarea.value.trim()) {
            showFieldError(paymentDetailsTextarea, 'Укажите детали платежа');
            isValid = false;
        }
    }
    
    return isValid;
}

function validateAmount(input) {
    const value = parseFloat(input.value);
    
    clearFieldError(input);
    
    if (!value || value <= 0) {
        showFieldError(input, 'Введите корректную сумму');
        return false;
    }
    
    if (value < 10) {
        showFieldError(input, 'Минимальная сумма пополнения: 10 USDT');
        return false;
    }

    if (value > 10000) {
        showFieldError(input, 'Максимальная сумма пополнения: 10,000 USDT');
        return false;
    }
    
    return true;
}

// Способы оплаты
function initPaymentMethods() {
    const paymentOptions = document.querySelectorAll('.payment-method-option');
    
    paymentOptions.forEach(option => {
        option.addEventListener('click', function() {
            const radio = this.querySelector('input[type="radio"]');
            radio.checked = true;
            
            // Удаление активного класса у всех опций
            paymentOptions.forEach(opt => opt.classList.remove('active'));
            
            // Добавление активного класса к выбранной опции
            this.classList.add('active');
            
            // Обновление итоговой суммы
            updateSummary();
            
            // Обновление placeholder для деталей платежа
            updatePaymentDetailsPlaceholder(radio.value);

            // Показ/скрытие секции кошелька для криптовалют
            updateCryptoWalletSection(radio.value);

            // Анимация
            const card = this.querySelector('.payment-method-card');
            card.style.transform = 'scale(0.95)';
            setTimeout(() => {
                card.style.transform = 'scale(1)';
            }, 150);
        });
    });
}

function updatePaymentDetailsPlaceholder(paymentMethod) {
    const paymentDetailsTextarea = document.getElementById('payment_details');

    const placeholders = {
        'usdt_trc20': 'Вставьте хэш транзакции USDT (TRC20) после отправки',
        'usdt_erc20': 'Вставьте хэш транзакции USDT (ERC20) после отправки',
        'bitcoin': 'Вставьте хэш транзакции Bitcoin после отправки',
        'ethereum': 'Вставьте хэш транзакции Ethereum после отправки',
        'litecoin': 'Вставьте хэш транзакции Litecoin после отправки',
        'binance_coin': 'Вставьте хэш транзакции BNB после отправки'
    };

    paymentDetailsTextarea.placeholder = placeholders[paymentMethod] || 'Вставьте хэш транзакции после отправки средств';

    // Анимация изменения
    paymentDetailsTextarea.style.borderColor = 'var(--primary-green)';
    setTimeout(() => {
        paymentDetailsTextarea.style.borderColor = '';
    }, 1000);
}

// Утилиты
function formatCurrency(amount) {
    return new Intl.NumberFormat('en-US', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
    }).format(amount) + ' USDT';
}

function showFieldError(field, message) {
    field.classList.add('error');
    
    // Удаление существующего сообщения об ошибке
    const existingError = field.parentElement.querySelector('.field-error');
    if (existingError) {
        existingError.remove();
    }
    
    // Добавление нового сообщения об ошибке
    const errorElement = document.createElement('div');
    errorElement.className = 'field-error';
    errorElement.textContent = message;
    errorElement.style.cssText = `
        color: var(--error);
        font-size: var(--text-xs);
        margin-top: var(--space-1);
        display: flex;
        align-items: center;
        gap: var(--space-1);
    `;
    errorElement.innerHTML = `<i class="fas fa-exclamation-triangle"></i> ${message}`;
    
    field.parentElement.appendChild(errorElement);
}

function clearFieldError(field) {
    field.classList.remove('error');
    const errorElement = field.parentElement.querySelector('.field-error');
    if (errorElement) {
        errorElement.remove();
    }
}

function showFormError(message) {
    // Создание общего уведомления об ошибке
    const alert = document.createElement('div');
    alert.className = 'alert alert-error';
    alert.innerHTML = `
        <i class="fas fa-exclamation-triangle"></i>
        ${message}
    `;
    
    // Вставка в начало контента
    const content = document.querySelector('.dashboard-content');
    content.insertBefore(alert, content.firstChild);
    
    // Прокрутка к ошибке
    alert.scrollIntoView({ behavior: 'smooth', block: 'center' });
    
    // Автоматическое скрытие через 5 секунд
    setTimeout(() => {
        if (alert.parentElement) {
            alert.style.opacity = '0';
            setTimeout(() => {
                alert.remove();
            }, 300);
        }
    }, 5000);
}

// Криптовалютные кошельки
function updateCryptoWalletSection(paymentMethod) {
    const cryptoSection = document.getElementById('cryptoWalletSection');
    const cryptoMethods = ['usdt_trc20', 'usdt_erc20', 'bitcoin', 'ethereum', 'litecoin', 'binance_coin'];

    if (cryptoMethods.includes(paymentMethod)) {
        cryptoSection.style.display = 'block';
        updateWalletInfo(paymentMethod);
        generateWalletAddress(paymentMethod);
    } else {
        cryptoSection.style.display = 'none';
    }
}

function updateWalletInfo(paymentMethod) {
    const cryptoInfo = {
        'usdt_trc20': {
            name: 'USDT (TRC20)',
            network: 'Tron Network',
            icon: 'assets/images/crypto/usdt.png',
            iconFallback: 'fas fa-coins',
            confirmations: 1,
            time: '1-5 минут',
            fee: 'Очень низкая (~1 TRX)'
        },
        'usdt_erc20': {
            name: 'USDT (ERC20)',
            network: 'Ethereum Network',
            icon: 'assets/images/crypto/usdt.png',
            iconFallback: 'fas fa-coins',
            confirmations: 12,
            time: '5-30 минут',
            fee: 'Высокая (~$10-50)'
        },
        'bitcoin': {
            name: 'Bitcoin (BTC)',
            network: 'Bitcoin Network',
            icon: 'assets/images/crypto/btc.png',
            iconFallback: 'fab fa-bitcoin',
            confirmations: 3,
            time: '10-60 минут',
            fee: 'Средняя (~$1-10)'
        },
        'ethereum': {
            name: 'Ethereum (ETH)',
            network: 'Ethereum Network',
            icon: 'assets/images/crypto/eth.png',
            iconFallback: 'fab fa-ethereum',
            confirmations: 12,
            time: '5-30 минут',
            fee: 'Высокая (~$5-30)'
        },
        'litecoin': {
            name: 'Litecoin (LTC)',
            network: 'Litecoin Network',
            icon: 'assets/images/crypto/ltc.png',
            iconFallback: 'fas fa-coins',
            confirmations: 6,
            time: '5-15 минут',
            fee: 'Низкая (~$0.01-0.1)'
        },
        'binance_coin': {
            name: 'BNB',
            network: 'Binance Smart Chain',
            icon: 'assets/images/crypto/bnb.png',
            iconFallback: 'fas fa-coins',
            confirmations: 15,
            time: '1-5 минут',
            fee: 'Очень низкая (~$0.1-1)'
        }
    };

    const info = cryptoInfo[paymentMethod];
    if (!info) return;

    // Обновление иконки
    const iconImg = document.getElementById('selectedCryptoIcon');
    const iconFallback = document.getElementById('selectedCryptoIconFallback');

    iconImg.src = info.icon;
    iconImg.style.display = 'inline-block';
    iconFallback.className = info.iconFallback;

    iconImg.onerror = function() {
        this.style.display = 'none';
        iconFallback.style.display = 'inline-block';
    };

    // Обновление текста
    document.getElementById('selectedCryptoName').textContent = info.name;
    document.getElementById('selectedCryptoNetwork').textContent = info.network;
    document.getElementById('minConfirmations').textContent = info.confirmations;
    document.getElementById('depositTime').textContent = info.time;
    document.getElementById('networkFee').textContent = info.fee;
}

function generateWalletAddress(paymentMethod) {
    // Демо-адреса для разных криптовалют
    const demoAddresses = {
        'usdt_trc20': 'TQn9Y2khEsLJW1ChVWFMSMeRDow5KcbLSE',
        'usdt_erc20': '******************************************',
        'bitcoin': '**********************************',
        'ethereum': '******************************************',
        'litecoin': 'LQn9Y2khEsLJW1ChVWFMSMeRDow5KcbLSE',
        'binance_coin': '******************************************'
    };

    const address = demoAddresses[paymentMethod];
    if (!address) return;

    // Обновление адреса
    const walletAddressInput = document.getElementById('walletAddress');
    walletAddressInput.value = address;

    // Генерация QR-кода
    generateQRCode(address);
}

function generateQRCode(address) {
    const qrContainer = document.getElementById('qrCodeContainer');
    qrContainer.innerHTML = '';

    if (typeof QRCode !== 'undefined') {
        QRCode.toCanvas(qrContainer, address, {
            width: 200,
            height: 200,
            colorDark: '#1a1a1a',
            colorLight: '#ffffff',
            margin: 2
        }, function (error) {
            if (error) {
                console.error('QR Code generation error:', error);
                qrContainer.innerHTML = '<i class="fas fa-exclamation-triangle"></i><span>Ошибка генерации QR-кода</span>';
            }
        });
    } else {
        qrContainer.innerHTML = '<i class="fas fa-qrcode"></i><span>QR-код недоступен</span>';
    }
}

function copyAddress() {
    const walletAddress = document.getElementById('walletAddress');
    const copyBtn = document.getElementById('copyAddressBtn');

    walletAddress.select();
    walletAddress.setSelectionRange(0, 99999);

    try {
        document.execCommand('copy');

        // Анимация успешного копирования
        const originalIcon = copyBtn.innerHTML;
        copyBtn.innerHTML = '<i class="fas fa-check"></i>';
        copyBtn.style.backgroundColor = 'var(--success-color)';

        setTimeout(() => {
            copyBtn.innerHTML = originalIcon;
            copyBtn.style.backgroundColor = '';
        }, 2000);

        showNotification('Адрес скопирован в буфер обмена!', 'success');
    } catch (err) {
        console.error('Copy failed:', err);
        showNotification('Не удалось скопировать адрес', 'error');
    }
}

// Добавление стилей
const style = document.createElement('style');
style.textContent = `
    .amount-btn {
        padding: 8px 16px;
        border: 2px solid var(--gray-300);
        background: var(--white);
        color: var(--gray-700);
        border-radius: var(--radius-md);
        cursor: pointer;
        transition: all var(--transition-fast);
        font-size: var(--text-sm);
        font-weight: 500;
    }
    
    .amount-btn:hover {
        border-color: var(--primary-green);
        color: var(--primary-green);
    }
    
    .amount-btn.active {
        background: var(--primary-green);
        border-color: var(--primary-green);
        color: var(--white);
    }
    
    .amount-buttons {
        display: flex;
        gap: var(--space-2);
        margin-top: var(--space-2);
        flex-wrap: wrap;
    }
    
    .payment-method-option {
        cursor: pointer;
        transition: transform var(--transition-fast);
    }
    
    .payment-method-option:hover {
        transform: translateY(-2px);
    }
    
    .payment-method-option.active .payment-method-card {
        border-color: var(--primary-green);
        background: rgba(46, 204, 113, 0.05);
    }
    
    .payment-method-card {
        border: 2px solid var(--gray-200);
        border-radius: var(--radius-lg);
        padding: var(--space-4);
        text-align: center;
        transition: all var(--transition-fast);
        background: var(--white);
    }
    
    .payment-method-card i {
        font-size: var(--text-2xl);
        margin-bottom: var(--space-2);
        color: var(--primary-green);
    }
    
    .payment-method-card span {
        display: block;
        font-weight: 600;
        margin-bottom: var(--space-1);
        color: var(--gray-900);
    }
    
    .payment-method-card small {
        color: var(--gray-600);
        font-size: var(--text-xs);
    }
    
    .payment-methods {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: var(--space-3);
        margin-top: var(--space-2);
    }
    
    .deposit-summary {
        background: var(--gray-100);
        border-radius: var(--radius-lg);
        padding: var(--space-4);
        margin: var(--space-6) 0;
    }
    
    .summary-row {
        display: flex;
        justify-content: space-between;
        margin-bottom: var(--space-2);
    }
    
    .summary-row.total {
        border-top: 1px solid var(--gray-300);
        padding-top: var(--space-2);
        font-weight: 600;
        font-size: var(--text-lg);
    }
    
    .deposit-container {
        display: grid;
        grid-template-columns: 2fr 1fr;
        gap: var(--space-6);
    }
    
    @media (max-width: 768px) {
        .deposit-container {
            grid-template-columns: 1fr;
        }
        
        .payment-methods {
            grid-template-columns: repeat(2, 1fr);
        }
        
        .amount-buttons {
            grid-template-columns: repeat(2, 1fr);
        }
    }
    
    @media (max-width: 480px) {
        .payment-methods {
            grid-template-columns: 1fr;
        }
    }

    /* Crypto Wallet Styles */
    .crypto-wallet-section {
        margin: var(--space-6) 0;
        animation: slideDown 0.3s ease-out;
    }

    .wallet-info-card {
        background: linear-gradient(135deg, #f8fffe 0%, #f0f9ff 100%);
        border: 2px solid var(--primary-green);
        border-radius: var(--radius-lg);
        padding: var(--space-6);
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    }

    .wallet-header {
        display: flex;
        align-items: center;
        gap: var(--space-3);
        margin-bottom: var(--space-4);
        padding-bottom: var(--space-3);
        border-bottom: 1px solid var(--gray-200);
    }

    .crypto-selected-icon {
        width: 48px;
        height: 48px;
        border-radius: 50%;
        background: var(--white);
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }

    .crypto-selected-icon img {
        width: 32px;
        height: 32px;
    }

    .crypto-selected-icon i {
        font-size: 24px;
        color: var(--primary-green);
    }

    .wallet-title h4 {
        margin: 0;
        color: var(--dark);
        font-size: var(--text-lg);
    }

    .wallet-title small {
        color: var(--gray-600);
        font-size: var(--text-sm);
    }

    .wallet-address-container {
        display: grid;
        grid-template-columns: 200px 1fr;
        gap: var(--space-6);
        margin-bottom: var(--space-4);
    }

    .qr-code-section {
        display: flex;
        flex-direction: column;
        align-items: center;
    }

    .qr-code-placeholder {
        width: 200px;
        height: 200px;
        background: var(--white);
        border: 2px dashed var(--gray-300);
        border-radius: var(--radius-md);
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        color: var(--gray-500);
        font-size: var(--text-sm);
    }

    .qr-code-placeholder i {
        font-size: 48px;
        margin-bottom: var(--space-2);
    }

    .address-section label {
        display: block;
        margin-bottom: var(--space-2);
        font-weight: 600;
        color: var(--dark);
    }

    .address-input-group {
        display: flex;
        gap: var(--space-2);
        margin-bottom: var(--space-2);
    }

    .address-input-group input {
        flex: 1;
        padding: var(--space-3);
        border: 2px solid var(--gray-300);
        border-radius: var(--radius-md);
        font-family: 'Courier New', monospace;
        font-size: var(--text-sm);
        background: var(--gray-50);
    }

    .copy-btn {
        padding: var(--space-3);
        background: var(--primary-green);
        color: var(--white);
        border: none;
        border-radius: var(--radius-md);
        cursor: pointer;
        transition: all 0.3s ease;
        min-width: 48px;
    }

    .copy-btn:hover {
        background: var(--primary-green-dark);
        transform: translateY(-2px);
    }

    .address-warning {
        display: flex;
        align-items: center;
        gap: var(--space-2);
        color: var(--warning-color);
        font-size: var(--text-sm);
        font-weight: 500;
    }

    .payment-instructions h5 {
        display: flex;
        align-items: center;
        gap: var(--space-2);
        margin-bottom: var(--space-3);
        color: var(--dark);
    }

    .payment-instructions ol {
        margin-bottom: var(--space-4);
        padding-left: var(--space-4);
    }

    .payment-instructions li {
        margin-bottom: var(--space-2);
        color: var(--gray-700);
    }

    .network-info {
        background: var(--gray-50);
        border-radius: var(--radius-md);
        padding: var(--space-4);
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: var(--space-3);
    }

    .info-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .info-label {
        color: var(--gray-600);
        font-size: var(--text-sm);
    }

    .info-value {
        color: var(--dark);
        font-weight: 600;
        font-size: var(--text-sm);
    }

    .crypto-methods .payment-method-card {
        padding: var(--space-4);
        height: auto;
    }

    .crypto-option .payment-method-card {
        display: flex;
        align-items: center;
        gap: var(--space-3);
        text-align: left;
    }

    .crypto-icon {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background: var(--white);
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        flex-shrink: 0;
    }

    .crypto-icon img {
        width: 24px;
        height: 24px;
    }

    .crypto-icon i {
        font-size: 20px;
        color: var(--primary-green);
    }

    .crypto-info {
        flex: 1;
    }

    .crypto-name {
        display: block;
        font-weight: 600;
        color: var(--dark);
        margin-bottom: 2px;
    }

    .crypto-network {
        display: block;
        color: var(--gray-600);
        font-size: var(--text-xs);
        margin-bottom: var(--space-2);
    }

    .crypto-features {
        display: flex;
        gap: var(--space-1);
        flex-wrap: wrap;
    }

    .feature-badge {
        background: var(--primary-green);
        color: var(--white);
        padding: 2px 6px;
        border-radius: 10px;
        font-size: 10px;
        font-weight: 500;
    }

    @keyframes slideDown {
        from {
            opacity: 0;
            transform: translateY(-20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    @media (max-width: 768px) {
        .wallet-address-container {
            grid-template-columns: 1fr;
            gap: var(--space-4);
        }

        .qr-code-placeholder {
            width: 150px;
            height: 150px;
        }

        .network-info {
            grid-template-columns: 1fr;
        }

        .crypto-methods {
            grid-template-columns: 1fr;
        }
    }
`;
document.head.appendChild(style);
