<?php
/**
 * AstroGenix - Обновление статистики платформы
 * Cron скрипт для ежедневного обновления общей статистики
 */

if (php_sapi_name() !== 'cli') {
    die('Этот скрипт может быть запущен только из командной строки');
}

require_once dirname(__DIR__) . '/config/config.php';

function logMessage($message) {
    $timestamp = date('Y-m-d H:i:s');
    $log_file = dirname(__DIR__) . '/logs/update-stats.log';
    
    $log_dir = dirname($log_file);
    if (!is_dir($log_dir)) {
        mkdir($log_dir, 0755, true);
    }
    
    file_put_contents($log_file, "[$timestamp] $message" . PHP_EOL, FILE_APPEND | LOCK_EX);
    echo "[$timestamp] $message" . PHP_EOL;
}

try {
    logMessage("Запуск обновления статистики платформы");
    
    $database = new Database();
    $db = $database->getConnection();
    
    // Получение общей статистики пользователей
    $users_stats_query = "SELECT 
                            COUNT(*) as total_users,
                            COUNT(CASE WHEN is_active = 1 THEN 1 END) as active_users,
                            COUNT(CASE WHEN DATE(created_at) = CURDATE() THEN 1 END) as new_users_today,
                            COUNT(CASE WHEN DATE(last_login) >= DATE_SUB(CURDATE(), INTERVAL 7 DAY) THEN 1 END) as active_week,
                            SUM(balance) as total_balance,
                            SUM(total_invested) as total_invested,
                            SUM(total_earned) as total_earned
                          FROM users";
    
    $users_stats_stmt = $db->prepare($users_stats_query);
    $users_stats_stmt->execute();
    $users_stats = $users_stats_stmt->fetch(PDO::FETCH_ASSOC);
    
    logMessage("Статистика пользователей:");
    logMessage("- Всего пользователей: {$users_stats['total_users']}");
    logMessage("- Активных пользователей: {$users_stats['active_users']}");
    logMessage("- Новых сегодня: {$users_stats['new_users_today']}");
    logMessage("- Активных за неделю: {$users_stats['active_week']}");
    logMessage("- Общий баланс: {$users_stats['total_balance']} USDT");
    logMessage("- Общие инвестиции: {$users_stats['total_invested']} USDT");
    logMessage("- Общая прибыль: {$users_stats['total_earned']} USDT");
    
    // Получение статистики транзакций
    $transactions_stats_query = "SELECT 
                                   COUNT(*) as total_transactions,
                                   COUNT(CASE WHEN DATE(created_at) = CURDATE() THEN 1 END) as transactions_today,
                                   COUNT(CASE WHEN type = 'deposit' AND status = 'completed' THEN 1 END) as completed_deposits,
                                   COUNT(CASE WHEN type = 'withdrawal' AND status = 'completed' THEN 1 END) as completed_withdrawals,
                                   SUM(CASE WHEN type = 'deposit' AND status = 'completed' THEN amount ELSE 0 END) as total_deposits,
                                   SUM(CASE WHEN type = 'withdrawal' AND status = 'completed' THEN amount ELSE 0 END) as total_withdrawals
                                 FROM transactions";
    
    $transactions_stats_stmt = $db->prepare($transactions_stats_query);
    $transactions_stats_stmt->execute();
    $transactions_stats = $transactions_stats_stmt->fetch(PDO::FETCH_ASSOC);
    
    logMessage("Статистика транзакций:");
    logMessage("- Всего транзакций: {$transactions_stats['total_transactions']}");
    logMessage("- Транзакций сегодня: {$transactions_stats['transactions_today']}");
    logMessage("- Завершенных депозитов: {$transactions_stats['completed_deposits']}");
    logMessage("- Завершенных выводов: {$transactions_stats['completed_withdrawals']}");
    logMessage("- Сумма депозитов: {$transactions_stats['total_deposits']} USDT");
    logMessage("- Сумма выводов: {$transactions_stats['total_withdrawals']} USDT");
    
    // Получение статистики инвестиций
    $investments_stats_query = "SELECT 
                                  COUNT(*) as total_investments,
                                  COUNT(CASE WHEN status = 'active' THEN 1 END) as active_investments,
                                  COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_investments,
                                  SUM(amount) as total_investment_amount,
                                  SUM(total_earned) as total_investment_profit,
                                  AVG(daily_profit) as avg_daily_profit
                                FROM user_investments";
    
    $investments_stats_stmt = $db->prepare($investments_stats_query);
    $investments_stats_stmt->execute();
    $investments_stats = $investments_stats_stmt->fetch(PDO::FETCH_ASSOC);
    
    logMessage("Статистика инвестиций:");
    logMessage("- Всего инвестиций: {$investments_stats['total_investments']}");
    logMessage("- Активных инвестиций: {$investments_stats['active_investments']}");
    logMessage("- Завершенных инвестиций: {$investments_stats['completed_investments']}");
    logMessage("- Общая сумма инвестиций: {$investments_stats['total_investment_amount']} USDT");
    logMessage("- Общая прибыль: {$investments_stats['total_investment_profit']} USDT");
    logMessage("- Средняя дневная прибыль: " . round($investments_stats['avg_daily_profit'], 2) . " USDT");
    
    // Обновление экологической статистики
    $current_energy = $users_stats['active_users'] * rand(100, 300);
    $current_co2 = round($current_energy * 0.4, 2);
    $current_trees = $users_stats['active_users'] * rand(2, 8);
    
    // Получение текущей экологической статистики
    $current_eco_query = "SELECT * FROM green_energy_stats WHERE user_id = 0";
    $current_eco_stmt = $db->prepare($current_eco_query);
    $current_eco_stmt->execute();
    $current_eco = $current_eco_stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($current_eco) {
        // Обновление существующей записи
        $update_eco_query = "UPDATE green_energy_stats 
                            SET energy_generated = :energy, 
                                co2_saved = :co2, 
                                trees_planted = :trees,
                                updated_at = NOW()
                            WHERE user_id = 0";
    } else {
        // Создание новой записи
        $update_eco_query = "INSERT INTO green_energy_stats 
                            (user_id, energy_generated, co2_saved, trees_planted, updated_at) 
                            VALUES 
                            (0, :energy, :co2, :trees, NOW())";
    }
    
    $update_eco_stmt = $db->prepare($update_eco_query);
    $update_eco_stmt->bindParam(':energy', $current_energy);
    $update_eco_stmt->bindParam(':co2', $current_co2);
    $update_eco_stmt->bindParam(':trees', $current_trees, PDO::PARAM_INT);
    $update_eco_stmt->execute();
    
    logMessage("Экологическая статистика обновлена:");
    logMessage("- Зеленая энергия: {$current_energy} кВт⋅ч");
    logMessage("- CO₂ сохранено: {$current_co2} кг");
    logMessage("- Деревьев посажено: {$current_trees}");
    
    // Сохранение ежедневной статистики
    $daily_stats = [
        'date' => date('Y-m-d'),
        'total_users' => $users_stats['total_users'],
        'active_users' => $users_stats['active_users'],
        'new_users' => $users_stats['new_users_today'],
        'total_balance' => $users_stats['total_balance'],
        'total_invested' => $users_stats['total_invested'],
        'total_earned' => $users_stats['total_earned'],
        'transactions_today' => $transactions_stats['transactions_today'],
        'active_investments' => $investments_stats['active_investments'],
        'energy_generated' => $current_energy,
        'co2_saved' => $current_co2,
        'trees_planted' => $current_trees
    ];
    
    // Сохранение в системные настройки для отображения в админке
    $save_stats_query = "INSERT INTO system_settings 
                        (setting_key, setting_value, updated_at) 
                        VALUES 
                        ('daily_stats_' . CURDATE(), :stats, NOW()) 
                        ON DUPLICATE KEY UPDATE 
                        setting_value = :stats, updated_at = NOW()";
    
    $save_stats_stmt = $db->prepare($save_stats_query);
    $save_stats_stmt->bindParam(':stats', json_encode($daily_stats, JSON_UNESCAPED_UNICODE));
    $save_stats_stmt->execute();
    
    // Очистка старых ежедневных статистик (оставляем только последние 30 дней)
    $cleanup_stats_query = "DELETE FROM system_settings 
                           WHERE setting_key LIKE 'daily_stats_%' 
                           AND setting_key < 'daily_stats_' . DATE_SUB(CURDATE(), INTERVAL 30 DAY)";
    
    $cleanup_stats_stmt = $db->prepare($cleanup_stats_query);
    $cleanup_stats_stmt->execute();
    $cleaned = $cleanup_stats_stmt->rowCount();
    
    if ($cleaned > 0) {
        logMessage("Очищено старых записей статистики: $cleaned");
    }
    
    // Генерация случайных событий для демонстрации
    $events = [
        "Новый пользователь из {$users_stats['new_users_today']} стран зарегистрировался сегодня",
        "Активность платформы выросла на " . rand(5, 15) . "% за последнюю неделю",
        "Экологический эффект: сохранено " . round($current_co2 / 1000, 1) . " тонн CO₂",
        "Средняя прибыльность инвестиций составляет " . rand(15, 25) . "% годовых"
    ];
    
    $random_event = $events[array_rand($events)];
    logMessage("Событие дня: $random_event");
    
    // Сохранение события дня
    $save_event_query = "INSERT INTO system_settings 
                        (setting_key, setting_value, updated_at) 
                        VALUES 
                        ('daily_event_' . CURDATE(), :event, NOW()) 
                        ON DUPLICATE KEY UPDATE 
                        setting_value = :event, updated_at = NOW()";
    
    $save_event_stmt = $db->prepare($save_event_query);
    $save_event_stmt->bindParam(':event', $random_event);
    $save_event_stmt->execute();
    
    logMessage("Обновление статистики платформы завершено успешно");
    
} catch (Exception $e) {
    logMessage("ОШИБКА: " . $e->getMessage());
    logMessage("Трассировка: " . $e->getTraceAsString());
    exit(1);
}
?>
