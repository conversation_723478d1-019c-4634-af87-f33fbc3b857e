<?php
/**
 * AstroGenix - Управление медиафайлами CMS
 * Система управления контентом
 */

require_once '../config/config.php';
require_once '../classes/CMS.php';

// Проверка авторизации и прав администратора
require_admin();

// Инициализация переменных
$error_message = '';
$success_message = '';
$media_files = [];

// Параметры фильтрации и пагинации
$type_filter = sanitize_input($_GET['type'] ?? '');
$folder_filter = intval($_GET['folder'] ?? 0);
$search = sanitize_input($_GET['search'] ?? '');
$page = max(1, intval($_GET['page'] ?? 1));
$per_page = 24;
$offset = ($page - 1) * $per_page;

// Создание директории для загрузок если не существует
$upload_dir = '../uploads/';
$upload_subdirs = ['images', 'documents', 'videos', 'other'];

foreach ($upload_subdirs as $subdir) {
    $full_path = $upload_dir . $subdir;
    if (!is_dir($full_path)) {
        mkdir($full_path, 0755, true);
    }
}

try {
    $database = new Database();
    $db = $database->getConnection();

    // Обработка загрузки файлов
    if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_FILES['media_files'])) {
        if (!verify_csrf_token($_POST['csrf_token'] ?? '')) {
            $error_message = 'Ошибка безопасности. Попробуйте еще раз.';
        } else {
            $uploaded_count = 0;
            $errors = [];

            // Настройки загрузки
            $max_file_size = 10 * 1024 * 1024; // 10MB
            $allowed_types = [
                'image' => ['jpg', 'jpeg', 'png', 'gif', 'webp', 'svg'],
                'document' => ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'txt'],
                'video' => ['mp4', 'avi', 'mov', 'wmv', 'flv'],
                'audio' => ['mp3', 'wav', 'ogg', 'aac']
            ];

            foreach ($_FILES['media_files']['tmp_name'] as $key => $tmp_name) {
                if ($_FILES['media_files']['error'][$key] === UPLOAD_ERR_OK) {
                    $original_name = $_FILES['media_files']['name'][$key];
                    $file_size = $_FILES['media_files']['size'][$key];
                    $file_ext = strtolower(pathinfo($original_name, PATHINFO_EXTENSION));

                    // Проверка размера файла
                    if ($file_size > $max_file_size) {
                        $errors[] = "Файл {$original_name} слишком большой (максимум 10MB)";
                        continue;
                    }

                    // Определение типа файла
                    $file_type = 'other';
                    foreach ($allowed_types as $type => $extensions) {
                        if (in_array($file_ext, $extensions)) {
                            $file_type = $type;
                            break;
                        }
                    }

                    // Проверка разрешенных типов
                    $all_allowed = array_merge(...array_values($allowed_types));
                    if (!in_array($file_ext, $all_allowed)) {
                        $errors[] = "Тип файла {$file_ext} не разрешен";
                        continue;
                    }

                    // Генерация уникального имени файла
                    $filename = uniqid() . '_' . time() . '.' . $file_ext;
                    $subdir = $file_type === 'image' ? 'images' : ($file_type === 'document' ? 'documents' : ($file_type === 'video' ? 'videos' : 'other'));
                    $file_path = "/uploads/{$subdir}/{$filename}";
                    $full_file_path = $upload_dir . $subdir . '/' . $filename;

                    // Перемещение файла
                    if (move_uploaded_file($tmp_name, $full_file_path)) {
                        // Получение размеров изображения
                        $width = null;
                        $height = null;
                        if ($file_type === 'image' && function_exists('getimagesize')) {
                            $image_info = getimagesize($full_file_path);
                            if ($image_info) {
                                $width = $image_info[0];
                                $height = $image_info[1];
                            }
                        }

                        // Сохранение в базу данных
                        $insert_query = "INSERT INTO cms_media
                                        (filename, original_name, file_path, file_size, mime_type, file_type, width, height, uploaded_by, folder_id)
                                        VALUES
                                        (:filename, :original_name, :file_path, :file_size, :mime_type, :file_type, :width, :height, :uploaded_by, :folder_id)";

                        $insert_stmt = $db->prepare($insert_query);
                        $insert_stmt->bindParam(':filename', $filename);
                        $insert_stmt->bindParam(':original_name', $original_name);
                        $insert_stmt->bindParam(':file_path', $file_path);
                        $insert_stmt->bindParam(':file_size', $file_size, PDO::PARAM_INT);
                        $insert_stmt->bindParam(':mime_type', $_FILES['media_files']['type'][$key]);
                        $insert_stmt->bindParam(':file_type', $file_type);
                        $insert_stmt->bindParam(':width', $width, PDO::PARAM_INT);
                        $insert_stmt->bindParam(':height', $height, PDO::PARAM_INT);
                        $insert_stmt->bindParam(':uploaded_by', $_SESSION['user_id'], PDO::PARAM_INT);
                        $insert_stmt->bindParam(':folder_id', $folder_filter ?: null, PDO::PARAM_INT);

                        if ($insert_stmt->execute()) {
                            $uploaded_count++;
                        } else {
                            $errors[] = "Ошибка сохранения файла {$original_name} в базу данных";
                            unlink($full_file_path); // Удаляем файл при ошибке
                        }
                    } else {
                        $errors[] = "Ошибка загрузки файла {$original_name}";
                    }
                }
            }

            if ($uploaded_count > 0) {
                $success_message = "Успешно загружено файлов: {$uploaded_count}";
            }

            if (!empty($errors)) {
                $error_message = implode('<br>', $errors);
            }
        }
    }

    // Обработка удаления файлов
    if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'delete_media') {
        if (!verify_csrf_token($_POST['csrf_token'] ?? '')) {
            $error_message = 'Ошибка безопасности. Попробуйте еще раз.';
        } else {
            $media_id = intval($_POST['media_id'] ?? 0);

            if ($media_id > 0) {
                // Получение информации о файле
                $file_query = "SELECT file_path FROM cms_media WHERE id = :id";
                $file_stmt = $db->prepare($file_query);
                $file_stmt->bindParam(':id', $media_id, PDO::PARAM_INT);
                $file_stmt->execute();
                $file_info = $file_stmt->fetch(PDO::FETCH_ASSOC);

                if ($file_info) {
                    // Удаление файла с диска
                    $file_path = '..' . $file_info['file_path'];
                    if (file_exists($file_path)) {
                        unlink($file_path);
                    }

                    // Удаление записи из базы данных
                    $delete_query = "DELETE FROM cms_media WHERE id = :id";
                    $delete_stmt = $db->prepare($delete_query);
                    $delete_stmt->bindParam(':id', $media_id, PDO::PARAM_INT);

                    if ($delete_stmt->execute()) {
                        $success_message = 'Файл успешно удален.';
                    } else {
                        $error_message = 'Ошибка при удалении файла.';
                    }
                } else {
                    $error_message = 'Файл не найден.';
                }
            }
        }
    }

    // Построение запроса для получения медиафайлов
    $where_conditions = [];
    $params = [];

    if (!empty($type_filter)) {
        $where_conditions[] = "m.file_type = :type";
        $params[':type'] = $type_filter;
    }

    if ($folder_filter > 0) {
        $where_conditions[] = "m.folder_id = :folder_id";
        $params[':folder_id'] = $folder_filter;
    }

    if (!empty($search)) {
        $where_conditions[] = "(m.original_name LIKE :search OR m.filename LIKE :search OR m.alt_text LIKE :search)";
        $params[':search'] = '%' . $search . '%';
    }

    $where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

    // Получение медиафайлов
    $media_query = "SELECT m.*, u.username as uploaded_by_name, f.name as folder_name
                    FROM cms_media m
                    LEFT JOIN users u ON m.uploaded_by = u.id
                    LEFT JOIN cms_media_folders f ON m.folder_id = f.id
                    $where_clause
                    ORDER BY m.created_at DESC
                    LIMIT :limit OFFSET :offset";

    $media_stmt = $db->prepare($media_query);

    foreach ($params as $key => $value) {
        $media_stmt->bindValue($key, $value);
    }

    $media_stmt->bindValue(':limit', $per_page, PDO::PARAM_INT);
    $media_stmt->bindValue(':offset', $offset, PDO::PARAM_INT);
    $media_stmt->execute();

    $media_files = $media_stmt->fetchAll(PDO::FETCH_ASSOC);

    // Подсчет общего количества файлов
    $count_query = "SELECT COUNT(*) as total FROM cms_media m $where_clause";
    $count_stmt = $db->prepare($count_query);

    foreach ($params as $key => $value) {
        $count_stmt->bindValue($key, $value);
    }

    $count_stmt->execute();
    $total_files = $count_stmt->fetch(PDO::FETCH_ASSOC)['total'];
    $total_pages_count = ceil($total_files / $per_page);

    // Получение папок
    $folders_query = "SELECT * FROM cms_media_folders ORDER BY name";
    $folders_stmt = $db->prepare($folders_query);
    $folders_stmt->execute();
    $folders = $folders_stmt->fetchAll(PDO::FETCH_ASSOC);

} catch (Exception $e) {
    error_log("CMS media error: " . $e->getMessage());
    $error_message = "Ошибка загрузки медиафайлов. Попробуйте обновить страницу.";
}

$page_title = 'Управление медиафайлами - CMS';
?>
<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?></title>

    <!-- CSS -->
    <link rel="stylesheet" href="../assets/css/style.css">
    <link rel="stylesheet" href="../assets/css/dashboard.css">
    <link rel="stylesheet" href="../assets/css/admin.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body class="dashboard-page admin-page">
    <!-- Admin Sidebar -->
    <?php include 'includes/admin-sidebar.php'; ?>

    <!-- Main Content -->
    <main class="main-content">
        <!-- Header -->
        <header class="dashboard-header">
            <div class="header-left">
                <button class="mobile-menu-toggle" id="mobileMenuToggle">
                    <i class="fas fa-bars"></i>
                </button>
                <h1>Управление медиафайлами</h1>
            </div>
            <div class="header-right">
                <button class="btn btn-primary" onclick="openUploadModal()">
                    <i class="fas fa-upload"></i>
                    Загрузить файлы
                </button>
            </div>
        </header>

        <!-- Dashboard Content -->
        <div class="dashboard-content">
            <?php if (!empty($error_message)): ?>
                <div class="alert alert-error">
                    <i class="fas fa-exclamation-triangle"></i>
                    <?php echo $error_message; ?>
                </div>
            <?php endif; ?>

            <?php if (!empty($success_message)): ?>
                <div class="alert alert-success">
                    <i class="fas fa-check-circle"></i>
                    <?php echo htmlspecialchars($success_message); ?>
                </div>
            <?php endif; ?>

            <!-- Filters and Search -->
            <div class="media-controls">
                <div class="admin-filters">
                    <form method="GET" class="filters-form">
                        <div class="filter-group">
                            <input type="text" name="search" value="<?php echo htmlspecialchars($search); ?>"
                                   placeholder="Поиск файлов..." class="filter-input">
                        </div>
                        <div class="filter-group">
                            <select name="type" class="filter-input">
                                <option value="">Все типы</option>
                                <option value="image" <?php echo $type_filter === 'image' ? 'selected' : ''; ?>>Изображения</option>
                                <option value="document" <?php echo $type_filter === 'document' ? 'selected' : ''; ?>>Документы</option>
                                <option value="video" <?php echo $type_filter === 'video' ? 'selected' : ''; ?>>Видео</option>
                                <option value="audio" <?php echo $type_filter === 'audio' ? 'selected' : ''; ?>>Аудио</option>
                                <option value="other" <?php echo $type_filter === 'other' ? 'selected' : ''; ?>>Другое</option>
                            </select>
                        </div>
                        <div class="filter-group">
                            <select name="folder" class="filter-input">
                                <option value="">Все папки</option>
                                <?php foreach ($folders as $folder): ?>
                                    <option value="<?php echo $folder['id']; ?>" <?php echo $folder_filter == $folder['id'] ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($folder['name']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search"></i>
                            Найти
                        </button>
                        <a href="cms-media.php" class="btn btn-outline">
                            <i class="fas fa-times"></i>
                            Сбросить
                        </a>
                    </form>
                </div>

                <!-- View Toggle -->
                <div class="view-toggle">
                    <button class="view-btn active" data-view="grid" title="Сетка">
                        <i class="fas fa-th"></i>
                    </button>
                    <button class="view-btn" data-view="list" title="Список">
                        <i class="fas fa-list"></i>
                    </button>
                </div>
            </div>

            <!-- Media Grid -->
            <div class="media-container">
                <?php if (empty($media_files)): ?>
                    <div class="empty-state">
                        <i class="fas fa-images"></i>
                        <p>Медиафайлы не найдены</p>
                        <button class="btn btn-primary" onclick="openUploadModal()">
                            <i class="fas fa-upload"></i>
                            Загрузить первый файл
                        </button>
                    </div>
                <?php else: ?>
                    <div class="media-grid" id="mediaGrid">
                        <?php foreach ($media_files as $file): ?>
                            <div class="media-item" data-id="<?php echo $file['id']; ?>" data-type="<?php echo $file['file_type']; ?>">
                                <div class="media-preview">
                                    <?php if ($file['file_type'] === 'image'): ?>
                                        <img src="<?php echo SITE_URL . $file['file_path']; ?>"
                                             alt="<?php echo htmlspecialchars($file['alt_text'] ?: $file['original_name']); ?>"
                                             loading="lazy">
                                    <?php else: ?>
                                        <div class="file-icon">
                                            <i class="fas fa-<?php
                                                echo $file['file_type'] === 'document' ? 'file-alt' :
                                                    ($file['file_type'] === 'video' ? 'video' :
                                                    ($file['file_type'] === 'audio' ? 'music' : 'file'));
                                            ?>"></i>
                                        </div>
                                    <?php endif; ?>

                                    <div class="media-overlay">
                                        <div class="media-actions">
                                            <button class="action-btn" onclick="viewMedia(<?php echo $file['id']; ?>)" title="Просмотр">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <button class="action-btn" onclick="editMedia(<?php echo $file['id']; ?>)" title="Редактировать">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="action-btn" onclick="copyUrl('<?php echo SITE_URL . $file['file_path']; ?>')" title="Копировать URL">
                                                <i class="fas fa-link"></i>
                                            </button>
                                            <button class="action-btn delete-btn" onclick="deleteMedia(<?php echo $file['id']; ?>)" title="Удалить">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>

                                <div class="media-info">
                                    <div class="media-title" title="<?php echo htmlspecialchars($file['original_name']); ?>">
                                        <?php echo htmlspecialchars(strlen($file['original_name']) > 20 ? substr($file['original_name'], 0, 20) . '...' : $file['original_name']); ?>
                                    </div>
                                    <div class="media-meta">
                                        <span class="file-size"><?php echo formatFileSize($file['file_size']); ?></span>
                                        <?php if ($file['width'] && $file['height']): ?>
                                            <span class="file-dimensions"><?php echo $file['width']; ?>×<?php echo $file['height']; ?></span>
                                        <?php endif; ?>
                                    </div>
                                    <div class="media-date">
                                        <?php echo date('d.m.Y', strtotime($file['created_at'])); ?>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>

                    <!-- Pagination -->
                    <?php if ($total_pages_count > 1): ?>
                        <div class="pagination">
                            <?php if ($page > 1): ?>
                                <a href="?page=<?php echo $page - 1; ?>&<?php echo http_build_query(['type' => $type_filter, 'folder' => $folder_filter, 'search' => $search]); ?>"
                                   class="pagination-btn">
                                    <i class="fas fa-chevron-left"></i>
                                </a>
                            <?php endif; ?>

                            <?php for ($i = max(1, $page - 2); $i <= min($total_pages_count, $page + 2); $i++): ?>
                                <a href="?page=<?php echo $i; ?>&<?php echo http_build_query(['type' => $type_filter, 'folder' => $folder_filter, 'search' => $search]); ?>"
                                   class="pagination-btn <?php echo $i === $page ? 'active' : ''; ?>">
                                    <?php echo $i; ?>
                                </a>
                            <?php endfor; ?>

                            <?php if ($page < $total_pages_count): ?>
                                <a href="?page=<?php echo $page + 1; ?>&<?php echo http_build_query(['type' => $type_filter, 'folder' => $folder_filter, 'search' => $search]); ?>"
                                   class="pagination-btn">
                                    <i class="fas fa-chevron-right"></i>
                                </a>
                            <?php endif; ?>
                        </div>
                    <?php endif; ?>
                <?php endif; ?>
            </div>
        </div>
    </main>

    <!-- Upload Modal -->
    <div id="uploadModal" class="modal">
        <div class="modal-content modal-large">
            <div class="modal-header">
                <h3>Загрузка файлов</h3>
                <button class="modal-close" onclick="closeUploadModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <form method="POST" enctype="multipart/form-data" id="uploadForm">
                    <input type="hidden" name="csrf_token" value="<?php echo generate_csrf_token(); ?>">

                    <div class="upload-area" id="uploadArea">
                        <div class="upload-content">
                            <i class="fas fa-cloud-upload-alt"></i>
                            <h4>Перетащите файлы сюда или нажмите для выбора</h4>
                            <p>Поддерживаются: JPG, PNG, GIF, PDF, DOC, MP4 и другие (максимум 10MB)</p>
                            <input type="file" name="media_files[]" id="fileInput" multiple accept="*/*" style="display: none;">
                            <button type="button" class="btn btn-primary" onclick="document.getElementById('fileInput').click()">
                                Выбрать файлы
                            </button>
                        </div>
                    </div>

                    <div class="upload-preview" id="uploadPreview" style="display: none;">
                        <h4>Выбранные файлы:</h4>
                        <div class="preview-list" id="previewList"></div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-outline" onclick="closeUploadModal()">Отмена</button>
                <button type="submit" form="uploadForm" class="btn btn-primary" id="uploadBtn" disabled>
                    <i class="fas fa-upload"></i>
                    Загрузить файлы
                </button>
            </div>
        </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div id="deleteModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Подтверждение удаления</h3>
                <button class="modal-close" onclick="closeDeleteModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <p>Вы уверены, что хотите удалить этот файл? Это действие нельзя отменить.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-outline" onclick="closeDeleteModal()">Отмена</button>
                <form method="POST" style="display: inline;">
                    <input type="hidden" name="csrf_token" value="<?php echo generate_csrf_token(); ?>">
                    <input type="hidden" name="action" value="delete_media">
                    <input type="hidden" name="media_id" id="deleteMediaId">
                    <button type="submit" class="btn btn-danger">Удалить</button>
                </form>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="../assets/js/admin.js"></script>
    <script>
        // Функция для форматирования размера файла
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        // Переключение вида
        document.querySelectorAll('.view-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                document.querySelectorAll('.view-btn').forEach(b => b.classList.remove('active'));
                this.classList.add('active');

                const view = this.dataset.view;
                const mediaGrid = document.getElementById('mediaGrid');

                if (view === 'list') {
                    mediaGrid.classList.add('media-list');
                } else {
                    mediaGrid.classList.remove('media-list');
                }
            });
        });

        // Модальное окно загрузки
        function openUploadModal() {
            document.getElementById('uploadModal').style.display = 'block';
        }

        function closeUploadModal() {
            document.getElementById('uploadModal').style.display = 'none';
            document.getElementById('uploadForm').reset();
            document.getElementById('uploadPreview').style.display = 'none';
            document.getElementById('uploadBtn').disabled = true;
        }

        // Обработка выбора файлов
        document.getElementById('fileInput').addEventListener('change', function() {
            const files = this.files;
            const previewList = document.getElementById('previewList');
            const uploadPreview = document.getElementById('uploadPreview');
            const uploadBtn = document.getElementById('uploadBtn');

            if (files.length > 0) {
                previewList.innerHTML = '';
                uploadPreview.style.display = 'block';
                uploadBtn.disabled = false;

                Array.from(files).forEach(file => {
                    const fileItem = document.createElement('div');
                    fileItem.className = 'preview-item';
                    fileItem.innerHTML = `
                        <i class="fas fa-file"></i>
                        <span>${file.name}</span>
                        <small>${formatFileSize(file.size)}</small>
                    `;
                    previewList.appendChild(fileItem);
                });
            } else {
                uploadPreview.style.display = 'none';
                uploadBtn.disabled = true;
            }
        });

        // Drag & Drop
        const uploadArea = document.getElementById('uploadArea');
        const fileInput = document.getElementById('fileInput');

        uploadArea.addEventListener('dragover', function(e) {
            e.preventDefault();
            this.classList.add('drag-over');
        });

        uploadArea.addEventListener('dragleave', function(e) {
            e.preventDefault();
            this.classList.remove('drag-over');
        });

        uploadArea.addEventListener('drop', function(e) {
            e.preventDefault();
            this.classList.remove('drag-over');

            const files = e.dataTransfer.files;
            fileInput.files = files;
            fileInput.dispatchEvent(new Event('change'));
        });

        // Функции для работы с медиафайлами
        function viewMedia(id) {
            // Открытие файла в новом окне
            const mediaItem = document.querySelector(`[data-id="${id}"]`);
            const img = mediaItem.querySelector('img');
            if (img) {
                window.open(img.src, '_blank');
            }
        }

        function editMedia(id) {
            // Редактирование медиафайла (пока заглушка)
            alert('Функция редактирования будет добавлена в следующих версиях');
        }

        function copyUrl(url) {
            navigator.clipboard.writeText(url).then(function() {
                // Показываем уведомление
                const notification = document.createElement('div');
                notification.className = 'notification success';
                notification.innerHTML = '<i class="fas fa-check"></i> URL скопирован в буфер обмена';
                document.body.appendChild(notification);

                setTimeout(() => {
                    notification.remove();
                }, 3000);
            });
        }

        function deleteMedia(id) {
            document.getElementById('deleteMediaId').value = id;
            document.getElementById('deleteModal').style.display = 'block';
        }

        function closeDeleteModal() {
            document.getElementById('deleteModal').style.display = 'none';
        }

        // Закрытие модальных окон по клику вне их
        window.addEventListener('click', function(e) {
            const uploadModal = document.getElementById('uploadModal');
            const deleteModal = document.getElementById('deleteModal');

            if (e.target === uploadModal) {
                closeUploadModal();
            }
            if (e.target === deleteModal) {
                closeDeleteModal();
            }
        });
    </script>
</body>
</html>

<?php
// Функция для форматирования размера файла
function formatFileSize($bytes) {
    if ($bytes == 0) return '0 Bytes';
    $k = 1024;
    $sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    $i = floor(log($bytes) / log($k));
    return round($bytes / pow($k, $i), 2) . ' ' . $sizes[$i];
}
?>