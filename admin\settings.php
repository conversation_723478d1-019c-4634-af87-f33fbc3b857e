<?php
/**
 * AstroGenix - Настройки системы
 * Эко-майнинговая инвестиционная платформа
 */

require_once '../config/config.php';

// Проверка авторизации и прав администратора
require_admin();

// Инициализация переменных
$settings = [];
$error_message = '';
$success_message = '';

try {
    $database = new Database();
    $db = $database->getConnection();
    
    // Обработка сохранения настроек
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        if (!verify_csrf_token($_POST['csrf_token'] ?? '')) {
            $error_message = 'Ошибка безопасности. Попробуйте еще раз.';
        } else {
            $updated_settings = [];
            
            // Получение всех настроек из формы
            foreach ($_POST as $key => $value) {
                if ($key !== 'csrf_token' && strpos($key, 'setting_') === 0) {
                    $setting_key = substr($key, 8); // Убираем префикс 'setting_'
                    $updated_settings[$setting_key] = sanitize_input($value);
                }
            }
            
            // Обновление настроек в базе данных
            $update_query = "UPDATE system_settings SET setting_value = :value WHERE setting_key = :key";
            $update_stmt = $db->prepare($update_query);
            
            $db->beginTransaction();
            
            try {
                foreach ($updated_settings as $key => $value) {
                    $update_stmt->bindParam(':key', $key);
                    $update_stmt->bindParam(':value', $value);
                    $update_stmt->execute();
                }
                
                $db->commit();
                $success_message = 'Настройки успешно сохранены.';
                
            } catch (Exception $e) {
                $db->rollback();
                $error_message = 'Ошибка при сохранении настроек: ' . $e->getMessage();
            }
        }
    }
    
    // Получение всех настроек
    $settings_query = "SELECT * FROM system_settings ORDER BY setting_key";
    $settings_stmt = $db->prepare($settings_query);
    $settings_stmt->execute();
    $settings_result = $settings_stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Преобразование в ассоциативный массив
    foreach ($settings_result as $setting) {
        $settings[$setting['setting_key']] = $setting['setting_value'];
    }
    
} catch (Exception $e) {
    error_log("Admin settings error: " . $e->getMessage());
    $error_message = "Ошибка загрузки настроек. Попробуйте обновить страницу.";
}

$page_title = 'Настройки системы - Админ-панель';
?>
<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?></title>
    
    <!-- CSS -->
    <link rel="stylesheet" href="../assets/css/style.css">
    <link rel="stylesheet" href="../assets/css/dashboard.css">
    <link rel="stylesheet" href="../assets/css/admin.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body class="dashboard-page admin-page">
    <!-- Admin Sidebar -->
    <?php include 'includes/admin-sidebar.php'; ?>

    <!-- Main Content -->
    <main class="main-content">
        <!-- Header -->
        <header class="dashboard-header">
            <div class="header-left">
                <button class="mobile-menu-toggle" id="mobileMenuToggle">
                    <i class="fas fa-bars"></i>
                </button>
                <h1>Настройки системы</h1>
            </div>
        </header>

        <!-- Dashboard Content -->
        <div class="dashboard-content">
            <?php if (!empty($error_message)): ?>
                <div class="alert alert-error">
                    <i class="fas fa-exclamation-triangle"></i>
                    <?php echo htmlspecialchars($error_message); ?>
                </div>
            <?php endif; ?>

            <?php if (!empty($success_message)): ?>
                <div class="alert alert-success">
                    <i class="fas fa-check-circle"></i>
                    <?php echo htmlspecialchars($success_message); ?>
                </div>
            <?php endif; ?>

            <form method="POST" class="settings-form">
                <input type="hidden" name="csrf_token" value="<?php echo generate_csrf_token(); ?>">
                
                <!-- Основные настройки -->
                <div class="settings-section">
                    <div class="section-header">
                        <h3><i class="fas fa-cog"></i> Основные настройки</h3>
                    </div>
                    <div class="settings-grid">
                        <div class="setting-item">
                            <label for="site_name">Название сайта</label>
                            <input type="text" id="site_name" name="setting_site_name" 
                                   value="<?php echo htmlspecialchars($settings['site_name'] ?? 'AstroGenix'); ?>" required>
                        </div>
                        
                        <div class="setting-item">
                            <label for="site_description">Описание сайта</label>
                            <textarea id="site_description" name="setting_site_description" rows="3"><?php echo htmlspecialchars($settings['site_description'] ?? ''); ?></textarea>
                        </div>
                        
                        <div class="setting-item">
                            <label for="maintenance_mode">Режим обслуживания</label>
                            <select id="maintenance_mode" name="setting_maintenance_mode">
                                <option value="0" <?php echo ($settings['maintenance_mode'] ?? '0') === '0' ? 'selected' : ''; ?>>Отключен</option>
                                <option value="1" <?php echo ($settings['maintenance_mode'] ?? '0') === '1' ? 'selected' : ''; ?>>Включен</option>
                            </select>
                        </div>
                        
                        <div class="setting-item">
                            <label for="email_verification_required">Требовать верификацию email</label>
                            <select id="email_verification_required" name="setting_email_verification_required">
                                <option value="0" <?php echo ($settings['email_verification_required'] ?? '1') === '0' ? 'selected' : ''; ?>>Нет</option>
                                <option value="1" <?php echo ($settings['email_verification_required'] ?? '1') === '1' ? 'selected' : ''; ?>>Да</option>
                            </select>
                        </div>
                    </div>
                </div>

                <!-- Финансовые настройки -->
                <div class="settings-section">
                    <div class="section-header">
                        <h3><i class="fas fa-dollar-sign"></i> Финансовые настройки</h3>
                    </div>
                    <div class="settings-grid">
                        <div class="setting-item">
                            <label for="min_deposit_amount">Минимальная сумма пополнения (USDT)</label>
                            <input type="number" id="min_deposit_amount" name="setting_min_deposit_amount" 
                                   value="<?php echo htmlspecialchars($settings['min_deposit_amount'] ?? '10'); ?>" 
                                   step="0.01" min="0" required>
                        </div>
                        
                        <div class="setting-item">
                            <label for="max_deposit_amount">Максимальная сумма пополнения (USDT)</label>
                            <input type="number" id="max_deposit_amount" name="setting_max_deposit_amount" 
                                   value="<?php echo htmlspecialchars($settings['max_deposit_amount'] ?? '10000'); ?>" 
                                   step="0.01" min="0" required>
                        </div>
                        
                        <div class="setting-item">
                            <label for="min_withdrawal_amount">Минимальная сумма вывода (USDT)</label>
                            <input type="number" id="min_withdrawal_amount" name="setting_min_withdrawal_amount" 
                                   value="<?php echo htmlspecialchars($settings['min_withdrawal_amount'] ?? '10'); ?>" 
                                   step="0.01" min="0" required>
                        </div>
                        
                        <div class="setting-item">
                            <label for="max_withdrawal_amount">Максимальная сумма вывода (USDT)</label>
                            <input type="number" id="max_withdrawal_amount" name="setting_max_withdrawal_amount" 
                                   value="<?php echo htmlspecialchars($settings['max_withdrawal_amount'] ?? '5000'); ?>" 
                                   step="0.01" min="0" required>
                        </div>
                        
                        <div class="setting-item">
                            <label for="withdrawal_fee_percent">Комиссия за вывод (%)</label>
                            <input type="number" id="withdrawal_fee_percent" name="setting_withdrawal_fee_percent" 
                                   value="<?php echo htmlspecialchars($settings['withdrawal_fee_percent'] ?? '0'); ?>" 
                                   step="0.01" min="0" max="100">
                        </div>
                        
                        <div class="setting-item">
                            <label for="default_currency">Основная валюта</label>
                            <select id="default_currency" name="setting_default_currency">
                                <option value="USDT" <?php echo ($settings['default_currency'] ?? 'USDT') === 'USDT' ? 'selected' : ''; ?>>USDT</option>
                                <option value="USD" <?php echo ($settings['default_currency'] ?? 'USDT') === 'USD' ? 'selected' : ''; ?>>USD</option>
                                <option value="EUR" <?php echo ($settings['default_currency'] ?? 'USDT') === 'EUR' ? 'selected' : ''; ?>>EUR</option>
                            </select>
                        </div>
                    </div>
                </div>

                <!-- Реферальная программа -->
                <div class="settings-section">
                    <div class="section-header">
                        <h3><i class="fas fa-users"></i> Реферальная программа</h3>
                    </div>
                    <div class="settings-grid">
                        <div class="setting-item">
                            <label for="referral_level_1_percent">Комиссия 1-го уровня (%)</label>
                            <input type="number" id="referral_level_1_percent" name="setting_referral_level_1_percent" 
                                   value="<?php echo htmlspecialchars($settings['referral_level_1_percent'] ?? '5'); ?>" 
                                   step="0.01" min="0" max="100" required>
                        </div>
                        
                        <div class="setting-item">
                            <label for="referral_level_2_percent">Комиссия 2-го уровня (%)</label>
                            <input type="number" id="referral_level_2_percent" name="setting_referral_level_2_percent" 
                                   value="<?php echo htmlspecialchars($settings['referral_level_2_percent'] ?? '3'); ?>" 
                                   step="0.01" min="0" max="100" required>
                        </div>
                        
                        <div class="setting-item">
                            <label for="referral_level_3_percent">Комиссия 3-го уровня (%)</label>
                            <input type="number" id="referral_level_3_percent" name="setting_referral_level_3_percent" 
                                   value="<?php echo htmlspecialchars($settings['referral_level_3_percent'] ?? '2'); ?>" 
                                   step="0.01" min="0" max="100" required>
                        </div>
                        
                        <div class="setting-item">
                            <label for="referral_bonus_enabled">Реферальные бонусы</label>
                            <select id="referral_bonus_enabled" name="setting_referral_bonus_enabled">
                                <option value="0" <?php echo ($settings['referral_bonus_enabled'] ?? '1') === '0' ? 'selected' : ''; ?>>Отключены</option>
                                <option value="1" <?php echo ($settings['referral_bonus_enabled'] ?? '1') === '1' ? 'selected' : ''; ?>>Включены</option>
                            </select>
                        </div>
                    </div>
                </div>

                <!-- Экологические настройки -->
                <div class="settings-section">
                    <div class="section-header">
                        <h3><i class="fas fa-leaf"></i> Экологические настройки</h3>
                    </div>
                    <div class="settings-grid">
                        <div class="setting-item">
                            <label for="total_green_energy">Общая зеленая энергия (кВт⋅ч)</label>
                            <input type="number" id="total_green_energy" name="setting_total_green_energy" 
                                   value="<?php echo htmlspecialchars($settings['total_green_energy'] ?? '0'); ?>" 
                                   step="0.01" min="0">
                        </div>
                        
                        <div class="setting-item">
                            <label for="energy_per_investment">Энергия за 1 USDT инвестиций (кВт⋅ч)</label>
                            <input type="number" id="energy_per_investment" name="setting_energy_per_investment" 
                                   value="<?php echo htmlspecialchars($settings['energy_per_investment'] ?? '0.1'); ?>" 
                                   step="0.001" min="0">
                        </div>
                        
                        <div class="setting-item">
                            <label for="eco_counter_enabled">Счетчик экологии</label>
                            <select id="eco_counter_enabled" name="setting_eco_counter_enabled">
                                <option value="0" <?php echo ($settings['eco_counter_enabled'] ?? '1') === '0' ? 'selected' : ''; ?>>Отключен</option>
                                <option value="1" <?php echo ($settings['eco_counter_enabled'] ?? '1') === '1' ? 'selected' : ''; ?>>Включен</option>
                            </select>
                        </div>
                    </div>
                </div>

                <!-- Уведомления -->
                <div class="settings-section">
                    <div class="section-header">
                        <h3><i class="fas fa-bell"></i> Уведомления</h3>
                    </div>
                    <div class="settings-grid">
                        <div class="setting-item">
                            <label for="email_notifications">Email уведомления</label>
                            <select id="email_notifications" name="setting_email_notifications">
                                <option value="0" <?php echo ($settings['email_notifications'] ?? '1') === '0' ? 'selected' : ''; ?>>Отключены</option>
                                <option value="1" <?php echo ($settings['email_notifications'] ?? '1') === '1' ? 'selected' : ''; ?>>Включены</option>
                            </select>
                        </div>
                        
                        <div class="setting-item">
                            <label for="admin_email">Email администратора</label>
                            <input type="email" id="admin_email" name="setting_admin_email" 
                                   value="<?php echo htmlspecialchars($settings['admin_email'] ?? '<EMAIL>'); ?>">
                        </div>
                        
                        <div class="setting-item">
                            <label for="telegram_notifications">Telegram уведомления</label>
                            <select id="telegram_notifications" name="setting_telegram_notifications">
                                <option value="0" <?php echo ($settings['telegram_notifications'] ?? '0') === '0' ? 'selected' : ''; ?>>Отключены</option>
                                <option value="1" <?php echo ($settings['telegram_notifications'] ?? '0') === '1' ? 'selected' : ''; ?>>Включены</option>
                            </select>
                        </div>
                        
                        <div class="setting-item">
                            <label for="telegram_bot_token">Telegram Bot Token</label>
                            <input type="text" id="telegram_bot_token" name="setting_telegram_bot_token" 
                                   value="<?php echo htmlspecialchars($settings['telegram_bot_token'] ?? ''); ?>" 
                                   placeholder="123456789:ABCdefGHIjklMNOpqrsTUVwxyz">
                        </div>
                    </div>
                </div>

                <!-- Кнопки сохранения -->
                <div class="settings-actions">
                    <button type="submit" class="btn btn-primary btn-large">
                        <i class="fas fa-save"></i>
                        Сохранить настройки
                    </button>
                    <button type="button" class="btn btn-outline btn-large" onclick="resetForm()">
                        <i class="fas fa-undo"></i>
                        Сбросить
                    </button>
                </div>
            </form>
        </div>
    </main>

    <!-- JavaScript -->
    <script src="../assets/js/admin.js"></script>
    <script>
        function resetForm() {
            if (confirm('Вы уверены, что хотите сбросить все изменения?')) {
                location.reload();
            }
        }
        
        // Автосохранение при изменении критических настроек
        document.addEventListener('DOMContentLoaded', function() {
            const criticalSettings = ['maintenance_mode', 'email_verification_required'];
            
            criticalSettings.forEach(settingId => {
                const element = document.getElementById(settingId);
                if (element) {
                    element.addEventListener('change', function() {
                        if (confirm('Это критическая настройка. Сохранить изменения сейчас?')) {
                            document.querySelector('.settings-form').submit();
                        }
                    });
                }
            });
        });
    </script>
</body>
</html>
