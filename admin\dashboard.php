<?php
/**
 * AstroGenix - Административная панель
 * Эко-майнинговая инвестиционная платформа
 */

require_once '../config/config.php';

// Проверка авторизации и прав администратора
require_admin();

try {
    $database = new Database();
    $db = $database->getConnection();
    
    // Получение общей статистики
    $stats_query = "SELECT 
        (SELECT COUNT(*) FROM users WHERE is_active = 1) as total_users,
        (SELECT COUNT(*) FROM users WHERE created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)) as new_users_month,
        (SELECT COUNT(*) FROM user_investments WHERE status = 'active') as active_investments,
        (SELECT COALESCE(SUM(amount), 0) FROM user_investments WHERE status = 'active') as total_invested,
        (SELECT COALESCE(SUM(amount), 0) FROM transactions WHERE type = 'deposit' AND status = 'completed') as total_deposits,
        (SELECT COALESCE(SUM(amount), 0) FROM transactions WHERE type = 'withdrawal' AND status = 'completed') as total_withdrawals,
        (SELECT COALESCE(SUM(amount), 0) FROM transactions WHERE type = 'profit' AND status = 'completed') as total_profits,
        (SELECT COUNT(*) FROM transactions WHERE status = 'pending') as pending_transactions,
        (SELECT COALESCE(SUM(energy_generated), 0) FROM green_energy_stats) as total_green_energy";
    
    $stats_stmt = $db->prepare($stats_query);
    $stats_stmt->execute();
    $stats = $stats_stmt->fetch(PDO::FETCH_ASSOC);
    
    // Получение последних пользователей
    $users_query = "SELECT id, username, first_name, last_name, email, balance, total_invested, created_at 
                    FROM users ORDER BY created_at DESC LIMIT 10";
    $users_stmt = $db->prepare($users_query);
    $users_stmt->execute();
    $recent_users = $users_stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Получение ожидающих транзакций
    $pending_query = "SELECT t.*, u.username, u.first_name, u.last_name 
                      FROM transactions t 
                      JOIN users u ON t.user_id = u.id 
                      WHERE t.status = 'pending' 
                      ORDER BY t.created_at ASC LIMIT 10";
    $pending_stmt = $db->prepare($pending_query);
    $pending_stmt->execute();
    $pending_transactions = $pending_stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Получение топ инвесторов
    $investors_query = "SELECT u.id, u.username, u.first_name, u.last_name, u.total_invested, u.balance
                        FROM users u 
                        WHERE u.total_invested > 0 
                        ORDER BY u.total_invested DESC LIMIT 10";
    $investors_stmt = $db->prepare($investors_query);
    $investors_stmt->execute();
    $top_investors = $investors_stmt->fetchAll(PDO::FETCH_ASSOC);
    
} catch (Exception $e) {
    error_log("Admin dashboard error: " . $e->getMessage());
    $stats = [];
    $recent_users = [];
    $pending_transactions = [];
    $top_investors = [];
}

$page_title = 'Административная панель - ' . SITE_NAME;
?>
<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?></title>
    
    <!-- CSS -->
    <link rel="stylesheet" href="../assets/css/style.css">
    <link rel="stylesheet" href="../assets/css/dashboard.css">
    <link rel="stylesheet" href="../assets/css/admin.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.css" rel="stylesheet">
</head>
<body class="dashboard-page admin-page">
    <!-- Admin Sidebar -->
    <?php include 'includes/admin-sidebar.php'; ?>

    <!-- Main Content -->
    <main class="main-content">
        <!-- Header -->
        <header class="dashboard-header">
            <div class="header-left">
                <button class="mobile-menu-toggle" id="mobileMenuToggle">
                    <i class="fas fa-bars"></i>
                </button>
                <h1>Административная панель</h1>
            </div>
            <div class="header-right">
                <div class="admin-info">
                    <span class="admin-label">Администратор:</span>
                    <span class="admin-name"><?php echo htmlspecialchars($_SESSION['first_name'] . ' ' . $_SESSION['last_name']); ?></span>
                </div>
                <div class="user-avatar">
                    <img src="../assets/images/admin-avatar.png" alt="Admin Avatar">
                </div>
            </div>
        </header>

        <!-- Dashboard Content -->
        <div class="dashboard-content">
            <!-- Quick Stats -->
            <div class="admin-stats-grid">
                <div class="admin-stat-card users-card">
                    <div class="stat-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="stat-content">
                        <h3>Пользователи</h3>
                        <div class="stat-value"><?php echo number_format($stats['total_users']); ?></div>
                        <div class="stat-change positive">
                            <i class="fas fa-arrow-up"></i>
                            <span>+<?php echo $stats['new_users_month']; ?> за месяц</span>
                        </div>
                    </div>
                    <div class="stat-action">
                        <a href="users.php" class="btn btn-outline btn-small">Управление</a>
                    </div>
                </div>

                <div class="admin-stat-card investments-card">
                    <div class="stat-icon">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <div class="stat-content">
                        <h3>Активные инвестиции</h3>
                        <div class="stat-value"><?php echo number_format($stats['active_investments']); ?></div>
                        <div class="stat-subtitle">На сумму: <?php echo format_currency($stats['total_invested']); ?></div>
                    </div>
                    <div class="stat-action">
                        <a href="investments.php" class="btn btn-outline btn-small">Просмотр</a>
                    </div>
                </div>

                <div class="admin-stat-card transactions-card">
                    <div class="stat-icon">
                        <i class="fas fa-exchange-alt"></i>
                    </div>
                    <div class="stat-content">
                        <h3>Ожидают обработки</h3>
                        <div class="stat-value"><?php echo $stats['pending_transactions']; ?></div>
                        <div class="stat-subtitle">Транзакций на рассмотрении</div>
                    </div>
                    <div class="stat-action">
                        <a href="transactions.php?status=pending" class="btn btn-warning btn-small">Обработать</a>
                    </div>
                </div>

                <div class="admin-stat-card finance-card">
                    <div class="stat-icon">
                        <i class="fas fa-coins"></i>
                    </div>
                    <div class="stat-content">
                        <h3>Финансы</h3>
                        <div class="finance-stats">
                            <div class="finance-item">
                                <span>Депозиты:</span>
                                <span><?php echo format_currency($stats['total_deposits']); ?></span>
                            </div>
                            <div class="finance-item">
                                <span>Выводы:</span>
                                <span><?php echo format_currency($stats['total_withdrawals']); ?></span>
                            </div>
                            <div class="finance-item">
                                <span>Прибыль:</span>
                                <span><?php echo format_currency($stats['total_profits']); ?></span>
                            </div>
                        </div>
                    </div>
                    <div class="stat-action">
                        <a href="finance.php" class="btn btn-outline btn-small">Детали</a>
                    </div>
                </div>

                <div class="admin-stat-card energy-card">
                    <div class="stat-icon">
                        <i class="fas fa-leaf"></i>
                    </div>
                    <div class="stat-content">
                        <h3>Зеленая энергия</h3>
                        <div class="stat-value"><?php echo number_format($stats['total_green_energy'], 2); ?></div>
                        <div class="stat-subtitle">кВт⋅ч произведено</div>
                    </div>
                    <div class="stat-action">
                        <a href="ecology.php" class="btn btn-success btn-small">Экология</a>
                    </div>
                </div>
            </div>

            <!-- Charts Section -->
            <div class="charts-section">
                <div class="chart-card">
                    <div class="chart-header">
                        <h3>Статистика пользователей</h3>
                        <div class="chart-controls">
                            <button class="chart-period active" data-period="7">7 дней</button>
                            <button class="chart-period" data-period="30">30 дней</button>
                            <button class="chart-period" data-period="90">90 дней</button>
                        </div>
                    </div>
                    <div class="chart-container">
                        <canvas id="usersChart"></canvas>
                    </div>
                </div>

                <div class="chart-card">
                    <div class="chart-header">
                        <h3>Финансовая статистика</h3>
                    </div>
                    <div class="chart-container">
                        <canvas id="financeChart"></canvas>
                    </div>
                </div>
            </div>

            <!-- Data Tables -->
            <div class="admin-tables">
                <!-- Pending Transactions -->
                <div class="admin-table-card">
                    <div class="table-header">
                        <h3><i class="fas fa-clock"></i> Ожидающие транзакции</h3>
                        <a href="transactions.php?status=pending" class="view-all-link">Посмотреть все</a>
                    </div>
                    <div class="table-container">
                        <?php if (empty($pending_transactions)): ?>
                            <div class="empty-state">
                                <i class="fas fa-check-circle"></i>
                                <p>Нет ожидающих транзакций</p>
                            </div>
                        <?php else: ?>
                            <table class="admin-table">
                                <thead>
                                    <tr>
                                        <th>Пользователь</th>
                                        <th>Тип</th>
                                        <th>Сумма</th>
                                        <th>Дата</th>
                                        <th>Действия</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($pending_transactions as $transaction): ?>
                                        <tr>
                                            <td>
                                                <div class="user-info">
                                                    <strong><?php echo htmlspecialchars($transaction['first_name'] . ' ' . $transaction['last_name']); ?></strong>
                                                    <small>@<?php echo htmlspecialchars($transaction['username']); ?></small>
                                                </div>
                                            </td>
                                            <td>
                                                <span class="transaction-type type-<?php echo $transaction['type']; ?>">
                                                    <?php 
                                                    $types = [
                                                        'deposit' => 'Пополнение',
                                                        'withdrawal' => 'Вывод',
                                                        'investment' => 'Инвестиция'
                                                    ];
                                                    echo $types[$transaction['type']] ?? $transaction['type'];
                                                    ?>
                                                </span>
                                            </td>
                                            <td class="amount"><?php echo format_currency($transaction['amount']); ?></td>
                                            <td><?php echo date('d.m.Y H:i', strtotime($transaction['created_at'])); ?></td>
                                            <td>
                                                <div class="action-buttons">
                                                    <button class="btn btn-success btn-small approve-btn" 
                                                            data-id="<?php echo $transaction['id']; ?>">
                                                        <i class="fas fa-check"></i>
                                                    </button>
                                                    <button class="btn btn-error btn-small reject-btn" 
                                                            data-id="<?php echo $transaction['id']; ?>">
                                                        <i class="fas fa-times"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Recent Users -->
                <div class="admin-table-card">
                    <div class="table-header">
                        <h3><i class="fas fa-user-plus"></i> Новые пользователи</h3>
                        <a href="users.php" class="view-all-link">Посмотреть всех</a>
                    </div>
                    <div class="table-container">
                        <table class="admin-table">
                            <thead>
                                <tr>
                                    <th>Пользователь</th>
                                    <th>Email</th>
                                    <th>Баланс</th>
                                    <th>Инвестировано</th>
                                    <th>Дата регистрации</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($recent_users as $user): ?>
                                    <tr>
                                        <td>
                                            <div class="user-info">
                                                <strong><?php echo htmlspecialchars($user['first_name'] . ' ' . $user['last_name']); ?></strong>
                                                <small>@<?php echo htmlspecialchars($user['username']); ?></small>
                                            </div>
                                        </td>
                                        <td><?php echo htmlspecialchars($user['email']); ?></td>
                                        <td class="amount"><?php echo format_currency($user['balance']); ?></td>
                                        <td class="amount"><?php echo format_currency($user['total_invested']); ?></td>
                                        <td><?php echo date('d.m.Y', strtotime($user['created_at'])); ?></td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Top Investors -->
                <div class="admin-table-card">
                    <div class="table-header">
                        <h3><i class="fas fa-trophy"></i> Топ инвесторы</h3>
                        <a href="users.php?sort=invested" class="view-all-link">Рейтинг</a>
                    </div>
                    <div class="table-container">
                        <table class="admin-table">
                            <thead>
                                <tr>
                                    <th>Место</th>
                                    <th>Пользователь</th>
                                    <th>Инвестировано</th>
                                    <th>Баланс</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($top_investors as $index => $investor): ?>
                                    <tr>
                                        <td>
                                            <span class="rank rank-<?php echo $index + 1; ?>">
                                                <?php if ($index < 3): ?>
                                                    <i class="fas fa-medal"></i>
                                                <?php endif; ?>
                                                #<?php echo $index + 1; ?>
                                            </span>
                                        </td>
                                        <td>
                                            <div class="user-info">
                                                <strong><?php echo htmlspecialchars($investor['first_name'] . ' ' . $investor['last_name']); ?></strong>
                                                <small>@<?php echo htmlspecialchars($investor['username']); ?></small>
                                            </div>
                                        </td>
                                        <td class="amount invested"><?php echo format_currency($investor['total_invested']); ?></td>
                                        <td class="amount"><?php echo format_currency($investor['balance']); ?></td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Transaction Action Modal -->
    <div class="modal" id="transactionModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="modalTitle">Обработка транзакции</h3>
                <button class="modal-close" id="modalClose">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <form id="transactionForm">
                    <input type="hidden" id="transactionId">
                    <input type="hidden" id="transactionAction">
                    
                    <div class="form-group">
                        <label for="adminNote">Комментарий администратора:</label>
                        <textarea id="adminNote" rows="3" placeholder="Укажите причину или дополнительную информацию"></textarea>
                    </div>
                    
                    <div class="modal-actions">
                        <button type="button" class="btn btn-secondary" id="modalCancel">Отмена</button>
                        <button type="submit" class="btn btn-primary" id="modalSubmit">Подтвердить</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.js"></script>
    <script src="../assets/js/dashboard.js"></script>
    <script src="../assets/js/admin.js"></script>
</body>
</html>
