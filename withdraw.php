<?php
/**
 * AstroGenix - Страница вывода средств
 * Эко-майнинговая инвестиционная платформа
 */

require_once 'config/config.php';

// Проверка авторизации
if (!is_logged_in()) {
    redirect('login.php');
}

$errors = [];
$success_message = '';

// Получение текущего баланса пользователя
try {
    $database = new Database();
    $db = $database->getConnection();
    $user = new User($db);
    $user->getUserById($_SESSION['user_id']);
    $current_balance = $user->balance;
} catch (Exception $e) {
    $current_balance = 0;
    error_log("Error fetching user balance: " . $e->getMessage());
}

// Обработка формы вывода
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!verify_csrf_token($_POST['csrf_token'] ?? '')) {
        $errors[] = 'Ошибка безопасности. Попробуйте еще раз.';
    } else {
        $amount = floatval($_POST['amount'] ?? 0);
        $payment_method = sanitize_input($_POST['payment_method'] ?? '');
        $payment_details = sanitize_input($_POST['payment_details'] ?? '');

        // Валидация
        if ($amount <= 0) {
            $errors[] = 'Сумма должна быть больше нуля.';
        } elseif ($amount < 10) {
            $errors[] = 'Минимальная сумма вывода: 10 ₽.';
        } elseif ($amount > $current_balance) {
            $errors[] = 'Недостаточно средств на балансе.';
        } elseif ($amount > 50000) {
            $errors[] = 'Максимальная сумма вывода: 50,000 ₽.';
        }

        if (empty($payment_method)) {
            $errors[] = 'Выберите способ вывода.';
        }

        if (empty($payment_details)) {
            $errors[] = 'Укажите реквизиты для вывода средств.';
        }

        // Создание запроса на вывод
        if (empty($errors)) {
            try {
                $transaction = new Transaction($db);

                if ($transaction->createWithdrawalRequest($_SESSION['user_id'], $amount, $payment_method, $payment_details)) {
                    $success_message = 'Запрос на вывод создан успешно! Ожидайте обработки администратором.';
                    
                    // Обновление баланса в сессии
                    $_SESSION['balance'] = $current_balance;
                    
                    // Очистка формы
                    $amount = $payment_method = $payment_details = '';
                } else {
                    $errors[] = 'Ошибка при создании запроса. Попробуйте еще раз.';
                }
            } catch (Exception $e) {
                $errors[] = 'Ошибка сервера. Попробуйте позже.';
                error_log("Withdrawal error: " . $e->getMessage());
            }
        }
    }
}

// Получение последних транзакций вывода
try {
    $transaction = new Transaction($db);
    $recent_withdrawals = $transaction->getUserTransactions($_SESSION['user_id'], 10, 0, 'withdrawal');
} catch (Exception $e) {
    $recent_withdrawals = [];
    error_log("Error fetching withdrawals: " . $e->getMessage());
}

$page_title = 'Вывод средств - ' . SITE_NAME;
?>
<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?></title>
    
    <!-- CSS -->
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="assets/css/dashboard.css">
    <link rel="stylesheet" href="assets/css/forms.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body class="dashboard-page">
    <!-- Sidebar -->
    <?php include 'includes/sidebar.php'; ?>

    <!-- Main Content -->
    <main class="main-content">
        <!-- Header -->
        <header class="dashboard-header">
            <div class="header-left">
                <button class="mobile-menu-toggle" id="mobileMenuToggle">
                    <i class="fas fa-bars"></i>
                </button>
                <h1>Вывод средств</h1>
            </div>
            <div class="header-right">
                <div class="user-balance">
                    <span class="balance-label">Доступно для вывода:</span>
                    <span class="balance-amount"><?php echo format_currency($current_balance); ?></span>
                </div>
            </div>
        </header>

        <!-- Content -->
        <div class="dashboard-content">
            <?php if (!empty($errors)): ?>
                <div class="alert alert-error">
                    <i class="fas fa-exclamation-triangle"></i>
                    <ul>
                        <?php foreach ($errors as $error): ?>
                            <li><?php echo htmlspecialchars($error); ?></li>
                        <?php endforeach; ?>
                    </ul>
                </div>
            <?php endif; ?>

            <?php if (!empty($success_message)): ?>
                <div class="alert alert-success">
                    <i class="fas fa-check-circle"></i>
                    <?php echo htmlspecialchars($success_message); ?>
                </div>
            <?php endif; ?>

            <!-- Withdrawal Info -->
            <div class="withdrawal-info">
                <div class="info-card">
                    <div class="info-icon">
                        <i class="fas fa-info-circle"></i>
                    </div>
                    <div class="info-content">
                        <h3>Важная информация о выводе средств</h3>
                        <ul>
                            <li>Минимальная сумма вывода: <strong>10 ₽</strong></li>
                            <li>Максимальная сумма вывода: <strong>50,000 ₽</strong></li>
                            <li>Время обработки: <strong>1-3 рабочих дня</strong></li>
                            <li>Комиссия зависит от выбранного способа вывода</li>
                            <li>Все запросы проверяются администратором</li>
                        </ul>
                    </div>
                </div>
            </div>

            <div class="withdrawal-container">
                <!-- Withdrawal Form -->
                <div class="withdrawal-form-section">
                    <div class="card">
                        <div class="card-header">
                            <h3><i class="fas fa-minus-circle"></i> Создать запрос на вывод</h3>
                            <p>Заполните форму для создания запроса на вывод средств</p>
                        </div>
                        <div class="card-body">
                            <form class="withdrawal-form" method="POST" action="">
                                <input type="hidden" name="csrf_token" value="<?php echo generate_csrf_token(); ?>">
                                
                                <div class="form-group">
                                    <label for="amount">Сумма вывода (₽)</label>
                                    <div class="amount-input-group">
                                        <input type="number" id="amount" name="amount" 
                                               value="<?php echo htmlspecialchars($amount ?? ''); ?>" 
                                               min="10" max="<?php echo min($current_balance, 50000); ?>" step="0.01" required>
                                        <div class="amount-buttons">
                                            <?php if ($current_balance >= 1000): ?>
                                                <button type="button" class="amount-btn" data-amount="1000">1,000 ₽</button>
                                            <?php endif; ?>
                                            <?php if ($current_balance >= 5000): ?>
                                                <button type="button" class="amount-btn" data-amount="5000">5,000 ₽</button>
                                            <?php endif; ?>
                                            <?php if ($current_balance >= 10000): ?>
                                                <button type="button" class="amount-btn" data-amount="10000">10,000 ₽</button>
                                            <?php endif; ?>
                                            <button type="button" class="amount-btn" data-amount="<?php echo $current_balance; ?>">Все</button>
                                        </div>
                                    </div>
                                    <div class="amount-info">
                                        <small>Доступно: <?php echo format_currency($current_balance); ?></small>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label for="payment_method">Способ вывода</label>
                                    <div class="payment-methods">
                                        <label class="payment-method-option">
                                            <input type="radio" name="payment_method" value="bank_card" required>
                                            <div class="payment-method-card">
                                                <i class="fas fa-credit-card"></i>
                                                <span>Банковская карта</span>
                                                <small>Комиссия: 2%</small>
                                            </div>
                                        </label>
                                        
                                        <label class="payment-method-option">
                                            <input type="radio" name="payment_method" value="bank_transfer" required>
                                            <div class="payment-method-card">
                                                <i class="fas fa-university"></i>
                                                <span>Банковский перевод</span>
                                                <small>Комиссия: 1%</small>
                                            </div>
                                        </label>
                                        
                                        <label class="payment-method-option">
                                            <input type="radio" name="payment_method" value="qiwi" required>
                                            <div class="payment-method-card">
                                                <i class="fas fa-wallet"></i>
                                                <span>QIWI Кошелек</span>
                                                <small>Комиссия: 3%</small>
                                            </div>
                                        </label>
                                        
                                        <label class="payment-method-option">
                                            <input type="radio" name="payment_method" value="yandex_money" required>
                                            <div class="payment-method-card">
                                                <i class="fas fa-ruble-sign"></i>
                                                <span>ЮMoney</span>
                                                <small>Комиссия: 3%</small>
                                            </div>
                                        </label>
                                        
                                        <label class="payment-method-option">
                                            <input type="radio" name="payment_method" value="crypto" required>
                                            <div class="payment-method-card">
                                                <i class="fab fa-bitcoin"></i>
                                                <span>Криптовалюта</span>
                                                <small>Комиссия: 1%</small>
                                            </div>
                                        </label>
                                        
                                        <label class="payment-method-option">
                                            <input type="radio" name="payment_method" value="sbp" required>
                                            <div class="payment-method-card">
                                                <i class="fas fa-mobile-alt"></i>
                                                <span>СБП</span>
                                                <small>Комиссия: 0%</small>
                                            </div>
                                        </label>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label for="payment_details">Реквизиты для вывода</label>
                                    <textarea id="payment_details" name="payment_details" rows="4" required
                                              placeholder="Укажите точные реквизиты для вывода средств"><?php echo htmlspecialchars($payment_details ?? ''); ?></textarea>
                                    <small class="form-help">
                                        <i class="fas fa-shield-alt"></i>
                                        Убедитесь, что реквизиты указаны корректно. Средства будут переведены именно на указанные реквизиты.
                                    </small>
                                </div>

                                <div class="withdrawal-summary">
                                    <div class="summary-row">
                                        <span>Сумма к выводу:</span>
                                        <span class="summary-amount" id="summaryAmount">0 ₽</span>
                                    </div>
                                    <div class="summary-row">
                                        <span>Комиссия:</span>
                                        <span class="summary-fee" id="summaryFee">0 ₽</span>
                                    </div>
                                    <div class="summary-row total">
                                        <span>К получению:</span>
                                        <span class="summary-total" id="summaryTotal">0 ₽</span>
                                    </div>
                                </div>

                                <button type="submit" class="btn btn-primary btn-full btn-large">
                                    <i class="fas fa-minus-circle"></i>
                                    Создать запрос на вывод
                                </button>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- Recent Withdrawals -->
                <div class="recent-withdrawals-section">
                    <div class="card">
                        <div class="card-header">
                            <h3><i class="fas fa-history"></i> История выводов</h3>
                        </div>
                        <div class="card-body">
                            <?php if (empty($recent_withdrawals)): ?>
                                <div class="empty-state">
                                    <i class="fas fa-inbox"></i>
                                    <p>У вас пока нет запросов на вывод</p>
                                </div>
                            <?php else: ?>
                                <div class="withdrawals-list">
                                    <?php foreach ($recent_withdrawals as $withdrawal): ?>
                                        <div class="withdrawal-item">
                                            <div class="withdrawal-info">
                                                <div class="withdrawal-amount">
                                                    <?php echo format_currency($withdrawal['amount']); ?>
                                                </div>
                                                <div class="withdrawal-date">
                                                    <?php echo date('d.m.Y H:i', strtotime($withdrawal['created_at'])); ?>
                                                </div>
                                                <?php if (!empty($withdrawal['description'])): ?>
                                                    <div class="withdrawal-description">
                                                        <?php echo htmlspecialchars($withdrawal['description']); ?>
                                                    </div>
                                                <?php endif; ?>
                                                <?php if (!empty($withdrawal['admin_note']) && $withdrawal['status'] === 'rejected'): ?>
                                                    <div class="admin-note">
                                                        <strong>Причина отклонения:</strong> <?php echo htmlspecialchars($withdrawal['admin_note']); ?>
                                                    </div>
                                                <?php endif; ?>
                                            </div>
                                            <div class="withdrawal-status">
                                                <span class="status-badge status-<?php echo $withdrawal['status']; ?>">
                                                    <?php 
                                                    $statuses = [
                                                        'pending' => 'Ожидание',
                                                        'approved' => 'Одобрено',
                                                        'rejected' => 'Отклонено',
                                                        'completed' => 'Завершено'
                                                    ];
                                                    echo $statuses[$withdrawal['status']] ?? $withdrawal['status'];
                                                    ?>
                                                </span>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                                <div class="card-footer">
                                    <a href="transactions.php?type=withdrawal" class="btn btn-outline">
                                        Посмотреть все выводы
                                    </a>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- JavaScript -->
    <script src="assets/js/dashboard.js"></script>
    <script src="assets/js/withdraw.js"></script>
</body>
</html>
