<svg width="40" height="40" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="logoGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#2ECC71;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#8E44AD;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Основная форма - стилизованная планета/сфера -->
  <circle cx="20" cy="20" r="18" fill="url(#logoGradient)" stroke="white" stroke-width="2"/>
  
  <!-- Орбитальные кольца -->
  <ellipse cx="20" cy="20" rx="25" ry="8" fill="none" stroke="url(#logoGradient)" stroke-width="1.5" opacity="0.6"/>
  <ellipse cx="20" cy="20" rx="8" ry="25" fill="none" stroke="url(#logoGradient)" stroke-width="1.5" opacity="0.6"/>
  
  <!-- Центральный символ - листок/энергия -->
  <path d="M20 8 C25 12, 25 18, 20 20 C15 18, 15 12, 20 8 Z" fill="white" opacity="0.9"/>
  <path d="M20 32 C15 28, 15 22, 20 20 C25 22, 25 28, 20 32 Z" fill="white" opacity="0.9"/>
  
  <!-- Энергетические точки -->
  <circle cx="12" cy="12" r="1.5" fill="white" opacity="0.8"/>
  <circle cx="28" cy="12" r="1.5" fill="white" opacity="0.8"/>
  <circle cx="12" cy="28" r="1.5" fill="white" opacity="0.8"/>
  <circle cx="28" cy="28" r="1.5" fill="white" opacity="0.8"/>
</svg>
