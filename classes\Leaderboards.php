<?php
/**
 * AstroGenix - Система рейтингов и лидербордов
 * Управление рейтингами пользователей по различным критериям
 */

class Leaderboards {
    private $db;
    
    public function __construct($database) {
        $this->db = $database;
    }
    
    /**
     * Получение лидерборда по типу
     */
    public function getLeaderboard($type, $limit = 50, $user_id = null) {
        try {
            $query = "SELECT 
                        lv.*,
                        CASE 
                            WHEN lv.user_id = :user_id THEN 1 
                            ELSE 0 
                        END as is_current_user
                      FROM leaderboard_view lv
                      WHERE lv.ranking_type = :type
                      ORDER BY lv.current_rank ASC
                      LIMIT :limit";
            
            $stmt = $this->db->prepare($query);
            $stmt->bindParam(':type', $type);
            $stmt->bindParam(':user_id', $user_id, PDO::PARAM_INT);
            $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
            $stmt->execute();
            
            $leaderboard = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            // Декодирование JSON настроек отображения
            foreach ($leaderboard as &$entry) {
                $entry['display_settings'] = json_decode($entry['display_settings'], true);
            }
            
            return $leaderboard;
            
        } catch (Exception $e) {
            error_log("Get leaderboard error: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Получение позиции пользователя в рейтинге
     */
    public function getUserRanking($user_id, $type = null) {
        try {
            $where_clause = $type ? "AND ranking_type = :type" : "";
            
            $query = "SELECT 
                        ur.*,
                        rs.display_settings
                      FROM user_rankings ur
                      JOIN ranking_settings rs ON ur.ranking_type = rs.ranking_type
                      WHERE ur.user_id = :user_id {$where_clause}
                      ORDER BY ur.current_rank ASC";
            
            $stmt = $this->db->prepare($query);
            $stmt->bindParam(':user_id', $user_id, PDO::PARAM_INT);
            
            if ($type) {
                $stmt->bindParam(':type', $type);
            }
            
            $stmt->execute();
            $rankings = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            // Декодирование JSON настроек
            foreach ($rankings as &$ranking) {
                $ranking['display_settings'] = json_decode($ranking['display_settings'], true);
            }
            
            return $type ? ($rankings[0] ?? null) : $rankings;
            
        } catch (Exception $e) {
            error_log("Get user ranking error: " . $e->getMessage());
            return $type ? null : [];
        }
    }
    
    /**
     * Получение всех доступных типов рейтингов
     */
    public function getRankingTypes() {
        try {
            $query = "SELECT 
                        ranking_type,
                        display_settings,
                        is_enabled,
                        update_frequency
                      FROM ranking_settings
                      WHERE is_enabled = 1
                      ORDER BY ranking_type";
            
            $stmt = $this->db->prepare($query);
            $stmt->execute();
            $types = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            foreach ($types as &$type) {
                $type['display_settings'] = json_decode($type['display_settings'], true);
            }
            
            return $types;
            
        } catch (Exception $e) {
            error_log("Get ranking types error: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Обновление рейтингов пользователя
     */
    public function updateUserRankings($user_id) {
        try {
            $this->db->beginTransaction();
            
            // Обновление рейтинга рефералов
            $this->updateReferralRanking($user_id);
            
            // Обновление рейтинга инвестиций
            $this->updateInvestmentRanking($user_id);
            
            // Обновление рейтинга прибыли
            $this->updateEarningsRanking($user_id);
            
            // Обновление рейтинга активности
            $this->updateActivityRanking($user_id);
            
            // Обновление эко-рейтинга
            $this->updateEcoRanking($user_id);
            
            $this->db->commit();
            
            // Обновление позиций в рейтинге
            $this->updateRankingPositions();
            
            return true;
            
        } catch (Exception $e) {
            $this->db->rollback();
            error_log("Update user rankings error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Обновление рейтинга рефералов
     */
    private function updateReferralRanking($user_id) {
        // Подсчет прямых рефералов
        $direct_query = "SELECT COUNT(*) FROM users WHERE referrer_id = :user_id AND is_active = 1";
        $direct_stmt = $this->db->prepare($direct_query);
        $direct_stmt->bindParam(':user_id', $user_id, PDO::PARAM_INT);
        $direct_stmt->execute();
        $direct_count = $direct_stmt->fetchColumn();
        
        // Подсчет заработка с рефералов
        $earnings_query = "SELECT COALESCE(SUM(amount), 0) FROM transactions 
                          WHERE user_id = :user_id AND type = 'referral_bonus' AND status = 'completed'";
        $earnings_stmt = $this->db->prepare($earnings_query);
        $earnings_stmt->bindParam(':user_id', $user_id, PDO::PARAM_INT);
        $earnings_stmt->execute();
        $referral_earnings = $earnings_stmt->fetchColumn();
        
        // Расчет счета
        $score = ($direct_count * 10) + $referral_earnings;
        
        // Обновление рейтинга
        $this->updateRankingScore($user_id, 'referrals', $score);
    }
    
    /**
     * Обновление рейтинга инвестиций
     */
    private function updateInvestmentRanking($user_id) {
        $query = "SELECT 
                    COALESCE(SUM(amount), 0) as total_invested,
                    COUNT(CASE WHEN status = 'active' THEN 1 END) as active_count,
                    COUNT(DISTINCT package_id) as package_diversity
                  FROM user_investments 
                  WHERE user_id = :user_id";
        
        $stmt = $this->db->prepare($query);
        $stmt->bindParam(':user_id', $user_id, PDO::PARAM_INT);
        $stmt->execute();
        $data = $stmt->fetch(PDO::FETCH_ASSOC);
        
        $score = $data['total_invested'] + ($data['active_count'] * 100) + ($data['package_diversity'] * 50);
        
        $this->updateRankingScore($user_id, 'investments', $score);
    }
    
    /**
     * Обновление рейтинга прибыли
     */
    private function updateEarningsRanking($user_id) {
        $query = "SELECT 
                    COALESCE(SUM(total_earned), 0) as total_earned,
                    COALESCE(SUM(CASE WHEN status = 'active' THEN daily_profit ELSE 0 END), 0) as current_profit
                  FROM user_investments 
                  WHERE user_id = :user_id";
        
        $stmt = $this->db->prepare($query);
        $stmt->bindParam(':user_id', $user_id, PDO::PARAM_INT);
        $stmt->execute();
        $data = $stmt->fetch(PDO::FETCH_ASSOC);
        
        // Получение общих инвестиций для расчета ROI
        $invested_query = "SELECT total_invested FROM users WHERE id = :user_id";
        $invested_stmt = $this->db->prepare($invested_query);
        $invested_stmt->bindParam(':user_id', $user_id, PDO::PARAM_INT);
        $invested_stmt->execute();
        $total_invested = $invested_stmt->fetchColumn() ?: 0;
        
        $roi_bonus = $total_invested > 0 ? ($data['total_earned'] / $total_invested) * 100 : 0;
        $score = $data['total_earned'] + ($data['current_profit'] * 2) + ($roi_bonus * 10);
        
        $this->updateRankingScore($user_id, 'earnings', $score);
    }
    
    /**
     * Обновление рейтинга активности
     */
    private function updateActivityRanking($user_id) {
        // Подсчет дней входа
        $login_query = "SELECT DATEDIFF(CURDATE(), created_at) as days_since_registration,
                               COALESCE(login_count, 0) as login_count
                        FROM users WHERE id = :user_id";
        $login_stmt = $this->db->prepare($login_query);
        $login_stmt->bindParam(':user_id', $user_id, PDO::PARAM_INT);
        $login_stmt->execute();
        $login_data = $login_stmt->fetch(PDO::FETCH_ASSOC);
        
        // Подсчет транзакций
        $trans_query = "SELECT COUNT(*) FROM transactions WHERE user_id = :user_id";
        $trans_stmt = $this->db->prepare($trans_query);
        $trans_stmt->bindParam(':user_id', $user_id, PDO::PARAM_INT);
        $trans_stmt->execute();
        $transactions_count = $trans_stmt->fetchColumn();
        
        // Подсчет инвестиций
        $inv_query = "SELECT COUNT(*) FROM user_investments WHERE user_id = :user_id";
        $inv_stmt = $this->db->prepare($inv_query);
        $inv_stmt->bindParam(':user_id', $user_id, PDO::PARAM_INT);
        $inv_stmt->execute();
        $investments_count = $inv_stmt->fetchColumn();
        
        $score = ($login_data['login_count'] * 10) + $transactions_count + ($investments_count * 5);
        
        $this->updateRankingScore($user_id, 'activity', $score);
    }
    
    /**
     * Обновление эко-рейтинга
     */
    private function updateEcoRanking($user_id) {
        $query = "SELECT 
                    COALESCE(energy_generated, 0) as energy_generated,
                    COALESCE(co2_saved, 0) as co2_saved,
                    COALESCE(trees_planted, 0) as trees_planted
                  FROM green_energy_stats 
                  WHERE user_id = :user_id";
        
        $stmt = $this->db->prepare($query);
        $stmt->bindParam(':user_id', $user_id, PDO::PARAM_INT);
        $stmt->execute();
        $data = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$data) {
            $data = ['energy_generated' => 0, 'co2_saved' => 0, 'trees_planted' => 0];
        }
        
        $score = $data['energy_generated'] + ($data['co2_saved'] * 10) + ($data['trees_planted'] * 50);
        
        $this->updateRankingScore($user_id, 'eco_impact', $score);
    }
    
    /**
     * Обновление счета рейтинга
     */
    private function updateRankingScore($user_id, $type, $score) {
        $query = "INSERT INTO user_rankings (user_id, ranking_type, current_score)
                  VALUES (:user_id, :type, :score)
                  ON DUPLICATE KEY UPDATE 
                  previous_score = current_score,
                  current_score = :score";
        
        $stmt = $this->db->prepare($query);
        $stmt->bindParam(':user_id', $user_id, PDO::PARAM_INT);
        $stmt->bindParam(':type', $type);
        $stmt->bindParam(':score', $score);
        
        return $stmt->execute();
    }
    
    /**
     * Обновление позиций в рейтинге
     */
    private function updateRankingPositions() {
        try {
            $types = ['referrals', 'investments', 'earnings', 'activity', 'eco_impact'];
            
            foreach ($types as $type) {
                // Обновление текущих рангов
                $rank_query = "SET @rank = 0;
                              UPDATE user_rankings 
                              SET previous_rank = current_rank,
                                  current_rank = (@rank := @rank + 1)
                              WHERE ranking_type = :type
                              ORDER BY current_score DESC";
                
                $rank_stmt = $this->db->prepare($rank_query);
                $rank_stmt->bindParam(':type', $type);
                $rank_stmt->execute();
                
                // Расчет изменения ранга
                $change_query = "UPDATE user_rankings 
                                SET rank_change = previous_rank - current_rank
                                WHERE ranking_type = :type AND previous_rank > 0";
                
                $change_stmt = $this->db->prepare($change_query);
                $change_stmt->bindParam(':type', $type);
                $change_stmt->execute();
            }
            
            return true;
            
        } catch (Exception $e) {
            error_log("Update ranking positions error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Получение истории рейтинга пользователя
     */
    public function getUserRankingHistory($user_id, $type, $period = 'monthly', $limit = 12) {
        try {
            $query = "SELECT 
                        period_date,
                        score,
                        rank_position
                      FROM ranking_history
                      WHERE user_id = :user_id 
                      AND ranking_type = :type 
                      AND period_type = :period
                      ORDER BY period_date DESC
                      LIMIT :limit";
            
            $stmt = $this->db->prepare($query);
            $stmt->bindParam(':user_id', $user_id, PDO::PARAM_INT);
            $stmt->bindParam(':type', $type);
            $stmt->bindParam(':period', $period);
            $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
            $stmt->execute();
            
            return array_reverse($stmt->fetchAll(PDO::FETCH_ASSOC));
            
        } catch (Exception $e) {
            error_log("Get user ranking history error: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Сохранение исторических данных рейтинга
     */
    public function saveRankingHistory($period_type = 'daily') {
        try {
            $period_date = date('Y-m-d');
            
            $query = "INSERT INTO ranking_history (user_id, ranking_type, score, rank_position, period_type, period_date)
                      SELECT user_id, ranking_type, current_score, current_rank, :period_type, :period_date
                      FROM user_rankings
                      WHERE current_rank > 0";
            
            $stmt = $this->db->prepare($query);
            $stmt->bindParam(':period_type', $period_type);
            $stmt->bindParam(':period_date', $period_date);
            
            return $stmt->execute();
            
        } catch (Exception $e) {
            error_log("Save ranking history error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Получение топ пользователей по всем категориям
     */
    public function getTopUsersOverall($limit = 10) {
        try {
            $query = "SELECT 
                        u.id,
                        u.username,
                        u.avatar,
                        COUNT(ur.ranking_type) as categories_count,
                        AVG(ur.current_rank) as avg_rank,
                        SUM(CASE WHEN ur.current_rank <= 10 THEN 1 ELSE 0 END) as top10_count
                      FROM users u
                      JOIN user_rankings ur ON u.id = ur.user_id
                      WHERE u.is_active = 1 AND ur.current_rank > 0
                      GROUP BY u.id, u.username, u.avatar
                      HAVING categories_count >= 3
                      ORDER BY top10_count DESC, avg_rank ASC
                      LIMIT :limit";
            
            $stmt = $this->db->prepare($query);
            $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
            $stmt->execute();
            
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
            
        } catch (Exception $e) {
            error_log("Get top users overall error: " . $e->getMessage());
            return [];
        }
    }
}
?>
