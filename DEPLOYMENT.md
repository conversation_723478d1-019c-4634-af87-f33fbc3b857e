# 🚀 Руководство по развертыванию AstroGenix

## Обзор

Данное руководство поможет вам развернуть платформу AstroGenix на продакшн сервере с максимальной производительностью и безопасностью.

## 📋 Системные требования

### Минимальные требования
- **ОС**: Ubuntu 20.04+ / CentOS 8+ / Debian 11+
- **PHP**: 8.0 или выше
- **MySQL**: 8.0 или выше
- **Веб-сервер**: Apache 2.4+ или Nginx 1.18+
- **RAM**: 2GB минимум, 4GB рекомендуется
- **Диск**: 10GB свободного места
- **SSL сертификат**: обязательно для продакшн

### Рекомендуемые требования
- **ОС**: Ubuntu 22.04 LTS
- **PHP**: 8.2
- **MySQL**: 8.0.32+
- **Веб-сервер**: Nginx 1.22+
- **RAM**: 8GB
- **Диск**: SSD 50GB+
- **CDN**: CloudFlare или аналог

## 🔧 Установка на Ubuntu 22.04

### 1. Обновление системы
```bash
sudo apt update && sudo apt upgrade -y
```

### 2. Установка PHP 8.2
```bash
sudo apt install software-properties-common -y
sudo add-apt-repository ppa:ondrej/php -y
sudo apt update

sudo apt install php8.2 php8.2-fpm php8.2-mysql php8.2-curl php8.2-gd \
php8.2-mbstring php8.2-xml php8.2-zip php8.2-intl php8.2-bcmath -y
```

### 3. Установка MySQL 8.0
```bash
sudo apt install mysql-server -y
sudo mysql_secure_installation
```

### 4. Установка Nginx
```bash
sudo apt install nginx -y
sudo systemctl enable nginx
sudo systemctl start nginx
```

### 5. Настройка виртуального хоста Nginx
```bash
sudo nano /etc/nginx/sites-available/astrogenix
```

Содержимое файла:
```nginx
server {
    listen 80;
    server_name your-domain.com www.your-domain.com;
    root /var/www/astrogenix;
    index index.php index.html;

    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;

    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;

    # Static files caching
    location ~* \.(jpg|jpeg|png|gif|ico|css|js|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    # PHP processing
    location ~ \.php$ {
        include snippets/fastcgi-php.conf;
        fastcgi_pass unix:/var/run/php/php8.2-fpm.sock;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        include fastcgi_params;
    }

    # Deny access to sensitive files
    location ~ /\. {
        deny all;
    }
    
    location ~ /(config|classes|cron)/ {
        deny all;
    }

    # Pretty URLs
    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }
}
```

### 6. Активация сайта
```bash
sudo ln -s /etc/nginx/sites-available/astrogenix /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

## 🔐 Настройка SSL (Let's Encrypt)

### 1. Установка Certbot
```bash
sudo apt install certbot python3-certbot-nginx -y
```

### 2. Получение SSL сертификата
```bash
sudo certbot --nginx -d your-domain.com -d www.your-domain.com
```

### 3. Автоматическое обновление
```bash
sudo crontab -e
# Добавить строку:
0 12 * * * /usr/bin/certbot renew --quiet
```

## 📁 Развертывание приложения

### 1. Создание директории
```bash
sudo mkdir -p /var/www/astrogenix
sudo chown -R www-data:www-data /var/www/astrogenix
```

### 2. Загрузка файлов
```bash
# Через Git
cd /var/www/astrogenix
sudo -u www-data git clone https://github.com/your-repo/astrogenix.git .

# Или через FTP/SFTP
# Загрузите все файлы в /var/www/astrogenix/
```

### 3. Настройка прав доступа
```bash
sudo chown -R www-data:www-data /var/www/astrogenix
sudo chmod -R 755 /var/www/astrogenix
sudo chmod -R 644 /var/www/astrogenix/config/
sudo chmod 755 /var/www/astrogenix/assets/images/
```

## 🗄️ Настройка базы данных

### 1. Создание базы данных
```sql
mysql -u root -p
CREATE DATABASE astrogenix CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER 'astrogenix_user'@'localhost' IDENTIFIED BY 'strong_password_here';
GRANT ALL PRIVILEGES ON astrogenix.* TO 'astrogenix_user'@'localhost';
FLUSH PRIVILEGES;
EXIT;
```

### 2. Импорт структуры
```bash
mysql -u astrogenix_user -p astrogenix < /var/www/astrogenix/database.sql
```

### 3. Настройка конфигурации
```bash
sudo nano /var/www/astrogenix/config/database.php
```

## ⚙️ Настройка cron-задач

### 1. Создание cron-файла
```bash
sudo crontab -u www-data -e
```

### 2. Добавление задач
```bash
# Ежедневное начисление прибыли в 00:01
1 0 * * * /usr/bin/php8.2 /var/www/astrogenix/cron/daily_profits.php

# Очистка логов каждую неделю
0 2 * * 0 find /var/www/astrogenix/logs/ -name "*.log" -mtime +7 -delete

# Резервное копирование БД каждый день в 03:00
0 3 * * * mysqldump -u astrogenix_user -p'password' astrogenix > /var/backups/astrogenix_$(date +\%Y\%m\%d).sql
```

## 🔒 Настройки безопасности

### 1. Настройка файрвола
```bash
sudo ufw enable
sudo ufw allow ssh
sudo ufw allow 'Nginx Full'
sudo ufw allow mysql
```

### 2. Настройка PHP
```bash
sudo nano /etc/php/8.2/fpm/php.ini
```

Важные настройки:
```ini
expose_php = Off
max_execution_time = 30
max_input_time = 60
memory_limit = 256M
post_max_size = 32M
upload_max_filesize = 32M
session.cookie_httponly = 1
session.cookie_secure = 1
session.use_strict_mode = 1
```

### 3. Настройка MySQL
```bash
sudo nano /etc/mysql/mysql.conf.d/mysqld.cnf
```

Добавить:
```ini
bind-address = 127.0.0.1
skip-networking = 0
max_connections = 100
```

## 📊 Мониторинг и логирование

### 1. Настройка логов Nginx
```bash
sudo nano /etc/nginx/nginx.conf
```

### 2. Настройка ротации логов
```bash
sudo nano /etc/logrotate.d/astrogenix
```

Содержимое:
```
/var/www/astrogenix/logs/*.log {
    daily
    missingok
    rotate 52
    compress
    delaycompress
    notifempty
    create 644 www-data www-data
}
```

## 🚀 Оптимизация производительности

### 1. Настройка OPcache
```bash
sudo nano /etc/php/8.2/fpm/conf.d/10-opcache.ini
```

```ini
opcache.enable=1
opcache.memory_consumption=256
opcache.interned_strings_buffer=8
opcache.max_accelerated_files=4000
opcache.revalidate_freq=2
opcache.fast_shutdown=1
```

### 2. Настройка Redis (опционально)
```bash
sudo apt install redis-server php8.2-redis -y
sudo systemctl enable redis-server
```

### 3. Настройка CDN
- Настройте CloudFlare или другой CDN
- Включите минификацию CSS/JS
- Настройте кэширование статических файлов

## 🔄 Процедура обновления

### 1. Создание резервной копии
```bash
# Резервная копия файлов
sudo tar -czf /var/backups/astrogenix_files_$(date +%Y%m%d).tar.gz /var/www/astrogenix

# Резервная копия БД
mysqldump -u astrogenix_user -p astrogenix > /var/backups/astrogenix_db_$(date +%Y%m%d).sql
```

### 2. Обновление кода
```bash
cd /var/www/astrogenix
sudo -u www-data git pull origin main
```

### 3. Применение миграций БД
```bash
# Если есть новые миграции
mysql -u astrogenix_user -p astrogenix < migrations/new_migration.sql
```

### 4. Очистка кэша
```bash
sudo systemctl reload php8.2-fpm
sudo systemctl reload nginx
```

## 🆘 Устранение неполадок

### Проверка логов
```bash
# Логи Nginx
sudo tail -f /var/log/nginx/error.log

# Логи PHP
sudo tail -f /var/log/php8.2-fpm.log

# Логи MySQL
sudo tail -f /var/log/mysql/error.log
```

### Проверка статуса сервисов
```bash
sudo systemctl status nginx
sudo systemctl status php8.2-fpm
sudo systemctl status mysql
```

### Тестирование конфигурации
```bash
sudo nginx -t
sudo php8.2 -m | grep mysql
```

## 📞 Поддержка

При возникновении проблем с развертыванием:
- Проверьте логи сервисов
- Убедитесь в правильности настроек
- Обратитесь к документации
- Создайте issue в репозитории проекта
