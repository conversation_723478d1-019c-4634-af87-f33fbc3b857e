/**
 * AstroGenix - Стили административной панели
 * Эко-майнинговая инвестиционная платформа
 */

/* Основные стили админки */
.admin-page {
    background: linear-gradient(135deg, #f8fffe 0%, #f0f9ff 100%);
}

.admin-sidebar {
    background: linear-gradient(180deg, #1a1a1a 0%, #2d2d2d 100%);
    color: var(--white);
}

.admin-sidebar .sidebar-logo {
    color: var(--white);
}

.admin-sidebar .sidebar-logo span {
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Навигация админки */
.nav-section {
    margin: var(--space-4) 0 var(--space-2) 0;
}

.section-title {
    display: block;
    padding: 0 var(--space-6);
    font-size: var(--text-xs);
    font-weight: 600;
    color: #888;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.admin-sidebar .nav-link {
    color: #ccc;
    position: relative;
}

.admin-sidebar .nav-link:hover {
    color: var(--white);
    background: rgba(46, 204, 113, 0.1);
}

.admin-sidebar .nav-item.active .nav-link {
    color: var(--white);
    background: rgba(46, 204, 113, 0.2);
}

.nav-badge {
    position: absolute;
    right: var(--space-3);
    top: 50%;
    transform: translateY(-50%);
    background: var(--error);
    color: var(--white);
    font-size: var(--text-xs);
    padding: 2px 6px;
    border-radius: var(--radius-full);
    min-width: 18px;
    text-align: center;
    line-height: 1.2;
}

.nav-badge-warning {
    background: var(--warning);
}

/* Заголовок админки */
.admin-page .dashboard-header {
    background: var(--white);
    border-bottom: 1px solid var(--gray-200);
}

.admin-info {
    text-align: right;
}

.admin-label {
    display: block;
    font-size: var(--text-sm);
    color: var(--gray-600);
}

.admin-name {
    display: block;
    font-size: var(--text-lg);
    font-weight: 600;
    color: var(--gray-900);
}

/* Статистические карточки админки */
.admin-stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: var(--space-6);
    margin-bottom: var(--space-8);
}

.admin-stat-card {
    background: var(--white);
    border-radius: var(--radius-xl);
    padding: var(--space-6);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--gray-200);
    transition: all var(--transition-fast);
    position: relative;
    overflow: hidden;
}

.admin-stat-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
}

.admin-stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
}

.users-card::before {
    background: linear-gradient(135deg, #3498DB 0%, #2980B9 100%);
}

.investments-card::before {
    background: var(--gradient-primary);
}

.transactions-card::before {
    background: linear-gradient(135deg, #F39C12 0%, #E67E22 100%);
}

.finance-card::before {
    background: linear-gradient(135deg, #9B59B6 0%, #8E44AD 100%);
}

.energy-card::before {
    background: linear-gradient(135deg, #27AE60 0%, #2ECC71 100%);
}

.admin-stat-card .stat-icon {
    width: 60px;
    height: 60px;
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--text-2xl);
    color: var(--white);
    margin-bottom: var(--space-4);
}

.users-card .stat-icon {
    background: linear-gradient(135deg, #3498DB 0%, #2980B9 100%);
}

.investments-card .stat-icon {
    background: var(--gradient-primary);
}

.transactions-card .stat-icon {
    background: linear-gradient(135deg, #F39C12 0%, #E67E22 100%);
}

.finance-card .stat-icon {
    background: linear-gradient(135deg, #9B59B6 0%, #8E44AD 100%);
}

.energy-card .stat-icon {
    background: linear-gradient(135deg, #27AE60 0%, #2ECC71 100%);
}

.admin-stat-card .stat-content h3 {
    margin: 0 0 var(--space-2) 0;
    font-size: var(--text-base);
    color: var(--gray-600);
    font-weight: 500;
}

.admin-stat-card .stat-value {
    font-size: var(--text-3xl);
    font-weight: 700;
    color: var(--gray-900);
    margin-bottom: var(--space-2);
}

.stat-action {
    margin-top: var(--space-4);
}

.finance-stats {
    display: flex;
    flex-direction: column;
    gap: var(--space-2);
}

.finance-item {
    display: flex;
    justify-content: space-between;
    font-size: var(--text-sm);
}

.finance-item span:first-child {
    color: var(--gray-600);
}

.finance-item span:last-child {
    font-weight: 600;
    color: var(--gray-900);
}

/* Графики */
.charts-section {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--space-6);
    margin-bottom: var(--space-8);
}

.chart-card {
    background: var(--white);
    border-radius: var(--radius-lg);
    padding: var(--space-6);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--gray-200);
}

.chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--space-6);
}

.chart-header h3 {
    margin: 0;
    color: var(--gray-900);
}

.chart-container {
    height: 300px;
    position: relative;
}

/* Таблицы админки */
.admin-tables {
    display: grid;
    gap: var(--space-6);
}

.admin-table-card {
    background: var(--white);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--gray-200);
    overflow: hidden;
}

.table-header {
    padding: var(--space-6);
    border-bottom: 1px solid var(--gray-200);
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: var(--gray-50);
}

.table-header h3 {
    margin: 0;
    color: var(--gray-900);
    display: flex;
    align-items: center;
    gap: var(--space-2);
}

.table-container {
    overflow-x: auto;
}

.admin-table {
    width: 100%;
    border-collapse: collapse;
}

.admin-table th {
    background: var(--gray-100);
    padding: var(--space-4);
    text-align: left;
    font-weight: 600;
    color: var(--gray-700);
    border-bottom: 1px solid var(--gray-200);
    font-size: var(--text-sm);
}

.admin-table td {
    padding: var(--space-4);
    border-bottom: 1px solid var(--gray-200);
    vertical-align: middle;
}

.admin-table tr:hover {
    background: var(--gray-50);
}

.user-info {
    display: flex;
    flex-direction: column;
}

.user-info strong {
    color: var(--gray-900);
    margin-bottom: 2px;
}

.user-info small {
    color: var(--gray-500);
    font-size: var(--text-xs);
}

.transaction-type {
    padding: var(--space-1) var(--space-2);
    border-radius: var(--radius-sm);
    font-size: var(--text-xs);
    font-weight: 500;
}

.type-deposit {
    background: rgba(40, 167, 69, 0.2);
    color: var(--success);
}

.type-withdrawal {
    background: rgba(220, 53, 69, 0.2);
    color: var(--error);
}

.type-investment {
    background: rgba(46, 204, 113, 0.2);
    color: var(--primary-green);
}

.amount {
    font-weight: 600;
    color: var(--gray-900);
}

.amount.invested {
    color: var(--primary-green);
}

.action-buttons {
    display: flex;
    gap: var(--space-2);
}

.btn-small {
    padding: var(--space-1) var(--space-2);
    font-size: var(--text-xs);
    min-width: auto;
}

.rank {
    display: flex;
    align-items: center;
    gap: var(--space-1);
    font-weight: 600;
}

.rank-1 {
    color: #FFD700;
}

.rank-2 {
    color: #C0C0C0;
}

.rank-3 {
    color: #CD7F32;
}

/* Подвал админской боковой панели */
.sidebar-footer {
    padding: var(--space-4);
    border-top: 1px solid #444;
}

.admin-quick-stats {
    margin-bottom: var(--space-4);
}

.quick-stat {
    display: flex;
    align-items: center;
    gap: var(--space-2);
    margin-bottom: var(--space-2);
    font-size: var(--text-xs);
    color: #ccc;
}

.quick-stat i {
    color: var(--primary-green);
    width: 16px;
}

.admin-actions {
    display: flex;
    gap: var(--space-2);
}

.admin-action {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--space-1);
    padding: var(--space-2);
    color: #ccc;
    text-decoration: none;
    border-radius: var(--radius-md);
    transition: all var(--transition-fast);
    font-size: var(--text-xs);
}

.admin-action:hover {
    background: rgba(46, 204, 113, 0.1);
    color: var(--white);
}

.admin-action.logout:hover {
    background: rgba(220, 53, 69, 0.1);
    color: var(--error);
}

/* Адаптивность */
@media (max-width: 1200px) {
    .charts-section {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .admin-stats-grid {
        grid-template-columns: 1fr;
    }
    
    .admin-stat-card {
        padding: var(--space-4);
    }
    
    .chart-card {
        padding: var(--space-4);
    }
    
    .table-header {
        padding: var(--space-4);
        flex-direction: column;
        gap: var(--space-2);
        align-items: flex-start;
    }
    
    .admin-table {
        font-size: var(--text-sm);
    }
    
    .admin-table th,
    .admin-table td {
        padding: var(--space-2);
    }
    
    .action-buttons {
        flex-direction: column;
    }
}

@media (max-width: 480px) {
    .admin-stat-card .stat-value {
        font-size: var(--text-2xl);
    }
    
    .finance-stats {
        font-size: var(--text-xs);
    }
    
    .user-info {
        font-size: var(--text-xs);
    }

    .simulation-grid {
        grid-template-columns: 1fr;
    }

    .simulation-card .card-header,
    .simulation-card .card-body {
        padding: var(--space-3);
    }
}

/* Simulation Styles */
.simulation-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--space-6);
    margin-bottom: var(--space-6);
}

.simulation-card {
    background: var(--white);
    border-radius: var(--radius-lg);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    transition: all 0.3s ease;
}

.simulation-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
}

.simulation-card.full-width {
    grid-column: 1 / -1;
}

.simulation-card .card-header {
    background: linear-gradient(135deg, var(--primary-green) 0%, var(--primary-green-dark) 100%);
    color: var(--white);
    padding: var(--space-4);
    border-bottom: none;
}

.simulation-card .card-header h3 {
    margin: 0;
    display: flex;
    align-items: center;
    gap: var(--space-2);
    font-size: var(--text-lg);
}

.simulation-card .card-body {
    padding: var(--space-4);
}

.simulation-card .card-body p {
    color: var(--gray-600);
    margin-bottom: var(--space-4);
    line-height: 1.6;
}

.simulation-form {
    display: flex;
    flex-direction: column;
    gap: var(--space-3);
}

.simulation-form .form-group {
    margin-bottom: 0;
}

.simulation-form label {
    font-weight: 600;
    color: var(--dark);
    margin-bottom: var(--space-2);
}

.simulation-form input[type="number"] {
    padding: var(--space-3);
    border: 2px solid var(--gray-300);
    border-radius: var(--radius-md);
    font-size: var(--text-base);
    transition: border-color 0.3s ease;
}

.simulation-form input[type="number"]:focus {
    border-color: var(--primary-green);
    outline: none;
}

.simulation-results {
    margin-top: var(--space-6);
}

.simulation-results pre {
    background: var(--gray-100);
    padding: var(--space-4);
    border-radius: var(--radius-md);
    overflow-x: auto;
    font-size: var(--text-sm);
    line-height: 1.5;
    color: var(--dark);
    border: 1px solid var(--gray-300);
}

/* Database Setup Styles */
.database-setup-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--space-6);
    margin-bottom: var(--space-6);
}

.setup-card {
    background: var(--white);
    border-radius: var(--radius-lg);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    transition: all 0.3s ease;
}

.setup-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
}

.setup-card.danger-card {
    border: 2px solid var(--error-color);
}

.setup-card.danger-card .card-header {
    background: linear-gradient(135deg, var(--error-color) 0%, #c53030 100%);
}

.setup-card .card-header {
    background: linear-gradient(135deg, var(--primary-green) 0%, var(--primary-green-dark) 100%);
    color: var(--white);
    padding: var(--space-4);
    border-bottom: none;
}

.setup-card .card-header h3 {
    margin: 0;
    display: flex;
    align-items: center;
    gap: var(--space-2);
    font-size: var(--text-lg);
}

.setup-card .card-body {
    padding: var(--space-4);
}

.setup-card .card-body p {
    color: var(--gray-600);
    margin-bottom: var(--space-4);
    line-height: 1.6;
}

.setup-form {
    display: flex;
    flex-direction: column;
    gap: var(--space-3);
}

.setup-form .form-group {
    margin-bottom: 0;
}

.setup-form label {
    display: flex;
    align-items: center;
    gap: var(--space-2);
    font-weight: 500;
    color: var(--dark);
    cursor: pointer;
}

.setup-form input[type="checkbox"] {
    width: 18px;
    height: 18px;
    accent-color: var(--primary-green);
}

.setup-results {
    margin-top: var(--space-6);
}

.setup-results .results-summary {
    background: var(--gray-50);
    padding: var(--space-4);
    border-radius: var(--radius-md);
    margin-bottom: var(--space-4);
}

.setup-results .results-summary h4 {
    margin-bottom: var(--space-2);
    color: var(--dark);
}

.setup-results .results-summary ul {
    margin: 0;
    padding-left: var(--space-4);
}

.setup-results .results-summary li {
    margin-bottom: var(--space-1);
    color: var(--gray-700);
}

.setup-results .results-details h4 {
    margin-bottom: var(--space-2);
    color: var(--dark);
}

.setup-results pre {
    background: var(--gray-100);
    padding: var(--space-4);
    border-radius: var(--radius-md);
    overflow-x: auto;
    font-size: var(--text-sm);
    line-height: 1.5;
    color: var(--dark);
    border: 1px solid var(--gray-300);
    max-height: 400px;
    overflow-y: auto;
}

/* Logs Styles */
.logs-controls {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    gap: var(--space-4);
    margin-bottom: var(--space-6);
    flex-wrap: wrap;
}

.logs-actions {
    display: flex;
    gap: var(--space-2);
}

.logs-table {
    font-size: var(--text-sm);
}

.logs-table th,
.logs-table td {
    padding: var(--space-2) var(--space-3);
    vertical-align: top;
}

.log-row {
    transition: background-color 0.3s ease;
}

.log-row:hover {
    background: var(--gray-50);
}

.log-row.log-error {
    border-left: 3px solid var(--error-color);
}

.log-row.log-warning {
    border-left: 3px solid var(--warning-color);
}

.log-row.log-success {
    border-left: 3px solid var(--success-color);
}

.log-row.log-info {
    border-left: 3px solid var(--info-color);
}

.log-date {
    font-family: 'Courier New', monospace;
    font-size: var(--text-xs);
    color: var(--gray-600);
    white-space: nowrap;
}

.log-user .user-info {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.log-user .user-info strong {
    font-size: var(--text-sm);
    color: var(--dark);
}

.log-user .user-info small {
    font-size: var(--text-xs);
    color: var(--gray-500);
}

.action-badge {
    display: inline-block;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: var(--text-xs);
    font-weight: 500;
    text-transform: uppercase;
}

.action-user {
    background: var(--info-color);
    color: var(--white);
}

.action-admin {
    background: var(--warning-color);
    color: var(--white);
}

.action-transaction {
    background: var(--success-color);
    color: var(--white);
}

.action-security {
    background: var(--error-color);
    color: var(--white);
}

.action-system {
    background: var(--gray-500);
    color: var(--white);
}

.log-description {
    max-width: 300px;
    word-wrap: break-word;
    line-height: 1.4;
}

.log-ip {
    font-family: 'Courier New', monospace;
    font-size: var(--text-xs);
    color: var(--gray-600);
}

.log-details-content {
    display: grid;
    gap: var(--space-4);
}

.detail-group {
    display: grid;
    grid-template-columns: 150px 1fr;
    gap: var(--space-2);
    align-items: start;
}

.detail-group label {
    font-weight: 600;
    color: var(--gray-700);
}

.detail-group span {
    color: var(--dark);
    word-wrap: break-word;
}

.detail-group pre {
    background: var(--gray-100);
    padding: var(--space-3);
    border-radius: var(--radius-md);
    font-size: var(--text-xs);
    line-height: 1.4;
    overflow-x: auto;
    max-height: 200px;
    overflow-y: auto;
    border: 1px solid var(--gray-300);
}

.modal-large {
    max-width: 800px;
}

@media (max-width: 768px) {
    .logs-controls {
        flex-direction: column;
        align-items: stretch;
    }

    .logs-actions {
        justify-content: center;
    }

    .logs-table {
        font-size: var(--text-xs);
    }

    .logs-table th,
    .logs-table td {
        padding: var(--space-1) var(--space-2);
    }

    .log-description {
        max-width: 200px;
    }

    .detail-group {
        grid-template-columns: 1fr;
        gap: var(--space-1);
    }

    .modal-large {
        max-width: 95vw;
    }
}

/* Export Styles */
.export-intro {
    margin-bottom: var(--space-6);
}

.export-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: var(--space-6);
    margin-bottom: var(--space-6);
}

.export-card {
    background: var(--white);
    border-radius: var(--radius-lg);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    transition: all 0.3s ease;
}

.export-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
}

.export-card.full-width {
    grid-column: 1 / -1;
}

.export-card .card-header {
    background: linear-gradient(135deg, var(--primary-green) 0%, var(--primary-green-dark) 100%);
    color: var(--white);
    padding: var(--space-4);
    border-bottom: none;
}

.export-card .card-header h3 {
    margin: 0;
    display: flex;
    align-items: center;
    gap: var(--space-2);
    font-size: var(--text-lg);
}

.export-card .card-body {
    padding: var(--space-4);
}

.export-card .card-body p {
    color: var(--gray-600);
    margin-bottom: var(--space-4);
    line-height: 1.6;
}

.export-form {
    display: flex;
    flex-direction: column;
    gap: var(--space-3);
}

.export-form .form-group {
    margin-bottom: 0;
}

.export-form label {
    font-weight: 600;
    color: var(--dark);
    margin-bottom: var(--space-2);
}

.export-form input,
.export-form select {
    padding: var(--space-3);
    border: 2px solid var(--gray-300);
    border-radius: var(--radius-md);
    font-size: var(--text-base);
    transition: border-color 0.3s ease;
    width: 100%;
}

.export-form input:focus,
.export-form select:focus {
    border-color: var(--primary-green);
    outline: none;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--space-3);
}

.backup-options {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--space-4);
}

.backup-option {
    background: var(--gray-50);
    padding: var(--space-4);
    border-radius: var(--radius-md);
    text-align: center;
    border: 2px solid var(--gray-200);
    transition: all 0.3s ease;
}

.backup-option:hover {
    border-color: var(--primary-green);
    background: var(--white);
}

.backup-option h4 {
    margin-bottom: var(--space-2);
    color: var(--dark);
}

.backup-option p {
    color: var(--gray-600);
    margin-bottom: var(--space-3);
    font-size: var(--text-sm);
}

.export-status {
    margin-top: var(--space-6);
}

.status-content {
    display: flex;
    align-items: center;
    gap: var(--space-4);
}

.status-icon {
    font-size: 32px;
    color: var(--primary-green);
}

.status-text h4 {
    margin-bottom: var(--space-1);
    color: var(--dark);
}

.status-text p {
    color: var(--gray-600);
    margin: 0;
}

@media (max-width: 768px) {
    .export-grid {
        grid-template-columns: 1fr;
    }

    .form-row {
        grid-template-columns: 1fr;
    }

    .backup-options {
        grid-template-columns: 1fr;
    }

    .status-content {
        flex-direction: column;
        text-align: center;
        gap: var(--space-2);
    }
}

/* Test Styles */
.test-controls {
    margin-bottom: var(--space-6);
}

.test-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: var(--space-3);
    margin-top: var(--space-4);
}

.test-results {
    margin-bottom: var(--space-6);
}

.test-category {
    margin-bottom: var(--space-6);
}

.test-category h4 {
    color: var(--dark);
    margin-bottom: var(--space-3);
    padding-bottom: var(--space-2);
    border-bottom: 2px solid var(--gray-200);
    text-transform: capitalize;
}

.test-item {
    display: flex;
    align-items: center;
    gap: var(--space-3);
    padding: var(--space-3);
    margin-bottom: var(--space-2);
    border-radius: var(--radius-md);
    border-left: 4px solid;
    background: var(--white);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.test-item.test-success {
    border-left-color: var(--success-color);
    background: rgba(34, 197, 94, 0.05);
}

.test-item.test-warning {
    border-left-color: var(--warning-color);
    background: rgba(245, 158, 11, 0.05);
}

.test-item.test-error {
    border-left-color: var(--error-color);
    background: rgba(239, 68, 68, 0.05);
}

.test-icon {
    font-size: 20px;
    width: 24px;
    text-align: center;
}

.test-success .test-icon {
    color: var(--success-color);
}

.test-warning .test-icon {
    color: var(--warning-color);
}

.test-error .test-icon {
    color: var(--error-color);
}

.test-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: var(--space-1);
}

.test-name {
    font-weight: 600;
    color: var(--dark);
    font-size: var(--text-base);
}

.test-message {
    color: var(--gray-600);
    font-size: var(--text-sm);
    line-height: 1.4;
}

.system-info {
    margin-bottom: var(--space-6);
}

.info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--space-4);
}

.info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--space-3);
    background: var(--gray-50);
    border-radius: var(--radius-md);
    border: 1px solid var(--gray-200);
}

.info-item label {
    font-weight: 600;
    color: var(--gray-700);
}

.info-item span {
    color: var(--dark);
    font-family: 'Courier New', monospace;
    font-size: var(--text-sm);
}

@media (max-width: 768px) {
    .test-buttons {
        flex-direction: column;
        align-items: stretch;
    }

    .test-buttons form {
        width: 100%;
    }

    .test-buttons button {
        width: 100%;
    }

    .info-grid {
        grid-template-columns: 1fr;
    }

    .info-item {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--space-1);
    }
}

/* CMS Styles */
.cms-controls {
    margin-bottom: var(--space-6);
}

.cms-pages-table .page-title {
    display: flex;
    flex-direction: column;
    gap: var(--space-1);
}

.cms-pages-table .page-title strong {
    color: var(--dark);
    font-weight: 600;
}

.cms-pages-table .page-excerpt {
    color: var(--gray-600);
    font-size: var(--text-sm);
    line-height: 1.4;
}

.cms-pages-table .page-slug {
    font-family: 'Courier New', monospace;
    font-size: var(--text-sm);
    background: var(--gray-100);
    padding: 2px 6px;
    border-radius: 4px;
    color: var(--gray-700);
}

.status-badge {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: var(--text-xs);
    font-weight: 500;
    text-transform: uppercase;
}

.status-published {
    background: var(--success-color);
    color: var(--white);
}

.status-draft {
    background: var(--warning-color);
    color: var(--white);
}

.status-archived {
    background: var(--gray-500);
    color: var(--white);
}

.badge {
    display: inline-block;
    padding: 2px 6px;
    border-radius: 8px;
    font-size: var(--text-xs);
    font-weight: 500;
    margin-left: var(--space-1);
}

.badge-info {
    background: var(--info-color);
    color: var(--white);
}

.badge-secondary {
    background: var(--gray-400);
    color: var(--white);
}

.bulk-actions {
    display: flex;
    align-items: center;
    gap: var(--space-2);
    margin-bottom: var(--space-4);
    padding: var(--space-3);
    background: var(--gray-50);
    border-radius: var(--radius-md);
}

.bulk-actions select {
    padding: var(--space-2);
    border: 1px solid var(--gray-300);
    border-radius: var(--radius-sm);
    background: var(--white);
}

/* CMS Page Edit Form */
.cms-page-form {
    max-width: none;
}

.form-grid {
    display: grid;
    grid-template-columns: 1fr 350px;
    gap: var(--space-6);
}

.form-main {
    display: flex;
    flex-direction: column;
    gap: var(--space-6);
}

.form-sidebar {
    display: flex;
    flex-direction: column;
    gap: var(--space-4);
}

.form-card {
    background: var(--white);
    border-radius: var(--radius-lg);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.form-card-header {
    background: linear-gradient(135deg, var(--primary-green) 0%, var(--primary-green-dark) 100%);
    color: var(--white);
    padding: var(--space-4);
}

.form-card-header h3 {
    margin: 0;
    display: flex;
    align-items: center;
    gap: var(--space-2);
    font-size: var(--text-lg);
}

.form-card-body {
    padding: var(--space-4);
}

.form-group {
    margin-bottom: var(--space-4);
}

.form-group:last-child {
    margin-bottom: 0;
}

.form-group label {
    display: block;
    font-weight: 600;
    color: var(--dark);
    margin-bottom: var(--space-2);
}

.form-control {
    width: 100%;
    padding: var(--space-3);
    border: 2px solid var(--gray-300);
    border-radius: var(--radius-md);
    font-size: var(--text-base);
    transition: border-color 0.3s ease;
    background: var(--white);
}

.form-control:focus {
    border-color: var(--primary-green);
    outline: none;
    box-shadow: 0 0 0 3px rgba(34, 197, 94, 0.1);
}

.form-help {
    display: block;
    margin-top: var(--space-1);
    font-size: var(--text-sm);
    color: var(--gray-600);
}

.input-group {
    display: flex;
    align-items: stretch;
}

.input-group-text {
    background: var(--gray-100);
    border: 2px solid var(--gray-300);
    border-right: none;
    padding: var(--space-3);
    font-size: var(--text-sm);
    color: var(--gray-600);
    border-radius: var(--radius-md) 0 0 var(--radius-md);
    white-space: nowrap;
}

.input-group .form-control {
    border-radius: 0 var(--radius-md) var(--radius-md) 0;
    border-left: none;
}

.checkbox-label {
    display: flex;
    align-items: center;
    gap: var(--space-2);
    cursor: pointer;
    font-weight: normal;
}

.checkbox-label input[type="checkbox"] {
    display: none;
}

.checkbox-custom {
    width: 20px;
    height: 20px;
    border: 2px solid var(--gray-300);
    border-radius: 4px;
    position: relative;
    transition: all 0.3s ease;
}

.checkbox-label input[type="checkbox"]:checked + .checkbox-custom {
    background: var(--primary-green);
    border-color: var(--primary-green);
}

.checkbox-label input[type="checkbox"]:checked + .checkbox-custom::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: var(--white);
    font-size: 12px;
    font-weight: bold;
}

.code-editor {
    font-family: 'Courier New', monospace;
    font-size: var(--text-sm);
    line-height: 1.5;
}

.image-preview {
    margin-top: var(--space-3);
    border: 1px solid var(--gray-300);
    border-radius: var(--radius-md);
    overflow: hidden;
}

.form-actions {
    position: sticky;
    top: var(--space-4);
    display: flex;
    flex-direction: column;
    gap: var(--space-2);
}

@media (max-width: 1024px) {
    .form-grid {
        grid-template-columns: 1fr;
    }

    .form-sidebar {
        order: -1;
    }

    .form-actions {
        position: static;
        flex-direction: row;
        justify-content: space-between;
    }
}

@media (max-width: 768px) {
    .cms-controls .filters-form {
        flex-direction: column;
        gap: var(--space-2);
    }

    .bulk-actions {
        flex-direction: column;
        align-items: stretch;
    }

    .form-actions {
        flex-direction: column;
    }

    .input-group {
        flex-direction: column;
    }

    .input-group-text {
        border-right: 2px solid var(--gray-300);
        border-bottom: none;
        border-radius: var(--radius-md) var(--radius-md) 0 0;
    }

    .input-group .form-control {
        border-left: 2px solid var(--gray-300);
        border-top: none;
        border-radius: 0 0 var(--radius-md) var(--radius-md);
    }
}

/* Media Management Styles */
.media-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--space-6);
    gap: var(--space-4);
}

.view-toggle {
    display: flex;
    gap: var(--space-1);
    background: var(--gray-100);
    border-radius: var(--radius-md);
    padding: 4px;
}

.view-btn {
    padding: var(--space-2);
    border: none;
    background: transparent;
    border-radius: var(--radius-sm);
    cursor: pointer;
    transition: all 0.3s ease;
    color: var(--gray-600);
}

.view-btn.active,
.view-btn:hover {
    background: var(--white);
    color: var(--primary-green);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.media-container {
    margin-bottom: var(--space-6);
}

.media-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: var(--space-4);
}

.media-grid.media-list {
    grid-template-columns: 1fr;
}

.media-item {
    background: var(--white);
    border-radius: var(--radius-lg);
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    position: relative;
}

.media-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.media-list .media-item {
    display: grid;
    grid-template-columns: 80px 1fr auto;
    gap: var(--space-3);
    padding: var(--space-3);
    align-items: center;
}

.media-preview {
    position: relative;
    aspect-ratio: 1;
    overflow: hidden;
    background: var(--gray-100);
    display: flex;
    align-items: center;
    justify-content: center;
}

.media-list .media-preview {
    aspect-ratio: 1;
    width: 80px;
    height: 80px;
    border-radius: var(--radius-md);
}

.media-preview img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.media-item:hover .media-preview img {
    transform: scale(1.05);
}

.file-icon {
    font-size: 48px;
    color: var(--gray-400);
}

.media-list .file-icon {
    font-size: 32px;
}

.media-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.media-item:hover .media-overlay {
    opacity: 1;
}

.media-actions {
    display: flex;
    gap: var(--space-2);
}

.action-btn {
    width: 36px;
    height: 36px;
    border: none;
    border-radius: 50%;
    background: var(--white);
    color: var(--dark);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    font-size: 14px;
}

.action-btn:hover {
    background: var(--primary-green);
    color: var(--white);
    transform: scale(1.1);
}

.action-btn.delete-btn:hover {
    background: var(--error-color);
}

.media-info {
    padding: var(--space-3);
}

.media-list .media-info {
    padding: 0;
}

.media-title {
    font-weight: 600;
    color: var(--dark);
    margin-bottom: var(--space-1);
    font-size: var(--text-sm);
}

.media-meta {
    display: flex;
    gap: var(--space-2);
    font-size: var(--text-xs);
    color: var(--gray-600);
    margin-bottom: var(--space-1);
}

.media-date {
    font-size: var(--text-xs);
    color: var(--gray-500);
}

/* Upload Modal Styles */
.upload-area {
    border: 2px dashed var(--gray-300);
    border-radius: var(--radius-lg);
    padding: var(--space-8);
    text-align: center;
    transition: all 0.3s ease;
    cursor: pointer;
}

.upload-area:hover,
.upload-area.drag-over {
    border-color: var(--primary-green);
    background: var(--gray-50);
}

.upload-content i {
    font-size: 48px;
    color: var(--gray-400);
    margin-bottom: var(--space-4);
}

.upload-content h4 {
    color: var(--dark);
    margin-bottom: var(--space-2);
}

.upload-content p {
    color: var(--gray-600);
    margin-bottom: var(--space-4);
}

.upload-preview {
    margin-top: var(--space-4);
    padding-top: var(--space-4);
    border-top: 1px solid var(--gray-200);
}

.preview-list {
    display: flex;
    flex-direction: column;
    gap: var(--space-2);
    max-height: 200px;
    overflow-y: auto;
}

.preview-item {
    display: flex;
    align-items: center;
    gap: var(--space-2);
    padding: var(--space-2);
    background: var(--gray-50);
    border-radius: var(--radius-md);
}

.preview-item i {
    color: var(--gray-500);
}

.preview-item span {
    flex: 1;
    font-weight: 500;
}

.preview-item small {
    color: var(--gray-600);
}

/* Notification Styles */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: var(--space-3) var(--space-4);
    border-radius: var(--radius-md);
    color: var(--white);
    font-weight: 500;
    z-index: 10000;
    display: flex;
    align-items: center;
    gap: var(--space-2);
    animation: slideIn 0.3s ease;
}

.notification.success {
    background: var(--success-color);
}

.notification.error {
    background: var(--error-color);
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@media (max-width: 768px) {
    .media-controls {
        flex-direction: column;
        align-items: stretch;
    }

    .media-grid {
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
        gap: var(--space-3);
    }

    .media-list .media-item {
        grid-template-columns: 60px 1fr;
        gap: var(--space-2);
    }

    .media-list .media-preview {
        width: 60px;
        height: 60px;
    }

    .upload-area {
        padding: var(--space-6);
    }

    .upload-content i {
        font-size: 36px;
    }

    .media-actions {
        flex-wrap: wrap;
    }

    .action-btn {
        width: 32px;
        height: 32px;
        font-size: 12px;
    }
}

/* Enhanced Simulation Styles */
.checkbox-group {
    display: flex;
    flex-direction: column;
    gap: var(--space-2);
    margin-top: var(--space-2);
}

.checkbox-group .checkbox-label {
    font-weight: normal;
    font-size: var(--text-sm);
}

.simulation-form .form-group {
    margin-bottom: var(--space-4);
}

.simulation-form .form-group:last-of-type {
    margin-bottom: var(--space-6);
}

.simulation-form label {
    display: block;
    font-weight: 600;
    color: var(--dark);
    margin-bottom: var(--space-2);
    font-size: var(--text-sm);
}

.simulation-form input[type="number"],
.simulation-form select {
    width: 100%;
    padding: var(--space-3);
    border: 2px solid var(--gray-300);
    border-radius: var(--radius-md);
    font-size: var(--text-base);
    transition: border-color 0.3s ease;
    background: var(--white);
}

.simulation-form input[type="number"]:focus,
.simulation-form select:focus {
    border-color: var(--primary-green);
    outline: none;
    box-shadow: 0 0 0 3px rgba(34, 197, 94, 0.1);
}

.simulation-card .btn {
    width: 100%;
    justify-content: center;
    font-weight: 600;
    padding: var(--space-3) var(--space-4);
}

.simulation-card .btn i {
    margin-right: var(--space-2);
}

/* Results Display */
.simulation-results {
    margin-top: var(--space-6);
    padding: var(--space-4);
    background: var(--gray-50);
    border-radius: var(--radius-lg);
    border: 1px solid var(--gray-200);
}

.simulation-results h4 {
    color: var(--dark);
    margin-bottom: var(--space-3);
    display: flex;
    align-items: center;
    gap: var(--space-2);
}

.simulation-results .results-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--space-4);
}

.result-item {
    background: var(--white);
    padding: var(--space-3);
    border-radius: var(--radius-md);
    border: 1px solid var(--gray-200);
    text-align: center;
}

.result-item .result-value {
    font-size: var(--text-xl);
    font-weight: 700;
    color: var(--primary-green);
    display: block;
    margin-bottom: var(--space-1);
}

.result-item .result-label {
    font-size: var(--text-sm);
    color: var(--gray-600);
}

/* Auto Activity Status */
.auto-activity-status {
    background: linear-gradient(135deg, var(--warning-color) 0%, #f59e0b 100%);
    color: var(--white);
    padding: var(--space-4);
    border-radius: var(--radius-lg);
    margin-bottom: var(--space-4);
    display: flex;
    align-items: center;
    gap: var(--space-3);
}

.auto-activity-status .status-icon {
    font-size: 24px;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
}

.auto-activity-status .status-content h4 {
    margin: 0 0 var(--space-1) 0;
    color: var(--white);
}

.auto-activity-status .status-content p {
    margin: 0;
    opacity: 0.9;
}

/* Enhanced Button Styles */
.btn-warning {
    background: linear-gradient(135deg, var(--warning-color) 0%, #f59e0b 100%);
    color: var(--white);
    border: none;
}

.btn-warning:hover {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    transform: translateY(-1px);
}

.btn-success {
    background: linear-gradient(135deg, var(--success-color) 0%, #16a34a 100%);
    color: var(--white);
    border: none;
}

.btn-success:hover {
    background: linear-gradient(135deg, #16a34a 0%, #15803d 100%);
    transform: translateY(-1px);
}

/* Progress Indicators */
.progress-bar {
    width: 100%;
    height: 8px;
    background: var(--gray-200);
    border-radius: 4px;
    overflow: hidden;
    margin-top: var(--space-2);
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-green) 0%, var(--primary-green-dark) 100%);
    border-radius: 4px;
    transition: width 0.3s ease;
}

/* Responsive Enhancements */
@media (max-width: 768px) {
    .checkbox-group {
        gap: var(--space-3);
    }

    .simulation-results .results-grid {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: var(--space-3);
    }

    .auto-activity-status {
        flex-direction: column;
        text-align: center;
        gap: var(--space-2);
    }

    .simulation-form input[type="number"],
    .simulation-form select {
        font-size: 16px; /* Предотвращает зум на iOS */
    }
}
