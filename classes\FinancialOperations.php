<?php
/**
 * AstroGenix - Управление финансовыми операциями
 * Централизованная система для всех финансовых операций
 */

class FinancialOperations {
    private $db;
    
    public function __construct($database) {
        $this->db = $database;
    }
    
    /**
     * Получение баланса пользователя
     */
    public function getUserBalance($user_id) {
        try {
            $query = "SELECT balance, total_invested, total_earned, pending_withdrawals 
                      FROM users WHERE id = :user_id";
            $stmt = $this->db->prepare($query);
            $stmt->bindParam(':user_id', $user_id, PDO::PARAM_INT);
            $stmt->execute();
            
            return $stmt->fetch(PDO::FETCH_ASSOC);
        } catch (Exception $e) {
            error_log("Get user balance error: " . $e->getMessage());
            return null;
        }
    }
    
    /**
     * Создание запроса на вывод средств
     */
    public function createWithdrawalRequest($user_id, $amount, $payment_method, $payment_details) {
        try {
            $this->db->beginTransaction();
            
            // Проверка баланса
            $balance = $this->getUserBalance($user_id);
            if (!$balance || $balance['balance'] < $amount) {
                throw new Exception('Недостаточно средств на балансе');
            }
            
            // Проверка минимальной суммы вывода
            $min_withdrawal = $this->getMinWithdrawalAmount();
            if ($amount < $min_withdrawal) {
                throw new Exception("Минимальная сумма вывода: {$min_withdrawal} USDT");
            }
            
            // Проверка лимитов вывода
            if (!$this->checkWithdrawalLimits($user_id, $amount)) {
                throw new Exception('Превышены лимиты вывода');
            }
            
            // Расчет комиссии
            $fee = $this->calculateWithdrawalFee($amount, $payment_method);
            $net_amount = $amount - $fee;
            
            // Создание запроса на вывод
            $insert_query = "INSERT INTO withdrawal_requests 
                            (user_id, amount, fee, net_amount, payment_method, payment_details, status) 
                            VALUES 
                            (:user_id, :amount, :fee, :net_amount, :payment_method, :payment_details, 'pending')";
            
            $stmt = $this->db->prepare($insert_query);
            $stmt->bindParam(':user_id', $user_id, PDO::PARAM_INT);
            $stmt->bindParam(':amount', $amount);
            $stmt->bindParam(':fee', $fee);
            $stmt->bindParam(':net_amount', $net_amount);
            $stmt->bindParam(':payment_method', $payment_method);
            $stmt->bindParam(':payment_details', json_encode($payment_details));
            
            if (!$stmt->execute()) {
                throw new Exception('Ошибка создания запроса на вывод');
            }
            
            $withdrawal_id = $this->db->lastInsertId();
            
            // Блокировка средств на балансе
            $this->updateUserBalance($user_id, -$amount, 'withdrawal_pending');
            $this->updatePendingWithdrawals($user_id, $amount);
            
            // Создание транзакции
            $this->createTransaction($user_id, 'withdrawal', $amount, 'pending', 
                "Запрос на вывод #{$withdrawal_id}");
            
            // Создание уведомления
            $this->createNotification($user_id, 'withdrawal_created', 
                'Запрос на вывод создан', 
                "Создан запрос на вывод {$amount} USDT. Комиссия: {$fee} USDT");
            
            $this->db->commit();
            
            return [
                'success' => true,
                'withdrawal_id' => $withdrawal_id,
                'amount' => $amount,
                'fee' => $fee,
                'net_amount' => $net_amount
            ];
            
        } catch (Exception $e) {
            $this->db->rollback();
            error_log("Create withdrawal request error: " . $e->getMessage());
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Создание запроса на пополнение
     */
    public function createDepositRequest($user_id, $amount, $payment_method, $payment_details = []) {
        try {
            $this->db->beginTransaction();
            
            // Проверка минимальной суммы пополнения
            $min_deposit = $this->getMinDepositAmount();
            if ($amount < $min_deposit) {
                throw new Exception("Минимальная сумма пополнения: {$min_deposit} USDT");
            }
            
            // Генерация уникального адреса/реквизитов для пополнения
            $deposit_address = $this->generateDepositAddress($payment_method);
            
            // Создание запроса на пополнение
            $insert_query = "INSERT INTO deposit_requests 
                            (user_id, amount, payment_method, payment_details, deposit_address, status) 
                            VALUES 
                            (:user_id, :amount, :payment_method, :payment_details, :deposit_address, 'pending')";
            
            $stmt = $this->db->prepare($insert_query);
            $stmt->bindParam(':user_id', $user_id, PDO::PARAM_INT);
            $stmt->bindParam(':amount', $amount);
            $stmt->bindParam(':payment_method', $payment_method);
            $stmt->bindParam(':payment_details', json_encode($payment_details));
            $stmt->bindParam(':deposit_address', $deposit_address);
            
            if (!$stmt->execute()) {
                throw new Exception('Ошибка создания запроса на пополнение');
            }
            
            $deposit_id = $this->db->lastInsertId();
            
            // Создание транзакции
            $this->createTransaction($user_id, 'deposit', $amount, 'pending', 
                "Запрос на пополнение #{$deposit_id}");
            
            // Создание уведомления
            $this->createNotification($user_id, 'deposit_created', 
                'Запрос на пополнение создан', 
                "Создан запрос на пополнение {$amount} USDT через {$payment_method}");
            
            $this->db->commit();
            
            return [
                'success' => true,
                'deposit_id' => $deposit_id,
                'amount' => $amount,
                'deposit_address' => $deposit_address,
                'payment_method' => $payment_method
            ];
            
        } catch (Exception $e) {
            $this->db->rollback();
            error_log("Create deposit request error: " . $e->getMessage());
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Получение истории финансовых операций
     */
    public function getFinancialHistory($user_id, $type = null, $limit = 50, $offset = 0) {
        try {
            $where_clause = "WHERE user_id = :user_id";
            $params = [':user_id' => $user_id];
            
            if ($type) {
                $where_clause .= " AND type = :type";
                $params[':type'] = $type;
            }
            
            $query = "SELECT * FROM transactions 
                      {$where_clause}
                      ORDER BY created_at DESC 
                      LIMIT :limit OFFSET :offset";
            
            $stmt = $this->db->prepare($query);
            
            foreach ($params as $key => $value) {
                $stmt->bindValue($key, $value);
            }
            
            $stmt->bindValue(':limit', $limit, PDO::PARAM_INT);
            $stmt->bindValue(':offset', $offset, PDO::PARAM_INT);
            $stmt->execute();
            
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
            
        } catch (Exception $e) {
            error_log("Get financial history error: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Получение статистики финансовых операций
     */
    public function getFinancialStats($user_id, $period = 30) {
        try {
            $query = "SELECT 
                        COUNT(CASE WHEN type = 'deposit' AND status = 'completed' THEN 1 END) as deposits_count,
                        COUNT(CASE WHEN type = 'withdrawal' AND status = 'completed' THEN 1 END) as withdrawals_count,
                        COUNT(CASE WHEN type = 'investment' THEN 1 END) as investments_count,
                        COUNT(CASE WHEN type = 'profit' THEN 1 END) as profits_count,
                        COALESCE(SUM(CASE WHEN type = 'deposit' AND status = 'completed' THEN amount ELSE 0 END), 0) as total_deposits,
                        COALESCE(SUM(CASE WHEN type = 'withdrawal' AND status = 'completed' THEN amount ELSE 0 END), 0) as total_withdrawals,
                        COALESCE(SUM(CASE WHEN type = 'investment' THEN amount ELSE 0 END), 0) as total_investments,
                        COALESCE(SUM(CASE WHEN type = 'profit' THEN amount ELSE 0 END), 0) as total_profits
                      FROM transactions 
                      WHERE user_id = :user_id 
                      AND created_at >= DATE_SUB(CURDATE(), INTERVAL :period DAY)";
            
            $stmt = $this->db->prepare($query);
            $stmt->bindParam(':user_id', $user_id, PDO::PARAM_INT);
            $stmt->bindParam(':period', $period, PDO::PARAM_INT);
            $stmt->execute();
            
            $stats = $stmt->fetch(PDO::FETCH_ASSOC);
            
            // Расчет дополнительных метрик
            $stats['net_flow'] = $stats['total_deposits'] - $stats['total_withdrawals'];
            $stats['profit_margin'] = $stats['total_investments'] > 0 ? 
                round(($stats['total_profits'] / $stats['total_investments']) * 100, 2) : 0;
            
            return $stats;
            
        } catch (Exception $e) {
            error_log("Get financial stats error: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Получение доступных методов платежей
     */
    public function getPaymentMethods($type = 'all') {
        try {
            $where_clause = $type !== 'all' ? "WHERE type = :type OR type = 'both'" : "";
            
            $query = "SELECT * FROM payment_methods 
                      {$where_clause}
                      AND is_active = 1 
                      ORDER BY sort_order ASC";
            
            $stmt = $this->db->prepare($query);
            
            if ($type !== 'all') {
                $stmt->bindParam(':type', $type);
            }
            
            $stmt->execute();
            $methods = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            // Декодирование JSON настроек
            foreach ($methods as &$method) {
                $method['settings'] = json_decode($method['settings'], true) ?: [];
            }
            
            return $methods;
            
        } catch (Exception $e) {
            error_log("Get payment methods error: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Проверка лимитов вывода
     */
    private function checkWithdrawalLimits($user_id, $amount) {
        try {
            // Получение лимитов пользователя
            $limits_query = "SELECT daily_withdrawal_limit, monthly_withdrawal_limit 
                            FROM user_limits WHERE user_id = :user_id";
            $limits_stmt = $this->db->prepare($limits_query);
            $limits_stmt->bindParam(':user_id', $user_id, PDO::PARAM_INT);
            $limits_stmt->execute();
            $limits = $limits_stmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$limits) {
                // Создание лимитов по умолчанию
                $this->createDefaultLimits($user_id);
                return true;
            }
            
            // Проверка дневного лимита
            $daily_query = "SELECT COALESCE(SUM(amount), 0) as daily_total 
                           FROM withdrawal_requests 
                           WHERE user_id = :user_id 
                           AND DATE(created_at) = CURDATE() 
                           AND status IN ('pending', 'completed')";
            $daily_stmt = $this->db->prepare($daily_query);
            $daily_stmt->bindParam(':user_id', $user_id, PDO::PARAM_INT);
            $daily_stmt->execute();
            $daily_total = $daily_stmt->fetch(PDO::FETCH_ASSOC)['daily_total'];
            
            if (($daily_total + $amount) > $limits['daily_withdrawal_limit']) {
                return false;
            }
            
            // Проверка месячного лимита
            $monthly_query = "SELECT COALESCE(SUM(amount), 0) as monthly_total 
                             FROM withdrawal_requests 
                             WHERE user_id = :user_id 
                             AND created_at >= DATE_SUB(CURDATE(), INTERVAL 30 DAY) 
                             AND status IN ('pending', 'completed')";
            $monthly_stmt = $this->db->prepare($monthly_query);
            $monthly_stmt->bindParam(':user_id', $user_id, PDO::PARAM_INT);
            $monthly_stmt->execute();
            $monthly_total = $monthly_stmt->fetch(PDO::FETCH_ASSOC)['monthly_total'];
            
            if (($monthly_total + $amount) > $limits['monthly_withdrawal_limit']) {
                return false;
            }
            
            return true;
            
        } catch (Exception $e) {
            error_log("Check withdrawal limits error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Расчет комиссии за вывод
     */
    private function calculateWithdrawalFee($amount, $payment_method) {
        try {
            $method_query = "SELECT settings FROM payment_methods 
                            WHERE method_code = :method_code AND is_active = 1";
            $method_stmt = $this->db->prepare($method_query);
            $method_stmt->bindParam(':method_code', $payment_method);
            $method_stmt->execute();
            $method_data = $method_stmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$method_data) {
                return 0;
            }
            
            $settings = json_decode($method_data['settings'], true);
            $fee_percent = $settings['withdrawal_fee_percent'] ?? 2;
            $min_fee = $settings['min_withdrawal_fee'] ?? 1;
            $max_fee = $settings['max_withdrawal_fee'] ?? 50;
            
            $calculated_fee = ($amount * $fee_percent) / 100;
            
            return max($min_fee, min($max_fee, $calculated_fee));
            
        } catch (Exception $e) {
            error_log("Calculate withdrawal fee error: " . $e->getMessage());
            return 0;
        }
    }
    
    /**
     * Генерация адреса для пополнения
     */
    private function generateDepositAddress($payment_method) {
        // В реальной системе здесь будет интеграция с платежными системами
        switch ($payment_method) {
            case 'usdt_trc20':
                return 'TR' . bin2hex(random_bytes(16));
            case 'usdt_erc20':
                return '0x' . bin2hex(random_bytes(20));
            case 'bitcoin':
                return '1' . bin2hex(random_bytes(16));
            default:
                return 'ADDR_' . strtoupper(bin2hex(random_bytes(8)));
        }
    }
    
    /**
     * Вспомогательные методы
     */
    private function getMinWithdrawalAmount() {
        return 10; // Минимум 10 USDT
    }
    
    private function getMinDepositAmount() {
        return 5; // Минимум 5 USDT
    }
    
    private function updateUserBalance($user_id, $amount, $type = 'manual') {
        $query = "UPDATE users SET balance = balance + :amount WHERE id = :user_id";
        $stmt = $this->db->prepare($query);
        $stmt->bindParam(':amount', $amount);
        $stmt->bindParam(':user_id', $user_id, PDO::PARAM_INT);
        return $stmt->execute();
    }
    
    private function updatePendingWithdrawals($user_id, $amount) {
        $query = "UPDATE users SET pending_withdrawals = pending_withdrawals + :amount WHERE id = :user_id";
        $stmt = $this->db->prepare($query);
        $stmt->bindParam(':amount', $amount);
        $stmt->bindParam(':user_id', $user_id, PDO::PARAM_INT);
        return $stmt->execute();
    }
    
    private function createTransaction($user_id, $type, $amount, $status, $description) {
        $query = "INSERT INTO transactions (user_id, type, amount, status, description) 
                  VALUES (:user_id, :type, :amount, :status, :description)";
        $stmt = $this->db->prepare($query);
        $stmt->bindParam(':user_id', $user_id, PDO::PARAM_INT);
        $stmt->bindParam(':type', $type);
        $stmt->bindParam(':amount', $amount);
        $stmt->bindParam(':status', $status);
        $stmt->bindParam(':description', $description);
        return $stmt->execute();
    }
    
    private function createNotification($user_id, $type, $title, $message) {
        $query = "INSERT INTO notifications (user_id, type, title, message) 
                  VALUES (:user_id, :type, :title, :message)";
        $stmt = $this->db->prepare($query);
        $stmt->bindParam(':user_id', $user_id, PDO::PARAM_INT);
        $stmt->bindParam(':type', $type);
        $stmt->bindParam(':title', $title);
        $stmt->bindParam(':message', $message);
        return $stmt->execute();
    }
    
    private function createDefaultLimits($user_id) {
        $query = "INSERT INTO user_limits (user_id, daily_withdrawal_limit, monthly_withdrawal_limit) 
                  VALUES (:user_id, 1000, 10000)";
        $stmt = $this->db->prepare($query);
        $stmt->bindParam(':user_id', $user_id, PDO::PARAM_INT);
        return $stmt->execute();
    }
}
?>
