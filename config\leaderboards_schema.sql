-- AstroGenix - Структура БД для рейтингов и лидербордов
-- Система рейтингов пользователей по различным критериям

-- Таблица для хранения рейтингов пользователей
CREATE TABLE IF NOT EXISTS user_rankings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    ranking_type ENUM('referrals', 'investments', 'earnings', 'activity', 'eco_impact') NOT NULL,
    current_score DECIMAL(15,2) DEFAULT 0.00,
    previous_score DECIMAL(15,2) DEFAULT 0.00,
    current_rank INT DEFAULT 0,
    previous_rank INT DEFAULT 0,
    rank_change INT DEFAULT 0,
    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_ranking (user_id, ranking_type),
    INDEX idx_ranking_type (ranking_type),
    INDEX idx_current_rank (current_rank),
    INDEX idx_current_score (current_score),
    INDEX idx_last_updated (last_updated)
);

-- Таблица для истории рейтингов
CREATE TABLE IF NOT EXISTS ranking_history (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    ranking_type ENUM('referrals', 'investments', 'earnings', 'activity', 'eco_impact') NOT NULL,
    score DECIMAL(15,2) NOT NULL,
    rank_position INT NOT NULL,
    period_type ENUM('daily', 'weekly', 'monthly', 'yearly') NOT NULL,
    period_date DATE NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_ranking_period (user_id, ranking_type, period_type),
    INDEX idx_period_date (period_date),
    INDEX idx_ranking_type_period (ranking_type, period_type)
);

-- Таблица для настроек рейтингов
CREATE TABLE IF NOT EXISTS ranking_settings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    ranking_type ENUM('referrals', 'investments', 'earnings', 'activity', 'eco_impact') NOT NULL,
    is_enabled BOOLEAN DEFAULT TRUE,
    update_frequency ENUM('hourly', 'daily', 'weekly') DEFAULT 'daily',
    calculation_method TEXT,
    weight_factors JSON,
    display_settings JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY unique_ranking_type (ranking_type)
);

-- Таблица для достижений пользователей
CREATE TABLE IF NOT EXISTS user_achievements (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    achievement_type ENUM('first_investment', 'top_referrer', 'eco_warrior', 'big_investor', 'loyal_user', 'profit_master') NOT NULL,
    achievement_level INT DEFAULT 1,
    earned_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    is_displayed BOOLEAN DEFAULT TRUE,
    reward_amount DECIMAL(15,2) DEFAULT 0.00,
    reward_claimed BOOLEAN DEFAULT FALSE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_achievements (user_id),
    INDEX idx_achievement_type (achievement_type),
    INDEX idx_earned_at (earned_at)
);

-- Таблица для периодических наград
CREATE TABLE IF NOT EXISTS periodic_rewards (
    id INT AUTO_INCREMENT PRIMARY KEY,
    ranking_type ENUM('referrals', 'investments', 'earnings', 'activity', 'eco_impact') NOT NULL,
    period_type ENUM('daily', 'weekly', 'monthly') NOT NULL,
    period_date DATE NOT NULL,
    rank_from INT NOT NULL,
    rank_to INT NOT NULL,
    reward_amount DECIMAL(15,2) NOT NULL,
    reward_type ENUM('usdt', 'bonus', 'multiplier') DEFAULT 'usdt',
    is_distributed BOOLEAN DEFAULT FALSE,
    distributed_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_period_ranking (period_date, ranking_type),
    INDEX idx_is_distributed (is_distributed)
);

-- Таблица для истории наград
CREATE TABLE IF NOT EXISTS reward_history (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    reward_type ENUM('ranking', 'achievement', 'bonus') NOT NULL,
    ranking_type ENUM('referrals', 'investments', 'earnings', 'activity', 'eco_impact') NULL,
    achievement_type ENUM('first_investment', 'top_referrer', 'eco_warrior', 'big_investor', 'loyal_user', 'profit_master') NULL,
    amount DECIMAL(15,2) NOT NULL,
    description TEXT,
    earned_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    claimed_at TIMESTAMP NULL,
    is_claimed BOOLEAN DEFAULT FALSE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_rewards (user_id),
    INDEX idx_reward_type (reward_type),
    INDEX idx_earned_at (earned_at),
    INDEX idx_is_claimed (is_claimed)
);

-- Вставка начальных настроек рейтингов
INSERT INTO ranking_settings (ranking_type, calculation_method, weight_factors, display_settings) VALUES
('referrals', 'COUNT(referred_users) + SUM(referral_earnings)', 
 JSON_OBJECT('direct_referrals', 10, 'indirect_referrals', 5, 'referral_earnings', 1),
 JSON_OBJECT('title', 'Топ рефералов', 'icon', 'fas fa-users', 'color', '#22c55e')),

('investments', 'SUM(total_invested) + COUNT(active_investments) * 100', 
 JSON_OBJECT('total_invested', 1, 'active_investments', 100, 'investment_diversity', 50),
 JSON_OBJECT('title', 'Топ инвесторов', 'icon', 'fas fa-chart-line', 'color', '#3b82f6')),

('earnings', 'SUM(total_earned) + SUM(current_profit)', 
 JSON_OBJECT('total_earned', 1, 'current_profit', 2, 'roi_percentage', 10),
 JSON_OBJECT('title', 'Топ по прибыли', 'icon', 'fas fa-coins', 'color', '#f59e0b')),

('activity', 'login_days * 10 + transactions_count + investments_count * 5', 
 JSON_OBJECT('login_days', 10, 'transactions_count', 1, 'investments_count', 5),
 JSON_OBJECT('title', 'Самые активные', 'icon', 'fas fa-fire', 'color', '#ef4444')),

('eco_impact', 'energy_generated + co2_saved * 10 + trees_planted * 50', 
 JSON_OBJECT('energy_generated', 1, 'co2_saved', 10, 'trees_planted', 50),
 JSON_OBJECT('title', 'Эко-герои', 'icon', 'fas fa-leaf', 'color', '#16a34a'));

-- Создание представления для быстрого доступа к рейтингам
CREATE OR REPLACE VIEW leaderboard_view AS
SELECT 
    ur.ranking_type,
    ur.current_rank,
    ur.current_score,
    ur.rank_change,
    u.id as user_id,
    u.username,
    u.avatar,
    u.created_at as user_since,
    rs.display_settings,
    CASE 
        WHEN ur.rank_change > 0 THEN 'up'
        WHEN ur.rank_change < 0 THEN 'down'
        ELSE 'same'
    END as trend
FROM user_rankings ur
JOIN users u ON ur.user_id = u.id
JOIN ranking_settings rs ON ur.ranking_type = rs.ranking_type
WHERE rs.is_enabled = 1 AND u.is_active = 1
ORDER BY ur.ranking_type, ur.current_rank;

-- Функция для расчета рейтинга рефералов
DELIMITER //
CREATE FUNCTION calculate_referral_score(user_id INT) 
RETURNS DECIMAL(15,2)
READS SQL DATA
DETERMINISTIC
BEGIN
    DECLARE score DECIMAL(15,2) DEFAULT 0;
    DECLARE direct_count INT DEFAULT 0;
    DECLARE indirect_count INT DEFAULT 0;
    DECLARE total_earnings DECIMAL(15,2) DEFAULT 0;
    
    -- Подсчет прямых рефералов
    SELECT COUNT(*) INTO direct_count
    FROM users 
    WHERE referrer_id = user_id AND is_active = 1;
    
    -- Подсчет непрямых рефералов (2-й уровень)
    SELECT COUNT(*) INTO indirect_count
    FROM users u1
    JOIN users u2 ON u1.referrer_id = u2.id
    WHERE u2.referrer_id = user_id AND u1.is_active = 1;
    
    -- Подсчет заработка с рефералов
    SELECT COALESCE(SUM(amount), 0) INTO total_earnings
    FROM transactions 
    WHERE user_id = user_id AND type = 'referral_bonus' AND status = 'completed';
    
    -- Расчет итогового счета
    SET score = (direct_count * 10) + (indirect_count * 5) + total_earnings;
    
    RETURN score;
END //
DELIMITER ;

-- Функция для расчета рейтинга инвестиций
DELIMITER //
CREATE FUNCTION calculate_investment_score(user_id INT) 
RETURNS DECIMAL(15,2)
READS SQL DATA
DETERMINISTIC
BEGIN
    DECLARE score DECIMAL(15,2) DEFAULT 0;
    DECLARE total_invested DECIMAL(15,2) DEFAULT 0;
    DECLARE active_count INT DEFAULT 0;
    DECLARE package_diversity INT DEFAULT 0;
    
    -- Общая сумма инвестиций
    SELECT COALESCE(SUM(amount), 0) INTO total_invested
    FROM user_investments 
    WHERE user_id = user_id;
    
    -- Количество активных инвестиций
    SELECT COUNT(*) INTO active_count
    FROM user_investments 
    WHERE user_id = user_id AND status = 'active';
    
    -- Разнообразие пакетов
    SELECT COUNT(DISTINCT package_id) INTO package_diversity
    FROM user_investments 
    WHERE user_id = user_id;
    
    -- Расчет итогового счета
    SET score = total_invested + (active_count * 100) + (package_diversity * 50);
    
    RETURN score;
END //
DELIMITER ;

-- Функция для расчета рейтинга прибыли
DELIMITER //
CREATE FUNCTION calculate_earnings_score(user_id INT) 
RETURNS DECIMAL(15,2)
READS SQL DATA
DETERMINISTIC
BEGIN
    DECLARE score DECIMAL(15,2) DEFAULT 0;
    DECLARE total_earned DECIMAL(15,2) DEFAULT 0;
    DECLARE current_profit DECIMAL(15,2) DEFAULT 0;
    DECLARE roi_bonus DECIMAL(15,2) DEFAULT 0;
    
    -- Общая прибыль
    SELECT COALESCE(SUM(total_earned), 0) INTO total_earned
    FROM user_investments 
    WHERE user_id = user_id;
    
    -- Текущая ежедневная прибыль
    SELECT COALESCE(SUM(daily_profit), 0) INTO current_profit
    FROM user_investments 
    WHERE user_id = user_id AND status = 'active';
    
    -- Бонус за ROI
    SELECT COALESCE(total_invested, 0) INTO @total_invested
    FROM users WHERE id = user_id;
    
    IF @total_invested > 0 THEN
        SET roi_bonus = (total_earned / @total_invested) * 100;
    END IF;
    
    -- Расчет итогового счета
    SET score = total_earned + (current_profit * 2) + (roi_bonus * 10);
    
    RETURN score;
END //
DELIMITER ;

-- Процедура для обновления всех рейтингов
DELIMITER //
CREATE PROCEDURE update_all_rankings()
BEGIN
    DECLARE done INT DEFAULT FALSE;
    DECLARE user_id INT;
    DECLARE cur CURSOR FOR SELECT id FROM users WHERE is_active = 1;
    DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = TRUE;
    
    OPEN cur;
    
    read_loop: LOOP
        FETCH cur INTO user_id;
        IF done THEN
            LEAVE read_loop;
        END IF;
        
        -- Обновление рейтинга рефералов
        INSERT INTO user_rankings (user_id, ranking_type, current_score)
        VALUES (user_id, 'referrals', calculate_referral_score(user_id))
        ON DUPLICATE KEY UPDATE 
        previous_score = current_score,
        current_score = calculate_referral_score(user_id);
        
        -- Обновление рейтинга инвестиций
        INSERT INTO user_rankings (user_id, ranking_type, current_score)
        VALUES (user_id, 'investments', calculate_investment_score(user_id))
        ON DUPLICATE KEY UPDATE 
        previous_score = current_score,
        current_score = calculate_investment_score(user_id);
        
        -- Обновление рейтинга прибыли
        INSERT INTO user_rankings (user_id, ranking_type, current_score)
        VALUES (user_id, 'earnings', calculate_earnings_score(user_id))
        ON DUPLICATE KEY UPDATE 
        previous_score = current_score,
        current_score = calculate_earnings_score(user_id);
        
    END LOOP;
    
    CLOSE cur;
    
    -- Обновление рангов
    CALL update_ranking_positions();
    
END //
DELIMITER ;

-- Процедура для обновления позиций в рейтинге
DELIMITER //
CREATE PROCEDURE update_ranking_positions()
BEGIN
    DECLARE ranking_type_var VARCHAR(20);
    DECLARE done INT DEFAULT FALSE;
    DECLARE cur CURSOR FOR SELECT DISTINCT ranking_type FROM user_rankings;
    DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = TRUE;
    
    OPEN cur;
    
    ranking_loop: LOOP
        FETCH cur INTO ranking_type_var;
        IF done THEN
            LEAVE ranking_loop;
        END IF;
        
        SET @rank = 0;
        UPDATE user_rankings 
        SET previous_rank = current_rank,
            current_rank = (@rank := @rank + 1)
        WHERE ranking_type = ranking_type_var
        ORDER BY current_score DESC;
        
        -- Расчет изменения ранга
        UPDATE user_rankings 
        SET rank_change = previous_rank - current_rank
        WHERE ranking_type = ranking_type_var AND previous_rank > 0;
        
    END LOOP;
    
    CLOSE cur;
    
END //
DELIMITER ;
