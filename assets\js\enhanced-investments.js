/**
 * AstroGenix - JavaScript для расширенных инвестиций
 * Эко-майнинговая инвестиционная платформа
 */

document.addEventListener('DOMContentLoaded', function() {
    initializeEnhancedInvestments();
});

function initializeEnhancedInvestments() {
    // Инициализация фильтров пакетов
    initPackageFilters();
    
    // Инициализация кнопок инвестирования
    initInvestButtons();
    
    // Инициализация модального окна
    initInvestmentModal();
    
    // Инициализация расширенного калькулятора
    initEnhancedCalculator();
    
    // Инициализация анимаций
    initInvestmentAnimations();
}

// Фильтры пакетов
function initPackageFilters() {
    const filterButtons = document.querySelectorAll('.filter-btn');
    const packageCards = document.querySelectorAll('.all-packages-grid .package-card');
    
    filterButtons.forEach(button => {
        button.addEventListener('click', function() {
            const filter = this.dataset.filter;
            
            // Обновление активной кнопки
            filterButtons.forEach(btn => btn.classList.remove('active'));
            this.classList.add('active');
            
            // Фильтрация пакетов
            packageCards.forEach(card => {
                const packageType = card.dataset.packageType;
                
                if (filter === 'all' || packageType === filter) {
                    card.style.display = 'block';
                    card.classList.add('animate-fade-in');
                } else {
                    card.style.display = 'none';
                    card.classList.remove('animate-fade-in');
                }
            });
        });
    });
}

// Кнопки инвестирования
function initInvestButtons() {
    const investButtons = document.querySelectorAll('.invest-btn');
    
    investButtons.forEach(button => {
        button.addEventListener('click', function() {
            const packageId = this.dataset.packageId;
            const packageName = this.dataset.packageName;
            const minAmount = parseFloat(this.dataset.minAmount);
            const maxAmount = parseFloat(this.dataset.maxAmount);
            const profitPercent = parseFloat(this.dataset.profitPercent);
            const duration = parseInt(this.dataset.duration);
            const packageType = this.dataset.packageType;
            
            openInvestmentModal({
                id: packageId,
                name: packageName,
                minAmount: minAmount,
                maxAmount: maxAmount,
                profitPercent: profitPercent,
                duration: duration,
                type: packageType
            });
        });
    });
}

// Модальное окно инвестирования
function initInvestmentModal() {
    const modal = document.getElementById('investment-modal');
    const closeButtons = modal.querySelectorAll('.modal-close');
    const autoReinvestCheckbox = document.getElementById('modal-auto-reinvest');
    const reinvestSettings = document.getElementById('modal-reinvest-settings');
    const reinvestRange = document.getElementById('modal-reinvest-percent');
    const reinvestValue = document.getElementById('modal-reinvest-value');
    const amountInput = document.getElementById('modal-amount');
    
    // Закрытие модального окна
    closeButtons.forEach(button => {
        button.addEventListener('click', function() {
            modal.style.display = 'none';
        });
    });
    
    // Закрытие по клику вне модального окна
    window.addEventListener('click', function(e) {
        if (e.target === modal) {
            modal.style.display = 'none';
        }
    });
    
    // Переключение настроек реинвестирования
    if (autoReinvestCheckbox) {
        autoReinvestCheckbox.addEventListener('change', function() {
            if (this.checked) {
                reinvestSettings.style.display = 'block';
            } else {
                reinvestSettings.style.display = 'none';
            }
            updateInvestmentSummary();
        });
    }
    
    // Обновление значения реинвестирования
    if (reinvestRange) {
        reinvestRange.addEventListener('input', function() {
            reinvestValue.textContent = this.value;
            updateInvestmentSummary();
        });
    }
    
    // Обновление сводки при изменении суммы
    if (amountInput) {
        amountInput.addEventListener('input', updateInvestmentSummary);
    }
}

// Открытие модального окна инвестирования
function openInvestmentModal(packageData) {
    const modal = document.getElementById('investment-modal');
    const packageNameEl = document.getElementById('modal-package-name');
    const packageIdInput = document.getElementById('modal-package-id');
    const packageInfoEl = document.getElementById('modal-package-info');
    const amountInput = document.getElementById('modal-amount');
    
    // Заполнение данных пакета
    packageNameEl.textContent = `Инвестиция в пакет "${packageData.name}"`;
    packageIdInput.value = packageData.id;
    
    // Информация о пакете
    packageInfoEl.innerHTML = `
        <div class="package-info-grid">
            <div class="info-item">
                <span class="info-label">Тип пакета:</span>
                <span class="info-value">${getPackageTypeName(packageData.type)}</span>
            </div>
            <div class="info-item">
                <span class="info-label">Доходность:</span>
                <span class="info-value">${packageData.profitPercent}% в день</span>
            </div>
            <div class="info-item">
                <span class="info-label">Срок:</span>
                <span class="info-value">${packageData.duration} дней</span>
            </div>
            <div class="info-item">
                <span class="info-label">Диапазон сумм:</span>
                <span class="info-value">${formatCurrency(packageData.minAmount)} - ${formatCurrency(packageData.maxAmount)} USDT</span>
            </div>
        </div>
    `;
    
    // Настройка поля суммы
    amountInput.min = packageData.minAmount;
    amountInput.max = packageData.maxAmount;
    amountInput.value = packageData.minAmount;
    
    // Сохранение данных пакета для расчетов
    modal.dataset.packageData = JSON.stringify(packageData);
    
    // Обновление сводки
    updateInvestmentSummary();
    
    // Показ модального окна
    modal.style.display = 'flex';
}

// Обновление сводки по инвестиции
function updateInvestmentSummary() {
    const modal = document.getElementById('investment-modal');
    const summaryEl = document.getElementById('modal-investment-summary');
    const amountInput = document.getElementById('modal-amount');
    const autoReinvestCheckbox = document.getElementById('modal-auto-reinvest');
    const reinvestRange = document.getElementById('modal-reinvest-percent');
    
    if (!modal.dataset.packageData) return;
    
    const packageData = JSON.parse(modal.dataset.packageData);
    const amount = parseFloat(amountInput.value) || 0;
    const autoReinvest = autoReinvestCheckbox.checked;
    const reinvestPercent = parseInt(reinvestRange.value) || 0;
    
    if (amount < packageData.minAmount || amount > packageData.maxAmount) {
        summaryEl.innerHTML = `
            <div class="summary-error">
                <i class="fas fa-exclamation-triangle"></i>
                Сумма должна быть от ${formatCurrency(packageData.minAmount)} до ${formatCurrency(packageData.maxAmount)} USDT
            </div>
        `;
        return;
    }
    
    // Расчет прибыли
    const dailyProfit = (amount * packageData.profitPercent) / 100;
    const totalProfit = dailyProfit * packageData.duration;
    const totalReturn = amount + totalProfit;
    const roi = ((totalProfit / amount) * 100).toFixed(2);
    
    let reinvestInfo = '';
    if (autoReinvest) {
        const reinvestAmount = (dailyProfit * reinvestPercent) / 100;
        reinvestInfo = `
            <div class="summary-item">
                <span class="summary-label">Реинвестирование:</span>
                <span class="summary-value">${formatCurrency(reinvestAmount)} USDT в день (${reinvestPercent}%)</span>
            </div>
        `;
    }
    
    summaryEl.innerHTML = `
        <div class="summary-header">
            <h4>Сводка по инвестиции</h4>
        </div>
        <div class="summary-grid">
            <div class="summary-item">
                <span class="summary-label">Сумма инвестиции:</span>
                <span class="summary-value">${formatCurrency(amount)} USDT</span>
            </div>
            <div class="summary-item">
                <span class="summary-label">Ежедневная прибыль:</span>
                <span class="summary-value">${formatCurrency(dailyProfit)} USDT</span>
            </div>
            <div class="summary-item">
                <span class="summary-label">Прибыль за период:</span>
                <span class="summary-value">${formatCurrency(totalProfit)} USDT</span>
            </div>
            <div class="summary-item">
                <span class="summary-label">Общий доход:</span>
                <span class="summary-value">${formatCurrency(totalReturn)} USDT</span>
            </div>
            <div class="summary-item">
                <span class="summary-label">ROI:</span>
                <span class="summary-value">${roi}%</span>
            </div>
            ${reinvestInfo}
        </div>
    `;
}

// Расширенный калькулятор
function initEnhancedCalculator() {
    const packageSelect = document.getElementById('calc-package');
    const amountInput = document.getElementById('calc-amount');
    const reinvestCheckbox = document.getElementById('calc-reinvest');
    const reinvestOptions = document.querySelector('.reinvest-options');
    const reinvestRange = document.getElementById('calc-reinvest-percent');
    const rangeValue = document.querySelector('.range-value');
    const calculateBtn = document.getElementById('calculate-btn');
    const resultsContainer = document.getElementById('calculator-results');
    
    // Обновление диапазона сумм при выборе пакета
    if (packageSelect) {
        packageSelect.addEventListener('change', function() {
            const selectedOption = this.options[this.selectedIndex];
            const minAmount = selectedOption.dataset.min;
            const maxAmount = selectedOption.dataset.max;
            
            if (minAmount && maxAmount) {
                amountInput.min = minAmount;
                amountInput.max = maxAmount;
                
                const rangeMin = document.querySelector('.range-min');
                const rangeMax = document.querySelector('.range-max');
                
                if (rangeMin) rangeMin.textContent = `Мин: ${formatCurrency(minAmount)} USDT`;
                if (rangeMax) rangeMax.textContent = `Макс: ${formatCurrency(maxAmount)} USDT`;
                
                // Установка значения в диапазон
                if (parseFloat(amountInput.value) < parseFloat(minAmount)) {
                    amountInput.value = minAmount;
                }
                if (parseFloat(amountInput.value) > parseFloat(maxAmount)) {
                    amountInput.value = maxAmount;
                }
            }
        });
    }
    
    // Переключение опций реинвестирования
    if (reinvestCheckbox) {
        reinvestCheckbox.addEventListener('change', function() {
            if (this.checked) {
                reinvestOptions.style.display = 'block';
            } else {
                reinvestOptions.style.display = 'none';
            }
        });
    }
    
    // Обновление значения ползунка
    if (reinvestRange) {
        reinvestRange.addEventListener('input', function() {
            rangeValue.textContent = this.value + '%';
        });
    }
    
    // Расчет прибыли
    if (calculateBtn) {
        calculateBtn.addEventListener('click', calculateProfit);
    }
}

// Расчет прибыли
function calculateProfit() {
    const packageSelect = document.getElementById('calc-package');
    const amountInput = document.getElementById('calc-amount');
    const reinvestCheckbox = document.getElementById('calc-reinvest');
    const reinvestRange = document.getElementById('calc-reinvest-percent');
    const resultsContainer = document.getElementById('calculator-results');
    
    const selectedOption = packageSelect.options[packageSelect.selectedIndex];
    
    if (!selectedOption.value) {
        showNotification('Выберите инвестиционный пакет', 'error');
        return;
    }
    
    const amount = parseFloat(amountInput.value);
    const profitPercent = parseFloat(selectedOption.dataset.profit);
    const duration = parseInt(selectedOption.dataset.duration);
    const packageType = selectedOption.dataset.type;
    const bonus = parseFloat(selectedOption.dataset.bonus) || 0;
    const autoReinvest = reinvestCheckbox.checked;
    const reinvestPercent = parseInt(reinvestRange.value) || 0;
    
    if (!amount || amount <= 0) {
        showNotification('Введите корректную сумму инвестиции', 'error');
        return;
    }
    
    // Базовые расчеты
    let dailyProfit = (amount * profitPercent) / 100;
    
    // Добавление бонуса для VIP пакетов
    if (packageType === 'vip' && bonus > 0) {
        dailyProfit += (amount * bonus) / 100;
    }
    
    let totalProfit = 0;
    let currentAmount = amount;
    const profitData = [];
    
    // Расчет с учетом реинвестирования
    for (let day = 1; day <= duration; day++) {
        let dayProfit = (currentAmount * profitPercent) / 100;
        
        if (packageType === 'vip' && bonus > 0) {
            dayProfit += (currentAmount * bonus) / 100;
        }
        
        totalProfit += dayProfit;
        
        // Реинвестирование
        if (autoReinvest && reinvestPercent > 0) {
            const reinvestAmount = (dayProfit * reinvestPercent) / 100;
            currentAmount += reinvestAmount;
            totalProfit -= reinvestAmount; // Вычитаем реинвестированную сумму из прибыли
        }
        
        profitData.push({
            day: day,
            profit: dayProfit,
            totalProfit: totalProfit,
            currentAmount: currentAmount
        });
    }
    
    const totalReturn = amount + totalProfit;
    const roi = ((totalProfit / amount) * 100).toFixed(2);
    
    // Отображение результатов
    document.getElementById('daily-profit').textContent = formatCurrency(dailyProfit) + ' USDT';
    document.getElementById('total-profit').textContent = formatCurrency(totalProfit) + ' USDT';
    document.getElementById('total-return').textContent = formatCurrency(totalReturn) + ' USDT';
    document.getElementById('roi-percent').textContent = roi + '%';
    
    // Показ результатов
    resultsContainer.style.display = 'block';
    resultsContainer.scrollIntoView({ behavior: 'smooth' });
    
    // Построение графика
    drawProfitChart(profitData);
}

// Построение графика прибыли
function drawProfitChart(profitData) {
    const ctx = document.getElementById('profit-chart').getContext('2d');
    
    // Уничтожение предыдущего графика
    if (window.profitChart) {
        window.profitChart.destroy();
    }
    
    const labels = profitData.map(item => `День ${item.day}`);
    const totalProfitData = profitData.map(item => item.totalProfit);
    const currentAmountData = profitData.map(item => item.currentAmount);
    
    window.profitChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: labels,
            datasets: [{
                label: 'Накопленная прибыль (USDT)',
                data: totalProfitData,
                borderColor: '#22c55e',
                backgroundColor: 'rgba(34, 197, 94, 0.1)',
                tension: 0.4,
                fill: true
            }, {
                label: 'Текущая сумма инвестиции (USDT)',
                data: currentAmountData,
                borderColor: '#3b82f6',
                backgroundColor: 'rgba(59, 130, 246, 0.1)',
                tension: 0.4,
                fill: false
            }]
        },
        options: {
            responsive: true,
            plugins: {
                title: {
                    display: true,
                    text: 'Прогноз роста инвестиции'
                },
                legend: {
                    display: true,
                    position: 'top'
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        callback: function(value) {
                            return formatCurrency(value) + ' USDT';
                        }
                    }
                }
            },
            interaction: {
                intersect: false,
                mode: 'index'
            }
        }
    });
}

// Анимации
function initInvestmentAnimations() {
    // Анимация появления карточек при скролле
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };
    
    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('animate-slide-up');
            }
        });
    }, observerOptions);
    
    document.querySelectorAll('.package-card').forEach(card => {
        observer.observe(card);
    });
}

// Вспомогательные функции
function getPackageTypeName(type) {
    const types = {
        'simple': 'Простой',
        'compound': 'Компаундный',
        'fixed': 'Фиксированный',
        'variable': 'Переменный',
        'vip': 'VIP'
    };
    return types[type] || 'Простой';
}

function formatCurrency(amount) {
    return new Intl.NumberFormat('ru-RU', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
    }).format(amount);
}

function showNotification(message, type = 'info') {
    // Создание уведомления
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
        <div class="notification-content">
            <span class="notification-message">${message}</span>
            <button class="notification-close">&times;</button>
        </div>
    `;
    
    // Добавление в DOM
    document.body.appendChild(notification);
    
    // Показ уведомления
    setTimeout(() => {
        notification.classList.add('show');
    }, 100);
    
    // Автоматическое скрытие
    setTimeout(() => {
        hideNotification(notification);
    }, 5000);
    
    // Обработчик закрытия
    notification.querySelector('.notification-close').addEventListener('click', () => {
        hideNotification(notification);
    });
}

function hideNotification(notification) {
    notification.classList.remove('show');
    setTimeout(() => {
        if (notification.parentNode) {
            notification.parentNode.removeChild(notification);
        }
    }, 300);
}
