/**
 * AstroGenix - Стили страницы профиля
 * Эко-майнинговая инвестиционная платформа
 */

/* Заголовок профиля */
.user-info {
    text-align: right;
}

.user-name {
    display: block;
    font-size: var(--text-lg);
    font-weight: 600;
    color: var(--gray-900);
}

.user-email {
    display: block;
    font-size: var(--text-sm);
    color: var(--gray-600);
}

/* Обзор профиля */
.profile-overview {
    margin-bottom: var(--space-8);
}

.profile-card {
    background: linear-gradient(135deg, rgba(46, 204, 113, 0.1) 0%, rgba(142, 68, 173, 0.1) 100%);
    border: 2px solid rgba(46, 204, 113, 0.2);
    border-radius: var(--radius-xl);
    padding: var(--space-8);
    position: relative;
    overflow: hidden;
}

.profile-card::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle, rgba(46, 204, 113, 0.1) 0%, transparent 70%);
    animation: profilePulse 8s ease-in-out infinite;
}

@keyframes profilePulse {
    0%, 100% { transform: scale(1) rotate(0deg); opacity: 0.3; }
    50% { transform: scale(1.3) rotate(180deg); opacity: 0.6; }
}

.profile-avatar {
    display: flex;
    align-items: center;
    gap: var(--space-6);
    margin-bottom: var(--space-8);
    position: relative;
    z-index: 2;
}

.avatar-circle {
    width: 120px;
    height: 120px;
    background: var(--gradient-primary);
    border-radius: var(--radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--text-5xl);
    color: var(--white);
    box-shadow: var(--shadow-lg);
    position: relative;
}

.avatar-circle::after {
    content: '';
    position: absolute;
    inset: -4px;
    background: var(--gradient-primary);
    border-radius: var(--radius-full);
    z-index: -1;
    animation: avatarGlow 3s ease-in-out infinite;
}

@keyframes avatarGlow {
    0%, 100% { opacity: 0.5; transform: scale(1); }
    50% { opacity: 0.8; transform: scale(1.05); }
}

.avatar-info h2 {
    margin: 0 0 var(--space-2) 0;
    font-size: var(--text-3xl);
    color: var(--gray-900);
}

.avatar-info p {
    margin: 0 0 var(--space-4) 0;
    color: var(--gray-600);
    font-size: var(--text-lg);
}

.user-badges {
    display: flex;
    gap: var(--space-2);
    flex-wrap: wrap;
}

.badge {
    display: inline-flex;
    align-items: center;
    gap: var(--space-1);
    padding: var(--space-1) var(--space-3);
    border-radius: var(--radius-full);
    font-size: var(--text-xs);
    font-weight: 500;
}

.badge-verified {
    background: rgba(40, 167, 69, 0.2);
    color: var(--success);
}

.badge-investor {
    background: rgba(46, 204, 113, 0.2);
    color: var(--primary-green);
}

.badge-referrer {
    background: rgba(155, 89, 182, 0.2);
    color: #9B59B6;
}

.profile-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--space-4);
    position: relative;
    z-index: 2;
}

.stat-item {
    display: flex;
    align-items: center;
    gap: var(--space-3);
    padding: var(--space-4);
    background: rgba(255, 255, 255, 0.8);
    border-radius: var(--radius-lg);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.stat-icon {
    width: 50px;
    height: 50px;
    background: var(--gradient-primary);
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--text-xl);
    color: var(--white);
}

.stat-content {
    display: flex;
    flex-direction: column;
}

.stat-label {
    font-size: var(--text-sm);
    color: var(--gray-600);
    margin-bottom: var(--space-1);
}

.stat-value {
    font-size: var(--text-lg);
    font-weight: 600;
    color: var(--gray-900);
}

/* Вкладки профиля */
.profile-tabs {
    background: var(--white);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--gray-200);
    overflow: hidden;
}

.tab-navigation {
    display: flex;
    background: var(--gray-50);
    border-bottom: 1px solid var(--gray-200);
}

.tab-btn {
    flex: 1;
    padding: var(--space-4) var(--space-6);
    background: none;
    border: none;
    color: var(--gray-600);
    font-size: var(--text-sm);
    font-weight: 500;
    cursor: pointer;
    transition: all var(--transition-fast);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--space-2);
    position: relative;
}

.tab-btn:hover {
    color: var(--primary-green);
    background: rgba(46, 204, 113, 0.05);
}

.tab-btn.active {
    color: var(--primary-green);
    background: var(--white);
}

.tab-btn.active::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: var(--gradient-primary);
}

.tab-content {
    display: none;
    padding: var(--space-8);
}

.tab-content.active {
    display: block;
}

/* Формы */
.profile-form,
.password-form {
    max-width: 600px;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--space-4);
}

.form-group {
    margin-bottom: var(--space-6);
}

.form-group label {
    display: block;
    margin-bottom: var(--space-2);
    font-weight: 500;
    color: var(--gray-700);
}

.form-group input {
    width: 100%;
    padding: var(--space-3);
    border: 2px solid var(--gray-300);
    border-radius: var(--radius-md);
    font-size: var(--text-base);
    transition: border-color var(--transition-fast);
}

.form-group input:focus {
    outline: none;
    border-color: var(--primary-green);
}

.form-group input:disabled {
    background: var(--gray-100);
    color: var(--gray-500);
    cursor: not-allowed;
}

.form-help {
    display: block;
    margin-top: var(--space-1);
    font-size: var(--text-xs);
    color: var(--gray-500);
}

/* Реферальная ссылка */
.referral-link-container {
    text-align: center;
}

.link-display {
    display: flex;
    gap: var(--space-3);
    margin-bottom: var(--space-6);
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.link-display input {
    flex: 1;
    padding: var(--space-4);
    border: 2px solid var(--gray-300);
    border-radius: var(--radius-lg);
    font-size: var(--text-base);
    background: var(--gray-50);
    color: var(--gray-700);
}

.referral-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--space-4);
    margin-bottom: var(--space-6);
}

.referral-stat {
    text-align: center;
    padding: var(--space-4);
    background: var(--gray-50);
    border-radius: var(--radius-lg);
    border: 1px solid var(--gray-200);
}

.referral-stat .stat-number {
    display: block;
    font-size: var(--text-2xl);
    font-weight: 700;
    color: var(--primary-green);
    margin-bottom: var(--space-1);
}

.referral-stat .stat-label {
    font-size: var(--text-sm);
    color: var(--gray-600);
}

/* Экология */
.ecology-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--space-6);
    margin-bottom: var(--space-8);
}

.ecology-item {
    display: flex;
    align-items: center;
    gap: var(--space-4);
    padding: var(--space-6);
    background: linear-gradient(135deg, rgba(39, 174, 96, 0.1) 0%, rgba(46, 204, 113, 0.1) 100%);
    border: 1px solid rgba(39, 174, 96, 0.2);
    border-radius: var(--radius-lg);
}

.ecology-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #27AE60 0%, #2ECC71 100%);
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--text-2xl);
    color: var(--white);
}

.ecology-content h4 {
    margin: 0 0 var(--space-2) 0;
    color: var(--gray-900);
}

.ecology-value {
    font-size: var(--text-2xl);
    font-weight: 700;
    color: var(--success);
    margin-bottom: var(--space-1);
}

.ecology-content p {
    margin: 0;
    font-size: var(--text-sm);
    color: var(--gray-600);
}

.ecology-progress {
    background: var(--white);
    padding: var(--space-6);
    border-radius: var(--radius-lg);
    border: 1px solid var(--gray-200);
}

.ecology-progress h4 {
    margin: 0 0 var(--space-4) 0;
    color: var(--gray-900);
    text-align: center;
}

.progress-bar {
    width: 100%;
    height: 12px;
    background: var(--gray-200);
    border-radius: var(--radius-full);
    overflow: hidden;
    margin-bottom: var(--space-2);
}

.progress-fill {
    height: 100%;
    background: var(--gradient-primary);
    border-radius: var(--radius-full);
    transition: width 1s ease;
}

.progress-info {
    display: flex;
    justify-content: space-between;
    font-size: var(--text-sm);
    color: var(--gray-600);
}

/* Адаптивность */
@media (max-width: 768px) {
    .profile-avatar {
        flex-direction: column;
        text-align: center;
        gap: var(--space-4);
    }

    .avatar-circle {
        width: 100px;
        height: 100px;
        font-size: var(--text-4xl);
    }

    .profile-stats {
        grid-template-columns: repeat(2, 1fr);
    }

    .tab-navigation {
        flex-wrap: wrap;
    }

    .tab-btn {
        flex: 1 1 50%;
        min-width: 120px;
    }

    .form-row {
        grid-template-columns: 1fr;
    }

    .link-display {
        flex-direction: column;
    }

    .ecology-stats {
        grid-template-columns: 1fr;
    }

    .ecology-item {
        flex-direction: column;
        text-align: center;
    }
}

@media (max-width: 480px) {
    .profile-card {
        padding: var(--space-4);
    }

    .tab-content {
        padding: var(--space-4);
    }

    .profile-stats {
        grid-template-columns: 1fr;
    }

    .tab-btn {
        flex: 1 1 100%;
        font-size: var(--text-xs);
        padding: var(--space-3);
    }

    .referral-stats {
        grid-template-columns: 1fr;
    }
}
