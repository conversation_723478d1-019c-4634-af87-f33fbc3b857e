# 🌟 AstroGenix - Эко-майнинговая инвестиционная платформа

![AstroGenix Logo](assets/images/logo.svg)

**AstroGenix** - современная инвестиционная платформа, объединяющая прибыльные инвестиции с заботой об окружающей среде. Платформа использует зелено-фиолетовую цветовую схему и фокусируется на экологических технологиях.

## 🚀 Особенности

### 💰 **Инвестиционная система**
- ✅ Инвестиционные пакеты с автоматическим начислением прибыли
- ✅ 30-дневные инвестиционные циклы
- ✅ Ежедневное начисление прибыли через cron-задачи
- ✅ Гибкая система тарифов

### 👥 **Реферальная программа**
- ✅ 3-уровневая реферальная система
- ✅ Комиссии: 5% / 3% / 2% с каждого уровня
- ✅ Уникальные реферальные ссылки
- ✅ Детальная статистика рефералов

### 💳 **Финансовая система**
- ✅ Пополнение и вывод средств
- ✅ История всех транзакций
- ✅ Модерация депозитов администратором
- ✅ Экспорт данных в CSV

### 🌱 **Экологическая тематика**
- ✅ Счетчик произведенной зеленой энергии
- ✅ Эквивалент посаженных деревьев
- ✅ Сокращение выбросов CO₂
- ✅ Мотивационные экологические баннеры

### 🛡️ **Безопасность**
- ✅ CSRF защита
- ✅ Хеширование паролей
- ✅ Валидация всех входных данных
- ✅ Защита от SQL-инъекций

### 📱 **Современный дизайн**
- ✅ Полностью адаптивный дизайн (320px - 1920px+)
- ✅ Плавные анимации и микроинтерракции
- ✅ Профессиональная главная страница
- ✅ Единообразный дизайн всех страниц
- ✅ Оптимизация производительности
- ✅ Поддержка доступности (a11y)
- ✅ Темная административная панель

## 📋 Требования

- **PHP** 7.4 или выше
- **MySQL** 5.7 или выше
- **Apache/Nginx** веб-сервер
- **Composer** (опционально)

## 🔧 Установка

### 1. Клонирование проекта
```bash
git clone https://github.com/your-username/astrogenix.git
cd astrogenix
```

### 2. Настройка базы данных
1. Создайте базу данных MySQL:
```sql
CREATE DATABASE astrogenix CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

2. Импортируйте структуру базы данных:
```bash
mysql -u username -p astrogenix < database.sql
```

### 3. Настройка конфигурации
Отредактируйте файл `config/database.php`:
```php
define('DB_HOST', 'localhost');
define('DB_NAME', 'astrogenix');
define('DB_USER', 'your_username');
define('DB_PASS', 'your_password');
```

Отредактируйте файл `config/config.php`:
```php
define('SITE_URL', 'http://your-domain.com');
define('SMTP_HOST', 'your-smtp-host');
define('SMTP_USERNAME', '<EMAIL>');
define('SMTP_PASSWORD', 'your-email-password');
```

### 4. Настройка cron-задач
Добавьте в crontab для автоматического начисления прибыли:
```bash
# Ежедневное начисление прибыли в 00:01
1 0 * * * /usr/bin/php /path/to/your/project/cron/daily_profits.php
```

### 5. Настройка прав доступа
```bash
chmod 755 uploads/
chmod 644 config/*.php
```

## 📁 Структура проекта

```
astrogenix/
├── 📁 admin/                 # Административная панель
│   ├── dashboard.php         # Главная страница админки
│   └── includes/             # Компоненты админки
├── 📁 assets/                # Статические ресурсы
│   ├── css/                  # Стили
│   ├── js/                   # JavaScript
│   └── images/               # Изображения
├── 📁 classes/               # PHP классы
│   ├── User.php              # Управление пользователями
│   ├── Investment.php        # Инвестиционная система
│   ├── Transaction.php       # Транзакции
│   └── Referral.php          # Реферальная система
├── 📁 config/                # Конфигурация
│   ├── config.php            # Основные настройки
│   └── database.php          # Подключение к БД
├── 📁 cron/                  # Автоматические задачи
│   └── daily_profits.php     # Начисление прибыли
├── 📁 includes/              # Общие компоненты
│   ├── header.php            # Заголовок
│   ├── footer.php            # Подвал
│   └── sidebar.php           # Боковая панель
├── 📄 index.php              # Главная страница
├── 📄 login.php              # Авторизация
├── 📄 register.php           # Регистрация
├── 📄 dashboard.php          # Панель пользователя
├── 📄 investments.php        # Инвестиции
├── 📄 referrals.php          # Реферальная программа
├── 📄 transactions.php       # История транзакций
├── 📄 profile.php            # Профиль пользователя
├── 📄 deposit.php            # Пополнение
├── 📄 withdraw.php           # Вывод средств
└── 📄 database.sql           # Структура БД
```

## 🎨 Цветовая схема

- **Основной зеленый**: `#2ECC71` (46, 204, 113)
- **Темно-зеленый**: `#27AE60` (39, 174, 96)
- **Основной фиолетовый**: `#8E44AD` (142, 68, 173)
- **Светло-фиолетовый**: `#9B59B6` (155, 89, 182)
- **Градиент**: `linear-gradient(135deg, #2ECC71 0%, #8E44AD 100%)`

## 👤 Учетные записи по умолчанию

### Администратор
- **Email**: <EMAIL>
- **Пароль**: admin123456

### Тестовый пользователь
- **Email**: <EMAIL>
- **Пароль**: user123456

## 🔧 API Endpoints

### Пользовательские
- `POST /api/auth/login` - Авторизация
- `POST /api/auth/register` - Регистрация
- `GET /api/user/balance` - Получение баланса
- `POST /api/investment/create` - Создание инвестиции
- `GET /api/transactions` - История транзакций

### Административные
- `GET /api/admin/stats` - Общая статистика
- `POST /api/admin/transaction/approve` - Одобрение транзакции
- `GET /api/admin/users` - Список пользователей

## 🧪 Тестирование

### Функциональные тесты
1. **Регистрация и авторизация**
   - Создание нового аккаунта
   - Вход в систему
   - Восстановление пароля

2. **Инвестиционная система**
   - Создание инвестиции
   - Начисление ежедневной прибыли
   - Завершение инвестиционного цикла

3. **Реферальная программа**
   - Регистрация по реферальной ссылке
   - Начисление реферальных комиссий
   - Отображение структуры рефералов

4. **Финансовые операции**
   - Пополнение баланса
   - Вывод средств
   - Модерация транзакций

### Тестирование безопасности
- CSRF атаки
- SQL инъекции
- XSS атаки
- Валидация входных данных

## 🐛 Известные проблемы

- Email уведомления требуют настройки SMTP
- Загрузка файлов требует настройки прав доступа
- Некоторые анимации могут тормозить на слабых устройствах

## 🤝 Вклад в проект

1. Форкните репозиторий
2. Создайте ветку для новой функции (`git checkout -b feature/AmazingFeature`)
3. Зафиксируйте изменения (`git commit -m 'Add some AmazingFeature'`)
4. Отправьте в ветку (`git push origin feature/AmazingFeature`)
5. Откройте Pull Request

## 📄 Лицензия

Этот проект лицензирован под MIT License - см. файл [LICENSE](LICENSE) для деталей.

## 📞 Поддержка

- **Email**: <EMAIL>
- **Telegram**: @astrogenix_support
- **Документация**: [docs.astrogenix.com](https://docs.astrogenix.com)

---

**Сделано с ❤️ для экологии и инноваций**
