/**
 * AstroGenix - JavaScript для автореинвестирования
 * Эко-майнинговая инвестиционная платформа
 */

document.addEventListener('DOMContentLoaded', function() {
    initializeAutoReinvestment();
});

function initializeAutoReinvestment() {
    // Инициализация ползунка процента
    initRangeSlider();
    
    // Инициализация предварительного расчета
    initCalculationPreview();
    
    // Инициализация тестового запуска
    initTestReinvestment();
    
    // Инициализация переключателя
    initToggleSwitch();
}

// Ползунок процента реинвестирования
function initRangeSlider() {
    const rangeInput = document.getElementById('reinvest_percent');
    const rangeValue = document.querySelector('.range-value');
    
    if (rangeInput && rangeValue) {
        rangeInput.addEventListener('input', function() {
            rangeValue.textContent = this.value + '%';
            updateCalculationPreview();
        });
    }
}

// Предварительный расчет
function initCalculationPreview() {
    const inputs = [
        'min_amount',
        'reinvest_percent',
        'max_investments_per_day'
    ];
    
    inputs.forEach(inputId => {
        const input = document.getElementById(inputId);
        if (input) {
            input.addEventListener('input', updateCalculationPreview);
        }
    });
    
    // Первоначальный расчет
    updateCalculationPreview();
}

// Обновление предварительного расчета
function updateCalculationPreview() {
    const minAmount = parseFloat(document.getElementById('min_amount').value) || 0;
    const reinvestPercent = parseFloat(document.getElementById('reinvest_percent').value) || 0;
    
    // Симуляция доступной прибыли (в реальности это будет получаться с сервера)
    const simulatedProfit = 150.75; // Пример накопленной прибыли
    
    const availableAmount = simulatedProfit;
    const reinvestAmount = (availableAmount * reinvestPercent) / 100;
    
    // Обновление отображения
    document.getElementById('available-amount').textContent = formatCurrency(availableAmount) + ' USDT';
    document.getElementById('reinvest-amount').textContent = formatCurrency(reinvestAmount) + ' USDT';
    
    // Определение статуса
    let status = '';
    let statusClass = '';
    
    if (reinvestAmount >= minAmount) {
        status = 'Готово к реинвестированию';
        statusClass = 'status-ready';
    } else if (availableAmount > 0) {
        status = 'Недостаточно для минимальной суммы';
        statusClass = 'status-waiting';
    } else {
        status = 'Нет доступной прибыли';
        statusClass = 'status-none';
    }
    
    const statusElement = document.getElementById('reinvest-status');
    statusElement.textContent = status;
    statusElement.className = 'preview-value ' + statusClass;
}

// Тестовый запуск реинвестирования
function initTestReinvestment() {
    const testButton = document.getElementById('test-reinvestment');
    
    if (testButton) {
        testButton.addEventListener('click', function() {
            this.disabled = true;
            this.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Выполняется...';
            
            // Симуляция тестового запуска
            setTimeout(() => {
                showTestResults();
                this.disabled = false;
                this.innerHTML = '<i class="fas fa-play"></i> Тестовый запуск';
            }, 2000);
        });
    }
}

// Показ результатов тестового запуска
function showTestResults() {
    const modal = createTestResultsModal();
    document.body.appendChild(modal);
    
    // Показ модального окна
    setTimeout(() => {
        modal.classList.add('show');
    }, 100);
}

// Создание модального окна с результатами теста
function createTestResultsModal() {
    const modal = document.createElement('div');
    modal.className = 'modal test-results-modal';
    
    const minAmount = parseFloat(document.getElementById('min_amount').value) || 0;
    const reinvestPercent = parseFloat(document.getElementById('reinvest_percent').value) || 0;
    const maxPerDay = parseInt(document.getElementById('max_investments_per_day').value) || 1;
    const preferredPackage = document.getElementById('preferred_package_id').selectedOptions[0].text;
    
    // Симуляция результатов
    const simulatedProfit = 150.75;
    const reinvestAmount = (simulatedProfit * reinvestPercent) / 100;
    const canReinvest = reinvestAmount >= minAmount;
    
    modal.innerHTML = `
        <div class="modal-content">
            <div class="modal-header">
                <h3>Результаты тестового запуска</h3>
                <button class="modal-close">&times;</button>
            </div>
            
            <div class="modal-body">
                <div class="test-results">
                    <div class="result-status ${canReinvest ? 'success' : 'warning'}">
                        <i class="fas ${canReinvest ? 'fa-check-circle' : 'fa-exclamation-triangle'}"></i>
                        <span>${canReinvest ? 'Реинвестирование возможно' : 'Реинвестирование невозможно'}</span>
                    </div>
                    
                    <div class="test-details">
                        <h4>Детали проверки:</h4>
                        
                        <div class="detail-item">
                            <span class="detail-label">Доступная прибыль:</span>
                            <span class="detail-value">${formatCurrency(simulatedProfit)} USDT</span>
                        </div>
                        
                        <div class="detail-item">
                            <span class="detail-label">Сумма для реинвестирования:</span>
                            <span class="detail-value">${formatCurrency(reinvestAmount)} USDT (${reinvestPercent}%)</span>
                        </div>
                        
                        <div class="detail-item">
                            <span class="detail-label">Минимальная сумма:</span>
                            <span class="detail-value">${formatCurrency(minAmount)} USDT</span>
                        </div>
                        
                        <div class="detail-item">
                            <span class="detail-label">Выбранный пакет:</span>
                            <span class="detail-value">${preferredPackage}</span>
                        </div>
                        
                        <div class="detail-item">
                            <span class="detail-label">Лимит в день:</span>
                            <span class="detail-value">${maxPerDay} инвестиций</span>
                        </div>
                        
                        <div class="detail-item">
                            <span class="detail-label">Инвестиций сегодня:</span>
                            <span class="detail-value">0 из ${maxPerDay}</span>
                        </div>
                    </div>
                    
                    ${canReinvest ? `
                        <div class="success-message">
                            <i class="fas fa-info-circle"></i>
                            При следующем запуске автореинвестирования будет создана инвестиция на сумму ${formatCurrency(reinvestAmount)} USDT.
                        </div>
                    ` : `
                        <div class="warning-message">
                            <i class="fas fa-exclamation-triangle"></i>
                            ${reinvestAmount < minAmount ? 
                                'Накопленной прибыли недостаточно для создания инвестиции. Дождитесь накопления минимальной суммы.' :
                                'Проверьте настройки и убедитесь, что все параметры корректны.'
                            }
                        </div>
                    `}
                </div>
            </div>
            
            <div class="modal-footer">
                <button class="btn btn-secondary modal-close">Закрыть</button>
                ${canReinvest ? '<button class="btn btn-primary" onclick="executeRealReinvestment()">Выполнить сейчас</button>' : ''}
            </div>
        </div>
    `;
    
    // Обработчики закрытия
    const closeButtons = modal.querySelectorAll('.modal-close');
    closeButtons.forEach(button => {
        button.addEventListener('click', () => {
            modal.classList.remove('show');
            setTimeout(() => {
                if (modal.parentNode) {
                    modal.parentNode.removeChild(modal);
                }
            }, 300);
        });
    });
    
    // Закрытие по клику вне модального окна
    modal.addEventListener('click', (e) => {
        if (e.target === modal) {
            modal.classList.remove('show');
            setTimeout(() => {
                if (modal.parentNode) {
                    modal.parentNode.removeChild(modal);
                }
            }, 300);
        }
    });
    
    return modal;
}

// Переключатель включения/выключения
function initToggleSwitch() {
    const toggleInput = document.querySelector('input[name="is_enabled"]');
    const settingsForm = document.querySelector('.settings-form');
    
    if (toggleInput) {
        toggleInput.addEventListener('change', function() {
            const isEnabled = this.checked;
            const formGroups = settingsForm.querySelectorAll('.form-group:not(:first-child)');
            
            formGroups.forEach(group => {
                if (isEnabled) {
                    group.classList.remove('disabled');
                    const inputs = group.querySelectorAll('input, select');
                    inputs.forEach(input => input.disabled = false);
                } else {
                    group.classList.add('disabled');
                    const inputs = group.querySelectorAll('input, select');
                    inputs.forEach(input => input.disabled = true);
                }
            });
        });
        
        // Первоначальная проверка
        toggleInput.dispatchEvent(new Event('change'));
    }
}

// Выполнение реального реинвестирования
function executeRealReinvestment() {
    if (!confirm('Вы уверены, что хотите выполнить реинвестирование сейчас?')) {
        return;
    }
    
    // Здесь будет AJAX запрос к серверу для выполнения реинвестирования
    showNotification('Функция будет доступна после сохранения настроек', 'info');
}

// Форматирование валюты
function formatCurrency(amount) {
    return new Intl.NumberFormat('ru-RU', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
    }).format(amount);
}

// Показ уведомлений
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
        <div class="notification-content">
            <span class="notification-message">${message}</span>
            <button class="notification-close">&times;</button>
        </div>
    `;
    
    document.body.appendChild(notification);
    
    setTimeout(() => {
        notification.classList.add('show');
    }, 100);
    
    setTimeout(() => {
        hideNotification(notification);
    }, 5000);
    
    notification.querySelector('.notification-close').addEventListener('click', () => {
        hideNotification(notification);
    });
}

function hideNotification(notification) {
    notification.classList.remove('show');
    setTimeout(() => {
        if (notification.parentNode) {
            notification.parentNode.removeChild(notification);
        }
    }, 300);
}

// Анимации при скролле
function initScrollAnimations() {
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };
    
    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('animate-slide-up');
            }
        });
    }, observerOptions);
    
    document.querySelectorAll('.stat-card, .step-item, .settings-section').forEach(element => {
        observer.observe(element);
    });
}

// Инициализация анимаций после загрузки DOM
document.addEventListener('DOMContentLoaded', function() {
    initScrollAnimations();
});
