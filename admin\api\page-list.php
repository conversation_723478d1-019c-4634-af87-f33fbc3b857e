<?php
/**
 * AstroGenix - API для получения списка страниц
 * Используется в редакторе для создания ссылок
 */

require_once '../../config/config.php';

// Проверка авторизации администратора
if (!is_logged_in() || !is_admin()) {
    http_response_code(403);
    exit('Access denied');
}

header('Content-Type: application/json; charset=utf-8');

try {
    $database = new Database();
    $db = $database->getConnection();
    
    // Получение всех опубликованных страниц
    $query = "SELECT id, title, slug, created_at 
              FROM cms_pages 
              WHERE status = 'published' 
              ORDER BY title ASC";
    
    $stmt = $db->prepare($query);
    $stmt->execute();
    $pages = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Формирование списка для TinyMCE
    $page_list = [];
    
    // Добавляем основные страницы сайта
    $page_list[] = [
        'title' => 'Главная страница',
        'value' => SITE_URL
    ];
    
    $page_list[] = [
        'title' => 'Личный кабинет',
        'value' => SITE_URL . '/dashboard.php'
    ];
    
    $page_list[] = [
        'title' => 'Вход',
        'value' => SITE_URL . '/login.php'
    ];
    
    $page_list[] = [
        'title' => 'Регистрация',
        'value' => SITE_URL . '/register.php'
    ];
    
    // Добавляем разделитель
    $page_list[] = [
        'title' => '--- CMS Страницы ---',
        'value' => ''
    ];
    
    // Добавляем страницы из CMS
    foreach ($pages as $page) {
        $page_list[] = [
            'title' => $page['title'],
            'value' => SITE_URL . '/' . $page['slug']
        ];
    }
    
    echo json_encode($page_list, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
    
} catch (Exception $e) {
    error_log("Page list API error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['error' => 'Internal server error'], JSON_UNESCAPED_UNICODE);
}
?>
