<?php
/**
 * AstroGenix - Расширенные инвестиционные пакеты
 * Эко-майнинговая инвестиционная платформа
 */

session_start();
require_once 'config/config.php';
require_once 'classes/Investment.php';
require_once 'classes/EnhancedInvestment.php';

// Проверка авторизации
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit();
}

$database = new Database();
$db = $database->getConnection();
$investment = new Investment($db);
$enhanced_investment = new EnhancedInvestment($db);

$errors = [];
$success_message = '';

// Обработка создания инвестиции
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['create_investment'])) {
    if (!verify_csrf_token($_POST['csrf_token'] ?? '')) {
        $errors[] = 'Ошибка безопасности. Попробуйте еще раз.';
    } else {
        $package_id = intval($_POST['package_id'] ?? 0);
        $amount = floatval($_POST['amount'] ?? 0);
        $auto_reinvest = isset($_POST['auto_reinvest']);
        $reinvest_percent = floatval($_POST['reinvest_percent'] ?? 0);

        $options = [
            'auto_reinvest' => $auto_reinvest,
            'reinvest_percent' => $reinvest_percent
        ];

        $result = $enhanced_investment->createEnhancedInvestment($_SESSION['user_id'], $package_id, $amount, $options);
        
        if ($result['success']) {
            $success_message = 'Инвестиция успешно создана! Ежедневная прибыль будет начисляться автоматически.';
            // Обновление баланса в сессии
            $_SESSION['balance'] = $_SESSION['balance'] - $amount;
        } else {
            $errors[] = $result['error'];
        }
    }
}

// Получение данных
$packages = $enhanced_investment->getAllPackages();
$featured_packages = $enhanced_investment->getAllPackages(true);
$user_balance = $_SESSION['balance'] ?? 0;
$user_analytics = $enhanced_investment->getUserInvestmentAnalytics($_SESSION['user_id']);

$page_title = 'Расширенные инвестиции - AstroGenix';
?>
<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?></title>
    <meta name="description" content="Расширенные инвестиционные возможности на платформе AstroGenix">
    
    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="assets/images/favicon.svg">
    
    <!-- Стили -->
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="assets/css/investments.css">
    <link rel="stylesheet" href="assets/css/animations.css">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <!-- Chart.js для графиков -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
    <?php include 'includes/header.php'; ?>

    <main class="main-content">
        <!-- Hero секция -->
        <section class="hero-section enhanced-investments-hero">
            <div class="container">
                <div class="hero-content animate-fade-in">
                    <h1 class="hero-title">
                        Расширенные <span class="gradient-text">инвестиции</span>
                    </h1>
                    <p class="hero-subtitle">
                        Профессиональные инвестиционные инструменты с продвинутой аналитикой, 
                        автоматическим реинвестированием и персонализированными стратегиями.
                    </p>
                </div>
                
                <!-- Быстрая статистика пользователя -->
                <div class="user-quick-stats">
                    <div class="stat-item">
                        <div class="stat-value"><?php echo number_format($user_analytics['total_investments']); ?></div>
                        <div class="stat-label">Всего инвестиций</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value"><?php echo number_format($user_analytics['total_invested'], 2); ?> USDT</div>
                        <div class="stat-label">Инвестировано</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value"><?php echo number_format($user_analytics['total_earned'], 2); ?> USDT</div>
                        <div class="stat-label">Заработано</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value"><?php echo $user_analytics['roi_percent']; ?>%</div>
                        <div class="stat-label">ROI</div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Сообщения -->
        <?php if (!empty($errors)): ?>
            <div class="alert alert-error">
                <ul>
                    <?php foreach ($errors as $error): ?>
                        <li><?php echo htmlspecialchars($error); ?></li>
                    <?php endforeach; ?>
                </ul>
            </div>
        <?php endif; ?>

        <?php if ($success_message): ?>
            <div class="alert alert-success">
                <?php echo htmlspecialchars($success_message); ?>
            </div>
        <?php endif; ?>

        <!-- Рекомендуемые пакеты -->
        <?php if (!empty($featured_packages)): ?>
        <section class="section featured-packages">
            <div class="container">
                <div class="section-header">
                    <h2 class="section-title">Рекомендуемые пакеты</h2>
                    <p class="section-subtitle">Специально отобранные инвестиционные возможности</p>
                </div>
                
                <div class="packages-grid featured-grid">
                    <?php foreach ($featured_packages as $package): ?>
                        <div class="package-card featured-package animate-on-scroll" data-package-id="<?php echo $package['id']; ?>">
                            <div class="package-badge">Рекомендуем</div>
                            
                            <div class="package-header" style="background: linear-gradient(135deg, <?php echo $package['color']; ?> 0%, <?php echo $package['color']; ?>CC 100%);">
                                <div class="package-icon">
                                    <i class="<?php echo $package['icon']; ?>"></i>
                                </div>
                                <h3 class="package-name"><?php echo htmlspecialchars($package['name']); ?></h3>
                                <div class="package-type"><?php echo htmlspecialchars($package['type_name']); ?></div>
                            </div>
                            
                            <div class="package-body">
                                <div class="package-description">
                                    <?php echo htmlspecialchars($package['description']); ?>
                                </div>
                                
                                <div class="package-profit-info">
                                    <div class="profit-main">
                                        <?php if ($package['package_type'] === 'variable'): ?>
                                            <span class="profit-range">
                                                <?php echo $package['min_profit_percent']; ?>% - <?php echo $package['max_profit_percent']; ?>%
                                            </span>
                                        <?php else: ?>
                                            <span class="profit-rate"><?php echo $package['daily_profit_percent']; ?>%</span>
                                        <?php endif; ?>
                                        <span class="profit-period">в день</span>
                                    </div>
                                    
                                    <?php if ($package['bonus_percent'] > 0): ?>
                                        <div class="bonus-info">
                                            +<?php echo $package['bonus_percent']; ?>% бонус
                                        </div>
                                    <?php endif; ?>
                                </div>
                                
                                <div class="package-details">
                                    <div class="detail-item">
                                        <span class="detail-label">Сумма:</span>
                                        <span class="detail-value">
                                            <?php echo number_format($package['min_amount']); ?> - <?php echo number_format($package['max_amount']); ?> USDT
                                        </span>
                                    </div>
                                    <div class="detail-item">
                                        <span class="detail-label">Срок:</span>
                                        <span class="detail-value"><?php echo $package['duration_days']; ?> дней</span>
                                    </div>
                                    <div class="detail-item">
                                        <span class="detail-label">Риск:</span>
                                        <span class="detail-value risk-<?php echo $package['risk_level']; ?>">
                                            <?php echo htmlspecialchars($package['risk_name']); ?>
                                        </span>
                                    </div>
                                </div>
                                
                                <div class="package-features">
                                    <?php foreach ($package['features'] as $feature): ?>
                                        <div class="feature-item">
                                            <i class="fas fa-check"></i>
                                            <?php echo htmlspecialchars($feature); ?>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                            
                            <div class="package-footer">
                                <button class="btn btn-primary btn-full invest-btn" 
                                        data-package-id="<?php echo $package['id']; ?>"
                                        data-package-name="<?php echo htmlspecialchars($package['name']); ?>"
                                        data-min-amount="<?php echo $package['min_amount']; ?>"
                                        data-max-amount="<?php echo $package['max_amount']; ?>"
                                        data-profit-percent="<?php echo $package['daily_profit_percent']; ?>"
                                        data-duration="<?php echo $package['duration_days']; ?>"
                                        data-package-type="<?php echo $package['package_type']; ?>">
                                    <i class="<?php echo $package['icon']; ?>"></i>
                                    Инвестировать
                                </button>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </section>
        <?php endif; ?>

        <!-- Все пакеты -->
        <section class="section all-packages">
            <div class="container">
                <div class="section-header">
                    <h2 class="section-title">Все инвестиционные пакеты</h2>
                    <div class="package-filters">
                        <button class="filter-btn active" data-filter="all">Все</button>
                        <button class="filter-btn" data-filter="simple">Простые</button>
                        <button class="filter-btn" data-filter="compound">Компаундные</button>
                        <button class="filter-btn" data-filter="fixed">Фиксированные</button>
                        <button class="filter-btn" data-filter="variable">Переменные</button>
                        <button class="filter-btn" data-filter="vip">VIP</button>
                    </div>
                </div>
                
                <div class="packages-grid all-packages-grid">
                    <?php foreach ($packages as $package): ?>
                        <div class="package-card animate-on-scroll" 
                             data-package-id="<?php echo $package['id']; ?>"
                             data-package-type="<?php echo $package['package_type']; ?>">
                            
                            <div class="package-header" style="background: linear-gradient(135deg, <?php echo $package['color']; ?> 0%, <?php echo $package['color']; ?>CC 100%);">
                                <div class="package-icon">
                                    <i class="<?php echo $package['icon']; ?>"></i>
                                </div>
                                <h3 class="package-name"><?php echo htmlspecialchars($package['name']); ?></h3>
                                <div class="package-type"><?php echo htmlspecialchars($package['type_name']); ?></div>
                            </div>
                            
                            <div class="package-body">
                                <div class="package-description">
                                    <?php echo htmlspecialchars($package['description']); ?>
                                </div>
                                
                                <div class="package-profit-info">
                                    <div class="profit-main">
                                        <?php if ($package['package_type'] === 'variable'): ?>
                                            <span class="profit-range">
                                                <?php echo $package['min_profit_percent']; ?>% - <?php echo $package['max_profit_percent']; ?>%
                                            </span>
                                        <?php else: ?>
                                            <span class="profit-rate"><?php echo $package['daily_profit_percent']; ?>%</span>
                                        <?php endif; ?>
                                        <span class="profit-period">в день</span>
                                    </div>
                                    
                                    <?php if ($package['bonus_percent'] > 0): ?>
                                        <div class="bonus-info">
                                            +<?php echo $package['bonus_percent']; ?>% бонус
                                        </div>
                                    <?php endif; ?>
                                </div>
                                
                                <div class="package-details">
                                    <div class="detail-item">
                                        <span class="detail-label">Сумма:</span>
                                        <span class="detail-value">
                                            <?php echo number_format($package['min_amount']); ?> - <?php echo number_format($package['max_amount']); ?> USDT
                                        </span>
                                    </div>
                                    <div class="detail-item">
                                        <span class="detail-label">Срок:</span>
                                        <span class="detail-value"><?php echo $package['duration_days']; ?> дней</span>
                                    </div>
                                    <div class="detail-item">
                                        <span class="detail-label">Риск:</span>
                                        <span class="detail-value risk-<?php echo $package['risk_level']; ?>">
                                            <?php echo htmlspecialchars($package['risk_name']); ?>
                                        </span>
                                    </div>
                                </div>
                                
                                <div class="package-features">
                                    <?php foreach (array_slice($package['features'], 0, 4) as $feature): ?>
                                        <div class="feature-item">
                                            <i class="fas fa-check"></i>
                                            <?php echo htmlspecialchars($feature); ?>
                                        </div>
                                    <?php endforeach; ?>
                                    
                                    <?php if (count($package['features']) > 4): ?>
                                        <div class="feature-item more-features">
                                            <i class="fas fa-plus"></i>
                                            Еще <?php echo count($package['features']) - 4; ?> возможностей
                                        </div>
                                    <?php endif; ?>
                                </div>
                                
                                <?php if (!empty($package['requirements'])): ?>
                                    <div class="package-requirements">
                                        <div class="requirements-title">Требования:</div>
                                        <?php foreach ($package['requirements'] as $requirement): ?>
                                            <div class="requirement-item">
                                                <i class="fas fa-info-circle"></i>
                                                <?php echo htmlspecialchars($requirement); ?>
                                            </div>
                                        <?php endforeach; ?>
                                    </div>
                                <?php endif; ?>
                            </div>
                            
                            <div class="package-footer">
                                <button class="btn btn-primary btn-full invest-btn" 
                                        data-package-id="<?php echo $package['id']; ?>"
                                        data-package-name="<?php echo htmlspecialchars($package['name']); ?>"
                                        data-min-amount="<?php echo $package['min_amount']; ?>"
                                        data-max-amount="<?php echo $package['max_amount']; ?>"
                                        data-profit-percent="<?php echo $package['daily_profit_percent']; ?>"
                                        data-duration="<?php echo $package['duration_days']; ?>"
                                        data-package-type="<?php echo $package['package_type']; ?>">
                                    <i class="<?php echo $package['icon']; ?>"></i>
                                    Инвестировать
                                </button>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </section>

        <!-- Расширенный калькулятор -->
        <section class="section enhanced-calculator">
            <div class="container">
                <div class="section-header">
                    <h2 class="section-title">Калькулятор доходности</h2>
                    <p class="section-subtitle">Рассчитайте потенциальную прибыль с учетом всех параметров</p>
                </div>
                
                <div class="calculator-container">
                    <div class="calculator-form">
                        <div class="form-row">
                            <div class="form-group">
                                <label for="calc-package">Инвестиционный пакет</label>
                                <select id="calc-package" class="form-control">
                                    <option value="">Выберите пакет</option>
                                    <?php foreach ($packages as $package): ?>
                                        <option value="<?php echo $package['id']; ?>" 
                                                data-min="<?php echo $package['min_amount']; ?>"
                                                data-max="<?php echo $package['max_amount']; ?>"
                                                data-profit="<?php echo $package['daily_profit_percent']; ?>"
                                                data-duration="<?php echo $package['duration_days']; ?>"
                                                data-type="<?php echo $package['package_type']; ?>"
                                                data-bonus="<?php echo $package['bonus_percent']; ?>">
                                            <?php echo htmlspecialchars($package['name']); ?> (<?php echo $package['daily_profit_percent']; ?>%)
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            
                            <div class="form-group">
                                <label for="calc-amount">Сумма инвестиции (USDT)</label>
                                <input type="number" id="calc-amount" min="50" value="1000" class="form-control">
                                <div class="amount-range">
                                    <span class="range-min">Мин: 50 USDT</span>
                                    <span class="range-max">Макс: 50,000 USDT</span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="form-row">
                            <div class="form-group">
                                <label>
                                    <input type="checkbox" id="calc-reinvest"> 
                                    Автоматическое реинвестирование
                                </label>
                                <div class="reinvest-options" style="display: none;">
                                    <label for="calc-reinvest-percent">Процент для реинвестирования</label>
                                    <input type="range" id="calc-reinvest-percent" min="0" max="100" value="50" class="form-range">
                                    <span class="range-value">50%</span>
                                </div>
                            </div>
                        </div>
                        
                        <button type="button" id="calculate-btn" class="btn btn-primary">
                            <i class="fas fa-calculator"></i>
                            Рассчитать прибыль
                        </button>
                    </div>
                    
                    <div class="calculator-results" id="calculator-results" style="display: none;">
                        <div class="results-header">
                            <h3>Прогноз доходности</h3>
                        </div>
                        
                        <div class="results-grid">
                            <div class="result-item">
                                <div class="result-label">Ежедневная прибыль</div>
                                <div class="result-value" id="daily-profit">0 USDT</div>
                            </div>
                            <div class="result-item">
                                <div class="result-label">Прибыль за период</div>
                                <div class="result-value" id="total-profit">0 USDT</div>
                            </div>
                            <div class="result-item">
                                <div class="result-label">Общий доход</div>
                                <div class="result-value" id="total-return">0 USDT</div>
                            </div>
                            <div class="result-item">
                                <div class="result-label">ROI</div>
                                <div class="result-value" id="roi-percent">0%</div>
                            </div>
                        </div>
                        
                        <div class="profit-chart-container">
                            <canvas id="profit-chart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- Модальное окно инвестирования -->
    <div id="investment-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="modal-package-name">Создание инвестиции</h3>
                <button class="modal-close">&times;</button>
            </div>
            
            <form method="POST" class="investment-form">
                <input type="hidden" name="csrf_token" value="<?php echo generate_csrf_token(); ?>">
                <input type="hidden" name="create_investment" value="1">
                <input type="hidden" name="package_id" id="modal-package-id">
                
                <div class="modal-body">
                    <div class="package-info" id="modal-package-info">
                        <!-- Информация о пакете будет загружена через JavaScript -->
                    </div>
                    
                    <div class="form-group">
                        <label for="modal-amount">Сумма инвестиции (USDT)</label>
                        <input type="number" id="modal-amount" name="amount" required class="form-control">
                        <div class="amount-info">
                            <span>Доступно: <?php echo number_format($user_balance, 2); ?> USDT</span>
                        </div>
                    </div>
                    
                    <div class="advanced-options">
                        <h4>Дополнительные настройки</h4>
                        
                        <div class="form-group">
                            <label class="checkbox-label">
                                <input type="checkbox" name="auto_reinvest" id="modal-auto-reinvest">
                                <span class="checkbox-custom"></span>
                                Автоматическое реинвестирование прибыли
                            </label>
                        </div>
                        
                        <div class="reinvest-settings" id="modal-reinvest-settings" style="display: none;">
                            <div class="form-group">
                                <label for="modal-reinvest-percent">Процент для реинвестирования</label>
                                <input type="range" id="modal-reinvest-percent" name="reinvest_percent" min="0" max="100" value="50" class="form-range">
                                <div class="range-display">
                                    <span id="modal-reinvest-value">50</span>%
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="investment-summary" id="modal-investment-summary">
                        <!-- Сводка по инвестиции -->
                    </div>
                </div>
                
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary modal-close">Отмена</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-rocket"></i>
                        Создать инвестицию
                    </button>
                </div>
            </form>
        </div>
    </div>

    <?php include 'includes/footer.php'; ?>

    <!-- JavaScript -->
    <script src="assets/js/main.js"></script>
    <script src="assets/js/enhanced-investments.js"></script>
</body>
</html>
