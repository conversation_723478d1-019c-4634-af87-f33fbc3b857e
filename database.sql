-- AstroGenix Database Schema
-- Эко-майнинговая инвестиционная платформа

CREATE DATABASE IF NOT EXISTS astrogenix CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE astrogenix;

-- Таблица пользователей
CREATE TABLE users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VA<PERSON>HA<PERSON>(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    first_name VA<PERSON>HAR(50) NOT NULL,
    last_name <PERSON><PERSON><PERSON><PERSON>(50) NOT NULL,
    phone VARCHAR(20),
    balance DECIMAL(15,2) DEFAULT 0.00,
    total_invested DECIMAL(15,2) DEFAULT 0.00,
    total_earned DECIMAL(15,2) DEFAULT 0.00,
    referral_code VARCHAR(20) UNIQUE NOT NULL,
    referred_by INT NULL,
    email_verified BOOLEAN DEFAULT FALSE,
    email_verification_token VARCHAR(100),
    password_reset_token VARCHAR(100),
    password_reset_expires DATETIME,
    is_active BOOLEAN DEFAULT TRUE,
    is_admin BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (referred_by) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_referral_code (referral_code),
    INDEX idx_email (email),
    INDEX idx_referred_by (referred_by)
);

-- Таблица инвестиционных пакетов (тарифы)
CREATE TABLE investment_packages (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    min_amount DECIMAL(15,2) NOT NULL,
    max_amount DECIMAL(15,2) NOT NULL,
    daily_profit_percent DECIMAL(5,2) NOT NULL,
    duration_days INT DEFAULT 30,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Таблица активных инвестиций пользователей
CREATE TABLE user_investments (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    package_id INT NOT NULL,
    amount DECIMAL(15,2) NOT NULL,
    daily_profit DECIMAL(15,2) NOT NULL,
    total_earned DECIMAL(15,2) DEFAULT 0.00,
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    status ENUM('active', 'completed', 'cancelled') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (package_id) REFERENCES investment_packages(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_status (status),
    INDEX idx_end_date (end_date)
);

-- Таблица транзакций
CREATE TABLE transactions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    type ENUM('deposit', 'withdrawal', 'investment', 'profit', 'referral_bonus') NOT NULL,
    amount DECIMAL(15,2) NOT NULL,
    status ENUM('pending', 'approved', 'rejected', 'completed') DEFAULT 'pending',
    description TEXT,
    admin_note TEXT,
    processed_by INT NULL,
    processed_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (processed_by) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_user_id (user_id),
    INDEX idx_type (type),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at)
);

-- Таблица реферальных комиссий
CREATE TABLE referral_commissions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    referrer_id INT NOT NULL,
    referred_id INT NOT NULL,
    level INT NOT NULL CHECK (level BETWEEN 1 AND 3),
    commission_percent DECIMAL(5,2) NOT NULL,
    amount DECIMAL(15,2) NOT NULL,
    source_transaction_id INT NOT NULL,
    status ENUM('pending', 'paid') DEFAULT 'pending',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (referrer_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (referred_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (source_transaction_id) REFERENCES transactions(id) ON DELETE CASCADE,
    INDEX idx_referrer_id (referrer_id),
    INDEX idx_referred_id (referred_id),
    INDEX idx_level (level)
);

-- Таблица настроек системы
CREATE TABLE system_settings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    setting_key VARCHAR(100) UNIQUE NOT NULL,
    setting_value TEXT NOT NULL,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Таблица для отслеживания ежедневных прибылей
CREATE TABLE daily_profits (
    id INT AUTO_INCREMENT PRIMARY KEY,
    investment_id INT NOT NULL,
    user_id INT NOT NULL,
    amount DECIMAL(15,2) NOT NULL,
    profit_date DATE NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (investment_id) REFERENCES user_investments(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_daily_profit (investment_id, profit_date),
    INDEX idx_user_id (user_id),
    INDEX idx_profit_date (profit_date)
);

-- Таблица для счетчика зеленой энергии
CREATE TABLE green_energy_stats (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    energy_generated DECIMAL(15,2) DEFAULT 0.00,
    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id)
);

-- Вставка базовых настроек системы
INSERT INTO system_settings (setting_key, setting_value, description) VALUES
('site_name', 'AstroGenix', 'Название сайта'),
('site_description', 'Эко-майнинговая инвестиционная платформа', 'Описание сайта'),
('referral_level_1_percent', '5.00', 'Процент комиссии 1-го уровня'),
('referral_level_2_percent', '3.00', 'Процент комиссии 2-го уровня'),
('referral_level_3_percent', '2.00', 'Процент комиссии 3-го уровня'),
('min_withdrawal_amount', '10.00', 'Минимальная сумма для вывода'),
('max_withdrawal_amount', '10000.00', 'Максимальная сумма для вывода'),
('email_verification_required', '1', 'Требуется ли верификация email'),
('maintenance_mode', '0', 'Режим технического обслуживания'),
('total_green_energy', '0.00', 'Общее количество сгенерированной зеленой энергии');

-- Вставка базовых инвестиционных пакетов
INSERT INTO investment_packages (name, description, min_amount, max_amount, daily_profit_percent, duration_days) VALUES
('Эко Старт', 'Начальный пакет для новичков в эко-майнинге', 10.00, 100.00, 1.50, 30),
('Зеленая Энергия', 'Средний пакет с хорошей доходностью', 101.00, 500.00, 2.00, 30),
('Солнечная Мощь', 'Продвинутый пакет для опытных инвесторов', 501.00, 1000.00, 2.50, 30),
('Эко Премиум', 'Премиальный пакет с максимальной доходностью', 1001.00, 5000.00, 3.00, 30),
('Астро Элит', 'Элитный пакет для крупных инвесторов', 5001.00, 50000.00, 3.50, 30);

-- Создание администратора по умолчанию (пароль: admin123)
INSERT INTO users (username, email, password_hash, first_name, last_name, referral_code, email_verified, is_admin) VALUES
('admin', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Администратор', 'Системы', 'ADMIN001', TRUE, TRUE);
