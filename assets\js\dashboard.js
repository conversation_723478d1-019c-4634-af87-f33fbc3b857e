/**
 * AstroGenix - JavaScript для дашборда
 * Эко-майнинговая инвестиционная платформа
 */

document.addEventListener('DOMContentLoaded', function() {
    initializeDashboard();
});

function initializeDashboard() {
    // Инициализация мобильного меню
    initMobileMenu();
    
    // Инициализация графика прибыли
    initProfitChart();
    
    // Инициализация счетчика энергии
    initEnergyCounter();
    
    // Инициализация анимаций
    initDashboardAnimations();
    
    // Инициализация обновления данных
    initDataRefresh();
}

// Мобильное меню
function initMobileMenu() {
    const mobileToggle = document.getElementById('mobileMenuToggle');
    const sidebar = document.querySelector('.sidebar');
    const overlay = document.createElement('div');
    
    overlay.className = 'sidebar-overlay';
    overlay.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.5);
        z-index: 999;
        display: none;
        opacity: 0;
        transition: opacity 0.3s ease;
    `;
    
    document.body.appendChild(overlay);
    
    if (mobileToggle) {
        mobileToggle.addEventListener('click', function() {
            toggleSidebar();
        });
    }
    
    overlay.addEventListener('click', function() {
        closeSidebar();
    });
    
    function toggleSidebar() {
        if (sidebar.classList.contains('open')) {
            closeSidebar();
        } else {
            openSidebar();
        }
    }
    
    function openSidebar() {
        sidebar.classList.add('open');
        overlay.style.display = 'block';
        setTimeout(() => {
            overlay.style.opacity = '1';
        }, 10);
        document.body.style.overflow = 'hidden';
    }
    
    function closeSidebar() {
        sidebar.classList.remove('open');
        overlay.style.opacity = '0';
        setTimeout(() => {
            overlay.style.display = 'none';
        }, 300);
        document.body.style.overflow = '';
    }
    
    // Закрытие при изменении размера экрана
    window.addEventListener('resize', function() {
        if (window.innerWidth > 768) {
            closeSidebar();
        }
    });
}

// График прибыли
function initProfitChart() {
    const canvas = document.getElementById('profitChart');
    if (!canvas) return;
    
    const ctx = canvas.getContext('2d');
    
    // Генерация данных для демонстрации
    const generateChartData = (days) => {
        const data = [];
        const labels = [];
        const today = new Date();
        
        for (let i = days - 1; i >= 0; i--) {
            const date = new Date(today);
            date.setDate(date.getDate() - i);
            
            labels.push(date.toLocaleDateString('ru-RU', { 
                month: 'short', 
                day: 'numeric' 
            }));
            
            // Генерация случайных данных с трендом роста
            const baseValue = 1000 + (days - i) * 50;
            const randomVariation = (Math.random() - 0.5) * 200;
            data.push(Math.max(0, baseValue + randomVariation));
        }
        
        return { labels, data };
    };
    
    let currentPeriod = 7;
    let chartInstance = null;
    
    const createChart = (period) => {
        const chartData = generateChartData(period);
        
        if (chartInstance) {
            chartInstance.destroy();
        }
        
        chartInstance = new Chart(ctx, {
            type: 'line',
            data: {
                labels: chartData.labels,
                datasets: [{
                    label: 'Прибыль (₽)',
                    data: chartData.data,
                    borderColor: 'rgb(46, 204, 113)',
                    backgroundColor: 'rgba(46, 204, 113, 0.1)',
                    borderWidth: 3,
                    fill: true,
                    tension: 0.4,
                    pointBackgroundColor: 'rgb(46, 204, 113)',
                    pointBorderColor: '#fff',
                    pointBorderWidth: 2,
                    pointRadius: 5,
                    pointHoverRadius: 8
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    x: {
                        grid: {
                            display: false
                        },
                        ticks: {
                            color: '#6C757D'
                        }
                    },
                    y: {
                        grid: {
                            color: 'rgba(0, 0, 0, 0.1)'
                        },
                        ticks: {
                            color: '#6C757D',
                            callback: function(value) {
                                return value.toLocaleString('ru-RU') + ' ₽';
                            }
                        }
                    }
                },
                interaction: {
                    intersect: false,
                    mode: 'index'
                },
                elements: {
                    point: {
                        hoverBackgroundColor: 'rgb(46, 204, 113)'
                    }
                }
            }
        });
    };
    
    // Инициализация графика
    createChart(currentPeriod);
    
    // Обработчики кнопок периода
    const periodButtons = document.querySelectorAll('.chart-period');
    periodButtons.forEach(button => {
        button.addEventListener('click', function() {
            // Удаление активного класса у всех кнопок
            periodButtons.forEach(btn => btn.classList.remove('active'));
            
            // Добавление активного класса к текущей кнопке
            this.classList.add('active');
            
            // Обновление графика
            const period = parseInt(this.dataset.period);
            currentPeriod = period;
            createChart(period);
        });
    });
}

// Счетчик зеленой энергии
function initEnergyCounter() {
    const counter = document.getElementById('userEnergyCounter');
    if (!counter) return;
    
    const targetValue = parseFloat(counter.textContent);
    let currentValue = 0;
    const increment = targetValue / 100;
    const duration = 2000; // 2 секунды
    const stepTime = duration / 100;
    
    const updateCounter = () => {
        currentValue += increment;
        if (currentValue >= targetValue) {
            currentValue = targetValue;
            counter.textContent = currentValue.toFixed(2);
            return;
        }
        
        counter.textContent = currentValue.toFixed(2);
        setTimeout(updateCounter, stepTime);
    };
    
    // Запуск анимации при появлении элемента в области видимости
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                updateCounter();
                observer.unobserve(entry.target);
            }
        });
    });
    
    observer.observe(counter);
}

// Анимации дашборда
function initDashboardAnimations() {
    // Анимация появления карточек статистики
    const statCards = document.querySelectorAll('.stat-card');
    statCards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(20px)';
        
        setTimeout(() => {
            card.style.transition = 'all 0.6s ease';
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, index * 100);
    });
    
    // Анимация виджетов
    const widgets = document.querySelectorAll('.dashboard-widget');
    const widgetObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
            }
        });
    });
    
    widgets.forEach(widget => {
        widget.style.opacity = '0';
        widget.style.transform = 'translateY(30px)';
        widget.style.transition = 'all 0.6s ease';
        widgetObserver.observe(widget);
    });
    
    // Анимация прогресс-баров
    const progressBars = document.querySelectorAll('.progress-fill');
    progressBars.forEach(bar => {
        const width = bar.style.width;
        bar.style.width = '0%';
        
        setTimeout(() => {
            bar.style.transition = 'width 1s ease';
            bar.style.width = width;
        }, 500);
    });
}

// Обновление данных
function initDataRefresh() {
    // Автоматическое обновление данных каждые 5 минут
    setInterval(() => {
        refreshDashboardData();
    }, 5 * 60 * 1000);
    
    // Обновление при фокусе на вкладке
    document.addEventListener('visibilitychange', () => {
        if (!document.hidden) {
            refreshDashboardData();
        }
    });
}

function refreshDashboardData() {
    // Здесь можно добавить AJAX запросы для обновления данных
    console.log('Обновление данных дашборда...');
    
    // Пример обновления баланса
    updateBalance();
    
    // Пример обновления уведомлений
    checkNotifications();
}

function updateBalance() {
    // Симуляция обновления баланса
    const balanceElements = document.querySelectorAll('.balance-amount');
    balanceElements.forEach(element => {
        element.style.animation = 'pulse 0.5s ease';
        setTimeout(() => {
            element.style.animation = '';
        }, 500);
    });
}

function checkNotifications() {
    // Проверка новых уведомлений
    // Здесь можно добавить логику для показа уведомлений
}

// Утилиты
function formatCurrency(amount) {
    return new Intl.NumberFormat('en-US', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
    }).format(amount) + ' USDT';
}

function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
        <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-triangle' : 'info-circle'}"></i>
        <span>${message}</span>
        <button class="notification-close">
            <i class="fas fa-times"></i>
        </button>
    `;
    
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: ${type === 'success' ? '#28a745' : type === 'error' ? '#dc3545' : '#17a2b8'};
        color: white;
        padding: 16px 20px;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        z-index: 9999;
        display: flex;
        align-items: center;
        gap: 12px;
        max-width: 400px;
        transform: translateX(100%);
        transition: transform 0.3s ease;
    `;
    
    document.body.appendChild(notification);
    
    // Анимация появления
    setTimeout(() => {
        notification.style.transform = 'translateX(0)';
    }, 100);
    
    // Обработчик закрытия
    const closeBtn = notification.querySelector('.notification-close');
    closeBtn.addEventListener('click', () => {
        hideNotification(notification);
    });
    
    // Автоматическое скрытие
    setTimeout(() => {
        hideNotification(notification);
    }, 5000);
}

function hideNotification(notification) {
    notification.style.transform = 'translateX(100%)';
    setTimeout(() => {
        if (notification.parentElement) {
            notification.parentElement.removeChild(notification);
        }
    }, 300);
}

// Добавление CSS анимаций
const style = document.createElement('style');
style.textContent = `
    @keyframes pulse {
        0% { transform: scale(1); }
        50% { transform: scale(1.05); }
        100% { transform: scale(1); }
    }
    
    .notification-close {
        background: none;
        border: none;
        color: inherit;
        cursor: pointer;
        padding: 4px;
        border-radius: 4px;
        transition: background 0.2s ease;
    }
    
    .notification-close:hover {
        background: rgba(255, 255, 255, 0.2);
    }
`;
document.head.appendChild(style);
