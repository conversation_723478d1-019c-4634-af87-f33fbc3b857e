# AstroGenix - Дизайн-система

## Обзор

AstroGenix использует современную дизайн-систему с зелено-фиолетовой цветовой схемой, отражающей экологическую направленность платформы и инновационные технологии.

## Цветовая палитра

### Основные цвета
- **Основной зеленый**: `#2ECC71` - символизирует экологию и рост
- **Основной фиолетовый**: `#8E44AD` - представляет инновации и технологии
- **Темный зеленый**: `#27AE60` - для hover-эффектов
- **Темный фиолетовый**: `#7D3C98` - для акцентов

### Градиенты
- **Основной градиент**: `linear-gradient(135deg, #2ECC71 0%, #8E44AD 100%)`
- **Вторичный градиент**: `linear-gradient(135deg, #58D68D 0%, #BB8FCE 100%)`
- **Темный градиент**: `linear-gradient(135deg, #27AE60 0%, #7D3C98 100%)`

### Нейтральные цвета
- **Белый**: `#FFFFFF`
- **Черный**: `#1A1A1A`
- **Серая шкала**: от `#F8F9FA` до `#212529`

## Типографика

### Шрифты
- **Основной**: Inter (системные шрифты как fallback)
- **Заголовки**: Poppins
- **Размеры**: от 0.75rem до 3rem с адаптивным масштабированием

### Иерархия заголовков
- **H1**: 3rem (48px) - главные заголовки
- **H2**: 1.875rem (30px) - заголовки секций
- **H3**: 1.5rem (24px) - подзаголовки
- **H4**: 1.25rem (20px) - карточки и компоненты

## Компоненты

### Кнопки
- **Основная**: градиентный фон, белый текст
- **Контурная**: прозрачный фон, зеленая граница
- **Вторичная**: серый фон
- **Размеры**: small, normal, large
- **Состояния**: hover, focus, disabled

### Карточки
- **Базовая**: белый фон, тень, скругленные углы
- **Функции**: иконка, заголовок, описание
- **Пакеты**: заголовок, цена, список функций, кнопка
- **Hover-эффекты**: подъем и увеличение тени

### Формы
- **Поля ввода**: граница, focus-состояние, валидация
- **Лейблы**: полужирный шрифт, серый цвет
- **Ошибки**: красный цвет, иконка предупреждения

## Анимации

### Типы анимаций
- **Появление**: fadeIn, slideUp, scaleIn
- **Прокрутка**: animate-on-scroll с Intersection Observer
- **Hover**: transform, box-shadow
- **Загрузка**: спиннеры, прогресс-бары

### Производительность
- **Оптимизация**: will-change для анимируемых элементов
- **Адаптация**: отключение сложных анимаций на слабых устройствах
- **Доступность**: respect для prefers-reduced-motion

## Адаптивность

### Брейкпоинты
- **Мобильные**: 320px - 480px
- **Планшеты (портрет)**: 481px - 768px
- **Планшеты (альбом)**: 769px - 1024px
- **Десктоп**: 1025px - 1440px
- **Большие экраны**: 1441px+

### Сетки
- **Функции**: auto-fit, minmax(300px, 1fr)
- **Пакеты**: auto-fit, minmax(320px, 1fr)
- **Статистика**: repeat(auto-fit, minmax(200px, 1fr))

### Мобильная оптимизация
- Стекинг элементов в колонку
- Увеличенные области касания (44px минимум)
- Упрощенная навигация
- Оптимизированные размеры шрифтов

## Иконки

### Библиотека
- **Font Awesome 6.0.0** - основная библиотека иконок
- **Стиль**: solid, regular, brands
- **Размеры**: адаптивные через CSS переменные

### Использование
- **Навигация**: fas fa-tachometer-alt, fas fa-chart-line
- **Функции**: fas fa-seedling, fas fa-shield-alt
- **Действия**: fas fa-plus-circle, fas fa-minus-circle

## Доступность

### Контрастность
- **Минимум**: 4.5:1 для обычного текста
- **Заголовки**: 3:1 для крупного текста
- **Высокая контрастность**: поддержка prefers-contrast

### Навигация
- **Клавиатура**: tab-навигация для всех интерактивных элементов
- **Focus**: видимые индикаторы фокуса
- **ARIA**: семантическая разметка

### Анимации
- **Reduced motion**: отключение анимаций по запросу пользователя
- **Производительность**: оптимизация для слабых устройств

## Файловая структура

```
assets/
├── css/
│   ├── style.css          # Основные стили
│   ├── animations.css     # Анимации и эффекты
│   ├── auth.css          # Стили аутентификации
│   └── [page].css        # Специфичные стили страниц
├── js/
│   ├── main.js           # Основная логика
│   ├── animations.js     # Анимации главной страницы
│   └── [page].js         # Специфичная логика страниц
└── images/
    ├── logo.svg          # Логотип
    └── favicon.svg       # Иконка сайта
```

## Рекомендации по использованию

### CSS переменные
Используйте CSS переменные для всех цветов, размеров и отступов:
```css
color: var(--primary-green);
padding: var(--space-4);
font-size: var(--text-lg);
```

### Классы утилит
Применяйте готовые классы для быстрой стилизации:
```html
<div class="d-flex justify-center align-center">
<p class="text-center text-gradient">
```

### Компоненты
Следуйте установленной структуре компонентов:
```html
<div class="feature-card">
    <div class="feature-icon">
        <i class="fas fa-icon"></i>
    </div>
    <h3>Заголовок</h3>
    <p>Описание</p>
</div>
```

## Тестирование

### Адаптивность
- Используйте `test-responsive.html` для проверки
- Тестируйте на реальных устройствах
- Проверяйте все брейкпоинты

### Производительность
- Lighthouse аудит
- Проверка на медленных устройствах
- Оптимизация изображений

### Доступность
- Проверка контрастности
- Тестирование с клавиатуры
- Валидация HTML семантики
