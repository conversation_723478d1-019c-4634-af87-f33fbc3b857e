/**
 * AstroGenix - Живая статистика
 * Динамическое обновление счетчиков на главной странице
 */

class LiveStats {
    constructor() {
        this.apiUrl = 'api/stats.php';
        this.updateInterval = 30000; // 30 секунд
        this.animationDuration = 2000; // 2 секунды для анимации
        this.isAnimating = false;
        
        this.init();
    }
    
    init() {
        // Запускаем первое обновление
        this.updateStats();
        
        // Устанавливаем интервал обновления
        setInterval(() => {
            this.updateStats();
        }, this.updateInterval);
        
        // Обновляем счетчик зеленой энергии каждую секунду
        setInterval(() => {
            this.updateEnergyCounter();
        }, 1000);
    }
    
    async updateStats() {
        try {
            const response = await fetch(this.apiUrl);
            const result = await response.json();
            
            if (result.success && result.data) {
                this.animateCounters(result.data);
            } else {
                console.warn('Failed to fetch stats, using fallback data');
                this.animateCounters(result.data);
            }
        } catch (error) {
            console.error('Error fetching stats:', error);
            // Используем статические данные в случае ошибки
            this.animateCounters({
                total_invested: '284,750',
                active_users: '15,420',
                green_energy: '847,250.0'
            });
        }
    }
    
    animateCounters(data) {
        if (this.isAnimating) return;
        this.isAnimating = true;
        
        // Анимация общего объема инвестиций
        this.animateCounter('total-invested', data.total_invested, 'USDT');
        
        // Анимация активных пользователей
        this.animateCounter('active-users', data.active_users);
        
        // Анимация зеленой энергии
        this.animateCounter('green-energy', data.green_energy, 'кВт⋅ч');
        
        // Анимация счетчика зеленой энергии
        this.animateCounter('live-energy-counter', data.green_energy);
        
        setTimeout(() => {
            this.isAnimating = false;
        }, this.animationDuration);
    }
    
    animateCounter(elementId, targetValue, suffix = '') {
        const element = document.getElementById(elementId);
        if (!element) return;
        
        const currentValue = this.parseNumber(element.textContent);
        const target = this.parseNumber(targetValue);
        
        if (currentValue === target) return;
        
        const startTime = Date.now();
        const difference = target - currentValue;
        
        const animate = () => {
            const elapsed = Date.now() - startTime;
            const progress = Math.min(elapsed / this.animationDuration, 1);
            
            // Используем easing функцию для плавной анимации
            const easeProgress = this.easeOutCubic(progress);
            const current = currentValue + (difference * easeProgress);
            
            // Форматируем число
            let displayValue;
            if (target > 1000) {
                displayValue = this.formatNumber(Math.round(current));
            } else {
                displayValue = Math.round(current * 10) / 10;
            }
            
            element.textContent = displayValue + (suffix ? ' ' + suffix : '');
            
            if (progress < 1) {
                requestAnimationFrame(animate);
            } else {
                element.textContent = targetValue + (suffix ? ' ' + suffix : '');
            }
        };
        
        requestAnimationFrame(animate);
    }
    
    updateEnergyCounter() {
        const element = document.getElementById('live-energy-counter');
        if (!element) return;
        
        const currentValue = this.parseNumber(element.textContent);
        // Увеличиваем на случайное значение от 0.1 до 0.5 каждую секунду
        const increment = Math.random() * 0.4 + 0.1;
        const newValue = currentValue + increment;
        
        element.textContent = this.formatNumber(newValue, 1);
    }
    
    parseNumber(str) {
        return parseFloat(str.replace(/[^\d.-]/g, '')) || 0;
    }
    
    formatNumber(num, decimals = 0) {
        return new Intl.NumberFormat('ru-RU', {
            minimumFractionDigits: decimals,
            maximumFractionDigits: decimals
        }).format(num);
    }
    
    easeOutCubic(t) {
        return 1 - Math.pow(1 - t, 3);
    }
}

// Инициализация при загрузке страницы
document.addEventListener('DOMContentLoaded', () => {
    // Проверяем, что мы на главной странице
    if (document.getElementById('total-invested') || document.getElementById('live-energy-counter')) {
        new LiveStats();
    }
});

// Экспорт для использования в других модулях
if (typeof module !== 'undefined' && module.exports) {
    module.exports = LiveStats;
}
