# 🧪 Руководство по тестированию AstroGenix

## 📋 Чек-лист для тестирования

### ✅ 1. Подготовка к тестированию

**Перед началом тестирования убедитесь:**
- [ ] База данных создана и импортирована из `database.sql`
- [ ] Файл `config/database.php` настроен правильно
- [ ] Файл `config/config.php` содержит корректные настройки
- [ ] Веб-сервер запущен и доступен
- [ ] Права доступа к папкам настроены корректно

### ✅ 2. Тестирование аутентификации

#### 2.1 Регистрация пользователя
- [ ] Откройте `/register.php`
- [ ] Заполните форму регистрации:
  - Имя: `Тест`
  - Фамилия: `Пользователь`
  - Email: `<EMAIL>`
  - Пароль: `test123456`
  - Подтверждение пароля: `test123456`
- [ ] Нажмите "Зарегистрироваться"
- [ ] Проверьте, что пользователь создан в БД
- [ ] Проверьте автоматический вход в систему

#### 2.2 Авторизация
- [ ] Выйдите из системы (`/logout.php`)
- [ ] Откройте `/login.php`
- [ ] Войдите с созданными учетными данными
- [ ] Проверьте перенаправление на дашборд

#### 2.3 Восстановление пароля
- [ ] На странице входа нажмите "Забыли пароль?"
- [ ] Введите email зарегистрированного пользователя
- [ ] Проверьте создание записи в таблице `password_resets`

### ✅ 3. Тестирование пользовательской панели

#### 3.1 Дашборд
- [ ] Откройте `/dashboard.php`
- [ ] Проверьте отображение:
  - Баланса пользователя
  - Общей суммы инвестиций
  - Заработанной прибыли
  - Количества рефералов
- [ ] Проверьте работу графиков
- [ ] Проверьте адаптивность на мобильных устройствах

#### 3.2 Профиль пользователя
- [ ] Откройте `/profile.php`
- [ ] Проверьте все вкладки:
  - Личные данные
  - Безопасность
  - Реферальная ссылка
  - Экология
- [ ] Обновите личные данные
- [ ] Смените пароль
- [ ] Скопируйте реферальную ссылку

### ✅ 4. Тестирование финансовых операций

#### 4.1 Пополнение баланса
- [ ] Откройте `/deposit.php`
- [ ] Создайте запрос на пополнение:
  - Сумма: `1000`
  - Способ: `Банковская карта`
- [ ] Проверьте создание записи в таблице `transactions`
- [ ] Проверьте статус "pending"

#### 4.2 Вывод средств
- [ ] Пополните баланс пользователя в БД
- [ ] Откройте `/withdraw.php`
- [ ] Создайте запрос на вывод:
  - Сумма: `500`
  - Реквизиты: `4111111111111111`
- [ ] Проверьте создание записи в таблице `transactions`

#### 4.3 История транзакций
- [ ] Откройте `/transactions.php`
- [ ] Проверьте отображение всех транзакций
- [ ] Протестируйте фильтры:
  - По типу транзакции
  - По статусу
- [ ] Проверьте пагинацию
- [ ] Протестируйте экспорт в CSV

### ✅ 5. Тестирование инвестиционной системы

#### 5.1 Просмотр пакетов
- [ ] Откройте `/investments.php`
- [ ] Проверьте отображение всех пакетов
- [ ] Проверьте корректность расчетов прибыли

#### 5.2 Создание инвестиции
- [ ] Выберите инвестиционный пакет
- [ ] Введите сумму инвестиции: `1000`
- [ ] Подтвердите создание
- [ ] Проверьте:
  - Списание с баланса
  - Создание записи в `user_investments`
  - Расчет ежедневной прибыли

#### 5.3 Автоматическое начисление прибыли
- [ ] Запустите cron-скрипт: `php cron/daily_profits.php`
- [ ] Проверьте:
  - Создание записей в `daily_profits`
  - Обновление баланса пользователя
  - Создание транзакций типа "profit"

### ✅ 6. Тестирование реферальной программы

#### 6.1 Регистрация по реферальной ссылке
- [ ] Скопируйте реферальную ссылку из профиля
- [ ] Откройте ссылку в новом браузере/инкогнито
- [ ] Зарегистрируйте нового пользователя
- [ ] Проверьте поле `referred_by` в таблице `users`

#### 6.2 Начисление реферальных комиссий
- [ ] Создайте инвестицию от имени реферала
- [ ] Проверьте создание записи в `referral_commissions`
- [ ] Проверьте начисление комиссии рефереру

#### 6.3 Статистика рефералов
- [ ] Откройте `/referrals.php`
- [ ] Проверьте отображение:
  - Общей статистики
  - Списка рефералов по уровням
  - Истории комиссий

### ✅ 7. Тестирование административной панели

#### 7.1 Вход в админку
- [ ] Создайте администратора в БД:
```sql
UPDATE users SET is_admin = 1 WHERE email = '<EMAIL>';
```
- [ ] Войдите как администратор
- [ ] Откройте `/admin/dashboard.php`

#### 7.2 Управление транзакциями
- [ ] Проверьте список ожидающих транзакций
- [ ] Одобрите депозит пользователя
- [ ] Проверьте обновление баланса
- [ ] Отклоните транзакцию с комментарием

#### 7.3 Статистика и отчеты
- [ ] Проверьте отображение общей статистики
- [ ] Проверьте работу графиков
- [ ] Проверьте список пользователей
- [ ] Проверьте топ инвесторов

### ✅ 8. Тестирование безопасности

#### 8.1 CSRF защита
- [ ] Попробуйте отправить форму без CSRF токена
- [ ] Проверьте отклонение запроса

#### 8.2 SQL инъекции
- [ ] Попробуйте ввести SQL код в поля форм
- [ ] Проверьте, что данные экранируются

#### 8.3 XSS атаки
- [ ] Попробуйте ввести JavaScript код в текстовые поля
- [ ] Проверьте экранирование при выводе

### ✅ 9. Тестирование адаптивности

#### 9.1 Мобильные устройства
- [ ] Откройте сайт на смартфоне
- [ ] Проверьте все основные страницы
- [ ] Проверьте работу меню
- [ ] Проверьте формы

#### 9.2 Планшеты
- [ ] Откройте сайт на планшете
- [ ] Проверьте адаптацию интерфейса
- [ ] Проверьте работу графиков

### ✅ 10. Тестирование производительности

#### 10.1 Скорость загрузки
- [ ] Проверьте время загрузки главной страницы
- [ ] Проверьте время загрузки дашборда
- [ ] Проверьте оптимизацию изображений

#### 10.2 Нагрузочное тестирование
- [ ] Создайте несколько пользователей
- [ ] Создайте множественные транзакции
- [ ] Проверьте работу при высокой нагрузке

## 🐛 Отчет о багах

При обнаружении ошибок заполните следующую информацию:

**Описание бага:**
- Что произошло?
- Что ожидалось?

**Шаги для воспроизведения:**
1. Шаг 1
2. Шаг 2
3. Шаг 3

**Окружение:**
- Браузер и версия
- Операционная система
- Версия PHP
- Версия MySQL

**Скриншоты:**
Приложите скриншоты если возможно

## ✅ Критерии успешного тестирования

Тестирование считается успешным, если:
- [ ] Все основные функции работают корректно
- [ ] Нет критических ошибок безопасности
- [ ] Интерфейс адаптивен на всех устройствах
- [ ] Производительность приемлема
- [ ] Данные сохраняются корректно
- [ ] Cron-задачи выполняются без ошибок

## 📞 Поддержка

При возникновении проблем с тестированием:
- Проверьте логи ошибок PHP
- Проверьте логи веб-сервера
- Проверьте настройки базы данных
- Обратитесь к документации в README.md
