/**
 * AstroGenix - Стили для рейтингов и лидербордов
 * Эко-майнинговая инвестиционная платформа
 */

/* Leaderboards Hero */
.leaderboards-hero {
    background: linear-gradient(135deg, var(--primary-green) 0%, var(--primary-purple) 100%);
    padding: var(--space-12) 0 var(--space-8);
    position: relative;
    overflow: hidden;
}

.leaderboards-hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="leaderboard-grid" width="15" height="15" patternUnits="userSpaceOnUse"><path d="M 15 0 L 0 0 0 15" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23leaderboard-grid)"/></svg>');
    opacity: 0.3;
}

/* My Rankings Section */
.my-rankings {
    padding: var(--space-12) 0;
    background: var(--white);
}

.rankings-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: var(--space-6);
}

.ranking-card {
    background: var(--white);
    border-radius: var(--radius-xl);
    padding: var(--space-6);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid var(--gray-200);
    display: flex;
    align-items: center;
    gap: var(--space-4);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.ranking-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-green) 0%, var(--primary-purple) 100%);
}

.ranking-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.ranking-icon {
    width: 60px;
    height: 60px;
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    color: var(--white);
    background: var(--primary-green);
}

.ranking-content {
    flex: 1;
}

.ranking-title {
    font-weight: 600;
    color: var(--dark);
    margin-bottom: var(--space-2);
}

.ranking-position {
    display: flex;
    align-items: center;
    gap: var(--space-2);
    margin-bottom: var(--space-1);
}

.position {
    font-size: var(--text-2xl);
    font-weight: 700;
    color: var(--primary-green);
}

.change {
    display: flex;
    align-items: center;
    gap: var(--space-1);
    font-size: var(--text-sm);
    font-weight: 600;
    padding: var(--space-1) var(--space-2);
    border-radius: var(--radius-md);
}

.change.up {
    background: rgba(34, 197, 94, 0.1);
    color: var(--success-color);
}

.change.down {
    background: rgba(239, 68, 68, 0.1);
    color: var(--error-color);
}

.ranking-score {
    font-size: var(--text-sm);
    color: var(--gray-600);
}

/* Top Users Overall */
.top-users-overall {
    padding: var(--space-12) 0;
    background: var(--gray-50);
}

.top-users-list {
    display: flex;
    flex-direction: column;
    gap: var(--space-4);
}

.top-user-item {
    background: var(--white);
    border-radius: var(--radius-xl);
    padding: var(--space-6);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid var(--gray-200);
    display: flex;
    align-items: center;
    gap: var(--space-4);
    transition: all 0.3s ease;
}

.top-user-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.user-rank {
    width: 60px;
    display: flex;
    justify-content: center;
    align-items: center;
}

.medal {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    font-size: 24px;
}

.medal-gold {
    background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%);
    color: #b45309;
    box-shadow: 0 4px 15px rgba(255, 215, 0, 0.3);
}

.medal-silver {
    background: linear-gradient(135deg, #c0c0c0 0%, #e5e7eb 100%);
    color: #6b7280;
    box-shadow: 0 4px 15px rgba(192, 192, 192, 0.3);
}

.medal-bronze {
    background: linear-gradient(135deg, #cd7f32 0%, #d97706 100%);
    color: #92400e;
    box-shadow: 0 4px 15px rgba(205, 127, 50, 0.3);
}

.rank-number {
    font-size: var(--text-xl);
    font-weight: 700;
    color: var(--gray-600);
}

.user-avatar {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    overflow: hidden;
    border: 2px solid var(--gray-200);
}

.user-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.avatar-placeholder {
    width: 100%;
    height: 100%;
    background: var(--gray-200);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--gray-500);
    font-size: 20px;
}

.user-info {
    flex: 1;
}

.username {
    font-weight: 600;
    color: var(--dark);
    margin-bottom: var(--space-1);
    display: flex;
    align-items: center;
    gap: var(--space-2);
}

.you-badge {
    background: var(--primary-green);
    color: var(--white);
    padding: var(--space-1) var(--space-2);
    border-radius: var(--radius-md);
    font-size: var(--text-xs);
    font-weight: 600;
}

.user-stats {
    display: flex;
    gap: var(--space-4);
}

.stat {
    display: flex;
    align-items: center;
    gap: var(--space-1);
    font-size: var(--text-sm);
    color: var(--gray-600);
}

.stat i {
    color: var(--primary-green);
}

/* Category Tabs */
.ranking-categories {
    padding: var(--space-8) 0;
    background: var(--white);
}

.categories-tabs {
    display: flex;
    gap: var(--space-3);
    justify-content: center;
    flex-wrap: wrap;
}

.category-tab {
    display: flex;
    align-items: center;
    gap: var(--space-2);
    padding: var(--space-4) var(--space-6);
    border: 2px solid var(--gray-300);
    background: var(--white);
    color: var(--gray-600);
    border-radius: var(--radius-xl);
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.category-tab::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: var(--category-color, var(--primary-green));
    opacity: 0.1;
    transition: left 0.3s ease;
}

.category-tab:hover {
    border-color: var(--category-color, var(--primary-green));
    color: var(--category-color, var(--primary-green));
    transform: translateY(-2px);
}

.category-tab:hover::before {
    left: 0;
}

.category-tab.active {
    background: var(--category-color, var(--primary-green));
    border-color: var(--category-color, var(--primary-green));
    color: var(--white);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.category-tab i {
    font-size: var(--text-lg);
}

/* Current Leaderboard */
.current-leaderboard {
    padding: var(--space-12) 0;
    background: var(--gray-50);
}

.leaderboard-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--space-8);
    flex-wrap: wrap;
    gap: var(--space-4);
}

.leaderboard-title {
    display: flex;
    align-items: center;
    gap: var(--space-3);
    font-size: var(--text-2xl);
    font-weight: 700;
    color: var(--dark);
}

.leaderboard-title i {
    font-size: var(--text-xl);
}

.leaderboard-controls {
    display: flex;
    gap: var(--space-3);
    align-items: center;
}

.leaderboard-container {
    background: var(--white);
    border-radius: var(--radius-xl);
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid var(--gray-200);
}

.leaderboard-table {
    width: 100%;
}

.table-header {
    display: grid;
    grid-template-columns: 80px 1fr 120px 120px 100px;
    gap: var(--space-4);
    padding: var(--space-4) var(--space-6);
    background: var(--gray-50);
    border-bottom: 1px solid var(--gray-200);
    font-weight: 600;
    color: var(--gray-700);
    font-size: var(--text-sm);
}

.table-body {
    max-height: 800px;
    overflow-y: auto;
}

.table-body.loading {
    opacity: 0.6;
    pointer-events: none;
}

.leaderboard-row {
    display: grid;
    grid-template-columns: 80px 1fr 120px 120px 100px;
    gap: var(--space-4);
    padding: var(--space-4) var(--space-6);
    border-bottom: 1px solid var(--gray-200);
    transition: all 0.3s ease;
    align-items: center;
}

.leaderboard-row:last-child {
    border-bottom: none;
}

.leaderboard-row:hover {
    background: var(--gray-50);
}

.leaderboard-row.current-user {
    background: rgba(34, 197, 94, 0.05);
    border-left: 4px solid var(--primary-green);
}

.row-rank {
    display: flex;
    justify-content: center;
    align-items: center;
}

.row-user {
    display: flex;
    align-items: center;
    gap: var(--space-3);
}

.user-details {
    flex: 1;
    min-width: 0;
}

.user-since {
    font-size: var(--text-xs);
    color: var(--gray-500);
}

.row-score {
    text-align: center;
}

.score-value {
    font-weight: 700;
    color: var(--dark);
    display: block;
}

.score-label {
    font-size: var(--text-xs);
    color: var(--gray-500);
}

.row-change {
    text-align: center;
}

.change.same {
    background: rgba(107, 114, 128, 0.1);
    color: var(--gray-600);
}

.row-since {
    text-align: center;
    font-size: var(--text-sm);
    color: var(--gray-600);
}

/* Ranking Info */
.ranking-info {
    padding: var(--space-12) 0;
    background: var(--white);
}

.info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--space-6);
}

.info-card {
    background: var(--white);
    border-radius: var(--radius-xl);
    padding: var(--space-6);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid var(--gray-200);
    transition: all 0.3s ease;
}

.info-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.info-icon {
    width: 60px;
    height: 60px;
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    margin-bottom: var(--space-4);
    background: rgba(34, 197, 94, 0.1);
}

.info-content h3 {
    color: var(--dark);
    margin-bottom: var(--space-3);
    font-size: var(--text-lg);
}

.info-content p {
    color: var(--gray-600);
    line-height: 1.6;
    font-size: var(--text-sm);
}

/* Responsive Design */
@media (max-width: 1024px) {
    .table-header,
    .leaderboard-row {
        grid-template-columns: 60px 1fr 100px 100px 80px;
        gap: var(--space-3);
        padding: var(--space-3) var(--space-4);
    }
    
    .leaderboard-header {
        flex-direction: column;
        align-items: stretch;
    }
    
    .leaderboard-controls {
        justify-content: center;
    }
}

@media (max-width: 768px) {
    .rankings-grid {
        grid-template-columns: 1fr;
    }
    
    .categories-tabs {
        flex-direction: column;
        align-items: center;
    }
    
    .category-tab {
        width: 100%;
        max-width: 300px;
        justify-content: center;
    }
    
    .table-header {
        display: none;
    }
    
    .leaderboard-row {
        display: flex;
        flex-direction: column;
        gap: var(--space-3);
        padding: var(--space-4);
        text-align: center;
    }
    
    .row-user {
        flex-direction: column;
        text-align: center;
    }
    
    .user-details {
        text-align: center;
    }
    
    .info-grid {
        grid-template-columns: 1fr;
    }
    
    .top-user-item {
        flex-direction: column;
        text-align: center;
        gap: var(--space-3);
    }
    
    .user-stats {
        justify-content: center;
    }
}

/* Animations */
@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.animate-slide-up {
    animation: slideUp 0.6s ease forwards;
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

.animate-fade-in {
    animation: fadeIn 0.4s ease forwards;
}

/* Loading States */
.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 2px solid var(--gray-300);
    border-radius: 50%;
    border-top-color: var(--primary-green);
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}
