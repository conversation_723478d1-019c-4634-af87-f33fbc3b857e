/**
 * AstroGenix - JavaScript для аутентификации
 * Эко-майнинговая инвестиционная платформа
 */

document.addEventListener('DOMContentLoaded', function() {
    initializeAuth();
});

function initializeAuth() {
    // Инициализация переключения видимости пароля
    initPasswordToggle();
    
    // Инициализация валидации форм
    initFormValidation();
    
    // Инициализация анимаций
    initAuthAnimations();
    
    // Автозаполнение реферального кода из URL
    fillReferralCodeFromURL();
}

// Переключение видимости пароля
function togglePassword(inputId) {
    const input = document.getElementById(inputId);
    const toggle = input.parentElement.querySelector('.password-toggle i');
    
    if (input.type === 'password') {
        input.type = 'text';
        toggle.classList.remove('fa-eye');
        toggle.classList.add('fa-eye-slash');
    } else {
        input.type = 'password';
        toggle.classList.remove('fa-eye-slash');
        toggle.classList.add('fa-eye');
    }
}

function initPasswordToggle() {
    const passwordToggles = document.querySelectorAll('.password-toggle');
    
    passwordToggles.forEach(toggle => {
        toggle.addEventListener('click', function(e) {
            e.preventDefault();
            const input = this.parentElement.querySelector('input');
            const icon = this.querySelector('i');
            
            if (input.type === 'password') {
                input.type = 'text';
                icon.classList.remove('fa-eye');
                icon.classList.add('fa-eye-slash');
            } else {
                input.type = 'password';
                icon.classList.remove('fa-eye-slash');
                icon.classList.add('fa-eye');
            }
        });
    });
}

// Валидация форм
function initFormValidation() {
    const forms = document.querySelectorAll('.auth-form');
    
    forms.forEach(form => {
        form.addEventListener('submit', function(e) {
            if (!validateForm(this)) {
                e.preventDefault();
            }
        });
        
        // Валидация в реальном времени
        const inputs = form.querySelectorAll('input');
        inputs.forEach(input => {
            input.addEventListener('blur', function() {
                validateField(this);
            });
            
            input.addEventListener('input', function() {
                clearFieldError(this);
            });
        });
    });
}

function validateForm(form) {
    let isValid = true;
    const inputs = form.querySelectorAll('input[required]');
    
    inputs.forEach(input => {
        if (!validateField(input)) {
            isValid = false;
        }
    });
    
    // Дополнительная валидация для регистрации
    if (form.querySelector('#confirm_password')) {
        const password = form.querySelector('#password');
        const confirmPassword = form.querySelector('#confirm_password');
        
        if (password.value !== confirmPassword.value) {
            showFieldError(confirmPassword, 'Пароли не совпадают');
            isValid = false;
        }
    }
    
    return isValid;
}

function validateField(field) {
    const value = field.value.trim();
    const type = field.type;
    const name = field.name;
    
    // Очистка предыдущих ошибок
    clearFieldError(field);
    
    // Проверка обязательных полей
    if (field.hasAttribute('required') && !value) {
        showFieldError(field, 'Это поле обязательно для заполнения');
        return false;
    }
    
    // Валидация email
    if (type === 'email' && value) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(value)) {
            showFieldError(field, 'Некорректный формат email');
            return false;
        }
    }
    
    // Валидация пароля
    if (name === 'password' && value) {
        if (value.length < 8) {
            showFieldError(field, 'Пароль должен содержать минимум 8 символов');
            return false;
        }
        
        if (!/(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/.test(value)) {
            showFieldError(field, 'Пароль должен содержать строчные, заглавные буквы и цифры');
            return false;
        }
    }
    
    // Валидация имени пользователя
    if (name === 'username' && value) {
        if (value.length < 3) {
            showFieldError(field, 'Имя пользователя должно содержать минимум 3 символа');
            return false;
        }
        
        if (!/^[a-zA-Z0-9_]+$/.test(value)) {
            showFieldError(field, 'Имя пользователя может содержать только буквы, цифры и подчеркивания');
            return false;
        }
    }
    
    // Валидация телефона
    if (name === 'phone' && value) {
        const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
        if (!phoneRegex.test(value.replace(/[\s\-\(\)]/g, ''))) {
            showFieldError(field, 'Некорректный формат телефона');
            return false;
        }
    }
    
    return true;
}

function showFieldError(field, message) {
    field.classList.add('error');
    
    // Удаление существующего сообщения об ошибке
    const existingError = field.parentElement.querySelector('.field-error');
    if (existingError) {
        existingError.remove();
    }
    
    // Добавление нового сообщения об ошибке
    const errorElement = document.createElement('div');
    errorElement.className = 'field-error';
    errorElement.textContent = message;
    errorElement.style.color = 'var(--error)';
    errorElement.style.fontSize = 'var(--text-xs)';
    errorElement.style.marginTop = 'var(--space-1)';
    
    field.parentElement.appendChild(errorElement);
}

function clearFieldError(field) {
    field.classList.remove('error');
    const errorElement = field.parentElement.querySelector('.field-error');
    if (errorElement) {
        errorElement.remove();
    }
}

// Анимации
function initAuthAnimations() {
    // Анимация появления формы
    const authContent = document.querySelector('.auth-content');
    if (authContent) {
        authContent.style.opacity = '0';
        authContent.style.transform = 'translateY(30px)';
        
        setTimeout(() => {
            authContent.style.transition = 'all 0.6s ease-out';
            authContent.style.opacity = '1';
            authContent.style.transform = 'translateY(0)';
        }, 100);
    }
    
    // Анимация фокуса на полях ввода
    const inputs = document.querySelectorAll('input');
    inputs.forEach(input => {
        input.addEventListener('focus', function() {
            this.parentElement.style.transform = 'scale(1.02)';
            this.parentElement.style.transition = 'transform 0.2s ease';
        });
        
        input.addEventListener('blur', function() {
            this.parentElement.style.transform = 'scale(1)';
        });
    });
    
    // Анимация кнопок
    const buttons = document.querySelectorAll('.btn');
    buttons.forEach(button => {
        button.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-2px)';
        });
        
        button.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
        });
    });
}

// Автозаполнение реферального кода
function fillReferralCodeFromURL() {
    const urlParams = new URLSearchParams(window.location.search);
    const refCode = urlParams.get('ref');
    const referralInput = document.getElementById('referral_code');
    
    if (refCode && referralInput) {
        referralInput.value = refCode;
        referralInput.style.background = 'rgba(46, 204, 113, 0.1)';
        referralInput.style.borderColor = 'var(--primary-green)';
        
        // Показать уведомление
        showNotification('Реферальный код применен!', 'success');
    }
}

// Уведомления
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
        <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-triangle' : 'info-circle'}"></i>
        <span>${message}</span>
    `;
    
    // Стили уведомления
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: ${type === 'success' ? 'var(--success)' : type === 'error' ? 'var(--error)' : 'var(--info)'};
        color: white;
        padding: var(--space-4);
        border-radius: var(--radius-md);
        box-shadow: var(--shadow-lg);
        z-index: 9999;
        display: flex;
        align-items: center;
        gap: var(--space-2);
        transform: translateX(100%);
        transition: transform 0.3s ease;
    `;
    
    document.body.appendChild(notification);
    
    // Анимация появления
    setTimeout(() => {
        notification.style.transform = 'translateX(0)';
    }, 100);
    
    // Автоматическое скрытие
    setTimeout(() => {
        notification.style.transform = 'translateX(100%)';
        setTimeout(() => {
            if (notification.parentElement) {
                notification.parentElement.removeChild(notification);
            }
        }, 300);
    }, 3000);
}

// Обработка социальной авторизации
function initSocialAuth() {
    const socialButtons = document.querySelectorAll('.btn-social');
    
    socialButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            
            const provider = this.classList.contains('btn-google') ? 'google' : 'vk';
            showNotification(`Авторизация через ${provider.toUpperCase()} временно недоступна`, 'info');
        });
    });
}

// Инициализация социальной авторизации
document.addEventListener('DOMContentLoaded', function() {
    initSocialAuth();
});

// Обработка загрузки формы
function showFormLoading(form, isLoading = true) {
    const submitButton = form.querySelector('button[type="submit"]');
    const originalText = submitButton.innerHTML;
    
    if (isLoading) {
        submitButton.disabled = true;
        submitButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Загрузка...';
    } else {
        submitButton.disabled = false;
        submitButton.innerHTML = originalText;
    }
}

// Проверка силы пароля
function checkPasswordStrength(password) {
    let strength = 0;
    const checks = [
        /.{8,}/, // Минимум 8 символов
        /[a-z]/, // Строчные буквы
        /[A-Z]/, // Заглавные буквы
        /[0-9]/, // Цифры
        /[^A-Za-z0-9]/ // Специальные символы
    ];
    
    checks.forEach(check => {
        if (check.test(password)) strength++;
    });
    
    return {
        score: strength,
        level: strength < 2 ? 'weak' : strength < 4 ? 'medium' : 'strong'
    };
}

// Показать индикатор силы пароля
function showPasswordStrength(input) {
    const password = input.value;
    const strength = checkPasswordStrength(password);
    
    let indicator = input.parentElement.querySelector('.password-strength');
    if (!indicator) {
        indicator = document.createElement('div');
        indicator.className = 'password-strength';
        input.parentElement.appendChild(indicator);
    }
    
    const colors = {
        weak: '#dc3545',
        medium: '#ffc107',
        strong: '#28a745'
    };
    
    const labels = {
        weak: 'Слабый',
        medium: 'Средний',
        strong: 'Сильный'
    };
    
    if (password.length > 0) {
        indicator.innerHTML = `
            <div class="strength-bar">
                <div class="strength-fill" style="width: ${(strength.score / 5) * 100}%; background: ${colors[strength.level]}"></div>
            </div>
            <span style="color: ${colors[strength.level]}; font-size: var(--text-xs);">${labels[strength.level]} пароль</span>
        `;
        indicator.style.marginTop = 'var(--space-2)';
    } else {
        indicator.innerHTML = '';
    }
}

// Добавление индикатора силы пароля для поля пароля при регистрации
document.addEventListener('DOMContentLoaded', function() {
    const passwordInput = document.getElementById('password');
    if (passwordInput && document.querySelector('#confirm_password')) {
        passwordInput.addEventListener('input', function() {
            showPasswordStrength(this);
        });
    }
});
