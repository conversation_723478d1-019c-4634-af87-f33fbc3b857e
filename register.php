<?php
/**
 * AstroGenix - Страница регистрации
 * Эко-майнинговая инвестиционная платформа
 */

require_once 'config/config.php';

// Если пользователь уже авторизован, перенаправляем в дашборд
if (is_logged_in()) {
    redirect('dashboard.php');
}

$errors = [];
$success_message = '';

// Обработка формы регистрации
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!verify_csrf_token($_POST['csrf_token'] ?? '')) {
        $errors[] = 'Ошибка безопасности. Попробуйте еще раз.';
    } else {
        $username = sanitize_input($_POST['username'] ?? '');
        $email = sanitize_input($_POST['email'] ?? '');
        $password = $_POST['password'] ?? '';
        $confirm_password = $_POST['confirm_password'] ?? '';
        $first_name = sanitize_input($_POST['first_name'] ?? '');
        $last_name = sanitize_input($_POST['last_name'] ?? '');
        $phone = sanitize_input($_POST['phone'] ?? '');
        $referral_code = sanitize_input($_POST['referral_code'] ?? '');
        $agree_terms = isset($_POST['agree_terms']);

        // Валидация
        if (empty($username)) {
            $errors[] = 'Имя пользователя обязательно для заполнения.';
        } elseif (strlen($username) < 3) {
            $errors[] = 'Имя пользователя должно содержать минимум 3 символа.';
        }

        if (empty($email)) {
            $errors[] = 'Email обязателен для заполнения.';
        } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            $errors[] = 'Некорректный формат email.';
        }

        if (empty($password)) {
            $errors[] = 'Пароль обязателен для заполнения.';
        } elseif (strlen($password) < MIN_PASSWORD_LENGTH) {
            $errors[] = 'Пароль должен содержать минимум ' . MIN_PASSWORD_LENGTH . ' символов.';
        }

        if ($password !== $confirm_password) {
            $errors[] = 'Пароли не совпадают.';
        }

        if (empty($first_name)) {
            $errors[] = 'Имя обязательно для заполнения.';
        }

        if (empty($last_name)) {
            $errors[] = 'Фамилия обязательна для заполнения.';
        }

        if (!$agree_terms) {
            $errors[] = 'Необходимо согласиться с условиями использования.';
        }

        // Проверка уникальности
        if (empty($errors)) {
            try {
                $database = new Database();
                $db = $database->getConnection();
                $user = new User($db);

                if ($user->emailExists($email)) {
                    $errors[] = 'Пользователь с таким email уже существует.';
                }

                if ($user->usernameExists($username)) {
                    $errors[] = 'Пользователь с таким именем уже существует.';
                }

                // Проверка реферального кода
                $referred_by = null;
                if (!empty($referral_code)) {
                    $referred_by = $user->getUserByReferralCode($referral_code);
                    if (!$referred_by) {
                        $errors[] = 'Неверный реферальный код.';
                    }
                }

                // Регистрация пользователя
                if (empty($errors)) {
                    $user->username = $username;
                    $user->email = $email;
                    $user->password_hash = $password;
                    $user->first_name = $first_name;
                    $user->last_name = $last_name;
                    $user->phone = $phone;
                    $user->referred_by = $referred_by;

                    if ($user->register()) {
                        // Отправка email верификации (здесь можно добавить отправку email)
                        $success_message = 'Регистрация успешна! На ваш email отправлено письмо для подтверждения.';
                        
                        // Очистка формы
                        $username = $email = $first_name = $last_name = $phone = $referral_code = '';
                    } else {
                        $errors[] = 'Ошибка при регистрации. Попробуйте еще раз.';
                    }
                }
            } catch (Exception $e) {
                $errors[] = 'Ошибка сервера. Попробуйте позже.';
                error_log("Registration error: " . $e->getMessage());
            }
        }
    }
}

$page_title = 'Регистрация - ' . SITE_NAME;
?>
<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?></title>
    
    <!-- CSS -->
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="assets/css/auth.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body class="auth-page">
    <div class="auth-container">
        <div class="auth-background">
            <div class="eco-particles"></div>
        </div>
        
        <div class="auth-content">
            <div class="auth-header">
                <a href="index.php" class="auth-logo">
                    <img src="assets/images/logo.png" alt="AstroGenix">
                    <span>AstroGenix</span>
                </a>
                <h1>Создать аккаунт</h1>
                <p>Присоединяйтесь к эко-майнинговой революции</p>
            </div>

            <?php if (!empty($errors)): ?>
                <div class="alert alert-error">
                    <i class="fas fa-exclamation-triangle"></i>
                    <ul>
                        <?php foreach ($errors as $error): ?>
                            <li><?php echo htmlspecialchars($error); ?></li>
                        <?php endforeach; ?>
                    </ul>
                </div>
            <?php endif; ?>

            <?php if (!empty($success_message)): ?>
                <div class="alert alert-success">
                    <i class="fas fa-check-circle"></i>
                    <?php echo htmlspecialchars($success_message); ?>
                </div>
            <?php endif; ?>

            <form class="auth-form" method="POST" action="">
                <input type="hidden" name="csrf_token" value="<?php echo generate_csrf_token(); ?>">
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="first_name">Имя</label>
                        <input type="text" id="first_name" name="first_name" 
                               value="<?php echo htmlspecialchars($first_name ?? ''); ?>" required>
                    </div>
                    <div class="form-group">
                        <label for="last_name">Фамилия</label>
                        <input type="text" id="last_name" name="last_name" 
                               value="<?php echo htmlspecialchars($last_name ?? ''); ?>" required>
                    </div>
                </div>

                <div class="form-group">
                    <label for="username">Имя пользователя</label>
                    <input type="text" id="username" name="username" 
                           value="<?php echo htmlspecialchars($username ?? ''); ?>" required>
                </div>

                <div class="form-group">
                    <label for="email">Email</label>
                    <input type="email" id="email" name="email" 
                           value="<?php echo htmlspecialchars($email ?? ''); ?>" required>
                </div>

                <div class="form-group">
                    <label for="phone">Телефон (необязательно)</label>
                    <input type="tel" id="phone" name="phone" 
                           value="<?php echo htmlspecialchars($phone ?? ''); ?>">
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="password">Пароль</label>
                        <div class="password-input">
                            <input type="password" id="password" name="password" required>
                            <button type="button" class="password-toggle" onclick="togglePassword('password')">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="confirm_password">Подтвердите пароль</label>
                        <div class="password-input">
                            <input type="password" id="confirm_password" name="confirm_password" required>
                            <button type="button" class="password-toggle" onclick="togglePassword('confirm_password')">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <label for="referral_code">Реферальный код (необязательно)</label>
                    <input type="text" id="referral_code" name="referral_code" 
                           value="<?php echo htmlspecialchars($referral_code ?? ''); ?>" 
                           placeholder="Введите код, если вас пригласили">
                </div>

                <div class="form-group checkbox-group">
                    <label class="checkbox-label">
                        <input type="checkbox" name="agree_terms" required>
                        <span class="checkmark"></span>
                        Я согласен с <a href="terms.php" target="_blank">условиями использования</a> 
                        и <a href="privacy.php" target="_blank">политикой конфиденциальности</a>
                    </label>
                </div>

                <button type="submit" class="btn btn-primary btn-full">
                    <i class="fas fa-user-plus"></i>
                    Создать аккаунт
                </button>
            </form>

            <div class="auth-footer">
                <p>Уже есть аккаунт? <a href="login.php">Войти</a></p>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="assets/js/auth.js"></script>
</body>
</html>
