/**
 * AstroGenix - Стили реферальной программы
 * Эко-майнинговая инвестиционная платформа
 */

/* Информационный баннер */
.referral-info-section {
    margin-bottom: var(--space-8);
}

.info-banner {
    background: linear-gradient(135deg, rgba(46, 204, 113, 0.1) 0%, rgba(142, 68, 173, 0.1) 100%);
    border: 2px solid rgba(46, 204, 113, 0.2);
    border-radius: var(--radius-xl);
    padding: var(--space-8);
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    overflow: hidden;
}

.info-banner::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle, rgba(46, 204, 113, 0.1) 0%, transparent 70%);
    animation: bannerPulse 6s ease-in-out infinite;
}

@keyframes bannerPulse {
    0%, 100% { transform: scale(1) rotate(0deg); opacity: 0.5; }
    50% { transform: scale(1.2) rotate(180deg); opacity: 0.8; }
}

.banner-content {
    display: flex;
    align-items: center;
    gap: var(--space-6);
    z-index: 2;
}

.banner-icon {
    width: 80px;
    height: 80px;
    background: var(--gradient-primary);
    border-radius: var(--radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--text-4xl);
    color: var(--white);
    animation: iconFloat 3s ease-in-out infinite;
}

@keyframes iconFloat {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

.banner-text h2 {
    margin: 0 0 var(--space-2) 0;
    font-size: var(--text-3xl);
    color: var(--gray-900);
}

.banner-text p {
    margin: 0;
    color: var(--gray-600);
    font-size: var(--text-lg);
}

.commission-levels {
    display: flex;
    gap: var(--space-4);
    z-index: 2;
}

.level-item {
    text-align: center;
    padding: var(--space-4);
    background: rgba(255, 255, 255, 0.8);
    border-radius: var(--radius-lg);
    border: 1px solid rgba(46, 204, 113, 0.3);
    backdrop-filter: blur(10px);
}

.level-item .level {
    display: block;
    font-size: var(--text-sm);
    color: var(--gray-600);
    margin-bottom: var(--space-1);
}

.level-item .percent {
    display: block;
    font-size: var(--text-2xl);
    font-weight: 700;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Статистика рефералов */
.referral-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--space-6);
    margin-bottom: var(--space-8);
}

.stat-breakdown {
    display: flex;
    gap: var(--space-3);
    font-size: var(--text-xs);
    color: var(--gray-500);
    margin-top: var(--space-2);
}

.level-earnings {
    display: flex;
    flex-direction: column;
    gap: var(--space-1);
}

.level-earning {
    display: flex;
    justify-content: space-between;
    font-size: var(--text-sm);
}

.level-earning span:first-child {
    color: var(--gray-600);
}

.level-earning span:last-child {
    font-weight: 600;
    color: var(--primary-green);
}

/* Секция реферальной ссылки */
.referral-link-section {
    margin-bottom: var(--space-8);
}

.link-container {
    text-align: center;
}

.link-input-group {
    display: flex;
    gap: var(--space-3);
    margin-bottom: var(--space-6);
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.link-input-group input {
    flex: 1;
    padding: var(--space-4);
    border: 2px solid var(--gray-300);
    border-radius: var(--radius-lg);
    font-size: var(--text-base);
    background: var(--gray-50);
    color: var(--gray-700);
}

.link-actions {
    display: flex;
    justify-content: center;
    gap: var(--space-3);
    flex-wrap: wrap;
}

.share-btn {
    min-width: 120px;
}

.referral-code-info {
    background: var(--gray-100);
    border-radius: var(--radius-lg);
    padding: var(--space-4);
    margin-top: var(--space-6);
    text-align: center;
}

.code-item {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: var(--space-3);
    font-size: var(--text-lg);
}

.code-item strong {
    color: var(--primary-green);
    font-family: 'Courier New', monospace;
    background: var(--white);
    padding: var(--space-2) var(--space-4);
    border-radius: var(--radius-md);
    border: 1px solid var(--primary-green);
}

/* Уровни рефералов */
.referral-levels-section {
    margin-bottom: var(--space-8);
}

.referral-levels-section .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--space-6);
}

.level-tabs {
    display: flex;
    gap: var(--space-2);
}

.level-tab {
    padding: var(--space-3) var(--space-6);
    border: 2px solid var(--gray-300);
    background: var(--white);
    color: var(--gray-600);
    border-radius: var(--radius-lg);
    cursor: pointer;
    transition: all var(--transition-fast);
    font-weight: 500;
}

.level-tab:hover {
    border-color: var(--primary-green);
    color: var(--primary-green);
}

.level-tab.active {
    background: var(--gradient-primary);
    border-color: var(--primary-green);
    color: var(--white);
}

.referral-level-content {
    display: none;
}

.referral-level-content.active {
    display: block;
}

/* Сетка рефералов */
.referrals-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: var(--space-4);
}

.referral-card {
    background: var(--white);
    border-radius: var(--radius-lg);
    padding: var(--space-6);
    box-shadow: var(--shadow-sm);
    border: 2px solid var(--gray-200);
    transition: all var(--transition-fast);
    position: relative;
}

.referral-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-md);
    border-color: var(--primary-green);
}

.referral-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--gradient-primary);
    border-radius: var(--radius-lg) var(--radius-lg) 0 0;
}

.referral-card.level-2::before {
    background: linear-gradient(135deg, #3498DB 0%, #2980B9 100%);
}

.referral-card.level-3::before {
    background: linear-gradient(135deg, #9B59B6 0%, #8E44AD 100%);
}

.referral-avatar {
    width: 60px;
    height: 60px;
    background: var(--gradient-primary);
    border-radius: var(--radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--text-2xl);
    color: var(--white);
    margin: 0 auto var(--space-4) auto;
}

.referral-info {
    text-align: center;
}

.referral-info h4 {
    margin: 0 0 var(--space-1) 0;
    color: var(--gray-900);
}

.referral-username {
    color: var(--gray-500);
    font-size: var(--text-sm);
    margin-bottom: var(--space-4);
}

.referral-stats {
    text-align: left;
}

.referral-stats .stat-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: var(--space-2);
    padding-bottom: var(--space-2);
    border-bottom: 1px solid var(--gray-200);
    font-size: var(--text-sm);
}

.referral-stats .stat-item:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.referral-stats .stat-item span:first-child {
    color: var(--gray-600);
}

.referral-stats .stat-item span:last-child {
    font-weight: 500;
    color: var(--gray-900);
}

.referral-stats .commission {
    color: var(--primary-green) !important;
    font-weight: 600 !important;
}

/* Список комиссий */
.commissions-list {
    display: flex;
    flex-direction: column;
    gap: var(--space-4);
}

.commission-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--space-4);
    border: 1px solid var(--gray-200);
    border-radius: var(--radius-lg);
    transition: all var(--transition-fast);
}

.commission-item:hover {
    border-color: var(--primary-green);
    background: rgba(46, 204, 113, 0.02);
}

.commission-info {
    flex: 1;
}

.commission-user {
    display: flex;
    align-items: center;
    gap: var(--space-3);
    margin-bottom: var(--space-2);
}

.commission-user strong {
    color: var(--gray-900);
}

.level-badge {
    padding: var(--space-1) var(--space-2);
    border-radius: var(--radius-full);
    font-size: var(--text-xs);
    font-weight: 500;
}

.level-badge.level-1 {
    background: rgba(46, 204, 113, 0.2);
    color: var(--primary-green);
}

.level-badge.level-2 {
    background: rgba(52, 152, 219, 0.2);
    color: #3498DB;
}

.level-badge.level-3 {
    background: rgba(155, 89, 182, 0.2);
    color: #9B59B6;
}

.commission-details {
    display: flex;
    gap: var(--space-4);
    font-size: var(--text-xs);
    color: var(--gray-600);
    margin-bottom: var(--space-1);
}

.commission-date {
    font-size: var(--text-xs);
    color: var(--gray-500);
}

.commission-amount {
    font-size: var(--text-lg);
    font-weight: 600;
    color: var(--success);
    margin-right: var(--space-4);
}

.commission-status {
    padding: var(--space-1) var(--space-3);
    border-radius: var(--radius-full);
    font-size: var(--text-xs);
    font-weight: 500;
}

.status-paid {
    background: rgba(40, 167, 69, 0.2);
    color: var(--success);
}

.status-pending {
    background: rgba(255, 193, 7, 0.2);
    color: #856404;
}

/* QR Modal */
.qr-container {
    text-align: center;
    padding: var(--space-4);
}

.qr-container #qrcode {
    margin: 0 auto var(--space-4) auto;
}

.qr-container p {
    color: var(--gray-600);
    margin: 0;
}

/* Адаптивность */
@media (max-width: 768px) {
    .info-banner {
        flex-direction: column;
        gap: var(--space-6);
        text-align: center;
    }
    
    .commission-levels {
        flex-direction: column;
        gap: var(--space-3);
    }
    
    .referral-stats {
        grid-template-columns: 1fr;
    }
    
    .link-input-group {
        flex-direction: column;
    }
    
    .link-actions {
        flex-direction: column;
        align-items: center;
    }
    
    .share-btn {
        width: 100%;
        max-width: 200px;
    }
    
    .referral-levels-section .section-header {
        flex-direction: column;
        gap: var(--space-4);
        align-items: flex-start;
    }
    
    .level-tabs {
        width: 100%;
        justify-content: space-between;
    }
    
    .level-tab {
        flex: 1;
        text-align: center;
        padding: var(--space-2) var(--space-3);
        font-size: var(--text-sm);
    }
    
    .referrals-grid {
        grid-template-columns: 1fr;
    }
    
    .commission-item {
        flex-direction: column;
        gap: var(--space-3);
        align-items: flex-start;
    }
    
    .commission-details {
        flex-direction: column;
        gap: var(--space-1);
    }
}

@media (max-width: 480px) {
    .banner-content {
        flex-direction: column;
        gap: var(--space-4);
    }
    
    .banner-icon {
        width: 60px;
        height: 60px;
        font-size: var(--text-2xl);
    }
    
    .banner-text h2 {
        font-size: var(--text-2xl);
    }
    
    .commission-levels {
        gap: var(--space-2);
    }
    
    .level-item {
        padding: var(--space-3);
    }
    
    .level-item .percent {
        font-size: var(--text-xl);
    }
}
