<?php
/**
 * AstroGenix - Страница реферальной программы
 * Эко-майнинговая инвестиционная платформа
 */

require_once 'config/config.php';

// Проверка авторизации
if (!is_logged_in()) {
    redirect('login.php');
}

try {
    $database = new Database();
    $db = $database->getConnection();
    
    $referral = new Referral($db);
    $user = new User($db);
    $user->getUserById($_SESSION['user_id']);
    
    // Получение реферальной статистики
    $referral_stats = $referral->getUserReferralStats($_SESSION['user_id']);
    $referral_link = $referral->generateReferralLink($_SESSION['user_id']);
    $referral_settings = $referral->getReferralSettings();
    
    // Получение рефералов по уровням
    $level_1_referrals = $referral->getUserReferrals($_SESSION['user_id'], 1, 10);
    $level_2_referrals = $referral->getUserReferrals($_SESSION['user_id'], 2, 10);
    $level_3_referrals = $referral->getUserReferrals($_SESSION['user_id'], 3, 10);
    
    // Получение истории комиссий
    $recent_commissions = $referral->getReferralCommissions($_SESSION['user_id'], 10);
    
    // Получение статистики по уровням
    $level_stats = $referral->getReferralLevelStats($_SESSION['user_id']);
    
} catch (Exception $e) {
    error_log("Referrals page error: " . $e->getMessage());
    $referral_stats = [];
    $referral_link = '';
    $level_1_referrals = [];
    $level_2_referrals = [];
    $level_3_referrals = [];
    $recent_commissions = [];
    $level_stats = [];
}

$page_title = 'Реферальная программа - ' . SITE_NAME;
?>
<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?></title>
    
    <!-- CSS -->
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="assets/css/dashboard.css">
    <link rel="stylesheet" href="assets/css/referrals.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body class="dashboard-page">
    <!-- Sidebar -->
    <?php include 'includes/sidebar.php'; ?>

    <!-- Main Content -->
    <main class="main-content">
        <!-- Header -->
        <header class="dashboard-header">
            <div class="header-left">
                <button class="mobile-menu-toggle" id="mobileMenuToggle">
                    <i class="fas fa-bars"></i>
                </button>
                <h1>Реферальная программа</h1>
            </div>
            <div class="header-right">
                <div class="user-balance">
                    <span class="balance-label">Заработано с рефералов:</span>
                    <span class="balance-amount"><?php echo format_currency($referral_stats['total_earned'] ?? 0); ?></span>
                </div>
            </div>
        </header>

        <!-- Content -->
        <div class="dashboard-content">
            <!-- Referral Program Info -->
            <div class="referral-info-section">
                <div class="info-banner">
                    <div class="banner-content">
                        <div class="banner-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="banner-text">
                            <h2>Приглашайте друзей и зарабатывайте!</h2>
                            <p>Получайте до 10% с каждой инвестиции ваших рефералов на 3 уровня в глубину</p>
                        </div>
                    </div>
                    <div class="commission-levels">
                        <div class="level-item">
                            <span class="level">1 уровень</span>
                            <span class="percent"><?php echo $referral_settings['referral_level_1_percent'] ?? '5'; ?>%</span>
                        </div>
                        <div class="level-item">
                            <span class="level">2 уровень</span>
                            <span class="percent"><?php echo $referral_settings['referral_level_2_percent'] ?? '3'; ?>%</span>
                        </div>
                        <div class="level-item">
                            <span class="level">3 уровень</span>
                            <span class="percent"><?php echo $referral_settings['referral_level_3_percent'] ?? '2'; ?>%</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Referral Stats -->
            <div class="referral-stats">
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="stat-content">
                        <h3>Всего рефералов</h3>
                        <div class="stat-value"><?php echo $referral_stats['total_referrals'] ?? 0; ?></div>
                        <div class="stat-breakdown">
                            <span>1 ур: <?php echo $referral_stats['level_1_referrals'] ?? 0; ?></span>
                            <span>2 ур: <?php echo $referral_stats['level_2_referrals'] ?? 0; ?></span>
                            <span>3 ур: <?php echo $referral_stats['level_3_referrals'] ?? 0; ?></span>
                        </div>
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-coins"></i>
                    </div>
                    <div class="stat-content">
                        <h3>Общий заработок</h3>
                        <div class="stat-value"><?php echo format_currency($referral_stats['total_earned'] ?? 0); ?></div>
                        <div class="stat-change positive">
                            <i class="fas fa-arrow-up"></i>
                            <span>+<?php echo format_currency($referral_stats['level_1_earned'] ?? 0); ?> за месяц</span>
                        </div>
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-clock"></i>
                    </div>
                    <div class="stat-content">
                        <h3>Ожидает выплаты</h3>
                        <div class="stat-value"><?php echo format_currency($referral_stats['pending_earnings'] ?? 0); ?></div>
                        <div class="stat-subtitle">Будет зачислено автоматически</div>
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <div class="stat-content">
                        <h3>По уровням</h3>
                        <div class="level-earnings">
                            <div class="level-earning">
                                <span>1 ур:</span>
                                <span><?php echo format_currency($referral_stats['level_1_earned'] ?? 0); ?></span>
                            </div>
                            <div class="level-earning">
                                <span>2 ур:</span>
                                <span><?php echo format_currency($referral_stats['level_2_earned'] ?? 0); ?></span>
                            </div>
                            <div class="level-earning">
                                <span>3 ур:</span>
                                <span><?php echo format_currency($referral_stats['level_3_earned'] ?? 0); ?></span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Referral Link Section -->
            <div class="referral-link-section">
                <div class="card">
                    <div class="card-header">
                        <h3><i class="fas fa-link"></i> Ваша реферальная ссылка</h3>
                        <p>Поделитесь этой ссылкой с друзьями и получайте комиссию с их инвестиций</p>
                    </div>
                    <div class="card-body">
                        <div class="link-container">
                            <div class="link-input-group">
                                <input type="text" id="referralLink" value="<?php echo htmlspecialchars($referral_link); ?>" readonly>
                                <button class="btn btn-primary" id="copyLinkBtn">
                                    <i class="fas fa-copy"></i>
                                    Копировать
                                </button>
                            </div>
                            <div class="link-actions">
                                <button class="btn btn-outline share-btn" data-platform="telegram">
                                    <i class="fab fa-telegram"></i>
                                    Telegram
                                </button>
                                <button class="btn btn-outline share-btn" data-platform="vk">
                                    <i class="fab fa-vk"></i>
                                    VKontakte
                                </button>
                                <button class="btn btn-outline share-btn" data-platform="whatsapp">
                                    <i class="fab fa-whatsapp"></i>
                                    WhatsApp
                                </button>
                                <button class="btn btn-outline" id="qrCodeBtn">
                                    <i class="fas fa-qrcode"></i>
                                    QR-код
                                </button>
                            </div>
                        </div>
                        
                        <div class="referral-code-info">
                            <div class="code-item">
                                <span>Ваш реферальный код:</span>
                                <strong><?php echo htmlspecialchars($user->referral_code); ?></strong>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Referral Levels -->
            <div class="referral-levels-section">
                <div class="section-header">
                    <h2>Структура рефералов</h2>
                    <div class="level-tabs">
                        <button class="level-tab active" data-level="1">1 уровень (<?php echo count($level_1_referrals); ?>)</button>
                        <button class="level-tab" data-level="2">2 уровень (<?php echo count($level_2_referrals); ?>)</button>
                        <button class="level-tab" data-level="3">3 уровень (<?php echo count($level_3_referrals); ?>)</button>
                    </div>
                </div>

                <div class="referral-level-content" id="level-1" style="display: block;">
                    <?php if (empty($level_1_referrals)): ?>
                        <div class="empty-state">
                            <i class="fas fa-user-plus"></i>
                            <h3>Пока нет рефералов 1-го уровня</h3>
                            <p>Поделитесь своей реферальной ссылкой, чтобы пригласить первых рефералов</p>
                        </div>
                    <?php else: ?>
                        <div class="referrals-grid">
                            <?php foreach ($level_1_referrals as $referral): ?>
                                <div class="referral-card">
                                    <div class="referral-avatar">
                                        <i class="fas fa-user"></i>
                                    </div>
                                    <div class="referral-info">
                                        <h4><?php echo htmlspecialchars($referral['first_name'] . ' ' . $referral['last_name']); ?></h4>
                                        <div class="referral-username">@<?php echo htmlspecialchars($referral['username']); ?></div>
                                        <div class="referral-stats">
                                            <div class="stat-item">
                                                <span>Инвестировано:</span>
                                                <span><?php echo format_currency($referral['total_invested']); ?></span>
                                            </div>
                                            <div class="stat-item">
                                                <span>Ваша комиссия:</span>
                                                <span class="commission"><?php echo format_currency($referral['total_commission']); ?></span>
                                            </div>
                                            <div class="stat-item">
                                                <span>Дата регистрации:</span>
                                                <span><?php echo date('d.m.Y', strtotime($referral['created_at'])); ?></span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>

                <div class="referral-level-content" id="level-2" style="display: none;">
                    <?php if (empty($level_2_referrals)): ?>
                        <div class="empty-state">
                            <i class="fas fa-users"></i>
                            <h3>Пока нет рефералов 2-го уровня</h3>
                            <p>Рефералы 2-го уровня появятся, когда ваши рефералы пригласят своих друзей</p>
                        </div>
                    <?php else: ?>
                        <div class="referrals-grid">
                            <?php foreach ($level_2_referrals as $referral): ?>
                                <div class="referral-card level-2">
                                    <div class="referral-avatar">
                                        <i class="fas fa-user"></i>
                                    </div>
                                    <div class="referral-info">
                                        <h4><?php echo htmlspecialchars($referral['first_name'] . ' ' . $referral['last_name']); ?></h4>
                                        <div class="referral-username">@<?php echo htmlspecialchars($referral['username']); ?></div>
                                        <div class="referral-stats">
                                            <div class="stat-item">
                                                <span>Инвестировано:</span>
                                                <span><?php echo format_currency($referral['total_invested']); ?></span>
                                            </div>
                                            <div class="stat-item">
                                                <span>Ваша комиссия:</span>
                                                <span class="commission"><?php echo format_currency($referral['total_commission']); ?></span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>

                <div class="referral-level-content" id="level-3" style="display: none;">
                    <?php if (empty($level_3_referrals)): ?>
                        <div class="empty-state">
                            <i class="fas fa-sitemap"></i>
                            <h3>Пока нет рефералов 3-го уровня</h3>
                            <p>Рефералы 3-го уровня появятся, когда рефералы ваших рефералов пригласят друзей</p>
                        </div>
                    <?php else: ?>
                        <div class="referrals-grid">
                            <?php foreach ($level_3_referrals as $referral): ?>
                                <div class="referral-card level-3">
                                    <div class="referral-avatar">
                                        <i class="fas fa-user"></i>
                                    </div>
                                    <div class="referral-info">
                                        <h4><?php echo htmlspecialchars($referral['first_name'] . ' ' . $referral['last_name']); ?></h4>
                                        <div class="referral-username">@<?php echo htmlspecialchars($referral['username']); ?></div>
                                        <div class="referral-stats">
                                            <div class="stat-item">
                                                <span>Инвестировано:</span>
                                                <span><?php echo format_currency($referral['total_invested']); ?></span>
                                            </div>
                                            <div class="stat-item">
                                                <span>Ваша комиссия:</span>
                                                <span class="commission"><?php echo format_currency($referral['total_commission']); ?></span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Recent Commissions -->
            <div class="recent-commissions-section">
                <div class="card">
                    <div class="card-header">
                        <h3><i class="fas fa-history"></i> Последние комиссии</h3>
                        <a href="transactions.php?type=referral_bonus" class="view-all-link">Посмотреть все</a>
                    </div>
                    <div class="card-body">
                        <?php if (empty($recent_commissions)): ?>
                            <div class="empty-state">
                                <i class="fas fa-coins"></i>
                                <p>Комиссий пока нет</p>
                            </div>
                        <?php else: ?>
                            <div class="commissions-list">
                                <?php foreach ($recent_commissions as $commission): ?>
                                    <div class="commission-item">
                                        <div class="commission-info">
                                            <div class="commission-user">
                                                <strong><?php echo htmlspecialchars($commission['referred_first_name'] . ' ' . $commission['referred_last_name']); ?></strong>
                                                <span class="level-badge level-<?php echo $commission['level']; ?>">
                                                    <?php echo $commission['level']; ?> уровень
                                                </span>
                                            </div>
                                            <div class="commission-details">
                                                <span>Источник: <?php echo $commission['source_type'] === 'investment' ? 'Инвестиция' : 'Депозит'; ?></span>
                                                <span>Сумма операции: <?php echo format_currency($commission['source_amount']); ?></span>
                                                <span>Комиссия: <?php echo $commission['commission_percent']; ?>%</span>
                                            </div>
                                            <div class="commission-date">
                                                <?php echo date('d.m.Y H:i', strtotime($commission['created_at'])); ?>
                                            </div>
                                        </div>
                                        <div class="commission-amount">
                                            +<?php echo format_currency($commission['amount']); ?>
                                        </div>
                                        <div class="commission-status status-<?php echo $commission['status']; ?>">
                                            <?php echo $commission['status'] === 'paid' ? 'Выплачено' : 'Ожидание'; ?>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- QR Code Modal -->
    <div class="modal" id="qrModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>QR-код реферальной ссылки</h3>
                <button class="modal-close" id="qrModalClose">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="qr-container">
                    <div id="qrcode"></div>
                    <p>Отсканируйте QR-код для быстрого перехода по реферальной ссылке</p>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/qrcode@1.5.3/build/qrcode.min.js"></script>
    <script src="assets/js/dashboard.js"></script>
    <script src="assets/js/referrals.js"></script>
</body>
</html>
