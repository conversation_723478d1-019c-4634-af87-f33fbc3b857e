/**
 * AstroGenix - Стили для портфеля инвестиций
 * Эко-майнинговая инвестиционная платформа
 */

/* Portfolio Hero */
.portfolio-hero {
    background: linear-gradient(135deg, var(--primary-green) 0%, var(--primary-purple) 100%);
    padding: var(--space-12) 0 var(--space-8);
    position: relative;
    overflow: hidden;
}

.portfolio-hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="portfolio-grid" width="20" height="20" patternUnits="userSpaceOnUse"><path d="M 20 0 L 0 0 0 20" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23portfolio-grid)"/></svg>');
    opacity: 0.3;
}

/* Portfolio Overview */
.portfolio-overview {
    padding: var(--space-12) 0;
    background: var(--white);
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--space-8);
}

.portfolio-status {
    display: flex;
    align-items: center;
    gap: var(--space-2);
    padding: var(--space-2) var(--space-4);
    border-radius: var(--radius-full);
    font-weight: 600;
    font-size: var(--text-sm);
}

.portfolio-status i {
    font-size: var(--text-xs);
}

.status-excellent {
    background: rgba(34, 197, 94, 0.1);
    color: var(--success-color);
}

.status-good {
    background: rgba(59, 130, 246, 0.1);
    color: #3b82f6;
}

.status-average {
    background: rgba(245, 158, 11, 0.1);
    color: var(--warning-color);
}

.status-low {
    background: rgba(239, 68, 68, 0.1);
    color: var(--error-color);
}

.status-inactive {
    background: rgba(107, 114, 128, 0.1);
    color: var(--gray-600);
}

/* Overview Grid */
.overview-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: var(--space-6);
}

.overview-card {
    background: var(--white);
    border-radius: var(--radius-xl);
    padding: var(--space-6);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid var(--gray-200);
    display: flex;
    align-items: center;
    gap: var(--space-4);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.overview-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-green) 0%, var(--primary-purple) 100%);
}

.overview-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.card-icon {
    width: 60px;
    height: 60px;
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    color: var(--white);
    background: linear-gradient(135deg, var(--primary-green) 0%, var(--primary-green-dark) 100%);
}

.total-invested .card-icon {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
}

.total-earned .card-icon {
    background: linear-gradient(135deg, var(--success-color) 0%, #16a34a 100%);
}

.daily-income .card-icon {
    background: linear-gradient(135deg, var(--warning-color) 0%, #f59e0b 100%);
}

.investments-count .card-icon {
    background: linear-gradient(135deg, var(--primary-purple) 0%, #7c3aed 100%);
}

.card-content {
    flex: 1;
}

.card-value {
    font-size: var(--text-2xl);
    font-weight: 700;
    color: var(--dark);
    margin-bottom: var(--space-1);
    line-height: 1.2;
}

.card-label {
    font-size: var(--text-sm);
    color: var(--gray-600);
    margin-bottom: var(--space-2);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.card-detail {
    font-size: var(--text-xs);
    color: var(--gray-500);
}

/* Charts Section */
.portfolio-charts {
    padding: var(--space-12) 0;
    background: var(--gray-50);
}

.charts-grid {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: var(--space-8);
}

.chart-container {
    background: var(--white);
    border-radius: var(--radius-xl);
    padding: var(--space-6);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid var(--gray-200);
}

.chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--space-6);
    padding-bottom: var(--space-4);
    border-bottom: 1px solid var(--gray-200);
}

.chart-header h3 {
    margin: 0;
    color: var(--dark);
    font-size: var(--text-lg);
}

.chart-controls {
    display: flex;
    gap: var(--space-2);
}

.chart-period-btn {
    padding: var(--space-2) var(--space-3);
    border: 1px solid var(--gray-300);
    background: var(--white);
    color: var(--gray-600);
    border-radius: var(--radius-md);
    font-size: var(--text-xs);
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.chart-period-btn:hover {
    border-color: var(--primary-green);
    color: var(--primary-green);
}

.chart-period-btn.active {
    background: var(--primary-green);
    border-color: var(--primary-green);
    color: var(--white);
}

.chart-wrapper {
    position: relative;
    height: 300px;
}

/* Distribution Chart */
.distribution-legend {
    margin-top: var(--space-6);
    padding-top: var(--space-4);
    border-top: 1px solid var(--gray-200);
}

.legend-item {
    display: flex;
    align-items: center;
    gap: var(--space-3);
    padding: var(--space-2) 0;
}

.legend-color {
    width: 16px;
    height: 16px;
    border-radius: 50%;
    flex-shrink: 0;
}

.legend-content {
    flex: 1;
}

.legend-name {
    font-weight: 600;
    color: var(--dark);
    font-size: var(--text-sm);
}

.legend-value {
    font-size: var(--text-xs);
    color: var(--gray-600);
}

/* Forecast Section */
.forecast-section {
    padding: var(--space-12) 0;
    background: var(--white);
}

.forecast-container {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: var(--space-8);
    align-items: start;
}

.forecast-chart-wrapper {
    background: var(--white);
    border-radius: var(--radius-xl);
    padding: var(--space-6);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid var(--gray-200);
    height: 400px;
    position: relative;
}

.forecast-summary {
    background: linear-gradient(135deg, var(--primary-green) 0%, var(--primary-green-dark) 100%);
    color: var(--white);
    border-radius: var(--radius-xl);
    padding: var(--space-6);
}

.forecast-summary h3 {
    margin: 0 0 var(--space-6) 0;
    color: var(--white);
}

.forecast-metrics {
    display: flex;
    flex-direction: column;
    gap: var(--space-4);
}

.forecast-metric {
    padding: var(--space-4);
    background: rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-lg);
    backdrop-filter: blur(10px);
}

.metric-label {
    font-size: var(--text-sm);
    opacity: 0.9;
    margin-bottom: var(--space-2);
}

.metric-value {
    font-size: var(--text-xl);
    font-weight: 700;
    color: var(--white);
}

/* Risk Analysis */
.risk-analysis {
    padding: var(--space-12) 0;
    background: var(--gray-50);
}

.risk-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--space-8);
}

.risk-overview {
    background: var(--white);
    border-radius: var(--radius-xl);
    padding: var(--space-6);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid var(--gray-200);
}

.risk-score {
    text-align: center;
    margin-bottom: var(--space-6);
    padding: var(--space-6);
    border-radius: var(--radius-lg);
}

.risk-score.risk-low {
    background: rgba(34, 197, 94, 0.1);
    border: 2px solid var(--success-color);
}

.risk-score.risk-medium {
    background: rgba(245, 158, 11, 0.1);
    border: 2px solid var(--warning-color);
}

.risk-score.risk-high {
    background: rgba(239, 68, 68, 0.1);
    border: 2px solid var(--error-color);
}

.risk-score.risk-very_high {
    background: rgba(220, 38, 38, 0.1);
    border: 2px solid #dc2626;
}

.risk-score-value {
    font-size: 3rem;
    font-weight: 700;
    color: var(--dark);
    margin-bottom: var(--space-2);
}

.risk-score-label {
    font-size: var(--text-sm);
    color: var(--gray-600);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.risk-description h3 {
    color: var(--dark);
    margin-bottom: var(--space-4);
}

.risk-recommendations {
    margin-top: var(--space-4);
}

.risk-recommendations h4 {
    color: var(--dark);
    margin-bottom: var(--space-3);
    font-size: var(--text-base);
}

.risk-recommendations ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.risk-recommendations li {
    padding: var(--space-2) 0;
    padding-left: var(--space-6);
    position: relative;
    color: var(--gray-700);
    font-size: var(--text-sm);
}

.risk-recommendations li::before {
    content: '•';
    position: absolute;
    left: 0;
    color: var(--primary-green);
    font-weight: bold;
}

.risk-distribution {
    background: var(--white);
    border-radius: var(--radius-xl);
    padding: var(--space-6);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid var(--gray-200);
}

.risk-distribution h3 {
    color: var(--dark);
    margin-bottom: var(--space-6);
}

.risk-bars {
    display: flex;
    flex-direction: column;
    gap: var(--space-4);
}

.risk-bar {
    display: flex;
    flex-direction: column;
    gap: var(--space-2);
}

.risk-bar-label {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.risk-level {
    font-weight: 600;
    font-size: var(--text-sm);
    padding: var(--space-1) var(--space-2);
    border-radius: var(--radius-md);
}

.risk-level.risk-low {
    background: rgba(34, 197, 94, 0.1);
    color: var(--success-color);
}

.risk-level.risk-medium {
    background: rgba(245, 158, 11, 0.1);
    color: var(--warning-color);
}

.risk-level.risk-high {
    background: rgba(239, 68, 68, 0.1);
    color: var(--error-color);
}

.risk-level.risk-very_high {
    background: rgba(220, 38, 38, 0.1);
    color: #dc2626;
}

.risk-percentage {
    font-weight: 600;
    color: var(--dark);
}

.risk-bar-fill {
    height: 8px;
    background: var(--gray-200);
    border-radius: 4px;
    overflow: hidden;
}

.risk-bar-progress {
    height: 100%;
    border-radius: 4px;
    transition: width 1s ease;
}

.risk-bar-progress.risk-low {
    background: var(--success-color);
}

.risk-bar-progress.risk-medium {
    background: var(--warning-color);
}

.risk-bar-progress.risk-high {
    background: var(--error-color);
}

.risk-bar-progress.risk-very_high {
    background: #dc2626;
}

.risk-bar-amount {
    font-size: var(--text-xs);
    color: var(--gray-600);
    text-align: right;
}

/* Package Details Table */
.package-details {
    padding: var(--space-12) 0;
    background: var(--white);
}

.packages-table-container {
    background: var(--white);
    border-radius: var(--radius-xl);
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid var(--gray-200);
}

.packages-table {
    width: 100%;
    border-collapse: collapse;
}

.packages-table th {
    background: var(--gray-50);
    padding: var(--space-4);
    text-align: left;
    font-weight: 600;
    color: var(--dark);
    font-size: var(--text-sm);
    border-bottom: 1px solid var(--gray-200);
}

.packages-table td {
    padding: var(--space-4);
    border-bottom: 1px solid var(--gray-200);
    font-size: var(--text-sm);
}

.packages-table tr:last-child td {
    border-bottom: none;
}

.packages-table tr:hover {
    background: var(--gray-50);
}

.package-name {
    display: flex;
    align-items: center;
    gap: var(--space-3);
}

.package-color {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    flex-shrink: 0;
}

.package-type {
    background: var(--gray-100);
    color: var(--gray-700);
    padding: var(--space-1) var(--space-2);
    border-radius: var(--radius-md);
    font-size: var(--text-xs);
    font-weight: 600;
    text-transform: capitalize;
}

.roi-value.positive {
    color: var(--success-color);
    font-weight: 600;
}

.roi-value.neutral {
    color: var(--gray-600);
}

.risk-badge {
    padding: var(--space-1) var(--space-2);
    border-radius: var(--radius-md);
    font-size: var(--text-xs);
    font-weight: 600;
    text-transform: capitalize;
}

.risk-badge.risk-low {
    background: rgba(34, 197, 94, 0.1);
    color: var(--success-color);
}

.risk-badge.risk-medium {
    background: rgba(245, 158, 11, 0.1);
    color: var(--warning-color);
}

.risk-badge.risk-high {
    background: rgba(239, 68, 68, 0.1);
    color: var(--error-color);
}

.risk-badge.risk-very_high {
    background: rgba(220, 38, 38, 0.1);
    color: #dc2626;
}

/* Transaction History */
.transaction-history {
    padding: var(--space-12) 0;
    background: var(--gray-50);
}

.transactions-container {
    background: var(--white);
    border-radius: var(--radius-xl);
    padding: var(--space-6);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid var(--gray-200);
}

.transactions-filters {
    display: flex;
    gap: var(--space-4);
    margin-bottom: var(--space-6);
    padding-bottom: var(--space-4);
    border-bottom: 1px solid var(--gray-200);
}

.transactions-filters .form-control {
    flex: 1;
    max-width: 200px;
}

.transactions-list {
    display: flex;
    flex-direction: column;
    gap: var(--space-3);
    max-height: 600px;
    overflow-y: auto;
}

.transaction-item {
    display: flex;
    align-items: center;
    gap: var(--space-4);
    padding: var(--space-4);
    border-radius: var(--radius-lg);
    border: 1px solid var(--gray-200);
    transition: all 0.3s ease;
}

.transaction-item:hover {
    background: var(--gray-50);
    border-color: var(--primary-green);
}

.transaction-icon {
    width: 40px;
    height: 40px;
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
    font-size: var(--text-sm);
}

.transaction-investment .transaction-icon {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
}

.transaction-profit .transaction-icon {
    background: linear-gradient(135deg, var(--success-color) 0%, #16a34a 100%);
}

.transaction-withdrawal .transaction-icon {
    background: linear-gradient(135deg, var(--error-color) 0%, #dc2626 100%);
}

.transaction-deposit .transaction-icon {
    background: linear-gradient(135deg, var(--warning-color) 0%, #f59e0b 100%);
}

.transaction-content {
    flex: 1;
}

.transaction-title {
    font-weight: 600;
    color: var(--dark);
    margin-bottom: var(--space-1);
}

.transaction-description {
    font-size: var(--text-sm);
    color: var(--gray-600);
    margin-bottom: var(--space-1);
}

.transaction-date {
    font-size: var(--text-xs);
    color: var(--gray-500);
}

.transaction-amount {
    text-align: right;
}

.amount {
    font-weight: 700;
    font-size: var(--text-base);
    display: block;
    margin-bottom: var(--space-1);
}

.amount.positive {
    color: var(--success-color);
}

.amount.negative {
    color: var(--error-color);
}

.status {
    font-size: var(--text-xs);
    padding: var(--space-1) var(--space-2);
    border-radius: var(--radius-md);
    font-weight: 600;
    text-transform: capitalize;
}

.status-completed {
    background: rgba(34, 197, 94, 0.1);
    color: var(--success-color);
}

.status-pending {
    background: rgba(245, 158, 11, 0.1);
    color: var(--warning-color);
}

.status-failed {
    background: rgba(239, 68, 68, 0.1);
    color: var(--error-color);
}

/* Responsive Design */
@media (max-width: 1024px) {
    .charts-grid {
        grid-template-columns: 1fr;
    }
    
    .forecast-container {
        grid-template-columns: 1fr;
    }
    
    .risk-container {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .overview-grid {
        grid-template-columns: 1fr;
    }
    
    .section-header {
        flex-direction: column;
        gap: var(--space-4);
        align-items: flex-start;
    }
    
    .chart-controls {
        flex-wrap: wrap;
    }
    
    .chart-wrapper {
        height: 250px;
    }
    
    .forecast-chart-wrapper {
        height: 300px;
    }
    
    .transactions-filters {
        flex-direction: column;
    }
    
    .transactions-filters .form-control {
        max-width: none;
    }
    
    .packages-table-container {
        overflow-x: auto;
    }
    
    .packages-table {
        min-width: 800px;
    }
    
    .transaction-item {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--space-3);
    }
    
    .transaction-amount {
        text-align: left;
        width: 100%;
    }
}
