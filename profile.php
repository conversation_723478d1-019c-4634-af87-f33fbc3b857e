<?php
/**
 * AstroGenix - Страница профиля пользователя
 * Эко-майнинговая инвестиционная платформа
 */

require_once 'config/config.php';

// Проверка авторизации
if (!is_logged_in()) {
    redirect('login.php');
}

$errors = [];
$success_message = '';

try {
    $database = new Database();
    $db = $database->getConnection();
    $user = new User($db);
    $user->getUserById($_SESSION['user_id']);
    
    // Обработка обновления профиля
    if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_profile'])) {
        if (!verify_csrf_token($_POST['csrf_token'] ?? '')) {
            $errors[] = 'Ошибка безопасности. Попробуйте еще раз.';
        } else {
            $first_name = sanitize_input($_POST['first_name'] ?? '');
            $last_name = sanitize_input($_POST['last_name'] ?? '');
            $phone = sanitize_input($_POST['phone'] ?? '');
            $country = sanitize_input($_POST['country'] ?? '');
            $city = sanitize_input($_POST['city'] ?? '');
            
            // Валидация
            if (empty($first_name)) {
                $errors[] = 'Имя обязательно для заполнения.';
            }
            
            if (empty($last_name)) {
                $errors[] = 'Фамилия обязательна для заполнения.';
            }
            
            if (!empty($phone) && !preg_match('/^\+?[1-9]\d{1,14}$/', $phone)) {
                $errors[] = 'Неверный формат телефона.';
            }
            
            // Обновление профиля
            if (empty($errors)) {
                $update_query = "UPDATE users 
                                 SET first_name = :first_name, 
                                     last_name = :last_name, 
                                     phone = :phone, 
                                     country = :country, 
                                     city = :city,
                                     updated_at = NOW()
                                 WHERE id = :user_id";
                
                $update_stmt = $db->prepare($update_query);
                $update_stmt->bindParam(':first_name', $first_name);
                $update_stmt->bindParam(':last_name', $last_name);
                $update_stmt->bindParam(':phone', $phone);
                $update_stmt->bindParam(':country', $country);
                $update_stmt->bindParam(':city', $city);
                $update_stmt->bindParam(':user_id', $_SESSION['user_id']);
                
                if ($update_stmt->execute()) {
                    // Обновление данных в сессии
                    $_SESSION['first_name'] = $first_name;
                    $_SESSION['last_name'] = $last_name;
                    
                    // Обновление объекта пользователя
                    $user->getUserById($_SESSION['user_id']);
                    
                    $success_message = 'Профиль успешно обновлен!';
                } else {
                    $errors[] = 'Ошибка при обновлении профиля.';
                }
            }
        }
    }
    
    // Обработка смены пароля
    if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['change_password'])) {
        if (!verify_csrf_token($_POST['csrf_token'] ?? '')) {
            $errors[] = 'Ошибка безопасности. Попробуйте еще раз.';
        } else {
            $current_password = $_POST['current_password'] ?? '';
            $new_password = $_POST['new_password'] ?? '';
            $confirm_password = $_POST['confirm_password'] ?? '';
            
            // Валидация
            if (empty($current_password)) {
                $errors[] = 'Введите текущий пароль.';
            } elseif (!password_verify($current_password, $user->password)) {
                $errors[] = 'Неверный текущий пароль.';
            }
            
            if (empty($new_password)) {
                $errors[] = 'Введите новый пароль.';
            } elseif (strlen($new_password) < 8) {
                $errors[] = 'Новый пароль должен содержать минимум 8 символов.';
            }
            
            if ($new_password !== $confirm_password) {
                $errors[] = 'Пароли не совпадают.';
            }
            
            // Смена пароля
            if (empty($errors)) {
                $hashed_password = password_hash($new_password, PASSWORD_DEFAULT);
                
                $password_query = "UPDATE users SET password = :password WHERE id = :user_id";
                $password_stmt = $db->prepare($password_query);
                $password_stmt->bindParam(':password', $hashed_password);
                $password_stmt->bindParam(':user_id', $_SESSION['user_id']);
                
                if ($password_stmt->execute()) {
                    $success_message = 'Пароль успешно изменен!';
                } else {
                    $errors[] = 'Ошибка при смене пароля.';
                }
            }
        }
    }
    
    // Получение статистики пользователя
    $stats_query = "SELECT 
                        (SELECT COUNT(*) FROM user_investments WHERE user_id = :user_id) as total_investments,
                        (SELECT COUNT(*) FROM user_investments WHERE user_id = :user_id AND status = 'active') as active_investments,
                        (SELECT COALESCE(SUM(amount), 0) FROM transactions WHERE user_id = :user_id AND type = 'profit' AND status = 'completed') as total_profits,
                        (SELECT COUNT(*) FROM users WHERE referred_by = :user_id) as total_referrals,
                        (SELECT COALESCE(SUM(energy_generated), 0) FROM green_energy_stats WHERE user_id = :user_id) as green_energy";
    
    $stats_stmt = $db->prepare($stats_query);
    $stats_stmt->bindParam(':user_id', $_SESSION['user_id']);
    $stats_stmt->execute();
    $user_stats = $stats_stmt->fetch(PDO::FETCH_ASSOC);
    
} catch (Exception $e) {
    error_log("Profile page error: " . $e->getMessage());
    $user_stats = [];
}

$page_title = 'Профиль - ' . SITE_NAME;
?>
<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?></title>
    
    <!-- CSS -->
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="assets/css/dashboard.css">
    <link rel="stylesheet" href="assets/css/profile.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body class="dashboard-page">
    <!-- Sidebar -->
    <?php include 'includes/sidebar.php'; ?>

    <!-- Main Content -->
    <main class="main-content">
        <!-- Header -->
        <header class="dashboard-header">
            <div class="header-left">
                <button class="mobile-menu-toggle" id="mobileMenuToggle">
                    <i class="fas fa-bars"></i>
                </button>
                <h1>Профиль пользователя</h1>
            </div>
            <div class="header-right">
                <div class="user-info">
                    <span class="user-name"><?php echo htmlspecialchars($user->first_name . ' ' . $user->last_name); ?></span>
                    <span class="user-email"><?php echo htmlspecialchars($user->email); ?></span>
                </div>
            </div>
        </header>

        <!-- Content -->
        <div class="dashboard-content">
            <?php if (!empty($errors)): ?>
                <div class="alert alert-error">
                    <i class="fas fa-exclamation-triangle"></i>
                    <ul>
                        <?php foreach ($errors as $error): ?>
                            <li><?php echo htmlspecialchars($error); ?></li>
                        <?php endforeach; ?>
                    </ul>
                </div>
            <?php endif; ?>

            <?php if (!empty($success_message)): ?>
                <div class="alert alert-success">
                    <i class="fas fa-check-circle"></i>
                    <?php echo htmlspecialchars($success_message); ?>
                </div>
            <?php endif; ?>

            <!-- Profile Overview -->
            <div class="profile-overview">
                <div class="profile-card">
                    <div class="profile-avatar">
                        <div class="avatar-circle">
                            <i class="fas fa-user"></i>
                        </div>
                        <div class="avatar-info">
                            <h2><?php echo htmlspecialchars($user->first_name . ' ' . $user->last_name); ?></h2>
                            <p>@<?php echo htmlspecialchars($user->username); ?></p>
                            <div class="user-badges">
                                <span class="badge badge-verified">
                                    <i class="fas fa-check-circle"></i>
                                    Верифицирован
                                </span>
                                <?php if ($user_stats['total_investments'] > 0): ?>
                                    <span class="badge badge-investor">
                                        <i class="fas fa-chart-line"></i>
                                        Инвестор
                                    </span>
                                <?php endif; ?>
                                <?php if ($user_stats['total_referrals'] > 0): ?>
                                    <span class="badge badge-referrer">
                                        <i class="fas fa-users"></i>
                                        Партнер
                                    </span>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                    
                    <div class="profile-stats">
                        <div class="stat-item">
                            <div class="stat-icon">
                                <i class="fas fa-calendar"></i>
                            </div>
                            <div class="stat-content">
                                <span class="stat-label">Дата регистрации</span>
                                <span class="stat-value"><?php echo date('d.m.Y', strtotime($user->created_at)); ?></span>
                            </div>
                        </div>
                        
                        <div class="stat-item">
                            <div class="stat-icon">
                                <i class="fas fa-chart-line"></i>
                            </div>
                            <div class="stat-content">
                                <span class="stat-label">Инвестиций</span>
                                <span class="stat-value"><?php echo $user_stats['total_investments']; ?></span>
                            </div>
                        </div>
                        
                        <div class="stat-item">
                            <div class="stat-icon">
                                <i class="fas fa-coins"></i>
                            </div>
                            <div class="stat-content">
                                <span class="stat-label">Заработано</span>
                                <span class="stat-value"><?php echo format_currency($user_stats['total_profits']); ?></span>
                            </div>
                        </div>
                        
                        <div class="stat-item">
                            <div class="stat-icon">
                                <i class="fas fa-leaf"></i>
                            </div>
                            <div class="stat-content">
                                <span class="stat-label">Зеленая энергия</span>
                                <span class="stat-value"><?php echo number_format($user_stats['green_energy'], 2); ?> кВт⋅ч</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Profile Tabs -->
            <div class="profile-tabs">
                <div class="tab-navigation">
                    <button class="tab-btn active" data-tab="personal">
                        <i class="fas fa-user"></i>
                        Личные данные
                    </button>
                    <button class="tab-btn" data-tab="security">
                        <i class="fas fa-shield-alt"></i>
                        Безопасность
                    </button>
                    <button class="tab-btn" data-tab="referral">
                        <i class="fas fa-link"></i>
                        Реферальная ссылка
                    </button>
                    <button class="tab-btn" data-tab="ecology">
                        <i class="fas fa-seedling"></i>
                        Экология
                    </button>
                </div>

                <!-- Personal Info Tab -->
                <div class="tab-content active" id="personal">
                    <div class="card">
                        <div class="card-header">
                            <h3>Личная информация</h3>
                            <p>Обновите свои личные данные</p>
                        </div>
                        <div class="card-body">
                            <form class="profile-form" method="POST" action="">
                                <input type="hidden" name="csrf_token" value="<?php echo generate_csrf_token(); ?>">
                                <input type="hidden" name="update_profile" value="1">
                                
                                <div class="form-row">
                                    <div class="form-group">
                                        <label for="first_name">Имя *</label>
                                        <input type="text" id="first_name" name="first_name" 
                                               value="<?php echo htmlspecialchars($user->first_name); ?>" required>
                                    </div>
                                    <div class="form-group">
                                        <label for="last_name">Фамилия *</label>
                                        <input type="text" id="last_name" name="last_name" 
                                               value="<?php echo htmlspecialchars($user->last_name); ?>" required>
                                    </div>
                                </div>
                                
                                <div class="form-group">
                                    <label for="email">Email</label>
                                    <input type="email" id="email" value="<?php echo htmlspecialchars($user->email); ?>" disabled>
                                    <small class="form-help">Email нельзя изменить</small>
                                </div>
                                
                                <div class="form-group">
                                    <label for="phone">Телефон</label>
                                    <input type="tel" id="phone" name="phone" 
                                           value="<?php echo htmlspecialchars($user->phone ?? ''); ?>"
                                           placeholder="+7 (999) 123-45-67">
                                </div>
                                
                                <div class="form-row">
                                    <div class="form-group">
                                        <label for="country">Страна</label>
                                        <input type="text" id="country" name="country" 
                                               value="<?php echo htmlspecialchars($user->country ?? ''); ?>"
                                               placeholder="Россия">
                                    </div>
                                    <div class="form-group">
                                        <label for="city">Город</label>
                                        <input type="text" id="city" name="city" 
                                               value="<?php echo htmlspecialchars($user->city ?? ''); ?>"
                                               placeholder="Москва">
                                    </div>
                                </div>
                                
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save"></i>
                                    Сохранить изменения
                                </button>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- Security Tab -->
                <div class="tab-content" id="security">
                    <div class="card">
                        <div class="card-header">
                            <h3>Смена пароля</h3>
                            <p>Обновите свой пароль для повышения безопасности</p>
                        </div>
                        <div class="card-body">
                            <form class="password-form" method="POST" action="">
                                <input type="hidden" name="csrf_token" value="<?php echo generate_csrf_token(); ?>">
                                <input type="hidden" name="change_password" value="1">
                                
                                <div class="form-group">
                                    <label for="current_password">Текущий пароль *</label>
                                    <input type="password" id="current_password" name="current_password" required>
                                </div>
                                
                                <div class="form-group">
                                    <label for="new_password">Новый пароль *</label>
                                    <input type="password" id="new_password" name="new_password" required>
                                    <small class="form-help">Минимум 8 символов</small>
                                </div>
                                
                                <div class="form-group">
                                    <label for="confirm_password">Подтвердите пароль *</label>
                                    <input type="password" id="confirm_password" name="confirm_password" required>
                                </div>
                                
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-key"></i>
                                    Изменить пароль
                                </button>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- Referral Tab -->
                <div class="tab-content" id="referral">
                    <div class="card">
                        <div class="card-header">
                            <h3>Ваша реферальная ссылка</h3>
                            <p>Приглашайте друзей и получайте бонусы</p>
                        </div>
                        <div class="card-body">
                            <div class="referral-link-container">
                                <div class="link-display">
                                    <input type="text" id="referralLinkInput" 
                                           value="<?php echo SITE_URL . '/register.php?ref=' . $user->referral_code; ?>" readonly>
                                    <button class="btn btn-primary" id="copyReferralLink">
                                        <i class="fas fa-copy"></i>
                                        Копировать
                                    </button>
                                </div>
                                
                                <div class="referral-stats">
                                    <div class="referral-stat">
                                        <span class="stat-number"><?php echo $user_stats['total_referrals']; ?></span>
                                        <span class="stat-label">Приглашенных друзей</span>
                                    </div>
                                    <div class="referral-stat">
                                        <span class="stat-number"><?php echo format_currency($user->total_referral_earned ?? 0); ?></span>
                                        <span class="stat-label">Заработано с рефералов</span>
                                    </div>
                                </div>
                                
                                <div class="referral-actions">
                                    <a href="referrals.php" class="btn btn-outline">
                                        <i class="fas fa-users"></i>
                                        Подробнее о программе
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Ecology Tab -->
                <div class="tab-content" id="ecology">
                    <div class="card">
                        <div class="card-header">
                            <h3>Ваш вклад в экологию</h3>
                            <p>Отслеживайте свой вклад в развитие зеленых технологий</p>
                        </div>
                        <div class="card-body">
                            <div class="ecology-stats">
                                <div class="ecology-item">
                                    <div class="ecology-icon">
                                        <i class="fas fa-leaf"></i>
                                    </div>
                                    <div class="ecology-content">
                                        <h4>Зеленая энергия</h4>
                                        <div class="ecology-value"><?php echo number_format($user_stats['green_energy'], 2); ?> кВт⋅ч</div>
                                        <p>Произведено благодаря вашим инвестициям</p>
                                    </div>
                                </div>
                                
                                <div class="ecology-item">
                                    <div class="ecology-icon">
                                        <i class="fas fa-tree"></i>
                                    </div>
                                    <div class="ecology-content">
                                        <h4>Эквивалент деревьев</h4>
                                        <div class="ecology-value"><?php echo number_format($user_stats['green_energy'] * 0.1, 0); ?></div>
                                        <p>Деревьев посажено эквивалентно</p>
                                    </div>
                                </div>
                                
                                <div class="ecology-item">
                                    <div class="ecology-icon">
                                        <i class="fas fa-smog"></i>
                                    </div>
                                    <div class="ecology-content">
                                        <h4>CO₂ сокращение</h4>
                                        <div class="ecology-value"><?php echo number_format($user_stats['green_energy'] * 0.5, 1); ?> кг</div>
                                        <p>Углекислого газа предотвращено</p>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="ecology-progress">
                                <h4>Прогресс к следующему уровню</h4>
                                <div class="progress-bar">
                                    <?php 
                                    $next_level = ceil($user_stats['green_energy'] / 100) * 100;
                                    $progress = ($user_stats['green_energy'] / $next_level) * 100;
                                    ?>
                                    <div class="progress-fill" style="width: <?php echo min(100, $progress); ?>%"></div>
                                </div>
                                <div class="progress-info">
                                    <span><?php echo number_format($user_stats['green_energy'], 1); ?> кВт⋅ч</span>
                                    <span><?php echo number_format($next_level); ?> кВт⋅ч</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- JavaScript -->
    <script src="assets/js/dashboard.js"></script>
    <script src="assets/js/profile.js"></script>
</body>
</html>
