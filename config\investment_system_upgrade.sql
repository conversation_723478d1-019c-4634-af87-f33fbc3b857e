-- AstroGenix - Обновление системы инвестирования
-- Расширенные возможности для инвестиционных пакетов

-- Добавление новых полей в таблицу инвестиционных пакетов
ALTER TABLE investment_packages 
ADD COLUMN package_type ENUM('simple', 'compound', 'fixed', 'variable', 'vip') DEFAULT 'simple' AFTER description,
ADD COLUMN min_profit_percent DECIMAL(5,2) DEFAULT NULL AFTER daily_profit_percent,
ADD COLUMN max_profit_percent DECIMAL(5,2) DEFAULT NULL AFTER min_profit_percent,
ADD COLUMN compound_frequency ENUM('daily', 'weekly', 'monthly') DEFAULT 'daily' AFTER max_profit_percent,
ADD COLUMN early_withdrawal_fee DECIMAL(5,2) DEFAULT 0.00 AFTER compound_frequency,
ADD COLUMN bonus_percent DECIMAL(5,2) DEFAULT 0.00 AFTER early_withdrawal_fee,
ADD COLUMN risk_level ENUM('low', 'medium', 'high', 'very_high') DEFAULT 'medium' AFTER bonus_percent,
ADD COLUMN features JSON AFTER risk_level,
ADD COLUMN requirements JSON AFTER features,
ADD COLUMN icon VARCHAR(50) DEFAULT 'fas fa-chart-line' AFTER requirements,
ADD COLUMN color VARCHAR(7) DEFAULT '#22c55e' AFTER icon,
ADD COLUMN is_featured BOOLEAN DEFAULT FALSE AFTER color,
ADD COLUMN sort_order INT DEFAULT 0 AFTER is_featured;

-- Добавление новых полей в таблицу пользовательских инвестиций
ALTER TABLE user_investments 
ADD COLUMN investment_type ENUM('simple', 'compound', 'fixed', 'variable') DEFAULT 'simple' AFTER status,
ADD COLUMN auto_reinvest BOOLEAN DEFAULT FALSE AFTER investment_type,
ADD COLUMN reinvest_percent DECIMAL(5,2) DEFAULT 0.00 AFTER auto_reinvest,
ADD COLUMN compound_earnings DECIMAL(15,2) DEFAULT 0.00 AFTER total_earned,
ADD COLUMN last_profit_date DATE DEFAULT NULL AFTER compound_earnings,
ADD COLUMN next_profit_date DATE DEFAULT NULL AFTER last_profit_date,
ADD COLUMN withdrawal_requests INT DEFAULT 0 AFTER next_profit_date,
ADD COLUMN notes TEXT AFTER withdrawal_requests;

-- Создание таблицы для истории прибыли
CREATE TABLE IF NOT EXISTS investment_profit_history (
    id INT AUTO_INCREMENT PRIMARY KEY,
    investment_id INT NOT NULL,
    user_id INT NOT NULL,
    profit_amount DECIMAL(15,2) NOT NULL,
    profit_type ENUM('daily', 'bonus', 'compound', 'final') DEFAULT 'daily',
    profit_rate DECIMAL(5,2) NOT NULL,
    calculation_base DECIMAL(15,2) NOT NULL,
    profit_date DATE NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (investment_id) REFERENCES user_investments(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_investment_id (investment_id),
    INDEX idx_user_id (user_id),
    INDEX idx_profit_date (profit_date),
    INDEX idx_profit_type (profit_type)
);

-- Создание таблицы для автоматического реинвестирования
CREATE TABLE IF NOT EXISTS auto_reinvestment_settings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    is_enabled BOOLEAN DEFAULT FALSE,
    min_amount DECIMAL(15,2) DEFAULT 50.00,
    preferred_package_id INT DEFAULT NULL,
    reinvest_percent DECIMAL(5,2) DEFAULT 100.00,
    max_investments_per_day INT DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (preferred_package_id) REFERENCES investment_packages(id) ON DELETE SET NULL,
    UNIQUE KEY unique_user_settings (user_id)
);

-- Создание таблицы для лимитов и ограничений
CREATE TABLE IF NOT EXISTS investment_limits (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    daily_investment_limit DECIMAL(15,2) DEFAULT 10000.00,
    monthly_investment_limit DECIMAL(15,2) DEFAULT 100000.00,
    max_active_investments INT DEFAULT 10,
    risk_tolerance ENUM('conservative', 'moderate', 'aggressive') DEFAULT 'moderate',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_limits (user_id)
);

-- Создание таблицы для уведомлений об инвестициях
CREATE TABLE IF NOT EXISTS investment_notifications (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    investment_id INT DEFAULT NULL,
    notification_type ENUM('profit_earned', 'investment_completed', 'reinvestment_made', 'risk_warning', 'milestone_reached') NOT NULL,
    title VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    is_read BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (investment_id) REFERENCES user_investments(id) ON DELETE SET NULL,
    INDEX idx_user_id (user_id),
    INDEX idx_is_read (is_read),
    INDEX idx_notification_type (notification_type)
);

-- Обновление существующих инвестиционных пакетов с новыми данными
UPDATE investment_packages SET 
    package_type = 'simple',
    risk_level = CASE 
        WHEN name = 'Стартовый' THEN 'low'
        WHEN name = 'Стандартный' THEN 'medium'
        WHEN name = 'Премиум' THEN 'high'
        ELSE 'medium'
    END,
    features = CASE 
        WHEN name = 'Стартовый' THEN JSON_ARRAY('Ежедневные выплаты', 'Базовая поддержка', 'Реферальные бонусы')
        WHEN name = 'Стандартный' THEN JSON_ARRAY('Ежедневные выплаты', 'Приоритетная поддержка', 'Реферальные бонусы', 'Бонусы за активность')
        WHEN name = 'Премиум' THEN JSON_ARRAY('Ежедневные выплаты', 'VIP поддержка', 'Реферальные бонусы', 'Бонусы за активность', 'Персональный менеджер')
        ELSE JSON_ARRAY('Ежедневные выплаты')
    END,
    icon = CASE 
        WHEN name = 'Стартовый' THEN 'fas fa-seedling'
        WHEN name = 'Стандартный' THEN 'fas fa-chart-line'
        WHEN name = 'Премиум' THEN 'fas fa-crown'
        ELSE 'fas fa-chart-line'
    END,
    color = CASE 
        WHEN name = 'Стартовый' THEN '#22c55e'
        WHEN name = 'Стандартный' THEN '#3b82f6'
        WHEN name = 'Премиум' THEN '#8b5cf6'
        ELSE '#22c55e'
    END,
    sort_order = CASE 
        WHEN name = 'Стартовый' THEN 1
        WHEN name = 'Стандартный' THEN 2
        WHEN name = 'Премиум' THEN 3
        ELSE 99
    END;

-- Добавление новых расширенных инвестиционных пакетов
INSERT INTO investment_packages (
    name, description, package_type, min_amount, max_amount, 
    daily_profit_percent, min_profit_percent, max_profit_percent,
    duration_days, compound_frequency, bonus_percent, risk_level,
    features, requirements, icon, color, is_featured, sort_order
) VALUES 
(
    'Компаундный Рост', 
    'Инвестиционный пакет с ежедневным реинвестированием прибыли для экспоненциального роста',
    'compound',
    100.00, 5000.00, 2.00, 1.80, 2.20, 45, 'daily', 0.50,
    'medium',
    JSON_ARRAY('Компаундный процент', 'Ежедневное реинвестирование', 'Бонус за удержание', 'Аналитика роста'),
    JSON_ARRAY('Минимальный опыт инвестирования', 'Понимание компаундного процента'),
    'fas fa-chart-area', '#10b981', TRUE, 4
),
(
    'Фиксированный Доход', 
    'Стабильный пакет с гарантированной фиксированной доходностью без рисков',
    'fixed',
    500.00, 10000.00, 1.80, 1.80, 1.80, 60, 'daily', 0.00,
    'low',
    JSON_ARRAY('Гарантированная доходность', 'Нулевой риск', 'Страхование депозита', 'Возможность досрочного вывода'),
    JSON_ARRAY('Подтверждение личности', 'Минимальный депозит 500 USDT'),
    'fas fa-shield-alt', '#06b6d4', FALSE, 5
),
(
    'Переменная Ставка', 
    'Динамический пакет с переменной доходностью в зависимости от рыночных условий',
    'variable',
    200.00, 20000.00, 2.50, 1.50, 4.00, 30, 'daily', 1.00,
    'high',
    JSON_ARRAY('Переменная доходность', 'Рыночные бонусы', 'Высокий потенциал', 'Ежедневная корректировка'),
    JSON_ARRAY('Опытный инвестор', 'Понимание рыночных рисков', 'Минимум 3 завершенные инвестиции'),
    'fas fa-chart-bar', '#f59e0b', TRUE, 6
),
(
    'VIP Элитный', 
    'Эксклюзивный пакет для крупных инвесторов с персональным обслуживанием',
    'vip',
    10000.00, 100000.00, 3.00, 2.80, 3.50, 90, 'daily', 2.00,
    'very_high',
    JSON_ARRAY('Персональный менеджер', 'Приоритетные выплаты', 'Эксклюзивные бонусы', 'VIP поддержка 24/7', 'Инсайдерская аналитика'),
    JSON_ARRAY('VIP статус', 'Минимум 50,000 USDT оборота', 'Рекомендация от существующего VIP клиента'),
    'fas fa-gem', '#ec4899', TRUE, 7
),
(
    'Эко-Зеленый', 
    'Специальный экологический пакет с дополнительными бонусами за зеленую энергию',
    'simple',
    75.00, 3000.00, 2.20, 2.00, 2.40, 40, 'daily', 0.30,
    'medium',
    JSON_ARRAY('Эко-бонусы', 'Зеленая энергия', 'Углеродные кредиты', 'Экологические проекты', 'Сертификат участника'),
    JSON_ARRAY('Интерес к экологии', 'Участие в зеленых проектах'),
    'fas fa-leaf', '#16a34a', TRUE, 8
);

-- Создание индексов для оптимизации
CREATE INDEX idx_package_type ON investment_packages(package_type);
CREATE INDEX idx_risk_level ON investment_packages(risk_level);
CREATE INDEX idx_is_featured ON investment_packages(is_featured);
CREATE INDEX idx_sort_order ON investment_packages(sort_order);

CREATE INDEX idx_investment_type ON user_investments(investment_type);
CREATE INDEX idx_auto_reinvest ON user_investments(auto_reinvest);
CREATE INDEX idx_last_profit_date ON user_investments(last_profit_date);
CREATE INDEX idx_next_profit_date ON user_investments(next_profit_date);

-- Создание представления для аналитики инвестиций
CREATE OR REPLACE VIEW investment_analytics AS
SELECT 
    ui.id,
    ui.user_id,
    ui.package_id,
    ip.name as package_name,
    ip.package_type,
    ip.risk_level,
    ui.amount,
    ui.daily_profit,
    ui.total_earned,
    ui.compound_earnings,
    ui.start_date,
    ui.end_date,
    ui.status,
    ui.investment_type,
    ui.auto_reinvest,
    DATEDIFF(CURDATE(), ui.start_date) as days_active,
    DATEDIFF(ui.end_date, CURDATE()) as days_remaining,
    CASE 
        WHEN ui.end_date < CURDATE() THEN 'expired'
        WHEN ui.status = 'active' THEN 'active'
        ELSE ui.status
    END as current_status,
    ROUND((ui.total_earned / ui.amount) * 100, 2) as roi_percent,
    ROUND(ui.total_earned / GREATEST(DATEDIFF(CURDATE(), ui.start_date), 1), 2) as avg_daily_earned
FROM user_investments ui
JOIN investment_packages ip ON ui.package_id = ip.id;

-- Создание функции для расчета следующей даты прибыли
DELIMITER //
CREATE FUNCTION calculate_next_profit_date(investment_id INT) 
RETURNS DATE
READS SQL DATA
DETERMINISTIC
BEGIN
    DECLARE next_date DATE;
    DECLARE last_date DATE;
    DECLARE freq VARCHAR(10);
    
    SELECT ui.last_profit_date, ip.compound_frequency
    INTO last_date, freq
    FROM user_investments ui
    JOIN investment_packages ip ON ui.package_id = ip.id
    WHERE ui.id = investment_id;
    
    IF last_date IS NULL THEN
        SET last_date = CURDATE();
    END IF;
    
    CASE freq
        WHEN 'daily' THEN SET next_date = DATE_ADD(last_date, INTERVAL 1 DAY);
        WHEN 'weekly' THEN SET next_date = DATE_ADD(last_date, INTERVAL 1 WEEK);
        WHEN 'monthly' THEN SET next_date = DATE_ADD(last_date, INTERVAL 1 MONTH);
        ELSE SET next_date = DATE_ADD(last_date, INTERVAL 1 DAY);
    END CASE;
    
    RETURN next_date;
END //
DELIMITER ;

-- Обновление существующих инвестиций с новыми полями
UPDATE user_investments ui
JOIN investment_packages ip ON ui.package_id = ip.id
SET 
    ui.investment_type = ip.package_type,
    ui.last_profit_date = ui.start_date,
    ui.next_profit_date = DATE_ADD(ui.start_date, INTERVAL 1 DAY)
WHERE ui.last_profit_date IS NULL;

-- Создание триггера для автоматического обновления next_profit_date
DELIMITER //
CREATE TRIGGER update_next_profit_date 
AFTER UPDATE ON user_investments
FOR EACH ROW
BEGIN
    IF NEW.last_profit_date != OLD.last_profit_date THEN
        UPDATE user_investments 
        SET next_profit_date = calculate_next_profit_date(NEW.id)
        WHERE id = NEW.id;
    END IF;
END //
DELIMITER ;
