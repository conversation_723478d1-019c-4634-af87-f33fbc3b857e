<?php
/**
 * AstroGenix - Пользовательская панель управления
 * Эко-майнинговая инвестиционная платформа
 */

require_once 'config/config.php';

// Проверка авторизации
if (!is_logged_in()) {
    redirect('login.php');
}

try {
    $database = new Database();
    $db = $database->getConnection();
    
    // Получение данных пользователя
    $user = new User($db);
    $user->getUserById($_SESSION['user_id']);
    
    // Получение статистики пользователя
    $stats_query = "SELECT 
        (SELECT COUNT(*) FROM user_investments WHERE user_id = :user_id AND status = 'active') as active_investments,
        (SELECT COALESCE(SUM(amount), 0) FROM transactions WHERE user_id = :user_id AND type = 'profit' AND status = 'completed') as total_profit,
        (SELECT COUNT(*) FROM users WHERE referred_by = :user_id) as referrals_count,
        (SELECT COALESCE(SUM(amount), 0) FROM referral_commissions WHERE referrer_id = :user_id AND status = 'paid') as referral_earnings";
    
    $stats_stmt = $db->prepare($stats_query);
    $stats_stmt->bindParam(':user_id', $_SESSION['user_id']);
    $stats_stmt->execute();
    $stats = $stats_stmt->fetch(PDO::FETCH_ASSOC);
    
    // Получение активных инвестиций
    $investments_query = "SELECT ui.*, ip.name as package_name, ip.daily_profit_percent,
                         DATEDIFF(ui.end_date, CURDATE()) as days_left
                         FROM user_investments ui 
                         JOIN investment_packages ip ON ui.package_id = ip.id 
                         WHERE ui.user_id = :user_id AND ui.status = 'active' 
                         ORDER BY ui.created_at DESC LIMIT 5";
    
    $investments_stmt = $db->prepare($investments_query);
    $investments_stmt->bindParam(':user_id', $_SESSION['user_id']);
    $investments_stmt->execute();
    $active_investments = $investments_stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Получение последних транзакций
    $transactions_query = "SELECT * FROM transactions 
                          WHERE user_id = :user_id 
                          ORDER BY created_at DESC LIMIT 10";
    
    $transactions_stmt = $db->prepare($transactions_query);
    $transactions_stmt->bindParam(':user_id', $_SESSION['user_id']);
    $transactions_stmt->execute();
    $recent_transactions = $transactions_stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Получение зеленой энергии пользователя
    $energy_query = "SELECT COALESCE(energy_generated, 0) as energy_generated 
                     FROM green_energy_stats WHERE user_id = :user_id";
    $energy_stmt = $db->prepare($energy_query);
    $energy_stmt->bindParam(':user_id', $_SESSION['user_id']);
    $energy_stmt->execute();
    $energy_data = $energy_stmt->fetch(PDO::FETCH_ASSOC);
    $user_energy = $energy_data ? $energy_data['energy_generated'] : 0;
    
} catch (Exception $e) {
    error_log("Dashboard error: " . $e->getMessage());
    $error_message = "Ошибка загрузки данных. Попробуйте обновить страницу.";
}

$page_title = 'Панель управления - ' . SITE_NAME;
?>
<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?></title>
    
    <!-- CSS -->
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="assets/css/dashboard.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.css" rel="stylesheet">
</head>
<body class="dashboard-page">
    <!-- Sidebar -->
    <aside class="sidebar">
        <div class="sidebar-header">
            <div class="sidebar-logo">
                <img src="assets/images/logo.png" alt="AstroGenix">
                <span>AstroGenix</span>
            </div>
            <button class="sidebar-toggle" id="sidebarToggle">
                <i class="fas fa-bars"></i>
            </button>
        </div>
        
        <nav class="sidebar-nav">
            <ul class="nav-list">
                <li class="nav-item active">
                    <a href="dashboard.php" class="nav-link">
                        <i class="fas fa-tachometer-alt"></i>
                        <span>Панель управления</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="investments.php" class="nav-link">
                        <i class="fas fa-chart-line"></i>
                        <span>Инвестиции</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="transactions.php" class="nav-link">
                        <i class="fas fa-exchange-alt"></i>
                        <span>Транзакции</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="referrals.php" class="nav-link">
                        <i class="fas fa-users"></i>
                        <span>Рефералы</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="deposit.php" class="nav-link">
                        <i class="fas fa-plus-circle"></i>
                        <span>Пополнить</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="withdraw.php" class="nav-link">
                        <i class="fas fa-minus-circle"></i>
                        <span>Вывести</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="profile.php" class="nav-link">
                        <i class="fas fa-user"></i>
                        <span>Профиль</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="support.php" class="nav-link">
                        <i class="fas fa-headset"></i>
                        <span>Поддержка</span>
                    </a>
                </li>
            </ul>
        </nav>
        
        <div class="sidebar-footer">
            <a href="logout.php" class="logout-btn">
                <i class="fas fa-sign-out-alt"></i>
                <span>Выйти</span>
            </a>
        </div>
    </aside>

    <!-- Main Content -->
    <main class="main-content">
        <!-- Header -->
        <header class="dashboard-header">
            <div class="header-left">
                <button class="mobile-menu-toggle" id="mobileMenuToggle">
                    <i class="fas fa-bars"></i>
                </button>
                <h1>Добро пожаловать, <?php echo htmlspecialchars($user->first_name); ?>!</h1>
            </div>
            <div class="header-right">
                <div class="user-balance">
                    <span class="balance-label">Баланс:</span>
                    <span class="balance-amount"><?php echo format_currency($user->balance); ?></span>
                </div>
                <div class="user-avatar">
                    <img src="assets/images/default-avatar.png" alt="Avatar">
                </div>
            </div>
        </header>

        <!-- Dashboard Content -->
        <div class="dashboard-content">
            <?php if (isset($error_message)): ?>
                <div class="alert alert-error">
                    <i class="fas fa-exclamation-triangle"></i>
                    <?php echo htmlspecialchars($error_message); ?>
                </div>
            <?php endif; ?>

            <!-- Stats Cards -->
            <div class="stats-grid">
                <div class="stat-card balance-card">
                    <div class="stat-icon">
                        <i class="fas fa-wallet"></i>
                    </div>
                    <div class="stat-content">
                        <h3>Текущий баланс</h3>
                        <div class="stat-value"><?php echo format_currency($user->balance); ?></div>
                        <div class="stat-change positive">
                            <i class="fas fa-arrow-up"></i>
                            <span>+5.2% за неделю</span>
                        </div>
                    </div>
                </div>

                <div class="stat-card investment-card">
                    <div class="stat-icon">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <div class="stat-content">
                        <h3>Активные инвестиции</h3>
                        <div class="stat-value"><?php echo $stats['active_investments']; ?></div>
                        <div class="stat-subtitle">Общая сумма: <?php echo format_currency($user->total_invested); ?></div>
                    </div>
                </div>

                <div class="stat-card profit-card">
                    <div class="stat-icon">
                        <i class="fas fa-coins"></i>
                    </div>
                    <div class="stat-content">
                        <h3>Общая прибыль</h3>
                        <div class="stat-value"><?php echo format_currency($stats['total_profit']); ?></div>
                        <div class="stat-change positive">
                            <i class="fas fa-arrow-up"></i>
                            <span>+12.8% за месяц</span>
                        </div>
                    </div>
                </div>

                <div class="stat-card referral-card">
                    <div class="stat-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="stat-content">
                        <h3>Рефералы</h3>
                        <div class="stat-value"><?php echo $stats['referrals_count']; ?></div>
                        <div class="stat-subtitle">Заработано: <?php echo format_currency($stats['referral_earnings']); ?></div>
                    </div>
                </div>
            </div>

            <!-- Green Energy Counter -->
            <div class="green-energy-widget">
                <div class="energy-header">
                    <div class="energy-icon">
                        <i class="fas fa-leaf"></i>
                    </div>
                    <div class="energy-info">
                        <h3>Ваш вклад в экологию</h3>
                        <p>Количество зеленой энергии, произведенной благодаря вашим инвестициям</p>
                    </div>
                </div>
                <div class="energy-display">
                    <div class="energy-value" id="userEnergyCounter"><?php echo number_format($user_energy, 2); ?></div>
                    <div class="energy-unit">кВт⋅ч</div>
                </div>
                <div class="energy-progress">
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: <?php echo min(($user_energy / 1000) * 100, 100); ?>%"></div>
                    </div>
                    <div class="progress-text">До следующего уровня: <?php echo max(0, 1000 - $user_energy); ?> кВт⋅ч</div>
                </div>
            </div>

            <!-- Main Dashboard Grid -->
            <div class="dashboard-grid">
                <!-- Active Investments -->
                <div class="dashboard-widget">
                    <div class="widget-header">
                        <h3>Активные инвестиции</h3>
                        <a href="investments.php" class="widget-link">Все инвестиции</a>
                    </div>
                    <div class="widget-content">
                        <?php if (empty($active_investments)): ?>
                            <div class="empty-state">
                                <i class="fas fa-chart-line"></i>
                                <p>У вас пока нет активных инвестиций</p>
                                <a href="investments.php" class="btn btn-primary">Начать инвестировать</a>
                            </div>
                        <?php else: ?>
                            <div class="investments-list">
                                <?php foreach ($active_investments as $investment): ?>
                                    <div class="investment-item">
                                        <div class="investment-info">
                                            <h4><?php echo htmlspecialchars($investment['package_name']); ?></h4>
                                            <div class="investment-details">
                                                <span class="amount"><?php echo format_currency($investment['amount']); ?></span>
                                                <span class="profit"><?php echo $investment['daily_profit_percent']; ?>% в день</span>
                                            </div>
                                        </div>
                                        <div class="investment-progress">
                                            <div class="days-left">
                                                <?php if ($investment['days_left'] > 0): ?>
                                                    <span class="days"><?php echo $investment['days_left']; ?></span>
                                                    <span class="label">дней осталось</span>
                                                <?php else: ?>
                                                    <span class="completed">Завершено</span>
                                                <?php endif; ?>
                                            </div>
                                            <div class="progress-circle">
                                                <svg viewBox="0 0 36 36">
                                                    <path d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                                                          fill="none" stroke="#e5e5e5" stroke-width="2"/>
                                                    <path d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                                                          fill="none" stroke="var(--primary-green)" stroke-width="2"
                                                          stroke-dasharray="<?php echo max(0, (30 - $investment['days_left']) / 30 * 100); ?>, 100"/>
                                                </svg>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Recent Transactions -->
                <div class="dashboard-widget">
                    <div class="widget-header">
                        <h3>Последние транзакции</h3>
                        <a href="transactions.php" class="widget-link">Все транзакции</a>
                    </div>
                    <div class="widget-content">
                        <?php if (empty($recent_transactions)): ?>
                            <div class="empty-state">
                                <i class="fas fa-exchange-alt"></i>
                                <p>Транзакций пока нет</p>
                            </div>
                        <?php else: ?>
                            <div class="transactions-list">
                                <?php foreach ($recent_transactions as $transaction): ?>
                                    <div class="transaction-item">
                                        <div class="transaction-icon <?php echo $transaction['type']; ?>">
                                            <i class="fas fa-<?php 
                                                echo $transaction['type'] === 'deposit' ? 'plus' : 
                                                    ($transaction['type'] === 'withdrawal' ? 'minus' : 
                                                    ($transaction['type'] === 'profit' ? 'coins' : 'exchange-alt')); 
                                            ?>"></i>
                                        </div>
                                        <div class="transaction-info">
                                            <div class="transaction-type">
                                                <?php 
                                                $types = [
                                                    'deposit' => 'Пополнение',
                                                    'withdrawal' => 'Вывод',
                                                    'investment' => 'Инвестиция',
                                                    'profit' => 'Прибыль',
                                                    'referral_bonus' => 'Реферальный бонус'
                                                ];
                                                echo $types[$transaction['type']] ?? $transaction['type'];
                                                ?>
                                            </div>
                                            <div class="transaction-date">
                                                <?php echo date('d.m.Y H:i', strtotime($transaction['created_at'])); ?>
                                            </div>
                                        </div>
                                        <div class="transaction-amount <?php echo in_array($transaction['type'], ['deposit', 'profit', 'referral_bonus']) ? 'positive' : 'negative'; ?>">
                                            <?php echo (in_array($transaction['type'], ['deposit', 'profit', 'referral_bonus']) ? '+' : '-') . format_currency($transaction['amount']); ?>
                                        </div>
                                        <div class="transaction-status status-<?php echo $transaction['status']; ?>">
                                            <?php 
                                            $statuses = [
                                                'pending' => 'Ожидание',
                                                'approved' => 'Одобрено',
                                                'rejected' => 'Отклонено',
                                                'completed' => 'Завершено'
                                            ];
                                            echo $statuses[$transaction['status']] ?? $transaction['status'];
                                            ?>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Profit Chart -->
                <div class="dashboard-widget chart-widget">
                    <div class="widget-header">
                        <h3>График прибыли</h3>
                        <div class="chart-controls">
                            <button class="chart-period active" data-period="7">7 дней</button>
                            <button class="chart-period" data-period="30">30 дней</button>
                            <button class="chart-period" data-period="90">90 дней</button>
                        </div>
                    </div>
                    <div class="widget-content">
                        <canvas id="profitChart"></canvas>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="dashboard-widget">
                    <div class="widget-header">
                        <h3>Быстрые действия</h3>
                    </div>
                    <div class="widget-content">
                        <div class="quick-actions">
                            <a href="deposit.php" class="quick-action deposit">
                                <i class="fas fa-plus-circle"></i>
                                <span>Пополнить баланс</span>
                            </a>
                            <a href="investments.php" class="quick-action invest">
                                <i class="fas fa-chart-line"></i>
                                <span>Инвестировать</span>
                            </a>
                            <a href="withdraw.php" class="quick-action withdraw">
                                <i class="fas fa-minus-circle"></i>
                                <span>Вывести средства</span>
                            </a>
                            <a href="referrals.php" class="quick-action referral">
                                <i class="fas fa-users"></i>
                                <span>Пригласить друзей</span>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.js"></script>
    <script src="assets/js/dashboard.js"></script>
</body>
</html>
