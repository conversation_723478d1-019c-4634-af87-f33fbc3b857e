<?php
/**
 * AstroGenix - Класс для работы с уведомлениями
 * Эко-майнинговая инвестиционная платформа
 */

class Notification {
    private $db;
    
    public function __construct($database) {
        $this->db = $database;
    }
    
    /**
     * Создание нового уведомления
     */
    public function create($user_id, $title, $message, $type = 'info', $action_url = null) {
        try {
            $query = "INSERT INTO notifications (user_id, title, message, type, action_url, created_at) 
                      VALUES (:user_id, :title, :message, :type, :action_url, NOW())";
            
            $stmt = $this->db->prepare($query);
            $stmt->bindParam(':user_id', $user_id, PDO::PARAM_INT);
            $stmt->bindParam(':title', $title);
            $stmt->bindParam(':message', $message);
            $stmt->bindParam(':type', $type);
            $stmt->bindParam(':action_url', $action_url);
            
            return $stmt->execute();
        } catch (Exception $e) {
            error_log("Notification creation error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Получение уведомлений пользователя
     */
    public function getUserNotifications($user_id, $limit = 10, $unread_only = false) {
        try {
            $where_clause = "WHERE user_id = :user_id";
            if ($unread_only) {
                $where_clause .= " AND is_read = 0";
            }
            
            $query = "SELECT * FROM notifications 
                      $where_clause 
                      ORDER BY created_at DESC 
                      LIMIT :limit";
            
            $stmt = $this->db->prepare($query);
            $stmt->bindParam(':user_id', $user_id, PDO::PARAM_INT);
            $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
            $stmt->execute();
            
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (Exception $e) {
            error_log("Get notifications error: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Получение количества непрочитанных уведомлений
     */
    public function getUnreadCount($user_id) {
        try {
            $query = "SELECT COUNT(*) as count FROM notifications 
                      WHERE user_id = :user_id AND is_read = 0";
            
            $stmt = $this->db->prepare($query);
            $stmt->bindParam(':user_id', $user_id, PDO::PARAM_INT);
            $stmt->execute();
            
            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            return $result['count'] ?? 0;
        } catch (Exception $e) {
            error_log("Get unread count error: " . $e->getMessage());
            return 0;
        }
    }
    
    /**
     * Отметка уведомления как прочитанного
     */
    public function markAsRead($notification_id, $user_id = null) {
        try {
            $where_clause = "WHERE id = :id";
            $params = [':id' => $notification_id];
            
            if ($user_id !== null) {
                $where_clause .= " AND user_id = :user_id";
                $params[':user_id'] = $user_id;
            }
            
            $query = "UPDATE notifications SET is_read = 1 $where_clause";
            
            $stmt = $this->db->prepare($query);
            foreach ($params as $key => $value) {
                $stmt->bindValue($key, $value, is_int($value) ? PDO::PARAM_INT : PDO::PARAM_STR);
            }
            
            return $stmt->execute();
        } catch (Exception $e) {
            error_log("Mark as read error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Отметка всех уведомлений пользователя как прочитанных
     */
    public function markAllAsRead($user_id) {
        try {
            $query = "UPDATE notifications SET is_read = 1 WHERE user_id = :user_id";
            
            $stmt = $this->db->prepare($query);
            $stmt->bindParam(':user_id', $user_id, PDO::PARAM_INT);
            
            return $stmt->execute();
        } catch (Exception $e) {
            error_log("Mark all as read error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Удаление уведомления
     */
    public function delete($notification_id, $user_id = null) {
        try {
            $where_clause = "WHERE id = :id";
            $params = [':id' => $notification_id];
            
            if ($user_id !== null) {
                $where_clause .= " AND user_id = :user_id";
                $params[':user_id'] = $user_id;
            }
            
            $query = "DELETE FROM notifications $where_clause";
            
            $stmt = $this->db->prepare($query);
            foreach ($params as $key => $value) {
                $stmt->bindValue($key, $value, is_int($value) ? PDO::PARAM_INT : PDO::PARAM_STR);
            }
            
            return $stmt->execute();
        } catch (Exception $e) {
            error_log("Delete notification error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Очистка старых уведомлений
     */
    public function cleanupOldNotifications($days = 30) {
        try {
            $query = "DELETE FROM notifications 
                      WHERE created_at < DATE_SUB(NOW(), INTERVAL :days DAY)";
            
            $stmt = $this->db->prepare($query);
            $stmt->bindParam(':days', $days, PDO::PARAM_INT);
            
            return $stmt->execute();
        } catch (Exception $e) {
            error_log("Cleanup notifications error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Создание системных уведомлений
     */
    public function createSystemNotification($title, $message, $type = 'info') {
        try {
            // Получение всех активных пользователей
            $users_query = "SELECT id FROM users WHERE is_active = 1";
            $users_stmt = $this->db->prepare($users_query);
            $users_stmt->execute();
            $users = $users_stmt->fetchAll(PDO::FETCH_COLUMN);
            
            $created_count = 0;
            foreach ($users as $user_id) {
                if ($this->create($user_id, $title, $message, $type)) {
                    $created_count++;
                }
            }
            
            return $created_count;
        } catch (Exception $e) {
            error_log("Create system notification error: " . $e->getMessage());
            return 0;
        }
    }
    
    /**
     * Создание уведомления о депозите
     */
    public function createDepositNotification($user_id, $amount, $status) {
        $titles = [
            'pending' => 'Запрос на пополнение создан',
            'completed' => 'Пополнение успешно обработано',
            'rejected' => 'Запрос на пополнение отклонен'
        ];
        
        $messages = [
            'pending' => "Ваш запрос на пополнение на сумму " . format_currency($amount) . " создан и ожидает обработки.",
            'completed' => "Ваш баланс пополнен на " . format_currency($amount) . ". Средства доступны для инвестирования.",
            'rejected' => "Ваш запрос на пополнение на сумму " . format_currency($amount) . " был отклонен. Обратитесь в поддержку."
        ];
        
        $types = [
            'pending' => 'info',
            'completed' => 'success',
            'rejected' => 'error'
        ];
        
        return $this->create(
            $user_id,
            $titles[$status] ?? 'Уведомление о депозите',
            $messages[$status] ?? 'Статус вашего депозита изменился.',
            $types[$status] ?? 'info',
            'transactions.php'
        );
    }
    
    /**
     * Создание уведомления о выводе
     */
    public function createWithdrawalNotification($user_id, $amount, $status) {
        $titles = [
            'pending' => 'Запрос на вывод создан',
            'completed' => 'Вывод средств завершен',
            'rejected' => 'Запрос на вывод отклонен'
        ];
        
        $messages = [
            'pending' => "Ваш запрос на вывод " . format_currency($amount) . " создан и ожидает обработки.",
            'completed' => "Вывод " . format_currency($amount) . " успешно обработан. Средства отправлены на указанные реквизиты.",
            'rejected' => "Ваш запрос на вывод " . format_currency($amount) . " был отклонен. Обратитесь в поддержку."
        ];
        
        $types = [
            'pending' => 'info',
            'completed' => 'success',
            'rejected' => 'error'
        ];
        
        return $this->create(
            $user_id,
            $titles[$status] ?? 'Уведомление о выводе',
            $messages[$status] ?? 'Статус вашего вывода изменился.',
            $types[$status] ?? 'info',
            'transactions.php'
        );
    }
    
    /**
     * Создание уведомления о прибыли
     */
    public function createProfitNotification($user_id, $amount, $investment_name) {
        return $this->create(
            $user_id,
            'Начислена прибыль',
            "По инвестиции \"$investment_name\" начислена прибыль в размере " . format_currency($amount) . ".",
            'success',
            'investments.php'
        );
    }
    
    /**
     * Создание уведомления о реферальном бонусе
     */
    public function createReferralNotification($user_id, $amount, $referred_user) {
        return $this->create(
            $user_id,
            'Реферальный бонус',
            "Вы получили реферальный бонус " . format_currency($amount) . " от пользователя $referred_user.",
            'success',
            'referrals.php'
        );
    }
    
    /**
     * Создание welcome уведомления для новых пользователей
     */
    public function createWelcomeNotification($user_id, $first_name) {
        return $this->create(
            $user_id,
            "Добро пожаловать в AstroGenix, $first_name!",
            "Спасибо за регистрацию! Изучите наши инвестиционные пакеты и начните зарабатывать с экологически чистыми технологиями.",
            'info',
            'investments.php'
        );
    }
}
?>
