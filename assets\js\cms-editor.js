/**
 * AstroGenix CMS - Расширенный редактор контента
 * Настройки и функции для WYSIWYG редактора
 */

// Конфигурация TinyMCE для CMS
const cmsEditorConfig = {
    // Базовые настройки
    selector: '.tinymce-editor',
    height: 500,
    menubar: 'file edit view insert format tools table help',
    
    // Плагины
    plugins: [
        'advlist', 'autolink', 'lists', 'link', 'image', 'charmap', 'preview',
        'anchor', 'searchreplace', 'visualblocks', 'code', 'fullscreen',
        'insertdatetime', 'media', 'table', 'help', 'wordcount', 'emoticons',
        'template', 'paste', 'textcolor', 'colorpicker', 'hr', 'pagebreak',
        'nonbreaking', 'toc', 'imagetools', 'codesample'
    ],
    
    // Панель инструментов
    toolbar1: 'undo redo | bold italic underline strikethrough | fontselect fontsizeselect formatselect | alignleft aligncenter alignright alignjustify',
    toolbar2: 'cut copy paste | searchreplace | bullist numlist | outdent indent blockquote | link unlink anchor | image media | insertdatetime preview | forecolor backcolor',
    toolbar3: 'table | hr removeformat | subscript superscript charmap emoticons | print fullscreen | ltr rtl | spellchecker | visualchars visualblocks nonbreaking template pagebreak restoredraft',
    
    // Настройки контента
    content_style: `
        body { 
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif; 
            font-size: 14px; 
            line-height: 1.6;
            color: #333;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        h1, h2, h3, h4, h5, h6 {
            color: #22c55e;
            margin-top: 1.5em;
            margin-bottom: 0.5em;
        }
        a {
            color: #8b5cf6;
            text-decoration: none;
        }
        a:hover {
            text-decoration: underline;
        }
        blockquote {
            border-left: 4px solid #22c55e;
            margin: 1.5em 0;
            padding: 0.5em 1em;
            background: #f0fdf4;
        }
        code {
            background: #f3f4f6;
            padding: 2px 4px;
            border-radius: 3px;
            font-family: 'Courier New', monospace;
        }
        pre {
            background: #1f2937;
            color: #f9fafb;
            padding: 1em;
            border-radius: 6px;
            overflow-x: auto;
        }
        table {
            border-collapse: collapse;
            width: 100%;
            margin: 1em 0;
        }
        table th, table td {
            border: 1px solid #d1d5db;
            padding: 8px 12px;
            text-align: left;
        }
        table th {
            background: #f9fafb;
            font-weight: 600;
        }
        .btn {
            display: inline-block;
            padding: 8px 16px;
            background: #22c55e;
            color: white;
            border-radius: 6px;
            text-decoration: none;
            font-weight: 500;
            transition: background 0.3s ease;
        }
        .btn:hover {
            background: #16a34a;
            text-decoration: none;
        }
        .btn-secondary {
            background: #8b5cf6;
        }
        .btn-secondary:hover {
            background: #7c3aed;
        }
    `,
    
    // Языковые настройки
    language: 'ru',
    
    // Настройки изображений
    image_advtab: true,
    image_caption: true,
    image_list: '/admin/api/media-list.php',
    
    // Настройки ссылок
    link_list: '/admin/api/page-list.php',
    link_context_toolbar: true,
    
    // Настройки медиа
    media_live_embeds: true,
    
    // Настройки таблиц
    table_responsive_width: true,
    table_default_attributes: {
        'class': 'table table-striped'
    },
    
    // Шаблоны контента
    templates: [
        {
            title: 'Двухколоночный макет',
            description: 'Контент в две колонки',
            content: `
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin: 20px 0;">
                    <div>
                        <h3>Левая колонка</h3>
                        <p>Содержимое левой колонки...</p>
                    </div>
                    <div>
                        <h3>Правая колонка</h3>
                        <p>Содержимое правой колонки...</p>
                    </div>
                </div>
            `
        },
        {
            title: 'Карточка с изображением',
            description: 'Карточка контента с изображением',
            content: `
                <div style="border: 1px solid #e5e7eb; border-radius: 8px; overflow: hidden; margin: 20px 0;">
                    <img src="https://via.placeholder.com/400x200" alt="Изображение" style="width: 100%; height: 200px; object-fit: cover;">
                    <div style="padding: 20px;">
                        <h3 style="margin-top: 0;">Заголовок карточки</h3>
                        <p>Описание содержимого карточки...</p>
                        <a href="#" class="btn">Подробнее</a>
                    </div>
                </div>
            `
        },
        {
            title: 'Блок с предупреждением',
            description: 'Информационный блок',
            content: `
                <div style="background: #fef3c7; border: 1px solid #f59e0b; border-radius: 6px; padding: 16px; margin: 20px 0;">
                    <h4 style="color: #92400e; margin-top: 0;">⚠️ Важная информация</h4>
                    <p style="color: #92400e; margin-bottom: 0;">Текст важного сообщения или предупреждения...</p>
                </div>
            `
        },
        {
            title: 'Блок с успехом',
            description: 'Блок успешного сообщения',
            content: `
                <div style="background: #d1fae5; border: 1px solid #22c55e; border-radius: 6px; padding: 16px; margin: 20px 0;">
                    <h4 style="color: #065f46; margin-top: 0;">✅ Успешно</h4>
                    <p style="color: #065f46; margin-bottom: 0;">Текст успешного сообщения...</p>
                </div>
            `
        },
        {
            title: 'Призыв к действию',
            description: 'CTA блок',
            content: `
                <div style="background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%); color: white; text-align: center; padding: 40px 20px; border-radius: 12px; margin: 30px 0;">
                    <h2 style="color: white; margin-top: 0;">Готовы начать?</h2>
                    <p style="font-size: 18px; margin-bottom: 30px;">Присоединяйтесь к тысячам довольных пользователей</p>
                    <a href="#" style="background: white; color: #22c55e; padding: 12px 30px; border-radius: 6px; text-decoration: none; font-weight: 600; display: inline-block;">Начать сейчас</a>
                </div>
            `
        }
    ],
    
    // Настройки автосохранения
    autosave_ask_before_unload: true,
    autosave_interval: '30s',
    autosave_prefix: 'astrogenix-cms-',
    
    // Настройки вставки
    paste_data_images: true,
    paste_as_text: false,
    paste_webkit_styles: 'color font-size',
    
    // Настройки проверки орфографии
    browser_spellcheck: true,
    
    // Настройки кода
    codesample_languages: [
        {text: 'HTML/XML', value: 'markup'},
        {text: 'JavaScript', value: 'javascript'},
        {text: 'CSS', value: 'css'},
        {text: 'PHP', value: 'php'},
        {text: 'Python', value: 'python'},
        {text: 'SQL', value: 'sql'},
        {text: 'JSON', value: 'json'}
    ],
    
    // Настройки форматирования
    formats: {
        alignleft: {selector: 'p,h1,h2,h3,h4,h5,h6,td,th,div,ul,ol,li,table,img', classes: 'text-left'},
        aligncenter: {selector: 'p,h1,h2,h3,h4,h5,h6,td,th,div,ul,ol,li,table,img', classes: 'text-center'},
        alignright: {selector: 'p,h1,h2,h3,h4,h5,h6,td,th,div,ul,ol,li,table,img', classes: 'text-right'},
        alignjustify: {selector: 'p,h1,h2,h3,h4,h5,h6,td,th,div,ul,ol,li,table,img', classes: 'text-justify'},
        bold: {inline: 'strong'},
        italic: {inline: 'em'},
        underline: {inline: 'u'},
        strikethrough: {inline: 'del'},
        customButton: {inline: 'span', classes: 'btn', attributes: {role: 'button'}},
        customHighlight: {inline: 'mark', classes: 'highlight'}
    },
    
    // Стили для выбора
    style_formats: [
        {title: 'Заголовки', items: [
            {title: 'Заголовок 1', format: 'h1'},
            {title: 'Заголовок 2', format: 'h2'},
            {title: 'Заголовок 3', format: 'h3'},
            {title: 'Заголовок 4', format: 'h4'},
            {title: 'Заголовок 5', format: 'h5'},
            {title: 'Заголовок 6', format: 'h6'}
        ]},
        {title: 'Блоки', items: [
            {title: 'Параграф', format: 'p'},
            {title: 'Цитата', format: 'blockquote'},
            {title: 'Код', format: 'code'},
            {title: 'Предварительно отформатированный', format: 'pre'}
        ]},
        {title: 'Выделение', items: [
            {title: 'Жирный', format: 'bold'},
            {title: 'Курсив', format: 'italic'},
            {title: 'Подчеркнутый', format: 'underline'},
            {title: 'Зачеркнутый', format: 'strikethrough'},
            {title: 'Выделенный', format: 'customHighlight'}
        ]},
        {title: 'Кнопки', items: [
            {title: 'Основная кнопка', inline: 'a', classes: 'btn'},
            {title: 'Вторичная кнопка', inline: 'a', classes: 'btn btn-secondary'}
        ]}
    ],
    
    // Настройки размеров шрифта
    fontsize_formats: '8pt 10pt 12pt 14pt 16pt 18pt 24pt 36pt 48pt',
    
    // Настройки шрифтов
    font_formats: 'Arial=arial,helvetica,sans-serif; Courier New=courier new,courier,monospace; AkrutiKndPadmini=Akpdmi-n; Times New Roman=times new roman,times,serif; Verdana=verdana,geneva,sans-serif;',
    
    // Обработчики событий
    setup: function(editor) {
        // Добавление кастомных кнопок
        editor.ui.registry.addButton('customSave', {
            text: 'Сохранить',
            icon: 'save',
            onAction: function() {
                // Сохранение контента
                const form = editor.getElement().closest('form');
                if (form) {
                    form.submit();
                }
            }
        });
        
        editor.ui.registry.addButton('customPreview', {
            text: 'Предпросмотр',
            icon: 'preview',
            onAction: function() {
                // Открытие предпросмотра
                const content = editor.getContent();
                const previewWindow = window.open('', '_blank', 'width=800,height=600');
                previewWindow.document.write(`
                    <!DOCTYPE html>
                    <html>
                    <head>
                        <title>Предпросмотр</title>
                        <link rel="stylesheet" href="/assets/css/style.css">
                        <style>
                            body { padding: 20px; max-width: 800px; margin: 0 auto; }
                        </style>
                    </head>
                    <body>
                        ${content}
                    </body>
                    </html>
                `);
                previewWindow.document.close();
            }
        });
        
        // Добавление кастомных кнопок в панель инструментов
        editor.on('init', function() {
            // Обновление панели инструментов
            const toolbar = editor.theme.panel.find('toolbar')[0];
            if (toolbar) {
                toolbar.settings.items.push('customSave customPreview');
            }
        });
        
        // Автосохранение
        let autoSaveTimer;
        editor.on('input', function() {
            clearTimeout(autoSaveTimer);
            autoSaveTimer = setTimeout(function() {
                const content = editor.getContent();
                localStorage.setItem('cms-autosave-' + Date.now(), content);
                console.log('Контент автоматически сохранен');
            }, 5000);
        });
        
        // Подсчет слов и символов
        editor.on('input', function() {
            const content = editor.getContent({format: 'text'});
            const wordCount = content.split(/\s+/).filter(word => word.length > 0).length;
            const charCount = content.length;
            
            // Обновление счетчика (если есть элемент)
            const counter = document.getElementById('content-counter');
            if (counter) {
                counter.innerHTML = `Слов: ${wordCount} | Символов: ${charCount}`;
            }
        });
    },
    
    // Настройки безопасности
    valid_elements: '*[*]',
    extended_valid_elements: 'script[src|async|defer|type|charset]',
    
    // Настройки производительности
    cache_suffix: '?v=6.7.0'
};

// Функция инициализации редактора
function initCMSEditor(selector = '.tinymce-editor', customConfig = {}) {
    const config = Object.assign({}, cmsEditorConfig, customConfig);
    config.selector = selector;
    
    return tinymce.init(config);
}

// Функция для простого редактора (без лишних функций)
function initSimpleEditor(selector = '.simple-editor') {
    return tinymce.init({
        selector: selector,
        height: 300,
        menubar: false,
        plugins: ['lists', 'link', 'image', 'charmap', 'preview', 'anchor', 'searchreplace', 'code', 'insertdatetime', 'media', 'table', 'help', 'wordcount'],
        toolbar: 'undo redo | formatselect | bold italic | alignleft aligncenter alignright | bullist numlist | link image | code',
        content_style: cmsEditorConfig.content_style,
        language: 'ru'
    });
}

// Экспорт функций для использования
window.CMSEditor = {
    init: initCMSEditor,
    initSimple: initSimpleEditor,
    config: cmsEditorConfig
};
