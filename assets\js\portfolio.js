/**
 * AstroGenix - JavaScript для портфеля инвестиций
 * Графики, аналитика и интерактивность
 */

document.addEventListener('DOMContentLoaded', function() {
    initializePortfolio();
});

let performanceChart = null;
let distributionChart = null;
let forecastChart = null;

function initializePortfolio() {
    // Инициализация графиков
    initPerformanceChart();
    initDistributionChart();
    initForecastChart();
    
    // Инициализация фильтров
    initTransactionFilters();
    
    // Инициализация экспорта
    initExportFunctionality();
    
    // Инициализация анимаций
    initPortfolioAnimations();
}

// График динамики доходности
function initPerformanceChart() {
    const ctx = document.getElementById('performance-chart');
    if (!ctx || !window.portfolioData) return;
    
    const data = window.portfolioData.performance;
    
    performanceChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: data.map(item => item.date),
            datasets: [{
                label: 'Накопленная прибыль',
                data: data.map(item => item.cumulative_profit),
                borderColor: '#22c55e',
                backgroundColor: 'rgba(34, 197, 94, 0.1)',
                tension: 0.4,
                fill: true
            }, {
                label: 'Инвестировано',
                data: data.map(item => item.cumulative_invested),
                borderColor: '#3b82f6',
                backgroundColor: 'rgba(59, 130, 246, 0.1)',
                tension: 0.4,
                fill: false
            }, {
                label: 'ROI (%)',
                data: data.map(item => item.cumulative_roi),
                borderColor: '#8b5cf6',
                backgroundColor: 'rgba(139, 92, 246, 0.1)',
                tension: 0.4,
                fill: false,
                yAxisID: 'y1'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                title: {
                    display: false
                },
                legend: {
                    display: true,
                    position: 'top'
                },
                tooltip: {
                    mode: 'index',
                    intersect: false,
                    callbacks: {
                        label: function(context) {
                            let label = context.dataset.label || '';
                            if (label) {
                                label += ': ';
                            }
                            if (context.datasetIndex === 2) {
                                label += context.parsed.y.toFixed(2) + '%';
                            } else {
                                label += formatCurrency(context.parsed.y) + ' USDT';
                            }
                            return label;
                        }
                    }
                }
            },
            scales: {
                x: {
                    type: 'time',
                    time: {
                        unit: 'day',
                        displayFormats: {
                            day: 'dd.MM'
                        }
                    },
                    grid: {
                        display: false
                    }
                },
                y: {
                    type: 'linear',
                    display: true,
                    position: 'left',
                    beginAtZero: true,
                    ticks: {
                        callback: function(value) {
                            return formatCurrency(value) + ' USDT';
                        }
                    }
                },
                y1: {
                    type: 'linear',
                    display: true,
                    position: 'right',
                    beginAtZero: true,
                    grid: {
                        drawOnChartArea: false,
                    },
                    ticks: {
                        callback: function(value) {
                            return value.toFixed(1) + '%';
                        }
                    }
                }
            },
            interaction: {
                mode: 'nearest',
                axis: 'x',
                intersect: false
            }
        }
    });
    
    // Обработчики кнопок периода
    const periodButtons = document.querySelectorAll('.chart-period-btn');
    periodButtons.forEach(button => {
        button.addEventListener('click', function() {
            periodButtons.forEach(btn => btn.classList.remove('active'));
            this.classList.add('active');
            
            const period = parseInt(this.dataset.period);
            updatePerformanceChart(period);
        });
    });
}

// График распределения по пакетам
function initDistributionChart() {
    const ctx = document.getElementById('distribution-chart');
    if (!ctx || !window.portfolioData) return;
    
    const data = window.portfolioData.distribution;
    
    distributionChart = new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: data.map(item => item.package_name),
            datasets: [{
                data: data.map(item => item.total_amount),
                backgroundColor: data.map(item => item.color),
                borderWidth: 2,
                borderColor: '#ffffff'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            const label = context.label || '';
                            const value = context.parsed;
                            const percentage = data[context.dataIndex].percentage;
                            return `${label}: ${formatCurrency(value)} USDT (${percentage}%)`;
                        }
                    }
                }
            },
            cutout: '60%'
        }
    });
}

// График прогноза доходов
function initForecastChart() {
    const ctx = document.getElementById('forecast-chart');
    if (!ctx || !window.portfolioData) return;
    
    const data = window.portfolioData.forecast;
    
    forecastChart = new Chart(ctx, {
        type: 'bar',
        data: {
            labels: data.map(item => item.date),
            datasets: [{
                label: 'Ежедневная прибыль',
                data: data.map(item => item.daily_profit),
                backgroundColor: 'rgba(34, 197, 94, 0.8)',
                borderColor: '#22c55e',
                borderWidth: 1
            }, {
                label: 'Накопленная прибыль',
                data: data.map(item => item.cumulative_profit),
                type: 'line',
                borderColor: '#8b5cf6',
                backgroundColor: 'rgba(139, 92, 246, 0.1)',
                tension: 0.4,
                fill: false,
                yAxisID: 'y1'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                title: {
                    display: false
                },
                legend: {
                    display: true,
                    position: 'top'
                },
                tooltip: {
                    mode: 'index',
                    intersect: false,
                    callbacks: {
                        label: function(context) {
                            let label = context.dataset.label || '';
                            if (label) {
                                label += ': ';
                            }
                            label += formatCurrency(context.parsed.y) + ' USDT';
                            return label;
                        }
                    }
                }
            },
            scales: {
                x: {
                    type: 'time',
                    time: {
                        unit: 'day',
                        displayFormats: {
                            day: 'dd.MM'
                        }
                    },
                    grid: {
                        display: false
                    }
                },
                y: {
                    type: 'linear',
                    display: true,
                    position: 'left',
                    beginAtZero: true,
                    ticks: {
                        callback: function(value) {
                            return formatCurrency(value) + ' USDT';
                        }
                    }
                },
                y1: {
                    type: 'linear',
                    display: true,
                    position: 'right',
                    beginAtZero: true,
                    grid: {
                        drawOnChartArea: false,
                    },
                    ticks: {
                        callback: function(value) {
                            return formatCurrency(value) + ' USDT';
                        }
                    }
                }
            }
        }
    });
}

// Обновление графика динамики для разных периодов
function updatePerformanceChart(period) {
    // В реальном приложении здесь будет AJAX запрос за данными
    // Пока используем существующие данные
    console.log(`Обновление графика для периода: ${period} дней`);
}

// Фильтры транзакций
function initTransactionFilters() {
    const typeFilter = document.getElementById('transaction-type-filter');
    const dateFromFilter = document.getElementById('date-from-filter');
    const dateToFilter = document.getElementById('date-to-filter');
    const applyButton = document.getElementById('apply-filters');
    
    if (applyButton) {
        applyButton.addEventListener('click', function() {
            const filters = {
                type: typeFilter.value,
                dateFrom: dateFromFilter.value,
                dateTo: dateToFilter.value
            };
            
            applyTransactionFilters(filters);
        });
    }
    
    // Установка значений по умолчанию для дат
    if (dateToFilter) {
        dateToFilter.value = new Date().toISOString().split('T')[0];
    }
    if (dateFromFilter) {
        const monthAgo = new Date();
        monthAgo.setMonth(monthAgo.getMonth() - 1);
        dateFromFilter.value = monthAgo.toISOString().split('T')[0];
    }
}

// Применение фильтров к транзакциям
function applyTransactionFilters(filters) {
    const transactions = document.querySelectorAll('.transaction-item');
    
    transactions.forEach(transaction => {
        let show = true;
        
        // Фильтр по типу
        if (filters.type && !transaction.classList.contains(`transaction-${filters.type}`)) {
            show = false;
        }
        
        // Фильтр по дате (упрощенная реализация)
        // В реальном приложении нужно парсить дату из элемента
        
        if (show) {
            transaction.style.display = 'flex';
            transaction.classList.add('animate-fade-in');
        } else {
            transaction.style.display = 'none';
            transaction.classList.remove('animate-fade-in');
        }
    });
}

// Экспорт данных
function initExportFunctionality() {
    const exportButton = document.getElementById('export-history');
    
    if (exportButton) {
        exportButton.addEventListener('click', function() {
            exportTransactionHistory();
        });
    }
}

// Экспорт истории транзакций
function exportTransactionHistory() {
    // Сбор данных транзакций
    const transactions = [];
    const transactionElements = document.querySelectorAll('.transaction-item');
    
    transactionElements.forEach(element => {
        if (element.style.display !== 'none') {
            const title = element.querySelector('.transaction-title').textContent;
            const description = element.querySelector('.transaction-description').textContent;
            const date = element.querySelector('.transaction-date').textContent;
            const amount = element.querySelector('.amount').textContent;
            
            transactions.push({
                title,
                description,
                date,
                amount
            });
        }
    });
    
    // Создание CSV
    const csvContent = createCSV(transactions);
    
    // Скачивание файла
    downloadCSV(csvContent, 'portfolio-transactions.csv');
}

// Создание CSV контента
function createCSV(data) {
    const headers = ['Тип', 'Описание', 'Дата', 'Сумма'];
    const csvRows = [headers.join(',')];
    
    data.forEach(row => {
        const values = [
            `"${row.title}"`,
            `"${row.description}"`,
            `"${row.date}"`,
            `"${row.amount}"`
        ];
        csvRows.push(values.join(','));
    });
    
    return csvRows.join('\n');
}

// Скачивание CSV файла
function downloadCSV(content, filename) {
    const blob = new Blob([content], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    
    if (link.download !== undefined) {
        const url = URL.createObjectURL(blob);
        link.setAttribute('href', url);
        link.setAttribute('download', filename);
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    }
}

// Анимации портфеля
function initPortfolioAnimations() {
    // Анимация карточек обзора
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };
    
    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('animate-slide-up');
                
                // Анимация чисел в карточках
                if (entry.target.classList.contains('overview-card')) {
                    animateCardValue(entry.target);
                }
            }
        });
    }, observerOptions);
    
    // Наблюдение за элементами
    document.querySelectorAll('.overview-card, .chart-container, .risk-container').forEach(element => {
        observer.observe(element);
    });
    
    // Анимация прогресс-баров рисков
    setTimeout(() => {
        document.querySelectorAll('.risk-bar-progress').forEach(bar => {
            const width = bar.style.width;
            bar.style.width = '0%';
            setTimeout(() => {
                bar.style.width = width;
            }, 500);
        });
    }, 1000);
}

// Анимация значений в карточках
function animateCardValue(card) {
    const valueElement = card.querySelector('.card-value');
    if (!valueElement) return;
    
    const finalValue = valueElement.textContent;
    const numericValue = parseFloat(finalValue.replace(/[^\d.-]/g, ''));
    
    if (isNaN(numericValue)) return;
    
    let currentValue = 0;
    const increment = numericValue / 50;
    const timer = setInterval(() => {
        currentValue += increment;
        if (currentValue >= numericValue) {
            currentValue = numericValue;
            clearInterval(timer);
        }
        
        // Форматирование в зависимости от типа значения
        if (finalValue.includes('USDT')) {
            valueElement.textContent = formatCurrency(currentValue) + ' USDT';
        } else if (finalValue.includes('%')) {
            valueElement.textContent = currentValue.toFixed(2) + '%';
        } else {
            valueElement.textContent = Math.round(currentValue);
        }
    }, 20);
}

// Вспомогательные функции
function formatCurrency(amount) {
    return new Intl.NumberFormat('ru-RU', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
    }).format(amount);
}

// Обработка изменения размера окна
window.addEventListener('resize', function() {
    if (performanceChart) performanceChart.resize();
    if (distributionChart) distributionChart.resize();
    if (forecastChart) forecastChart.resize();
});

// Обновление данных в реальном времени (заглушка)
function updatePortfolioData() {
    // В реальном приложении здесь будет AJAX запрос для обновления данных
    console.log('Обновление данных портфеля...');
}

// Автоматическое обновление каждые 5 минут
setInterval(updatePortfolioData, 5 * 60 * 1000);
