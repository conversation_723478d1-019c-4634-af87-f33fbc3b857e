/**
 * AstroGenix - JavaScript для рейтингов и лидербордов
 * Интерактивность и обновление данных
 */

document.addEventListener('DOMContentLoaded', function() {
    initializeLeaderboards();
});

function initializeLeaderboards() {
    // Инициализация переключения категорий
    initCategoryTabs();
    
    // Инициализация контролов лидерборда
    initLeaderboardControls();
    
    // Инициализация анимаций
    initLeaderboardAnimations();
    
    // Автоматическое обновление
    initAutoRefresh();
}

// Переключение категорий рейтингов
function initCategoryTabs() {
    const categoryTabs = document.querySelectorAll('.category-tab');
    
    categoryTabs.forEach(tab => {
        tab.addEventListener('click', function() {
            const type = this.dataset.type;
            
            // Обновление активной вкладки
            categoryTabs.forEach(t => t.classList.remove('active'));
            this.classList.add('active');
            
            // Обновление URL и загрузка данных
            updateLeaderboard(type, window.leaderboardData.currentLimit);
        });
    });
}

// Контролы лидерборда
function initLeaderboardControls() {
    const limitSelect = document.getElementById('limit-select');
    const refreshButton = document.getElementById('refresh-leaderboard');
    
    // Изменение лимита
    if (limitSelect) {
        limitSelect.addEventListener('change', function() {
            const limit = parseInt(this.value);
            updateLeaderboard(window.leaderboardData.currentType, limit);
        });
    }
    
    // Кнопка обновления
    if (refreshButton) {
        refreshButton.addEventListener('click', function() {
            this.disabled = true;
            this.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Обновление...';
            
            updateLeaderboard(
                window.leaderboardData.currentType, 
                window.leaderboardData.currentLimit,
                true
            );
        });
    }
}

// Обновление лидерборда
function updateLeaderboard(type, limit, forceRefresh = false) {
    const leaderboardBody = document.getElementById('leaderboard-body');
    const refreshButton = document.getElementById('refresh-leaderboard');
    
    // Показ индикатора загрузки
    if (leaderboardBody) {
        leaderboardBody.classList.add('loading');
    }
    
    // Обновление URL
    const newUrl = `leaderboards.php?type=${type}&limit=${limit}`;
    window.history.pushState({type, limit}, '', newUrl);
    
    // Обновление глобальных данных
    window.leaderboardData.currentType = type;
    window.leaderboardData.currentLimit = limit;
    
    // AJAX запрос для получения данных
    fetch(`api/leaderboard.php?type=${type}&limit=${limit}&refresh=${forceRefresh ? 1 : 0}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                updateLeaderboardDisplay(data.leaderboard, data.typeData);
            } else {
                showNotification('Ошибка загрузки рейтинга: ' + data.error, 'error');
            }
        })
        .catch(error => {
            console.error('Leaderboard update error:', error);
            showNotification('Ошибка соединения с сервером', 'error');
        })
        .finally(() => {
            // Скрытие индикатора загрузки
            if (leaderboardBody) {
                leaderboardBody.classList.remove('loading');
            }
            
            // Восстановление кнопки обновления
            if (refreshButton) {
                refreshButton.disabled = false;
                refreshButton.innerHTML = '<i class="fas fa-sync-alt"></i> Обновить';
            }
        });
}

// Обновление отображения лидерборда
function updateLeaderboardDisplay(leaderboard, typeData) {
    const leaderboardBody = document.getElementById('leaderboard-body');
    const leaderboardTitle = document.querySelector('.leaderboard-title');
    
    // Обновление заголовка
    if (leaderboardTitle && typeData) {
        leaderboardTitle.innerHTML = `
            <i class="${typeData.display_settings.icon}" style="color: ${typeData.display_settings.color}"></i>
            ${typeData.display_settings.title}
        `;
    }
    
    // Обновление содержимого
    if (leaderboardBody) {
        leaderboardBody.innerHTML = '';
        
        leaderboard.forEach((entry, index) => {
            const row = createLeaderboardRow(entry, index);
            leaderboardBody.appendChild(row);
        });
        
        // Запуск анимаций
        setTimeout(() => {
            initLeaderboardAnimations();
        }, 100);
    }
}

// Создание строки лидерборда
function createLeaderboardRow(entry, index) {
    const row = document.createElement('div');
    row.className = `leaderboard-row ${entry.is_current_user ? 'current-user' : ''} animate-on-scroll`;
    row.style.animationDelay = `${index * 0.05}s`;
    
    // Ранг
    let rankContent = '';
    if (entry.current_rank <= 3) {
        const medals = ['', 'gold', 'silver', 'bronze'];
        rankContent = `
            <div class="medal medal-${medals[entry.current_rank]}">
                <i class="fas fa-medal"></i>
                <span class="medal-number">${entry.current_rank}</span>
            </div>
        `;
    } else {
        rankContent = `<span class="rank-number">${entry.current_rank}</span>`;
    }
    
    // Аватар
    let avatarContent = '';
    if (entry.avatar) {
        avatarContent = `<img src="${entry.avatar}" alt="Avatar">`;
    } else {
        avatarContent = `
            <div class="avatar-placeholder">
                <i class="fas fa-user"></i>
            </div>
        `;
    }
    
    // Изменение ранга
    let changeContent = '';
    if (entry.rank_change > 0) {
        changeContent = `
            <span class="change up">
                <i class="fas fa-arrow-up"></i>
                +${entry.rank_change}
            </span>
        `;
    } else if (entry.rank_change < 0) {
        changeContent = `
            <span class="change down">
                <i class="fas fa-arrow-down"></i>
                ${entry.rank_change}
            </span>
        `;
    } else {
        changeContent = `
            <span class="change same">
                <i class="fas fa-minus"></i>
                0
            </span>
        `;
    }
    
    // Время на платформе
    const userSince = new Date(entry.user_since);
    const now = new Date();
    const daysDiff = Math.floor((now - userSince) / (1000 * 60 * 60 * 24));
    
    let sinceText = '';
    if (daysDiff < 30) {
        sinceText = `${daysDiff} дн.`;
    } else if (daysDiff < 365) {
        sinceText = `${Math.round(daysDiff / 30)} мес.`;
    } else {
        sinceText = `${(daysDiff / 365).toFixed(1)} г.`;
    }
    
    row.innerHTML = `
        <div class="row-rank">${rankContent}</div>
        <div class="row-user">
            <div class="user-avatar">${avatarContent}</div>
            <div class="user-details">
                <div class="username">
                    ${entry.username}
                    ${entry.is_current_user ? '<span class="you-badge">Вы</span>' : ''}
                </div>
                <div class="user-since">
                    С ${userSince.toLocaleDateString('ru-RU', {month: 'short', year: 'numeric'})}
                </div>
            </div>
        </div>
        <div class="row-score">
            <span class="score-value">${formatNumber(entry.current_score)}</span>
            <span class="score-label">очков</span>
        </div>
        <div class="row-change">${changeContent}</div>
        <div class="row-since">${sinceText}</div>
    `;
    
    return row;
}

// Анимации лидерборда
function initLeaderboardAnimations() {
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };
    
    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('animate-slide-up');
                
                // Анимация счетчиков очков
                if (entry.target.classList.contains('ranking-card')) {
                    animateRankingCard(entry.target);
                }
            }
        });
    }, observerOptions);
    
    // Наблюдение за элементами
    document.querySelectorAll('.ranking-card, .leaderboard-row, .top-user-item, .info-card').forEach(element => {
        observer.observe(element);
    });
}

// Анимация карточки рейтинга
function animateRankingCard(card) {
    const scoreElement = card.querySelector('.ranking-score');
    if (!scoreElement) return;
    
    const finalScore = parseFloat(scoreElement.textContent.replace(/[^\d.-]/g, ''));
    if (isNaN(finalScore)) return;
    
    let currentScore = 0;
    const increment = finalScore / 50;
    const timer = setInterval(() => {
        currentScore += increment;
        if (currentScore >= finalScore) {
            currentScore = finalScore;
            clearInterval(timer);
        }
        
        scoreElement.textContent = formatNumber(currentScore) + ' очков';
    }, 20);
}

// Автоматическое обновление
function initAutoRefresh() {
    // Обновление каждые 5 минут
    setInterval(() => {
        updateLeaderboard(
            window.leaderboardData.currentType, 
            window.leaderboardData.currentLimit,
            true
        );
    }, 5 * 60 * 1000);
}

// Поиск пользователя в рейтинге
function searchUser() {
    const searchInput = document.getElementById('user-search');
    if (!searchInput) return;
    
    const searchTerm = searchInput.value.toLowerCase().trim();
    const rows = document.querySelectorAll('.leaderboard-row');
    
    rows.forEach(row => {
        const username = row.querySelector('.username').textContent.toLowerCase();
        
        if (searchTerm === '' || username.includes(searchTerm)) {
            row.style.display = 'flex';
            row.classList.add('animate-fade-in');
        } else {
            row.style.display = 'none';
            row.classList.remove('animate-fade-in');
        }
    });
}

// Экспорт рейтинга
function exportLeaderboard() {
    const type = window.leaderboardData.currentType;
    const limit = window.leaderboardData.currentLimit;
    
    // Сбор данных
    const rows = document.querySelectorAll('.leaderboard-row');
    const data = [];
    
    rows.forEach(row => {
        if (row.style.display !== 'none') {
            const rank = row.querySelector('.rank-number, .medal-number').textContent;
            const username = row.querySelector('.username').textContent.replace('Вы', '').trim();
            const score = row.querySelector('.score-value').textContent;
            const change = row.querySelector('.change').textContent.trim();
            
            data.push({
                rank,
                username,
                score,
                change
            });
        }
    });
    
    // Создание CSV
    const csvContent = createCSV(data, ['Ранг', 'Пользователь', 'Очки', 'Изменение']);
    
    // Скачивание
    downloadCSV(csvContent, `leaderboard-${type}-${new Date().toISOString().split('T')[0]}.csv`);
}

// Создание CSV
function createCSV(data, headers) {
    const csvRows = [headers.join(',')];
    
    data.forEach(row => {
        const values = headers.map(header => {
            const key = header.toLowerCase().replace('ё', 'е');
            const keyMap = {
                'ранг': 'rank',
                'пользователь': 'username', 
                'очки': 'score',
                'изменение': 'change'
            };
            return `"${row[keyMap[key]] || ''}"`;
        });
        csvRows.push(values.join(','));
    });
    
    return csvRows.join('\n');
}

// Скачивание CSV
function downloadCSV(content, filename) {
    const blob = new Blob([content], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    
    if (link.download !== undefined) {
        const url = URL.createObjectURL(blob);
        link.setAttribute('href', url);
        link.setAttribute('download', filename);
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    }
}

// Вспомогательные функции
function formatNumber(num) {
    return new Intl.NumberFormat('ru-RU', {
        minimumFractionDigits: 0,
        maximumFractionDigits: 2
    }).format(num);
}

function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
        <div class="notification-content">
            <span class="notification-message">${message}</span>
            <button class="notification-close">&times;</button>
        </div>
    `;
    
    document.body.appendChild(notification);
    
    setTimeout(() => {
        notification.classList.add('show');
    }, 100);
    
    setTimeout(() => {
        hideNotification(notification);
    }, 5000);
    
    notification.querySelector('.notification-close').addEventListener('click', () => {
        hideNotification(notification);
    });
}

function hideNotification(notification) {
    notification.classList.remove('show');
    setTimeout(() => {
        if (notification.parentNode) {
            notification.parentNode.removeChild(notification);
        }
    }, 300);
}

// Обработка истории браузера
window.addEventListener('popstate', function(event) {
    if (event.state) {
        window.leaderboardData.currentType = event.state.type;
        window.leaderboardData.currentLimit = event.state.limit;
        
        // Обновление активной вкладки
        document.querySelectorAll('.category-tab').forEach(tab => {
            tab.classList.toggle('active', tab.dataset.type === event.state.type);
        });
        
        // Обновление лимита
        const limitSelect = document.getElementById('limit-select');
        if (limitSelect) {
            limitSelect.value = event.state.limit;
        }
        
        // Загрузка данных
        updateLeaderboard(event.state.type, event.state.limit);
    }
});
