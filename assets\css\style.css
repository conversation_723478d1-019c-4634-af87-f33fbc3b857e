/**
 * AstroGenix - Основные стили
 * Эко-майнинговая инвестиционная платформа
 */

/* Сброс стилей и базовые настройки */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* Основная цветовая палитра */
    --primary-green: #2ECC71;
    --primary-purple: #8E44AD;
    --dark-green: #27AE60;
    --dark-purple: #7D3C98;
    --light-green: #58D68D;
    --light-purple: #BB8FCE;
    
    /* Градиенты */
    --gradient-primary: linear-gradient(135deg, var(--primary-green) 0%, var(--primary-purple) 100%);
    --gradient-secondary: linear-gradient(135deg, var(--light-green) 0%, var(--light-purple) 100%);
    --gradient-dark: linear-gradient(135deg, var(--dark-green) 0%, var(--dark-purple) 100%);
    
    /* Нейтральные цвета */
    --white: #FFFFFF;
    --black: #1A1A1A;
    --gray-100: #F8F9FA;
    --gray-200: #E9ECEF;
    --gray-300: #DEE2E6;
    --gray-400: #CED4DA;
    --gray-500: #ADB5BD;
    --gray-600: #6C757D;
    --gray-700: #495057;
    --gray-800: #343A40;
    --gray-900: #212529;
    
    /* Статусные цвета */
    --success: #28A745;
    --warning: #FFC107;
    --error: #DC3545;
    --info: #17A2B8;
    
    /* Тени */
    --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.1);
    --shadow-md: 0 4px 8px rgba(0, 0, 0, 0.15);
    --shadow-lg: 0 8px 16px rgba(0, 0, 0, 0.2);
    --shadow-xl: 0 16px 32px rgba(0, 0, 0, 0.25);
    
    /* Радиусы */
    --radius-sm: 4px;
    --radius-md: 8px;
    --radius-lg: 12px;
    --radius-xl: 16px;
    --radius-full: 50%;
    
    /* Переходы */
    --transition-fast: 0.2s ease;
    --transition-normal: 0.3s ease;
    --transition-slow: 0.5s ease;
    
    /* Шрифты */
    --font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    --font-secondary: 'Poppins', sans-serif;
    
    /* Размеры шрифтов */
    --text-xs: 0.75rem;
    --text-sm: 0.875rem;
    --text-base: 1rem;
    --text-lg: 1.125rem;
    --text-xl: 1.25rem;
    --text-2xl: 1.5rem;
    --text-3xl: 1.875rem;
    --text-4xl: 2.25rem;
    --text-5xl: 3rem;
    
    /* Отступы */
    --space-1: 0.25rem;
    --space-2: 0.5rem;
    --space-3: 0.75rem;
    --space-4: 1rem;
    --space-5: 1.25rem;
    --space-6: 1.5rem;
    --space-8: 2rem;
    --space-10: 2.5rem;
    --space-12: 3rem;
    --space-16: 4rem;
    --space-20: 5rem;
    
    /* Контейнеры */
    --container-sm: 640px;
    --container-md: 768px;
    --container-lg: 1024px;
    --container-xl: 1280px;
    --container-2xl: 1536px;
}

/* Базовые стили */
html {
    scroll-behavior: smooth;
}

body {
    font-family: var(--font-primary);
    font-size: var(--text-base);
    line-height: 1.6;
    color: var(--gray-800);
    background-color: var(--white);
    overflow-x: hidden;
}

/* Контейнеры */
.container {
    width: 100%;
    max-width: var(--container-xl);
    margin: 0 auto;
    padding: 0 var(--space-4);
}

@media (min-width: 640px) {
    .container {
        padding: 0 var(--space-6);
    }
}

@media (min-width: 1024px) {
    .container {
        padding: 0 var(--space-8);
    }
}

/* Типография */
h1, h2, h3, h4, h5, h6 {
    font-family: var(--font-secondary);
    font-weight: 600;
    line-height: 1.2;
    margin-bottom: var(--space-4);
    color: var(--gray-900);
}

h1 { font-size: var(--text-4xl); }
h2 { font-size: var(--text-3xl); }
h3 { font-size: var(--text-2xl); }
h4 { font-size: var(--text-xl); }
h5 { font-size: var(--text-lg); }
h6 { font-size: var(--text-base); }

p {
    margin-bottom: var(--space-4);
}

a {
    color: var(--primary-green);
    text-decoration: none;
    transition: color var(--transition-fast);
}

a:hover {
    color: var(--dark-green);
}

/* Кнопки */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--space-2);
    padding: var(--space-3) var(--space-6);
    font-family: var(--font-primary);
    font-size: var(--text-sm);
    font-weight: 500;
    text-decoration: none;
    border: none;
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: all var(--transition-fast);
    white-space: nowrap;
    user-select: none;
}

.btn:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(46, 204, 113, 0.3);
}

.btn-primary {
    background: var(--gradient-primary);
    color: var(--white);
    box-shadow: var(--shadow-sm);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
    color: var(--white);
}

.btn-outline {
    background: transparent;
    color: var(--primary-green);
    border: 2px solid var(--primary-green);
}

.btn-outline:hover {
    background: var(--primary-green);
    color: var(--white);
}

.btn-secondary {
    background: var(--gray-200);
    color: var(--gray-700);
}

.btn-secondary:hover {
    background: var(--gray-300);
    color: var(--gray-800);
}

.btn-success {
    background: var(--success);
    color: var(--white);
}

.btn-warning {
    background: var(--warning);
    color: var(--gray-900);
}

.btn-error {
    background: var(--error);
    color: var(--white);
}

.btn-large {
    padding: var(--space-4) var(--space-8);
    font-size: var(--text-base);
}

.btn-small {
    padding: var(--space-2) var(--space-4);
    font-size: var(--text-xs);
}

.btn-full {
    width: 100%;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
}

/* Формы */
.form-group {
    margin-bottom: var(--space-5);
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--space-4);
}

@media (max-width: 768px) {
    .form-row {
        grid-template-columns: 1fr;
    }
}

label {
    display: block;
    font-weight: 500;
    margin-bottom: var(--space-2);
    color: var(--gray-700);
}

input, textarea, select {
    width: 100%;
    padding: var(--space-3);
    font-family: var(--font-primary);
    font-size: var(--text-sm);
    border: 2px solid var(--gray-300);
    border-radius: var(--radius-md);
    background-color: var(--white);
    transition: border-color var(--transition-fast);
}

input:focus, textarea:focus, select:focus {
    outline: none;
    border-color: var(--primary-green);
    box-shadow: 0 0 0 3px rgba(46, 204, 113, 0.1);
}

input.error, textarea.error, select.error {
    border-color: var(--error);
}

.input-error {
    color: var(--error);
    font-size: var(--text-sm);
    margin-top: var(--space-1);
    display: flex;
    align-items: center;
    gap: var(--space-1);
}

.input-error::before {
    content: '⚠';
    font-size: var(--text-xs);
}

/* Алерты */
.alert {
    padding: var(--space-4);
    border-radius: var(--radius-md);
    margin-bottom: var(--space-5);
    display: flex;
    align-items: flex-start;
    gap: var(--space-3);
}

.alert i {
    margin-top: var(--space-1);
}

.alert ul {
    margin: 0;
    padding-left: var(--space-4);
}

.alert-success {
    background-color: rgba(40, 167, 69, 0.1);
    color: var(--success);
    border: 1px solid rgba(40, 167, 69, 0.3);
}

.alert-error {
    background-color: rgba(220, 53, 69, 0.1);
    color: var(--error);
    border: 1px solid rgba(220, 53, 69, 0.3);
}

.alert-warning {
    background-color: rgba(255, 193, 7, 0.1);
    color: #856404;
    border: 1px solid rgba(255, 193, 7, 0.3);
}

.alert-info {
    background-color: rgba(23, 162, 184, 0.1);
    color: var(--info);
    border: 1px solid rgba(23, 162, 184, 0.3);
}

/* Карточки */
.card {
    background: var(--white);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-sm);
    overflow: hidden;
    transition: box-shadow var(--transition-fast);
}

.card:hover {
    box-shadow: var(--shadow-md);
}

.card-header {
    padding: var(--space-6);
    border-bottom: 1px solid var(--gray-200);
}

.card-body {
    padding: var(--space-6);
}

.card-footer {
    padding: var(--space-6);
    border-top: 1px solid var(--gray-200);
    background-color: var(--gray-100);
}

/* Утилиты */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.text-gradient {
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.d-none { display: none; }
.d-block { display: block; }
.d-flex { display: flex; }
.d-grid { display: grid; }

.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.align-center { align-items: center; }

.w-full { width: 100%; }
.h-full { height: 100%; }

.mt-auto { margin-top: auto; }
.mb-auto { margin-bottom: auto; }

/* Прелоадер */
.preloader {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--gradient-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    transition: opacity 0.5s ease;
}

.preloader-content {
    text-align: center;
    color: var(--white);
}

.logo-animation {
    margin-bottom: var(--space-6);
    animation: logoSpin 2s ease-in-out infinite;
}

.logo-animation img {
    width: 80px;
    height: 80px;
    filter: brightness(0) invert(1);
}

@keyframes logoSpin {
    0%, 100% { transform: scale(1) rotate(0deg); }
    50% { transform: scale(1.1) rotate(180deg); }
}

.loading-text {
    font-size: var(--text-lg);
    font-weight: 500;
    margin-bottom: var(--space-4);
}

.loading-bar {
    width: 200px;
    height: 4px;
    background: rgba(255, 255, 255, 0.3);
    border-radius: var(--radius-full);
    overflow: hidden;
    margin: 0 auto;
}

.loading-progress {
    height: 100%;
    background: var(--white);
    border-radius: var(--radius-full);
    width: 0%;
    transition: width 0.3s ease;
}

/* Стили для лендинговой страницы */
.landing-page {
    background: var(--white);
}

/* Header */
.header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    z-index: 1000;
    transition: all var(--transition-fast);
}

.navbar {
    padding: var(--space-4) 0;
}

.navbar .container {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.nav-brand {
    display: flex;
    align-items: center;
    gap: var(--space-3);
    text-decoration: none;
    color: var(--gray-900);
}

.nav-brand .logo {
    width: 40px;
    height: 40px;
}

.brand-text {
    font-size: var(--text-xl);
    font-weight: 700;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.nav-menu {
    display: flex;
    align-items: center;
    gap: var(--space-6);
}

.nav-link {
    color: var(--gray-700);
    font-weight: 500;
    text-decoration: none;
    transition: color var(--transition-fast);
}

.nav-link:hover {
    color: var(--primary-green);
}

.mobile-menu-toggle {
    display: none;
    font-size: var(--text-xl);
    color: var(--gray-700);
    cursor: pointer;
}

/* Hero Section */
.hero {
    min-height: 100vh;
    display: flex;
    align-items: center;
    position: relative;
    background: linear-gradient(135deg,
        rgba(46, 204, 113, 0.05) 0%,
        rgba(142, 68, 173, 0.05) 100%);
    overflow: hidden;
}

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        radial-gradient(circle at 20% 80%, rgba(46, 204, 113, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(142, 68, 173, 0.1) 0%, transparent 50%);
}

.hero-content {
    text-align: center;
    max-width: 800px;
    margin: 0 auto;
    padding-top: 100px;
}

.hero-title {
    font-size: var(--text-5xl);
    font-weight: 700;
    margin-bottom: var(--space-6);
    line-height: 1.1;
}

.hero-subtitle {
    font-size: var(--text-xl);
    color: var(--gray-600);
    margin-bottom: var(--space-8);
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.hero-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--space-6);
    margin-bottom: var(--space-8);
}

.stat-item {
    text-align: center;
}

.stat-number {
    font-size: var(--text-3xl);
    font-weight: 700;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: var(--space-2);
}

.stat-label {
    color: var(--gray-600);
    font-size: var(--text-sm);
}

.hero-actions {
    display: flex;
    gap: var(--space-4);
    justify-content: center;
    flex-wrap: wrap;
}

/* Green Energy Section */
.green-energy-section {
    padding: var(--space-16) 0;
    background: var(--gray-100);
}

.energy-counter {
    display: grid;
    grid-template-columns: auto 1fr auto;
    gap: var(--space-6);
    align-items: center;
    background: var(--white);
    padding: var(--space-8);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-md);
}

.energy-icon {
    width: 80px;
    height: 80px;
    background: var(--gradient-primary);
    border-radius: var(--radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--text-3xl);
    color: var(--white);
}

.energy-content h3 {
    margin-bottom: var(--space-3);
    color: var(--gray-900);
}

.energy-display {
    display: flex;
    align-items: baseline;
    gap: var(--space-2);
    margin-bottom: var(--space-3);
}

.energy-value {
    font-size: var(--text-4xl);
    font-weight: 700;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.energy-unit {
    font-size: var(--text-lg);
    color: var(--gray-600);
}

.energy-content p {
    color: var(--gray-600);
    margin: 0;
}

/* Адаптивность */
@media (max-width: 768px) {
    :root {
        --text-4xl: 2rem;
        --text-3xl: 1.5rem;
        --text-2xl: 1.25rem;
        --text-5xl: 2.5rem;
    }

    .container {
        padding: 0 var(--space-4);
    }

    .btn-large {
        padding: var(--space-3) var(--space-6);
        font-size: var(--text-sm);
    }

    .nav-menu {
        display: none;
    }

    .mobile-menu-toggle {
        display: block;
    }

    .hero-title {
        font-size: var(--text-4xl);
    }

    .hero-subtitle {
        font-size: var(--text-lg);
    }

    .hero-actions {
        flex-direction: column;
        align-items: center;
    }

    .hero-actions .btn {
        width: 100%;
        max-width: 300px;
    }

    .energy-counter {
        grid-template-columns: 1fr;
        text-align: center;
        gap: var(--space-4);
    }
}

/* Features Section */
.features-section {
    padding: var(--space-20) 0;
    background: var(--white);
}

.section-header {
    text-align: center;
    margin-bottom: var(--space-16);
}

.section-title {
    font-size: var(--text-4xl);
    font-weight: 700;
    margin-bottom: var(--space-4);
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.section-subtitle {
    font-size: var(--text-lg);
    color: var(--gray-600);
    max-width: 600px;
    margin: 0 auto;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--space-8);
}

.feature-card {
    background: var(--white);
    padding: var(--space-8);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-sm);
    text-align: center;
    transition: all var(--transition-normal);
    border: 1px solid var(--gray-200);
}

.feature-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
    border-color: var(--primary-green);
}

.feature-icon {
    width: 80px;
    height: 80px;
    background: var(--gradient-primary);
    border-radius: var(--radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto var(--space-6);
    font-size: var(--text-2xl);
    color: var(--white);
}

.feature-card h3 {
    font-size: var(--text-xl);
    margin-bottom: var(--space-4);
    color: var(--gray-900);
}

.feature-card p {
    color: var(--gray-600);
    line-height: 1.6;
    margin: 0;
}

/* Packages Section */
.packages-section {
    padding: var(--space-20) 0;
    background: var(--gray-100);
}

.packages-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: var(--space-8);
}

.package-card {
    background: var(--white);
    border-radius: var(--radius-xl);
    overflow: hidden;
    box-shadow: var(--shadow-md);
    transition: all var(--transition-normal);
    position: relative;
}

.package-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-xl);
}

.package-card.featured {
    border: 3px solid var(--primary-green);
    transform: scale(1.05);
}

.package-card.featured::before {
    content: 'Популярный';
    position: absolute;
    top: 20px;
    right: -30px;
    background: var(--gradient-primary);
    color: var(--white);
    padding: var(--space-2) var(--space-8);
    font-size: var(--text-sm);
    font-weight: 600;
    transform: rotate(45deg);
    z-index: 1;
}

.package-header {
    padding: var(--space-8);
    text-align: center;
    background: var(--gradient-primary);
    color: var(--white);
}

.package-name {
    font-size: var(--text-2xl);
    font-weight: 700;
    margin-bottom: var(--space-2);
}

.package-price {
    font-size: var(--text-4xl);
    font-weight: 700;
    margin-bottom: var(--space-2);
}

.package-period {
    opacity: 0.9;
    font-size: var(--text-sm);
}

.package-body {
    padding: var(--space-8);
}

.package-features {
    list-style: none;
    margin-bottom: var(--space-8);
}

.package-features li {
    display: flex;
    align-items: center;
    gap: var(--space-3);
    margin-bottom: var(--space-3);
    color: var(--gray-700);
}

.package-features li i {
    color: var(--primary-green);
    font-size: var(--text-sm);
}

.package-footer {
    padding: 0 var(--space-8) var(--space-8);
}

/* Motivational Section */
.motivational-section {
    padding: var(--space-16) 0;
    background: var(--white);
}

.motivational-banners {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--space-6);
}

.banner-item {
    display: flex;
    align-items: center;
    gap: var(--space-4);
    background: var(--gray-100);
    padding: var(--space-6);
    border-radius: var(--radius-lg);
    transition: all var(--transition-normal);
}

.banner-item:hover {
    background: var(--white);
    box-shadow: var(--shadow-md);
    transform: translateY(-2px);
}

.banner-icon {
    width: 60px;
    height: 60px;
    background: var(--gradient-primary);
    border-radius: var(--radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--text-xl);
    color: var(--white);
    flex-shrink: 0;
}

.banner-content h4 {
    font-size: var(--text-lg);
    margin-bottom: var(--space-2);
    color: var(--gray-900);
}

.banner-content p {
    color: var(--gray-600);
    margin: 0;
    font-size: var(--text-sm);
    line-height: 1.5;
}

/* Footer */
.footer {
    background: var(--gray-900);
    color: var(--white);
    padding: var(--space-16) 0 var(--space-8);
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--space-8);
    margin-bottom: var(--space-8);
}

.footer-section h4 {
    color: var(--white);
    margin-bottom: var(--space-4);
    font-size: var(--text-lg);
}

.footer-brand {
    display: flex;
    align-items: center;
    gap: var(--space-3);
    margin-bottom: var(--space-4);
}

.footer-logo {
    width: 40px;
    height: 40px;
}

.footer-brand-text {
    font-size: var(--text-xl);
    font-weight: 700;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.footer-description {
    color: var(--gray-400);
    line-height: 1.6;
}

.footer-links {
    list-style: none;
}

.footer-links li {
    margin-bottom: var(--space-2);
}

.footer-links a {
    color: var(--gray-400);
    text-decoration: none;
    transition: color var(--transition-fast);
}

.footer-links a:hover {
    color: var(--primary-green);
}

.contact-info p {
    display: flex;
    align-items: center;
    gap: var(--space-3);
    margin-bottom: var(--space-3);
    color: var(--gray-400);
}

.contact-info i {
    color: var(--primary-green);
    width: 20px;
}

.social-links {
    display: flex;
    gap: var(--space-3);
    margin-top: var(--space-4);
}

.social-links a {
    width: 40px;
    height: 40px;
    background: var(--gray-800);
    border-radius: var(--radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--gray-400);
    transition: all var(--transition-fast);
}

.social-links a:hover {
    background: var(--primary-green);
    color: var(--white);
    transform: translateY(-2px);
}

.footer-bottom {
    padding-top: var(--space-8);
    border-top: 1px solid var(--gray-800);
}

.footer-bottom-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: var(--space-4);
}

.copyright {
    color: var(--gray-500);
}

.footer-badges {
    display: flex;
    gap: var(--space-4);
    flex-wrap: wrap;
}

.footer-badges > div {
    display: flex;
    align-items: center;
    gap: var(--space-2);
    padding: var(--space-2) var(--space-3);
    background: var(--gray-800);
    border-radius: var(--radius-full);
    font-size: var(--text-xs);
    color: var(--gray-400);
    transition: all var(--transition-fast);
}

.footer-badges > div:hover {
    background: var(--gray-700);
    color: var(--gray-300);
}

.security-badge i {
    color: var(--blue-400);
}

.eco-badge i {
    color: var(--green-400);
}

.support-badge i {
    color: var(--purple-400);
}

.verified-badge i {
    color: var(--yellow-400);
}

/* Экологическая статистика в футере */
.eco-stats {
    margin-top: var(--space-4);
}

.eco-stat {
    display: flex;
    align-items: center;
    gap: var(--space-3);
    margin-bottom: var(--space-3);
}

.eco-stat:last-child {
    margin-bottom: 0;
}

.eco-stat i {
    color: var(--green-400);
    font-size: var(--text-lg);
    width: 24px;
    text-align: center;
}

.eco-info {
    display: flex;
    flex-direction: column;
}

.eco-value {
    font-weight: 600;
    color: var(--white);
    font-size: var(--text-sm);
}

.eco-label {
    font-size: var(--text-xs);
    color: var(--gray-400);
}

/* Социальные ссылки в футере */
.footer-social {
    display: flex;
    gap: var(--space-3);
    margin-top: var(--space-4);
}

.footer-social .social-link {
    width: 40px;
    height: 40px;
    background: var(--gray-800);
    border-radius: var(--radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--gray-400);
    text-decoration: none;
    transition: all var(--transition-fast);
}

.footer-social .social-link:hover {
    background: var(--primary-green);
    color: var(--white);
    transform: translateY(-2px);
}

/* Логотип в футере */
.footer-logo {
    display: flex;
    align-items: center;
    gap: var(--space-3);
    margin-bottom: var(--space-4);
}

.footer-logo img {
    width: 40px;
    height: 40px;
}

.footer-logo span {
    font-size: var(--text-xl);
    font-weight: 700;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.footer-description {
    color: var(--gray-400);
    line-height: 1.6;
    margin-bottom: var(--space-4);
}

/* Дополнительная адаптивность */
@media (max-width: 1024px) {
    .features-grid {
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: var(--space-6);
    }

    .packages-grid {
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    }

    .package-card.featured {
        transform: none;
    }
}

@media (max-width: 768px) {
    .features-section,
    .packages-section {
        padding: var(--space-16) 0;
    }

    .section-header {
        margin-bottom: var(--space-12);
    }

    .features-grid {
        grid-template-columns: 1fr;
        gap: var(--space-6);
    }

    .packages-grid {
        grid-template-columns: 1fr;
    }

    .motivational-banners {
        grid-template-columns: 1fr;
    }

    .banner-item {
        flex-direction: column;
        text-align: center;
    }

    .footer-content {
        grid-template-columns: 1fr;
        gap: var(--space-6);
        text-align: center;
    }

    .footer-bottom-content {
        flex-direction: column;
        text-align: center;
        gap: var(--space-6);
    }

    .footer-badges {
        justify-content: center;
    }

    .footer-social {
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .hero-stats {
        grid-template-columns: 1fr;
        gap: var(--space-4);
    }

    .feature-card,
    .package-card {
        padding: var(--space-6);
    }

    .energy-counter {
        padding: var(--space-6);
    }

    .energy-icon {
        width: 60px;
        height: 60px;
        font-size: var(--text-xl);
    }
}

/* Кнопка "Наверх" */
.back-to-top {
    position: fixed;
    bottom: 30px;
    right: 30px;
    width: 50px;
    height: 50px;
    background: var(--gradient-primary);
    color: var(--white);
    border: none;
    border-radius: var(--radius-full);
    cursor: pointer;
    opacity: 0;
    visibility: hidden;
    transition: all var(--transition-normal);
    z-index: 1000;
    box-shadow: var(--shadow-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--text-lg);
}

.back-to-top:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-xl);
}

.back-to-top.visible {
    opacity: 1;
    visibility: visible;
}

/* Уведомления */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background: var(--white);
    color: var(--gray-800);
    padding: var(--space-4) var(--space-5);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-xl);
    z-index: 9999;
    display: flex;
    align-items: center;
    gap: var(--space-3);
    max-width: 400px;
    transform: translateX(100%);
    transition: transform var(--transition-normal);
    border-left: 4px solid var(--primary-green);
}

.notification.visible {
    transform: translateX(0);
}

.notification-success {
    border-left-color: var(--success);
}

.notification-error {
    border-left-color: var(--error);
}

.notification-warning {
    border-left-color: var(--warning);
}

.notification-info {
    border-left-color: var(--info);
}

.notification-close {
    background: none;
    border: none;
    color: var(--gray-500);
    cursor: pointer;
    padding: var(--space-1);
    border-radius: var(--radius-sm);
    transition: color var(--transition-fast);
    margin-left: auto;
}

.notification-close:hover {
    color: var(--gray-700);
}

/* Дополнительные утилиты */
.text-success { color: var(--success); }
.text-error { color: var(--error); }
.text-warning { color: var(--warning); }
.text-info { color: var(--info); }

.bg-success { background-color: var(--success); }
.bg-error { background-color: var(--error); }
.bg-warning { background-color: var(--warning); }
.bg-info { background-color: var(--info); }

.border-success { border-color: var(--success); }
.border-error { border-color: var(--error); }
.border-warning { border-color: var(--warning); }
.border-info { border-color: var(--info); }

/* Дополнительные медиа-запросы для адаптивности */

/* Очень маленькие экраны (320px - 480px) */
@media (max-width: 480px) {
    :root {
        --text-5xl: 2rem;
        --text-4xl: 1.75rem;
        --text-3xl: 1.5rem;
        --text-2xl: 1.25rem;
        --space-16: 2rem;
        --space-20: 3rem;
    }

    .container {
        padding: 0 var(--space-3);
    }

    .hero-content {
        padding-top: 80px;
    }

    .hero-title {
        font-size: var(--text-4xl);
        margin-bottom: var(--space-4);
    }

    .hero-subtitle {
        font-size: var(--text-base);
        margin-bottom: var(--space-6);
    }

    .hero-stats {
        grid-template-columns: 1fr;
        gap: var(--space-4);
        margin-bottom: var(--space-6);
    }

    .stat-number {
        font-size: var(--text-2xl);
    }

    .hero-actions {
        flex-direction: column;
        gap: var(--space-3);
    }

    .btn-large {
        padding: var(--space-3) var(--space-4);
        font-size: var(--text-sm);
        width: 100%;
    }

    .features-section,
    .packages-section,
    .motivational-section {
        padding: var(--space-12) 0;
    }

    .section-title {
        font-size: var(--text-3xl);
    }

    .feature-card,
    .package-card {
        padding: var(--space-6);
    }

    .feature-icon,
    .energy-icon {
        width: 60px;
        height: 60px;
        font-size: var(--text-xl);
    }

    .package-header {
        padding: var(--space-6);
    }

    .package-name {
        font-size: var(--text-xl);
    }

    .package-price {
        font-size: var(--text-3xl);
    }

    .energy-counter {
        grid-template-columns: 1fr;
        text-align: center;
        padding: var(--space-6);
        gap: var(--space-4);
    }

    .energy-value {
        font-size: var(--text-3xl);
    }

    .banner-item {
        flex-direction: column;
        text-align: center;
        padding: var(--space-4);
    }

    .banner-icon {
        width: 50px;
        height: 50px;
        font-size: var(--text-lg);
    }

    .footer-content {
        grid-template-columns: 1fr;
        gap: var(--space-6);
        text-align: center;
    }

    .back-to-top {
        width: 45px;
        height: 45px;
        bottom: 20px;
        right: 20px;
        font-size: var(--text-base);
    }

    .notification {
        left: 10px;
        right: 10px;
        max-width: none;
        margin: 0;
    }
}

/* Планшеты в портретной ориентации (481px - 768px) */
@media (min-width: 481px) and (max-width: 768px) {
    .hero-stats {
        grid-template-columns: repeat(2, 1fr);
    }

    .features-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .packages-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .motivational-banners {
        grid-template-columns: repeat(2, 1fr);
    }

    .footer-content {
        grid-template-columns: repeat(2, 1fr);
    }
}

/* Планшеты в альбомной ориентации (769px - 1024px) */
@media (min-width: 769px) and (max-width: 1024px) {
    .hero-stats {
        grid-template-columns: repeat(3, 1fr);
    }

    .features-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .packages-grid {
        grid-template-columns: repeat(3, 1fr);
    }

    .package-card.featured {
        transform: none;
    }
}

/* Большие экраны (1025px - 1440px) */
@media (min-width: 1025px) and (max-width: 1440px) {
    .container {
        max-width: 1200px;
    }

    .hero-title {
        font-size: 4rem;
    }

    .features-grid {
        grid-template-columns: repeat(3, 1fr);
    }
}

/* Очень большие экраны (1441px+) */
@media (min-width: 1441px) {
    .container {
        max-width: 1400px;
    }

    .hero-title {
        font-size: 4.5rem;
    }

    .hero-subtitle {
        font-size: var(--text-2xl);
    }

    .section-title {
        font-size: 3.5rem;
    }

    .features-grid {
        grid-template-columns: repeat(3, 1fr);
        gap: var(--space-10);
    }

    .packages-grid {
        gap: var(--space-10);
    }
}

/* Оптимизация производительности */
.hero-background,
.auth-background,
.eco-particles {
    will-change: transform;
    transform: translateZ(0);
}

.feature-card,
.package-card,
.banner-item {
    will-change: transform;
}

/* Улучшения для печати */
@media print {
    .header,
    .sidebar,
    .footer,
    .back-to-top,
    .notification,
    .preloader {
        display: none !important;
    }

    .hero {
        min-height: auto;
        padding: var(--space-8) 0;
    }

    .hero-background,
    .eco-particles {
        display: none;
    }

    body {
        background: white !important;
        color: black !important;
    }

    .text-gradient {
        -webkit-text-fill-color: initial;
        background: none;
        color: var(--gray-900);
    }
}

/* Высокая контрастность для доступности */
@media (prefers-contrast: high) {
    :root {
        --primary-green: #1B5E20;
        --primary-purple: #4A148C;
        --gray-600: #424242;
        --gray-700: #212121;
    }

    .btn-outline {
        border-width: 3px;
    }

    .feature-card,
    .package-card {
        border: 2px solid var(--gray-300);
    }
}

/* Уменьшенная прозрачность для лучшей читаемости */
@media (prefers-reduced-transparency) {
    .auth-content,
    .header {
        background: var(--white);
        backdrop-filter: none;
    }

    .auth-background,
    .hero-background {
        opacity: 0.3;
    }
}
