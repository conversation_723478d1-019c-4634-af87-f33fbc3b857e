<?php
/**
 * AstroGenix - Редактирование страницы CMS
 * Система управления контентом
 */

require_once '../config/config.php';
require_once '../classes/CMS.php';

// Проверка авторизации и прав администратора
require_admin();

// Инициализация переменных
$cms = new CMS(new Database());
$error_message = '';
$success_message = '';
$page_data = null;
$is_edit = false;

// Получение ID страницы для редактирования
$page_id = intval($_GET['id'] ?? 0);

try {
    $database = new Database();
    $db = $database->getConnection();
    
    // Если редактируем существующую страницу
    if ($page_id > 0) {
        $page_query = "SELECT * FROM cms_pages WHERE id = :id";
        $page_stmt = $db->prepare($page_query);
        $page_stmt->bindParam(':id', $page_id, PDO::PARAM_INT);
        $page_stmt->execute();
        
        $page_data = $page_stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$page_data) {
            header('Location: cms-pages.php');
            exit;
        }
        
        $is_edit = true;
    }
    
    // Обработка сохранения
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        if (!verify_csrf_token($_POST['csrf_token'] ?? '')) {
            $error_message = 'Ошибка безопасности. Попробуйте еще раз.';
        } else {
            // Получение данных формы
            $form_data = [
                'title' => sanitize_input($_POST['title'] ?? ''),
                'slug' => sanitize_input($_POST['slug'] ?? ''),
                'content' => $_POST['content'] ?? '', // Не санитизируем контент для сохранения HTML
                'excerpt' => sanitize_input($_POST['excerpt'] ?? ''),
                'meta_title' => sanitize_input($_POST['meta_title'] ?? ''),
                'meta_description' => sanitize_input($_POST['meta_description'] ?? ''),
                'meta_keywords' => sanitize_input($_POST['meta_keywords'] ?? ''),
                'template' => sanitize_input($_POST['template'] ?? 'default'),
                'status' => sanitize_input($_POST['status'] ?? 'draft'),
                'featured_image' => sanitize_input($_POST['featured_image'] ?? ''),
                'parent_id' => intval($_POST['parent_id'] ?? 0) ?: null,
                'sort_order' => intval($_POST['sort_order'] ?? 0),
                'show_in_menu' => isset($_POST['show_in_menu']) ? 1 : 0,
                'custom_css' => $_POST['custom_css'] ?? '',
                'custom_js' => $_POST['custom_js'] ?? ''
            ];
            
            // Валидация
            if (empty($form_data['title'])) {
                $error_message = 'Название страницы обязательно для заполнения.';
            } elseif (empty($form_data['slug'])) {
                // Генерируем slug из заголовка
                $form_data['slug'] = $cms->generateSlug($form_data['title'], $page_id);
            } else {
                // Проверяем уникальность slug
                $slug_check_query = "SELECT id FROM cms_pages WHERE slug = :slug" . ($page_id ? " AND id != :page_id" : "");
                $slug_check_stmt = $db->prepare($slug_check_query);
                $slug_check_stmt->bindParam(':slug', $form_data['slug']);
                if ($page_id) {
                    $slug_check_stmt->bindParam(':page_id', $page_id, PDO::PARAM_INT);
                }
                $slug_check_stmt->execute();
                
                if ($slug_check_stmt->rowCount() > 0) {
                    $error_message = 'Страница с таким URL уже существует.';
                }
            }
            
            // Если нет ошибок, сохраняем
            if (empty($error_message)) {
                $saved_id = $cms->savePage($form_data, $page_id);
                
                if ($saved_id) {
                    $success_message = $is_edit ? 'Страница успешно обновлена.' : 'Страница успешно создана.';
                    
                    // Если создавали новую страницу, перенаправляем на редактирование
                    if (!$is_edit) {
                        header("Location: cms-page-edit.php?id=$saved_id&success=1");
                        exit;
                    }
                    
                    // Обновляем данные страницы
                    $page_stmt->execute();
                    $page_data = $page_stmt->fetch(PDO::FETCH_ASSOC);
                } else {
                    $error_message = 'Ошибка при сохранении страницы.';
                }
            }
        }
    }
    
    // Получение доступных шаблонов
    $templates_query = "SELECT * FROM cms_templates WHERE template_type = 'page' AND status = 'active' ORDER BY name";
    $templates_stmt = $db->prepare($templates_query);
    $templates_stmt->execute();
    $templates = $templates_stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Получение родительских страниц
    $parents_query = "SELECT id, title FROM cms_pages WHERE status != 'archived'" . ($page_id ? " AND id != :page_id" : "") . " ORDER BY title";
    $parents_stmt = $db->prepare($parents_query);
    if ($page_id) {
        $parents_stmt->bindParam(':page_id', $page_id, PDO::PARAM_INT);
    }
    $parents_stmt->execute();
    $parent_pages = $parents_stmt->fetchAll(PDO::FETCH_ASSOC);
    
} catch (Exception $e) {
    error_log("CMS page edit error: " . $e->getMessage());
    $error_message = "Ошибка загрузки страницы. Попробуйте обновить страницу.";
}

// Проверка на success параметр
if (isset($_GET['success']) && $_GET['success'] == '1') {
    $success_message = 'Страница успешно создана.';
}

$page_title = $is_edit ? 'Редактирование страницы - CMS' : 'Создание страницы - CMS';
?>
<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?></title>
    
    <!-- CSS -->
    <link rel="stylesheet" href="../assets/css/style.css">
    <link rel="stylesheet" href="../assets/css/dashboard.css">
    <link rel="stylesheet" href="../assets/css/admin.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <!-- TinyMCE Editor -->
    <script src="https://cdn.tiny.cloud/1/no-api-key/tinymce/6/tinymce.min.js" referrerpolicy="origin"></script>
    <script src="../assets/js/cms-editor.js"></script>
</head>
<body class="dashboard-page admin-page">
    <!-- Admin Sidebar -->
    <?php include 'includes/admin-sidebar.php'; ?>

    <!-- Main Content -->
    <main class="main-content">
        <!-- Header -->
        <header class="dashboard-header">
            <div class="header-left">
                <button class="mobile-menu-toggle" id="mobileMenuToggle">
                    <i class="fas fa-bars"></i>
                </button>
                <h1><?php echo $is_edit ? 'Редактирование страницы' : 'Создание страницы'; ?></h1>
            </div>
            <div class="header-right">
                <a href="cms-pages.php" class="btn btn-outline">
                    <i class="fas fa-arrow-left"></i>
                    Назад к списку
                </a>
            </div>
        </header>

        <!-- Dashboard Content -->
        <div class="dashboard-content">
            <?php if (!empty($error_message)): ?>
                <div class="alert alert-error">
                    <i class="fas fa-exclamation-triangle"></i>
                    <?php echo htmlspecialchars($error_message); ?>
                </div>
            <?php endif; ?>

            <?php if (!empty($success_message)): ?>
                <div class="alert alert-success">
                    <i class="fas fa-check-circle"></i>
                    <?php echo htmlspecialchars($success_message); ?>
                </div>
            <?php endif; ?>

            <!-- Page Edit Form -->
            <form method="POST" class="cms-page-form">
                <input type="hidden" name="csrf_token" value="<?php echo generate_csrf_token(); ?>">
                
                <div class="form-grid">
                    <!-- Main Content -->
                    <div class="form-main">
                        <div class="form-card">
                            <div class="form-card-header">
                                <h3><i class="fas fa-edit"></i> Основное содержимое</h3>
                            </div>
                            <div class="form-card-body">
                                <div class="form-group">
                                    <label for="title">Название страницы *</label>
                                    <input type="text" id="title" name="title" 
                                           value="<?php echo htmlspecialchars($page_data['title'] ?? ''); ?>" 
                                           required class="form-control">
                                </div>
                                
                                <div class="form-group">
                                    <label for="slug">URL (slug)</label>
                                    <div class="input-group">
                                        <span class="input-group-text"><?php echo rtrim(SITE_URL, '/'); ?>/</span>
                                        <input type="text" id="slug" name="slug" 
                                               value="<?php echo htmlspecialchars($page_data['slug'] ?? ''); ?>" 
                                               class="form-control" placeholder="auto-generate">
                                    </div>
                                    <small class="form-help">Оставьте пустым для автоматической генерации из названия</small>
                                </div>
                                
                                <div class="form-group">
                                    <label for="excerpt">Краткое описание</label>
                                    <textarea id="excerpt" name="excerpt" rows="3" class="form-control"
                                              placeholder="Краткое описание страницы для превью"><?php echo htmlspecialchars($page_data['excerpt'] ?? ''); ?></textarea>
                                </div>
                                
                                <div class="form-group">
                                    <label for="content">Содержимое страницы</label>
                                    <textarea id="content" name="content" class="form-control tinymce-editor"><?php echo $page_data['content'] ?? ''; ?></textarea>
                                    <div id="content-counter" class="form-help" style="margin-top: 8px; text-align: right; font-weight: 500;"></div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- SEO Settings -->
                        <div class="form-card">
                            <div class="form-card-header">
                                <h3><i class="fas fa-search"></i> SEO настройки</h3>
                            </div>
                            <div class="form-card-body">
                                <div class="form-group">
                                    <label for="meta_title">Meta Title</label>
                                    <input type="text" id="meta_title" name="meta_title" 
                                           value="<?php echo htmlspecialchars($page_data['meta_title'] ?? ''); ?>" 
                                           class="form-control" maxlength="60">
                                    <small class="form-help">Рекомендуется до 60 символов</small>
                                </div>
                                
                                <div class="form-group">
                                    <label for="meta_description">Meta Description</label>
                                    <textarea id="meta_description" name="meta_description" rows="3" 
                                              class="form-control" maxlength="160"
                                              placeholder="Описание страницы для поисковых систем"><?php echo htmlspecialchars($page_data['meta_description'] ?? ''); ?></textarea>
                                    <small class="form-help">Рекомендуется до 160 символов</small>
                                </div>
                                
                                <div class="form-group">
                                    <label for="meta_keywords">Ключевые слова</label>
                                    <input type="text" id="meta_keywords" name="meta_keywords" 
                                           value="<?php echo htmlspecialchars($page_data['meta_keywords'] ?? ''); ?>" 
                                           class="form-control" placeholder="ключевое слово, еще одно, третье">
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Sidebar -->
                    <div class="form-sidebar">
                        <!-- Publish Settings -->
                        <div class="form-card">
                            <div class="form-card-header">
                                <h3><i class="fas fa-cog"></i> Настройки публикации</h3>
                            </div>
                            <div class="form-card-body">
                                <div class="form-group">
                                    <label for="status">Статус</label>
                                    <select id="status" name="status" class="form-control">
                                        <option value="draft" <?php echo ($page_data['status'] ?? 'draft') === 'draft' ? 'selected' : ''; ?>>Черновик</option>
                                        <option value="published" <?php echo ($page_data['status'] ?? '') === 'published' ? 'selected' : ''; ?>>Опубликовано</option>
                                        <option value="archived" <?php echo ($page_data['status'] ?? '') === 'archived' ? 'selected' : ''; ?>>Архив</option>
                                    </select>
                                </div>
                                
                                <div class="form-group">
                                    <label for="template">Шаблон</label>
                                    <select id="template" name="template" class="form-control">
                                        <?php foreach ($templates as $template): ?>
                                            <option value="<?php echo htmlspecialchars($template['identifier']); ?>"
                                                    <?php echo ($page_data['template'] ?? 'default') === $template['identifier'] ? 'selected' : ''; ?>>
                                                <?php echo htmlspecialchars($template['name']); ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                                
                                <div class="form-group">
                                    <label for="parent_id">Родительская страница</label>
                                    <select id="parent_id" name="parent_id" class="form-control">
                                        <option value="">Нет родительской страницы</option>
                                        <?php foreach ($parent_pages as $parent): ?>
                                            <option value="<?php echo $parent['id']; ?>"
                                                    <?php echo ($page_data['parent_id'] ?? '') == $parent['id'] ? 'selected' : ''; ?>>
                                                <?php echo htmlspecialchars($parent['title']); ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                                
                                <div class="form-group">
                                    <label for="sort_order">Порядок сортировки</label>
                                    <input type="number" id="sort_order" name="sort_order" 
                                           value="<?php echo intval($page_data['sort_order'] ?? 0); ?>" 
                                           class="form-control" min="0">
                                </div>
                                
                                <div class="form-group">
                                    <label class="checkbox-label">
                                        <input type="checkbox" name="show_in_menu" value="1" 
                                               <?php echo ($page_data['show_in_menu'] ?? 1) ? 'checked' : ''; ?>>
                                        <span class="checkbox-custom"></span>
                                        Показывать в меню
                                    </label>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Featured Image -->
                        <div class="form-card">
                            <div class="form-card-header">
                                <h3><i class="fas fa-image"></i> Изображение</h3>
                            </div>
                            <div class="form-card-body">
                                <div class="form-group">
                                    <label for="featured_image">URL изображения</label>
                                    <input type="url" id="featured_image" name="featured_image" 
                                           value="<?php echo htmlspecialchars($page_data['featured_image'] ?? ''); ?>" 
                                           class="form-control" placeholder="https://example.com/image.jpg">
                                </div>
                                
                                <?php if (!empty($page_data['featured_image'])): ?>
                                    <div class="image-preview">
                                        <img src="<?php echo htmlspecialchars($page_data['featured_image']); ?>" 
                                             alt="Preview" style="max-width: 100%; height: auto;">
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                        
                        <!-- Custom Code -->
                        <div class="form-card">
                            <div class="form-card-header">
                                <h3><i class="fas fa-code"></i> Дополнительный код</h3>
                            </div>
                            <div class="form-card-body">
                                <div class="form-group">
                                    <label for="custom_css">Дополнительный CSS</label>
                                    <textarea id="custom_css" name="custom_css" rows="5" 
                                              class="form-control code-editor"
                                              placeholder="/* Дополнительные стили для этой страницы */"><?php echo htmlspecialchars($page_data['custom_css'] ?? ''); ?></textarea>
                                </div>
                                
                                <div class="form-group">
                                    <label for="custom_js">Дополнительный JavaScript</label>
                                    <textarea id="custom_js" name="custom_js" rows="5" 
                                              class="form-control code-editor"
                                              placeholder="// Дополнительные скрипты для этой страницы"><?php echo htmlspecialchars($page_data['custom_js'] ?? ''); ?></textarea>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Save Button -->
                        <div class="form-actions">
                            <button type="submit" class="btn btn-primary btn-large">
                                <i class="fas fa-save"></i>
                                <?php echo $is_edit ? 'Обновить страницу' : 'Создать страницу'; ?>
                            </button>
                            
                            <?php if ($is_edit && $page_data['status'] === 'published'): ?>
                                <a href="../<?php echo $page_data['slug']; ?>" target="_blank" class="btn btn-outline">
                                    <i class="fas fa-eye"></i>
                                    Просмотр
                                </a>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </main>

    <!-- JavaScript -->
    <script src="../assets/js/admin.js"></script>
    <script>
        // Инициализация расширенного CMS редактора
        document.addEventListener('DOMContentLoaded', function() {
            CMSEditor.init('.tinymce-editor', {
                height: 500,
                // Дополнительные настройки для страниц
                toolbar1: 'undo redo | bold italic underline | formatselect | alignleft aligncenter alignright alignjustify | customSave customPreview',
                toolbar2: 'cut copy paste | searchreplace | bullist numlist | outdent indent blockquote | link unlink image media | table | code fullscreen',
                toolbar3: 'forecolor backcolor | subscript superscript | charmap emoticons | template | removeformat | help'
            });
        });
        
        // Автогенерация slug из заголовка
        document.getElementById('title').addEventListener('input', function() {
            const slugField = document.getElementById('slug');
            if (!slugField.value || slugField.dataset.auto !== 'false') {
                let slug = this.value.toLowerCase()
                    .replace(/[а-я]/g, function(char) {
                        const map = {
                            'а': 'a', 'б': 'b', 'в': 'v', 'г': 'g', 'д': 'd', 'е': 'e', 'ё': 'yo',
                            'ж': 'zh', 'з': 'z', 'и': 'i', 'й': 'y', 'к': 'k', 'л': 'l', 'м': 'm',
                            'н': 'n', 'о': 'o', 'п': 'p', 'р': 'r', 'с': 's', 'т': 't', 'у': 'u',
                            'ф': 'f', 'х': 'h', 'ц': 'ts', 'ч': 'ch', 'ш': 'sh', 'щ': 'sch',
                            'ъ': '', 'ы': 'y', 'ь': '', 'э': 'e', 'ю': 'yu', 'я': 'ya'
                        };
                        return map[char] || char;
                    })
                    .replace(/[^a-z0-9\s-]/g, '')
                    .replace(/\s+/g, '-')
                    .replace(/-+/g, '-')
                    .replace(/^-|-$/g, '');
                
                slugField.value = slug;
            }
        });
        
        // Отслеживание ручного изменения slug
        document.getElementById('slug').addEventListener('input', function() {
            this.dataset.auto = 'false';
        });
        
        // Предпросмотр изображения
        document.getElementById('featured_image').addEventListener('input', function() {
            const url = this.value;
            const preview = document.querySelector('.image-preview');
            
            if (url) {
                if (!preview) {
                    const newPreview = document.createElement('div');
                    newPreview.className = 'image-preview';
                    newPreview.innerHTML = `<img src="${url}" alt="Preview" style="max-width: 100%; height: auto;">`;
                    this.parentNode.appendChild(newPreview);
                } else {
                    preview.querySelector('img').src = url;
                }
            } else if (preview) {
                preview.remove();
            }
        });
        
        // Подсчет символов для SEO полей
        function updateCharCount(fieldId, maxLength) {
            const field = document.getElementById(fieldId);
            const help = field.nextElementSibling;
            
            field.addEventListener('input', function() {
                const current = this.value.length;
                const remaining = maxLength - current;
                const color = remaining < 0 ? 'red' : remaining < 10 ? 'orange' : 'inherit';
                
                help.innerHTML = `${current}/${maxLength} символов <span style="color: ${color}">(${remaining >= 0 ? 'осталось' : 'превышено'}: ${Math.abs(remaining)})</span>`;
            });
        }
        
        updateCharCount('meta_title', 60);
        updateCharCount('meta_description', 160);
    </script>
</body>
</html>
