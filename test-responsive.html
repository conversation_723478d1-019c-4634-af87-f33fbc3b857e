<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Тест адаптивности - AstroGenix</title>
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="assets/css/animations.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .test-container {
            padding: 20px;
            background: #f8f9fa;
            margin: 20px;
            border-radius: 8px;
        }
        .test-section {
            margin-bottom: 40px;
            padding: 20px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-title {
            color: #2ECC71;
            border-bottom: 2px solid #2ECC71;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .viewport-info {
            position: fixed;
            top: 10px;
            right: 10px;
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 10px;
            border-radius: 4px;
            font-size: 12px;
            z-index: 9999;
        }
    </style>
</head>
<body>
    <div class="viewport-info" id="viewportInfo">
        Размер экрана: <span id="screenSize"></span>
    </div>

    <div class="test-container">
        <h1>Тест адаптивности AstroGenix</h1>
        
        <!-- Тест кнопок -->
        <div class="test-section">
            <h2 class="test-title">Кнопки</h2>
            <div style="display: flex; gap: 10px; flex-wrap: wrap;">
                <button class="btn btn-primary">Основная</button>
                <button class="btn btn-outline">Контурная</button>
                <button class="btn btn-secondary">Вторичная</button>
                <button class="btn btn-primary btn-large">Большая</button>
                <button class="btn btn-primary btn-small">Маленькая</button>
            </div>
        </div>

        <!-- Тест сетки -->
        <div class="test-section">
            <h2 class="test-title">Сетка функций</h2>
            <div class="features-grid">
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-seedling"></i>
                    </div>
                    <h3>Эко-технологии</h3>
                    <p>Тест адаптивности карточки функции</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-shield-alt"></i>
                    </div>
                    <h3>Безопасность</h3>
                    <p>Тест адаптивности карточки функции</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <h3>Стабильный доход</h3>
                    <p>Тест адаптивности карточки функции</p>
                </div>
            </div>
        </div>

        <!-- Тест статистики -->
        <div class="test-section">
            <h2 class="test-title">Статистика</h2>
            <div class="hero-stats">
                <div class="stat-item">
                    <div class="stat-number">2,847,500 ₽</div>
                    <div class="stat-label">Общий объем инвестиций</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">15,420</div>
                    <div class="stat-label">Активных пользователей</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">847,250</div>
                    <div class="stat-label">кВт зеленой энергии</div>
                </div>
            </div>
        </div>

        <!-- Тест пакетов -->
        <div class="test-section">
            <h2 class="test-title">Инвестиционные пакеты</h2>
            <div class="packages-grid">
                <div class="package-card">
                    <div class="package-header">
                        <h3 class="package-name">Стартовый</h3>
                        <div class="package-price">1,000 ₽</div>
                        <div class="package-period">минимум</div>
                    </div>
                    <div class="package-body">
                        <ul class="package-features">
                            <li><i class="fas fa-check"></i> Ежедневные выплаты</li>
                            <li><i class="fas fa-check"></i> Минимальный риск</li>
                            <li><i class="fas fa-check"></i> Поддержка 24/7</li>
                        </ul>
                    </div>
                    <div class="package-footer">
                        <button class="btn btn-primary btn-full">Выбрать пакет</button>
                    </div>
                </div>
                <div class="package-card featured">
                    <div class="package-header">
                        <h3 class="package-name">Профессиональный</h3>
                        <div class="package-price">10,000 ₽</div>
                        <div class="package-period">минимум</div>
                    </div>
                    <div class="package-body">
                        <ul class="package-features">
                            <li><i class="fas fa-check"></i> Ежедневные выплаты</li>
                            <li><i class="fas fa-check"></i> Персональный менеджер</li>
                            <li><i class="fas fa-check"></i> Приоритетная поддержка</li>
                        </ul>
                    </div>
                    <div class="package-footer">
                        <button class="btn btn-primary btn-full">Выбрать пакет</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Тест форм -->
        <div class="test-section">
            <h2 class="test-title">Формы</h2>
            <form>
                <div class="form-row">
                    <div class="form-group">
                        <label for="test-email">Email</label>
                        <input type="email" id="test-email" placeholder="Введите email">
                    </div>
                    <div class="form-group">
                        <label for="test-phone">Телефон</label>
                        <input type="tel" id="test-phone" placeholder="Введите телефон">
                    </div>
                </div>
                <div class="form-group">
                    <label for="test-message">Сообщение</label>
                    <textarea id="test-message" rows="4" placeholder="Введите сообщение"></textarea>
                </div>
                <button type="submit" class="btn btn-primary">Отправить</button>
            </form>
        </div>
    </div>

    <script>
        function updateViewportInfo() {
            const width = window.innerWidth;
            const height = window.innerHeight;
            const screenSize = document.getElementById('screenSize');
            
            let deviceType = '';
            if (width <= 480) deviceType = 'Мобильный';
            else if (width <= 768) deviceType = 'Планшет (портрет)';
            else if (width <= 1024) deviceType = 'Планшет (альбом)';
            else if (width <= 1440) deviceType = 'Десктоп';
            else deviceType = 'Большой экран';
            
            screenSize.textContent = `${width}x${height} (${deviceType})`;
        }

        window.addEventListener('resize', updateViewportInfo);
        updateViewportInfo();
    </script>
</body>
</html>
