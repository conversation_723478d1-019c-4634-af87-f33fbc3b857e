<?php
/**
 * AstroGenix - Управление экспортом данных
 * Эко-майнинговая инвестиционная платформа
 */

require_once '../config/config.php';

// Проверка авторизации и прав администратора
require_admin();

$page_title = 'Экспорт данных - Админ-панель';
?>
<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?></title>
    
    <!-- CSS -->
    <link rel="stylesheet" href="../assets/css/style.css">
    <link rel="stylesheet" href="../assets/css/dashboard.css">
    <link rel="stylesheet" href="../assets/css/admin.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body class="dashboard-page admin-page">
    <!-- Admin Sidebar -->
    <?php include 'includes/admin-sidebar.php'; ?>

    <!-- Main Content -->
    <main class="main-content">
        <!-- Header -->
        <header class="dashboard-header">
            <div class="header-left">
                <button class="mobile-menu-toggle" id="mobileMenuToggle">
                    <i class="fas fa-bars"></i>
                </button>
                <h1>Экспорт данных</h1>
            </div>
        </header>

        <!-- Dashboard Content -->
        <div class="dashboard-content">
            <div class="export-intro">
                <div class="card">
                    <div class="card-body">
                        <h3><i class="fas fa-download"></i> Экспорт данных платформы</h3>
                        <p>Выберите тип данных для экспорта и формат файла. Экспорт поддерживает фильтрацию данных и различные форматы вывода.</p>
                    </div>
                </div>
            </div>

            <!-- Export Options -->
            <div class="export-grid">
                <!-- Users Export -->
                <div class="export-card">
                    <div class="card-header">
                        <h3><i class="fas fa-users"></i> Пользователи</h3>
                    </div>
                    <div class="card-body">
                        <p>Экспорт данных пользователей с возможностью фильтрации по статусу и поиску.</p>
                        
                        <form class="export-form" id="usersExportForm">
                            <div class="form-group">
                                <label for="usersSearch">Поиск пользователей:</label>
                                <input type="text" id="usersSearch" name="search" 
                                       placeholder="Имя, email или username">
                            </div>
                            
                            <div class="form-group">
                                <label for="usersStatus">Статус:</label>
                                <select id="usersStatus" name="status">
                                    <option value="">Все пользователи</option>
                                    <option value="active">Активные</option>
                                    <option value="inactive">Неактивные</option>
                                    <option value="admin">Администраторы</option>
                                </select>
                            </div>
                            
                            <div class="form-group">
                                <label for="usersFormat">Формат:</label>
                                <select id="usersFormat" name="format">
                                    <option value="csv">CSV</option>
                                    <option value="json">JSON</option>
                                </select>
                            </div>
                            
                            <button type="button" class="btn btn-primary" onclick="exportData('users')">
                                <i class="fas fa-download"></i>
                                Экспортировать пользователей
                            </button>
                        </form>
                    </div>
                </div>

                <!-- Transactions Export -->
                <div class="export-card">
                    <div class="card-header">
                        <h3><i class="fas fa-exchange-alt"></i> Транзакции</h3>
                    </div>
                    <div class="card-body">
                        <p>Экспорт транзакций с фильтрацией по типу и статусу.</p>
                        
                        <form class="export-form" id="transactionsExportForm">
                            <div class="form-group">
                                <label for="transactionsType">Тип транзакции:</label>
                                <select id="transactionsType" name="type">
                                    <option value="">Все типы</option>
                                    <option value="deposit">Пополнения</option>
                                    <option value="withdrawal">Выводы</option>
                                    <option value="investment">Инвестиции</option>
                                    <option value="profit">Прибыль</option>
                                    <option value="referral_bonus">Реферальные бонусы</option>
                                </select>
                            </div>
                            
                            <div class="form-group">
                                <label for="transactionsStatus">Статус:</label>
                                <select id="transactionsStatus" name="status">
                                    <option value="">Все статусы</option>
                                    <option value="pending">Ожидание</option>
                                    <option value="completed">Завершено</option>
                                    <option value="rejected">Отклонено</option>
                                </select>
                            </div>
                            
                            <div class="form-group">
                                <label for="transactionsFormat">Формат:</label>
                                <select id="transactionsFormat" name="format">
                                    <option value="csv">CSV</option>
                                    <option value="json">JSON</option>
                                </select>
                            </div>
                            
                            <button type="button" class="btn btn-primary" onclick="exportData('transactions')">
                                <i class="fas fa-download"></i>
                                Экспортировать транзакции
                            </button>
                        </form>
                    </div>
                </div>

                <!-- Investments Export -->
                <div class="export-card">
                    <div class="card-header">
                        <h3><i class="fas fa-chart-line"></i> Инвестиции</h3>
                    </div>
                    <div class="card-body">
                        <p>Экспорт данных об инвестициях пользователей.</p>
                        
                        <form class="export-form" id="investmentsExportForm">
                            <div class="form-group">
                                <label for="investmentsFormat">Формат:</label>
                                <select id="investmentsFormat" name="format">
                                    <option value="csv">CSV</option>
                                    <option value="json">JSON</option>
                                </select>
                            </div>
                            
                            <button type="button" class="btn btn-primary" onclick="exportData('investments')">
                                <i class="fas fa-download"></i>
                                Экспортировать инвестиции
                            </button>
                        </form>
                    </div>
                </div>

                <!-- Logs Export -->
                <div class="export-card">
                    <div class="card-header">
                        <h3><i class="fas fa-list"></i> Логи системы</h3>
                    </div>
                    <div class="card-body">
                        <p>Экспорт логов системы с фильтрацией по действиям и датам.</p>
                        
                        <form class="export-form" id="logsExportForm">
                            <div class="form-group">
                                <label for="logsAction">Действие:</label>
                                <input type="text" id="logsAction" name="action" 
                                       placeholder="Поиск по действию">
                            </div>
                            
                            <div class="form-group">
                                <label for="logsIP">IP адрес:</label>
                                <input type="text" id="logsIP" name="ip_address" 
                                       placeholder="IP адрес">
                            </div>
                            
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="logsDateFrom">Дата от:</label>
                                    <input type="date" id="logsDateFrom" name="date_from">
                                </div>
                                
                                <div class="form-group">
                                    <label for="logsDateTo">Дата до:</label>
                                    <input type="date" id="logsDateTo" name="date_to">
                                </div>
                            </div>
                            
                            <div class="form-group">
                                <label for="logsFormat">Формат:</label>
                                <select id="logsFormat" name="format">
                                    <option value="csv">CSV</option>
                                    <option value="json">JSON</option>
                                </select>
                            </div>
                            
                            <button type="button" class="btn btn-primary" onclick="exportData('logs')">
                                <i class="fas fa-download"></i>
                                Экспортировать логи
                            </button>
                        </form>
                    </div>
                </div>

                <!-- Backup Export -->
                <div class="export-card full-width">
                    <div class="card-header">
                        <h3><i class="fas fa-database"></i> Полный экспорт данных</h3>
                    </div>
                    <div class="card-body">
                        <p>Создание полного архива всех данных платформы для резервного копирования.</p>
                        
                        <div class="backup-options">
                            <div class="backup-option">
                                <h4>Быстрый экспорт</h4>
                                <p>Экспорт основных данных (пользователи, транзакции, инвестиции)</p>
                                <button class="btn btn-success" onclick="createQuickBackup()">
                                    <i class="fas fa-rocket"></i>
                                    Быстрый экспорт
                                </button>
                            </div>
                            
                            <div class="backup-option">
                                <h4>Полный экспорт</h4>
                                <p>Экспорт всех данных включая логи и системные настройки</p>
                                <button class="btn btn-primary" onclick="createFullBackup()">
                                    <i class="fas fa-database"></i>
                                    Полный экспорт
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Export Status -->
            <div id="exportStatus" class="export-status" style="display: none;">
                <div class="card">
                    <div class="card-body">
                        <div class="status-content">
                            <div class="status-icon">
                                <i class="fas fa-spinner fa-spin"></i>
                            </div>
                            <div class="status-text">
                                <h4>Подготовка экспорта...</h4>
                                <p>Пожалуйста, подождите. Экспорт данных может занять некоторое время.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- JavaScript -->
    <script src="../assets/js/admin.js"></script>
    <script>
        function exportData(type) {
            const form = document.getElementById(type + 'ExportForm');
            const formData = new FormData(form);
            
            // Показываем статус экспорта
            showExportStatus();
            
            // Строим URL для экспорта
            const params = new URLSearchParams();
            params.set('type', type);
            
            for (let [key, value] of formData.entries()) {
                if (value) {
                    params.set(key, value);
                }
            }
            
            // Запускаем загрузку файла
            const exportUrl = 'export.php?' + params.toString();
            
            // Создаем скрытую ссылку для загрузки
            const link = document.createElement('a');
            link.href = exportUrl;
            link.style.display = 'none';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            
            // Скрываем статус через 3 секунды
            setTimeout(hideExportStatus, 3000);
        }
        
        function createQuickBackup() {
            showExportStatus();
            
            // Экспортируем основные данные
            const types = ['users', 'transactions', 'investments'];
            let completed = 0;
            
            types.forEach(type => {
                const link = document.createElement('a');
                link.href = `export.php?type=${type}&format=json`;
                link.style.display = 'none';
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
                
                completed++;
                if (completed === types.length) {
                    setTimeout(hideExportStatus, 3000);
                }
            });
        }
        
        function createFullBackup() {
            showExportStatus();
            
            // Экспортируем все данные
            const types = ['users', 'transactions', 'investments', 'logs'];
            let completed = 0;
            
            types.forEach(type => {
                const link = document.createElement('a');
                link.href = `export.php?type=${type}&format=json`;
                link.style.display = 'none';
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
                
                completed++;
                if (completed === types.length) {
                    setTimeout(hideExportStatus, 5000);
                }
            });
        }
        
        function showExportStatus() {
            document.getElementById('exportStatus').style.display = 'block';
            document.querySelector('.status-icon i').className = 'fas fa-spinner fa-spin';
            document.querySelector('.status-text h4').textContent = 'Подготовка экспорта...';
            document.querySelector('.status-text p').textContent = 'Пожалуйста, подождите. Экспорт данных может занять некоторое время.';
        }
        
        function hideExportStatus() {
            const statusDiv = document.getElementById('exportStatus');
            document.querySelector('.status-icon i').className = 'fas fa-check-circle';
            document.querySelector('.status-text h4').textContent = 'Экспорт завершен!';
            document.querySelector('.status-text p').textContent = 'Файлы успешно подготовлены для загрузки.';
            
            setTimeout(() => {
                statusDiv.style.display = 'none';
            }, 2000);
        }
    </script>
</body>
</html>
