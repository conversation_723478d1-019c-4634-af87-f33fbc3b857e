<?php
/**
 * AstroGenix - Класс транзакций
 * Эко-майнинговая инвестиционная платформа
 */

class Transaction {
    private $conn;
    private $table_name = "transactions";

    public $id;
    public $user_id;
    public $type;
    public $amount;
    public $status;
    public $description;
    public $admin_note;
    public $processed_by;
    public $processed_at;
    public $created_at;
    public $updated_at;

    public function __construct($db) {
        $this->conn = $db;
    }

    // Создание новой транзакции
    public function create() {
        $query = "INSERT INTO " . $this->table_name . " 
                  SET user_id=:user_id, type=:type, amount=:amount, 
                      status=:status, description=:description";

        $stmt = $this->conn->prepare($query);

        // Очистка данных
        $this->user_id = htmlspecialchars(strip_tags($this->user_id));
        $this->type = htmlspecialchars(strip_tags($this->type));
        $this->amount = htmlspecialchars(strip_tags($this->amount));
        $this->status = htmlspecialchars(strip_tags($this->status));
        $this->description = htmlspecialchars(strip_tags($this->description));

        // Привязка параметров
        $stmt->bindParam(":user_id", $this->user_id);
        $stmt->bindParam(":type", $this->type);
        $stmt->bindParam(":amount", $this->amount);
        $stmt->bindParam(":status", $this->status);
        $stmt->bindParam(":description", $this->description);

        if ($stmt->execute()) {
            $this->id = $this->conn->lastInsertId();
            return true;
        }

        return false;
    }

    // Создание запроса на депозит
    public function createDepositRequest($user_id, $amount, $payment_method, $payment_details = '') {
        $this->user_id = $user_id;
        $this->type = 'deposit';
        $this->amount = $amount;
        $this->status = 'pending';
        $this->description = "Запрос на пополнение через {$payment_method}. Детали: {$payment_details}";

        return $this->create();
    }

    // Создание запроса на вывод
    public function createWithdrawalRequest($user_id, $amount, $payment_method, $payment_details) {
        // Проверка баланса пользователя
        $user = new User($this->conn);
        $user->getUserById($user_id);
        
        if ($user->balance < $amount) {
            return false;
        }

        $this->user_id = $user_id;
        $this->type = 'withdrawal';
        $this->amount = $amount;
        $this->status = 'pending';
        $this->description = "Запрос на вывод через {$payment_method}. Реквизиты: {$payment_details}";

        return $this->create();
    }

    // Обработка транзакции администратором
    public function processTransaction($transaction_id, $status, $admin_id, $admin_note = '') {
        $query = "UPDATE " . $this->table_name . " 
                  SET status=:status, processed_by=:processed_by, 
                      processed_at=NOW(), admin_note=:admin_note 
                  WHERE id=:id";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":status", $status);
        $stmt->bindParam(":processed_by", $admin_id);
        $stmt->bindParam(":admin_note", $admin_note);
        $stmt->bindParam(":id", $transaction_id);

        if ($stmt->execute()) {
            // Если транзакция одобрена, обновляем баланс пользователя
            if ($status === 'approved') {
                $this->updateUserBalance($transaction_id);
            }
            return true;
        }

        return false;
    }

    // Обновление баланса пользователя после одобрения транзакции
    private function updateUserBalance($transaction_id) {
        // Получение данных транзакции
        $query = "SELECT user_id, type, amount FROM " . $this->table_name . " WHERE id = :id";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":id", $transaction_id);
        $stmt->execute();
        
        if ($stmt->rowCount() > 0) {
            $transaction = $stmt->fetch(PDO::FETCH_ASSOC);
            $user = new User($this->conn);
            $user->getUserById($transaction['user_id']);
            
            if ($transaction['type'] === 'deposit') {
                // Пополнение баланса
                $user->updateBalance($transaction['amount'], 'add');
            } elseif ($transaction['type'] === 'withdrawal') {
                // Списание с баланса
                $user->updateBalance($transaction['amount'], 'subtract');
            }
            
            // Обновление статуса на completed
            $update_query = "UPDATE " . $this->table_name . " SET status='completed' WHERE id=:id";
            $update_stmt = $this->conn->prepare($update_query);
            $update_stmt->bindParam(":id", $transaction_id);
            $update_stmt->execute();
        }
    }

    // Получение транзакций пользователя
    public function getUserTransactions($user_id, $limit = 20, $offset = 0, $type = null) {
        $where_clause = "WHERE user_id = :user_id";
        if ($type) {
            $where_clause .= " AND type = :type";
        }
        
        $query = "SELECT * FROM " . $this->table_name . " 
                  {$where_clause} 
                  ORDER BY created_at DESC 
                  LIMIT :limit OFFSET :offset";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":user_id", $user_id);
        $stmt->bindParam(":limit", $limit, PDO::PARAM_INT);
        $stmt->bindParam(":offset", $offset, PDO::PARAM_INT);
        
        if ($type) {
            $stmt->bindParam(":type", $type);
        }

        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    // Получение всех транзакций для админа
    public function getAllTransactions($limit = 50, $offset = 0, $status = null, $type = null) {
        $where_conditions = [];
        $params = [];
        
        if ($status) {
            $where_conditions[] = "status = :status";
            $params[':status'] = $status;
        }
        
        if ($type) {
            $where_conditions[] = "type = :type";
            $params[':type'] = $type;
        }
        
        $where_clause = !empty($where_conditions) ? "WHERE " . implode(" AND ", $where_conditions) : "";
        
        $query = "SELECT t.*, u.username, u.email, u.first_name, u.last_name 
                  FROM " . $this->table_name . " t 
                  JOIN users u ON t.user_id = u.id 
                  {$where_clause} 
                  ORDER BY t.created_at DESC 
                  LIMIT :limit OFFSET :offset";

        $stmt = $this->conn->prepare($query);
        
        foreach ($params as $key => $value) {
            $stmt->bindValue($key, $value);
        }
        
        $stmt->bindParam(":limit", $limit, PDO::PARAM_INT);
        $stmt->bindParam(":offset", $offset, PDO::PARAM_INT);

        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    // Получение статистики транзакций
    public function getTransactionStats($user_id = null) {
        $where_clause = $user_id ? "WHERE user_id = :user_id" : "";
        
        $query = "SELECT 
                    COUNT(*) as total_transactions,
                    SUM(CASE WHEN type = 'deposit' AND status = 'completed' THEN amount ELSE 0 END) as total_deposits,
                    SUM(CASE WHEN type = 'withdrawal' AND status = 'completed' THEN amount ELSE 0 END) as total_withdrawals,
                    SUM(CASE WHEN type = 'profit' AND status = 'completed' THEN amount ELSE 0 END) as total_profits,
                    COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_transactions
                  FROM " . $this->table_name . " 
                  {$where_clause}";

        $stmt = $this->conn->prepare($query);
        
        if ($user_id) {
            $stmt->bindParam(":user_id", $user_id);
        }

        $stmt->execute();
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    // Получение транзакции по ID
    public function getTransactionById($id) {
        $query = "SELECT t.*, u.username, u.email, u.first_name, u.last_name 
                  FROM " . $this->table_name . " t 
                  JOIN users u ON t.user_id = u.id 
                  WHERE t.id = :id";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":id", $id);
        $stmt->execute();

        if ($stmt->rowCount() > 0) {
            $row = $stmt->fetch(PDO::FETCH_ASSOC);
            
            $this->id = $row['id'];
            $this->user_id = $row['user_id'];
            $this->type = $row['type'];
            $this->amount = $row['amount'];
            $this->status = $row['status'];
            $this->description = $row['description'];
            $this->admin_note = $row['admin_note'];
            $this->processed_by = $row['processed_by'];
            $this->processed_at = $row['processed_at'];
            $this->created_at = $row['created_at'];
            $this->updated_at = $row['updated_at'];
            
            return $row;
        }

        return false;
    }

    // Создание автоматической транзакции прибыли
    public function createProfitTransaction($user_id, $amount, $investment_id) {
        $this->user_id = $user_id;
        $this->type = 'profit';
        $this->amount = $amount;
        $this->status = 'completed';
        $this->description = "Ежедневная прибыль от инвестиции #{$investment_id}";

        if ($this->create()) {
            // Автоматически добавляем прибыль к балансу
            $user = new User($this->conn);
            $user->getUserById($user_id);
            $user->updateBalance($amount, 'add');
            
            return true;
        }

        return false;
    }

    // Создание реферальной транзакции
    public function createReferralTransaction($user_id, $amount, $level, $source_transaction_id) {
        $this->user_id = $user_id;
        $this->type = 'referral_bonus';
        $this->amount = $amount;
        $this->status = 'completed';
        $this->description = "Реферальный бонус {$level} уровня от транзакции #{$source_transaction_id}";

        if ($this->create()) {
            // Автоматически добавляем бонус к балансу
            $user = new User($this->conn);
            $user->getUserById($user_id);
            $user->updateBalance($amount, 'add');
            
            return true;
        }

        return false;
    }

    // Отмена транзакции
    public function cancelTransaction($transaction_id, $admin_id, $reason = '') {
        $query = "UPDATE " . $this->table_name . " 
                  SET status='rejected', processed_by=:processed_by, 
                      processed_at=NOW(), admin_note=:admin_note 
                  WHERE id=:id AND status='pending'";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":processed_by", $admin_id);
        $stmt->bindParam(":admin_note", $reason);
        $stmt->bindParam(":id", $transaction_id);

        return $stmt->execute() && $stmt->rowCount() > 0;
    }
}
?>
