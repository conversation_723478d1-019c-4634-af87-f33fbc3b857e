<?php
/**
 * AstroGenix - Тестирование системы
 * Эко-майнинговая инвестиционная платформа
 */

require_once '../config/config.php';
require_once '../classes/Logger.php';
require_once '../classes/Notification.php';

// Проверка авторизации и прав администратора
require_admin();

// Инициализация переменных
$test_results = [];
$error_message = '';
$success_message = '';

try {
    $database = new Database();
    $db = $database->getConnection();
    
    // Обработка запуска тестов
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        if (!verify_csrf_token($_POST['csrf_token'] ?? '')) {
            $error_message = 'Ошибка безопасности. Попробуйте еще раз.';
        } else {
            $test_type = sanitize_input($_POST['test_type'] ?? '');
            
            switch ($test_type) {
                case 'database':
                    $test_results = testDatabase($db);
                    break;
                    
                case 'authentication':
                    $test_results = testAuthentication($db);
                    break;
                    
                case 'notifications':
                    $test_results = testNotifications($db);
                    break;
                    
                case 'logging':
                    $test_results = testLogging($db);
                    break;
                    
                case 'all':
                    $test_results = runAllTests($db);
                    break;
                    
                default:
                    $error_message = 'Неизвестный тип теста.';
                    break;
            }
            
            if (!empty($test_results)) {
                $success_message = 'Тестирование завершено. Проверьте результаты ниже.';
            }
        }
    }
    
} catch (Exception $e) {
    error_log("System test error: " . $e->getMessage());
    $error_message = "Ошибка тестирования: " . $e->getMessage();
}

// Функция тестирования базы данных
function testDatabase($db) {
    $results = [];
    
    // Тест подключения к БД
    try {
        $db->query("SELECT 1");
        $results['database_connection'] = ['status' => 'success', 'message' => 'Подключение к базе данных работает'];
    } catch (Exception $e) {
        $results['database_connection'] = ['status' => 'error', 'message' => 'Ошибка подключения к БД: ' . $e->getMessage()];
    }
    
    // Тест существования таблиц
    $required_tables = ['users', 'investment_packages', 'user_investments', 'transactions', 'system_settings'];
    $missing_tables = [];
    
    foreach ($required_tables as $table) {
        try {
            $stmt = $db->prepare("SHOW TABLES LIKE :table");
            $stmt->bindParam(':table', $table);
            $stmt->execute();
            
            if ($stmt->rowCount() === 0) {
                $missing_tables[] = $table;
            }
        } catch (Exception $e) {
            $missing_tables[] = $table . ' (error: ' . $e->getMessage() . ')';
        }
    }
    
    if (empty($missing_tables)) {
        $results['database_tables'] = ['status' => 'success', 'message' => 'Все необходимые таблицы существуют'];
    } else {
        $results['database_tables'] = ['status' => 'error', 'message' => 'Отсутствуют таблицы: ' . implode(', ', $missing_tables)];
    }
    
    // Тест производительности БД
    $start_time = microtime(true);
    try {
        $db->query("SELECT COUNT(*) FROM users");
        $end_time = microtime(true);
        $query_time = round(($end_time - $start_time) * 1000, 2);
        
        if ($query_time < 100) {
            $results['database_performance'] = ['status' => 'success', 'message' => "Запрос выполнен за {$query_time}мс"];
        } else {
            $results['database_performance'] = ['status' => 'warning', 'message' => "Медленный запрос: {$query_time}мс"];
        }
    } catch (Exception $e) {
        $results['database_performance'] = ['status' => 'error', 'message' => 'Ошибка тестирования производительности: ' . $e->getMessage()];
    }
    
    return $results;
}

// Функция тестирования аутентификации
function testAuthentication($db) {
    $results = [];
    
    // Тест функций аутентификации
    if (function_exists('is_logged_in')) {
        $results['auth_functions'] = ['status' => 'success', 'message' => 'Функции аутентификации загружены'];
    } else {
        $results['auth_functions'] = ['status' => 'error', 'message' => 'Функции аутентификации не найдены'];
    }
    
    // Тест сессий
    if (session_status() === PHP_SESSION_ACTIVE) {
        $results['sessions'] = ['status' => 'success', 'message' => 'Сессии работают корректно'];
    } else {
        $results['sessions'] = ['status' => 'error', 'message' => 'Проблемы с сессиями'];
    }
    
    // Тест CSRF токенов
    if (function_exists('generate_csrf_token')) {
        $token = generate_csrf_token();
        if (!empty($token)) {
            $results['csrf_tokens'] = ['status' => 'success', 'message' => 'CSRF токены генерируются'];
        } else {
            $results['csrf_tokens'] = ['status' => 'error', 'message' => 'Ошибка генерации CSRF токенов'];
        }
    } else {
        $results['csrf_tokens'] = ['status' => 'error', 'message' => 'Функция генерации CSRF токенов не найдена'];
    }
    
    return $results;
}

// Функция тестирования уведомлений
function testNotifications($db) {
    $results = [];
    
    try {
        $notification = new Notification($db);
        
        // Тест создания уведомления
        $test_user_id = $_SESSION['user_id'];
        $created = $notification->create($test_user_id, 'Тестовое уведомление', 'Это тестовое сообщение', 'info');
        
        if ($created) {
            $results['notification_creation'] = ['status' => 'success', 'message' => 'Создание уведомлений работает'];
            
            // Тест получения уведомлений
            $notifications = $notification->getUserNotifications($test_user_id, 1);
            if (!empty($notifications)) {
                $results['notification_retrieval'] = ['status' => 'success', 'message' => 'Получение уведомлений работает'];
                
                // Удаляем тестовое уведомление
                $notification->delete($notifications[0]['id'], $test_user_id);
            } else {
                $results['notification_retrieval'] = ['status' => 'error', 'message' => 'Ошибка получения уведомлений'];
            }
        } else {
            $results['notification_creation'] = ['status' => 'error', 'message' => 'Ошибка создания уведомлений'];
        }
        
    } catch (Exception $e) {
        $results['notification_system'] = ['status' => 'error', 'message' => 'Ошибка системы уведомлений: ' . $e->getMessage()];
    }
    
    return $results;
}

// Функция тестирования логирования
function testLogging($db) {
    $results = [];
    
    try {
        $logger = new Logger($db);
        
        // Тест записи лога
        $logged = $logger->log('system_test', 'Тестирование системы логирования');
        
        if ($logged) {
            $results['logging_creation'] = ['status' => 'success', 'message' => 'Запись логов работает'];
            
            // Тест получения логов
            $logs = $logger->getLogs([], 1);
            if (!empty($logs)) {
                $results['logging_retrieval'] = ['status' => 'success', 'message' => 'Получение логов работает'];
            } else {
                $results['logging_retrieval'] = ['status' => 'error', 'message' => 'Ошибка получения логов'];
            }
        } else {
            $results['logging_creation'] = ['status' => 'error', 'message' => 'Ошибка записи логов'];
        }
        
    } catch (Exception $e) {
        $results['logging_system'] = ['status' => 'error', 'message' => 'Ошибка системы логирования: ' . $e->getMessage()];
    }
    
    return $results;
}

// Функция запуска всех тестов
function runAllTests($db) {
    $all_results = [];
    
    $all_results['database'] = testDatabase($db);
    $all_results['authentication'] = testAuthentication($db);
    $all_results['notifications'] = testNotifications($db);
    $all_results['logging'] = testLogging($db);
    
    return $all_results;
}

$page_title = 'Тестирование системы - Админ-панель';
?>
<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?></title>
    
    <!-- CSS -->
    <link rel="stylesheet" href="../assets/css/style.css">
    <link rel="stylesheet" href="../assets/css/dashboard.css">
    <link rel="stylesheet" href="../assets/css/admin.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body class="dashboard-page admin-page">
    <!-- Admin Sidebar -->
    <?php include 'includes/admin-sidebar.php'; ?>

    <!-- Main Content -->
    <main class="main-content">
        <!-- Header -->
        <header class="dashboard-header">
            <div class="header-left">
                <button class="mobile-menu-toggle" id="mobileMenuToggle">
                    <i class="fas fa-bars"></i>
                </button>
                <h1>Тестирование системы</h1>
            </div>
        </header>

        <!-- Dashboard Content -->
        <div class="dashboard-content">
            <?php if (!empty($error_message)): ?>
                <div class="alert alert-error">
                    <i class="fas fa-exclamation-triangle"></i>
                    <?php echo htmlspecialchars($error_message); ?>
                </div>
            <?php endif; ?>

            <?php if (!empty($success_message)): ?>
                <div class="alert alert-success">
                    <i class="fas fa-check-circle"></i>
                    <?php echo htmlspecialchars($success_message); ?>
                </div>
            <?php endif; ?>

            <!-- Test Controls -->
            <div class="test-controls">
                <div class="card">
                    <div class="card-header">
                        <h3><i class="fas fa-vial"></i> Запуск тестов</h3>
                    </div>
                    <div class="card-body">
                        <p>Выберите тип тестирования для проверки работоспособности компонентов системы.</p>
                        
                        <div class="test-buttons">
                            <form method="POST" style="display: inline;">
                                <input type="hidden" name="csrf_token" value="<?php echo generate_csrf_token(); ?>">
                                <input type="hidden" name="test_type" value="database">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-database"></i>
                                    Тест базы данных
                                </button>
                            </form>
                            
                            <form method="POST" style="display: inline;">
                                <input type="hidden" name="csrf_token" value="<?php echo generate_csrf_token(); ?>">
                                <input type="hidden" name="test_type" value="authentication">
                                <button type="submit" class="btn btn-info">
                                    <i class="fas fa-lock"></i>
                                    Тест аутентификации
                                </button>
                            </form>
                            
                            <form method="POST" style="display: inline;">
                                <input type="hidden" name="csrf_token" value="<?php echo generate_csrf_token(); ?>">
                                <input type="hidden" name="test_type" value="notifications">
                                <button type="submit" class="btn btn-warning">
                                    <i class="fas fa-bell"></i>
                                    Тест уведомлений
                                </button>
                            </form>
                            
                            <form method="POST" style="display: inline;">
                                <input type="hidden" name="csrf_token" value="<?php echo generate_csrf_token(); ?>">
                                <input type="hidden" name="test_type" value="logging">
                                <button type="submit" class="btn btn-secondary">
                                    <i class="fas fa-list"></i>
                                    Тест логирования
                                </button>
                            </form>
                            
                            <form method="POST" style="display: inline;">
                                <input type="hidden" name="csrf_token" value="<?php echo generate_csrf_token(); ?>">
                                <input type="hidden" name="test_type" value="all">
                                <button type="submit" class="btn btn-success btn-large">
                                    <i class="fas fa-play"></i>
                                    Запустить все тесты
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Test Results -->
            <?php if (!empty($test_results)): ?>
                <div class="test-results">
                    <div class="card">
                        <div class="card-header">
                            <h3><i class="fas fa-chart-bar"></i> Результаты тестирования</h3>
                        </div>
                        <div class="card-body">
                            <?php foreach ($test_results as $category => $tests): ?>
                                <div class="test-category">
                                    <h4><?php echo ucfirst($category); ?></h4>
                                    
                                    <?php if (is_array($tests) && isset($tests['status'])): ?>
                                        <!-- Одиночный тест -->
                                        <div class="test-item test-<?php echo $tests['status']; ?>">
                                            <div class="test-icon">
                                                <i class="fas fa-<?php echo $tests['status'] === 'success' ? 'check-circle' : ($tests['status'] === 'warning' ? 'exclamation-triangle' : 'times-circle'); ?>"></i>
                                            </div>
                                            <div class="test-content">
                                                <span class="test-name"><?php echo ucfirst($category); ?></span>
                                                <span class="test-message"><?php echo htmlspecialchars($tests['message']); ?></span>
                                            </div>
                                        </div>
                                    <?php else: ?>
                                        <!-- Группа тестов -->
                                        <?php foreach ($tests as $test_name => $test_result): ?>
                                            <div class="test-item test-<?php echo $test_result['status']; ?>">
                                                <div class="test-icon">
                                                    <i class="fas fa-<?php echo $test_result['status'] === 'success' ? 'check-circle' : ($test_result['status'] === 'warning' ? 'exclamation-triangle' : 'times-circle'); ?>"></i>
                                                </div>
                                                <div class="test-content">
                                                    <span class="test-name"><?php echo ucfirst(str_replace('_', ' ', $test_name)); ?></span>
                                                    <span class="test-message"><?php echo htmlspecialchars($test_result['message']); ?></span>
                                                </div>
                                            </div>
                                        <?php endforeach; ?>
                                    <?php endif; ?>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>
            <?php endif; ?>

            <!-- System Information -->
            <div class="system-info">
                <div class="card">
                    <div class="card-header">
                        <h3><i class="fas fa-info-circle"></i> Информация о системе</h3>
                    </div>
                    <div class="card-body">
                        <div class="info-grid">
                            <div class="info-item">
                                <label>PHP версия:</label>
                                <span><?php echo PHP_VERSION; ?></span>
                            </div>
                            <div class="info-item">
                                <label>Сервер:</label>
                                <span><?php echo $_SERVER['SERVER_SOFTWARE'] ?? 'Неизвестно'; ?></span>
                            </div>
                            <div class="info-item">
                                <label>Память PHP:</label>
                                <span><?php echo ini_get('memory_limit'); ?></span>
                            </div>
                            <div class="info-item">
                                <label>Максимальное время выполнения:</label>
                                <span><?php echo ini_get('max_execution_time'); ?>с</span>
                            </div>
                            <div class="info-item">
                                <label>Загрузка файлов:</label>
                                <span><?php echo ini_get('file_uploads') ? 'Включена' : 'Отключена'; ?></span>
                            </div>
                            <div class="info-item">
                                <label>Максимальный размер файла:</label>
                                <span><?php echo ini_get('upload_max_filesize'); ?></span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- JavaScript -->
    <script src="../assets/js/admin.js"></script>
    <script>
        // Подтверждение перед запуском всех тестов
        document.querySelector('button[type="submit"]:last-of-type').addEventListener('click', function(e) {
            if (!confirm('Запустить все тесты? Это может занять некоторое время.')) {
                e.preventDefault();
            }
        });
    </script>
</body>
</html>
