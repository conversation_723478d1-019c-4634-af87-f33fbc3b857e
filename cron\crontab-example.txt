# AstroGenix - Примеры настройки cron задач
# Скопируйте нужные строки в ваш crontab (crontab -e)

# Автоматическая симуляция активности (каждые 5 минут)
*/5 * * * * /usr/bin/php /path/to/astrogenix/cron/auto-activity.php >> /path/to/astrogenix/logs/cron.log 2>&1

# Ежедневное начисление прибыли по инвестициям (в 00:05)
5 0 * * * /usr/bin/php /path/to/astrogenix/cron/daily-profits.php >> /path/to/astrogenix/logs/cron.log 2>&1

# Еженедельная очистка старых логов (каждое воскресенье в 02:00)
0 2 * * 0 /usr/bin/php /path/to/astrogenix/cron/cleanup-logs.php >> /path/to/astrogenix/logs/cron.log 2>&1

# Ежедневное обновление статистики платформы (в 01:00)
0 1 * * * /usr/bin/php /path/to/astrogenix/cron/update-stats.php >> /path/to/astrogenix/logs/cron.log 2>&1

# Проверка и завершение истекших инвестиций (каждый час)
0 * * * * /usr/bin/php /path/to/astrogenix/cron/check-investments.php >> /path/to/astrogenix/logs/cron.log 2>&1

# Отправка email уведомлений (каждые 15 минут)
*/15 * * * * /usr/bin/php /path/to/astrogenix/cron/send-notifications.php >> /path/to/astrogenix/logs/cron.log 2>&1

# Генерация ежедневных отчетов (в 23:30)
30 23 * * * /usr/bin/php /path/to/astrogenix/cron/daily-reports.php >> /path/to/astrogenix/logs/cron.log 2>&1

# Резервное копирование базы данных (каждый день в 03:00)
0 3 * * * /usr/bin/php /path/to/astrogenix/cron/backup-database.php >> /path/to/astrogenix/logs/cron.log 2>&1

# Примечания:
# 1. Замените /path/to/astrogenix на реальный путь к вашему проекту
# 2. Замените /usr/bin/php на путь к вашему PHP интерпретатору (which php)
# 3. Убедитесь, что директория logs/ существует и доступна для записи
# 4. Для тестирования можете запустить скрипты вручную: php /path/to/script.php

# Формат cron:
# * * * * * команда
# | | | | |
# | | | | +-- День недели (0-7, где 0 и 7 = воскресенье)
# | | | +---- Месяц (1-12)
# | | +------ День месяца (1-31)
# | +-------- Час (0-23)
# +---------- Минута (0-59)

# Специальные значения:
# */5 - каждые 5 единиц
# 0-23 - диапазон от 0 до 23
# 1,15 - в 1 и 15
# * - каждую единицу времени
