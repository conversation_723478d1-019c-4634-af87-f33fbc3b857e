<?php
/**
 * AstroGenix - Общий заголовок
 * Эко-майнинговая инвестиционная платформа
 */

// Проверка сессии
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

if (!defined('SITE_NAME')) {
    require_once 'config/config.php';
}

// Подключение общих функций
require_once 'config/functions.php';
?>
<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="<?php echo SITE_DESCRIPTION; ?>">
    <meta name="keywords" content="инвестиции, эко-майнинг, зеленая энергия, криптовалюта">
    <meta name="author" content="AstroGenix">
    
    <!-- Open Graph -->
    <meta property="og:title" content="<?php echo $page_title ?? SITE_NAME; ?>">
    <meta property="og:description" content="<?php echo SITE_DESCRIPTION; ?>">
    <meta property="og:type" content="website">
    <meta property="og:url" content="<?php echo SITE_URL; ?>">
    <meta property="og:image" content="<?php echo SITE_URL; ?>/assets/images/og-image.jpg">
    
    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="assets/images/favicon.svg">
    <link rel="apple-touch-icon" href="assets/images/logo.svg">
    
    <title><?php echo $page_title ?? SITE_NAME; ?></title>
    
    <!-- CSS -->
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="assets/css/unified-components.css">
    <link rel="stylesheet" href="assets/css/animations.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">

    <!-- Additional CSS -->
    <?php if (isset($additional_css)): ?>
        <?php foreach ($additional_css as $css): ?>
            <link rel="stylesheet" href="<?php echo $css; ?>">
        <?php endforeach; ?>
    <?php endif; ?>
</head>
<body class="<?php echo $body_class ?? ''; ?>">
    <!-- Preloader -->
    <div class="preloader" id="preloader">
        <div class="preloader-content">
            <div class="logo-animation">
                <img src="assets/images/logo.svg" alt="AstroGenix">
            </div>
            <div class="loading-text">Загрузка...</div>
            <div class="loading-bar">
                <div class="loading-progress"></div>
            </div>
        </div>
    </div>

    <!-- Header Navigation -->
    <?php if (!isset($hide_navigation) || !$hide_navigation): ?>
    <header class="header">
        <nav class="navbar">
            <div class="container">
                <div class="nav-brand">
                    <a href="<?php echo is_logged_in() ? 'dashboard.php' : 'index.php'; ?>" class="brand-link">
                        <img src="assets/images/logo.svg" alt="AstroGenix" class="logo">
                        <span class="brand-text">AstroGenix</span>
                    </a>
                </div>

                <?php if (is_logged_in()): ?>
                    <!-- Авторизованное меню -->
                    <div class="nav-menu">
                        <a href="dashboard.php" class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'dashboard.php' ? 'active' : ''; ?>">
                            <i class="fas fa-tachometer-alt"></i>
                            Дашборд
                        </a>
                        <a href="investments.php" class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'investments.php' ? 'active' : ''; ?>">
                            <i class="fas fa-chart-line"></i>
                            Инвестиции
                        </a>
                        <a href="investment-portfolio.php" class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'investment-portfolio.php' ? 'active' : ''; ?>">
                            <i class="fas fa-briefcase"></i>
                            Портфель
                        </a>
                        <a href="leaderboards.php" class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'leaderboards.php' ? 'active' : ''; ?>">
                            <i class="fas fa-trophy"></i>
                            Рейтинги
                        </a>
                        <a href="financial-operations.php" class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'financial-operations.php' ? 'active' : ''; ?>">
                            <i class="fas fa-wallet"></i>
                            Финансы
                        </a>
                        <a href="referrals.php" class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'referrals.php' ? 'active' : ''; ?>">
                            <i class="fas fa-users"></i>
                            Рефералы
                        </a>

                        <!-- Пользовательское меню -->
                        <div class="user-menu dropdown">
                            <button class="user-menu-toggle dropdown-toggle">
                                <div class="user-avatar">
                                    <?php if (isset($_SESSION['avatar']) && $_SESSION['avatar']): ?>
                                        <img src="<?php echo htmlspecialchars($_SESSION['avatar']); ?>" alt="Avatar">
                                    <?php else: ?>
                                        <i class="fas fa-user"></i>
                                    <?php endif; ?>
                                </div>
                                <span class="user-name"><?php echo htmlspecialchars($_SESSION['username'] ?? 'Пользователь'); ?></span>
                                <i class="fas fa-chevron-down"></i>
                            </button>
                            <div class="dropdown-menu">
                                <a href="profile.php" class="dropdown-item">
                                    <i class="fas fa-user-circle"></i>
                                    Профиль
                                </a>
                                <a href="settings.php" class="dropdown-item">
                                    <i class="fas fa-cog"></i>
                                    Настройки
                                </a>
                                <div class="dropdown-divider"></div>
                                <a href="logout.php" class="dropdown-item">
                                    <i class="fas fa-sign-out-alt"></i>
                                    Выйти
                                </a>
                            </div>
                        </div>
                    </div>
                <?php else: ?>
                    <!-- Гостевое меню -->
                    <div class="nav-menu">
                        <a href="features.php" class="nav-link">Возможности</a>
                        <a href="packages.php" class="nav-link">Тарифы</a>
                        <a href="about.php" class="nav-link">О нас</a>
                        <a href="contact.php" class="nav-link">Контакты</a>
                        <a href="login.php" class="btn btn-outline">Войти</a>
                        <a href="register.php" class="btn btn-primary">Регистрация</a>
                    </div>
                <?php endif; ?>

                <!-- Мобильное меню -->
                <button class="mobile-menu-toggle" id="mobile-menu-toggle">
                    <span></span>
                    <span></span>
                    <span></span>
                </button>
            </div>
        </nav>

        <!-- Мобильная навигация -->
        <div class="mobile-nav" id="mobile-nav">
            <div class="mobile-nav-content">
                <?php if (is_logged_in()): ?>
                    <a href="dashboard.php" class="mobile-nav-link">
                        <i class="fas fa-tachometer-alt"></i>
                        Дашборд
                    </a>
                    <a href="investments.php" class="mobile-nav-link">
                        <i class="fas fa-chart-line"></i>
                        Инвестиции
                    </a>
                    <a href="investment-portfolio.php" class="mobile-nav-link">
                        <i class="fas fa-briefcase"></i>
                        Портфель
                    </a>
                    <a href="leaderboards.php" class="mobile-nav-link">
                        <i class="fas fa-trophy"></i>
                        Рейтинги
                    </a>
                    <a href="financial-operations.php" class="mobile-nav-link">
                        <i class="fas fa-wallet"></i>
                        Финансы
                    </a>
                    <a href="referrals.php" class="mobile-nav-link">
                        <i class="fas fa-users"></i>
                        Рефералы
                    </a>
                    <div class="mobile-nav-divider"></div>
                    <a href="profile.php" class="mobile-nav-link">
                        <i class="fas fa-user-circle"></i>
                        Профиль
                    </a>
                    <a href="settings.php" class="mobile-nav-link">
                        <i class="fas fa-cog"></i>
                        Настройки
                    </a>
                    <a href="logout.php" class="mobile-nav-link">
                        <i class="fas fa-sign-out-alt"></i>
                        Выйти
                    </a>
                <?php else: ?>
                    <a href="features.php" class="mobile-nav-link">Возможности</a>
                    <a href="packages.php" class="mobile-nav-link">Тарифы</a>
                    <a href="about.php" class="mobile-nav-link">О нас</a>
                    <a href="contact.php" class="mobile-nav-link">Контакты</a>
                    <div class="mobile-nav-divider"></div>
                    <a href="login.php" class="mobile-nav-link">Войти</a>
                    <a href="register.php" class="mobile-nav-link">Регистрация</a>
                <?php endif; ?>
            </div>
        </div>
    </header>
    <?php endif; ?>
