<?php
/**
 * AstroGenix - Управление транзакциями
 * Эко-майнинговая инвестиционная платформа
 */

require_once '../config/config.php';

// Проверка авторизации и прав администратора
require_admin();

// Параметры фильтрации и пагинации
$type_filter = sanitize_input($_GET['type'] ?? '');
$status_filter = sanitize_input($_GET['status'] ?? '');
$page = max(1, intval($_GET['page'] ?? 1));
$per_page = 20;
$offset = ($page - 1) * $per_page;

// Инициализация переменных
$transactions = [];
$total_transactions = 0;
$error_message = '';
$success_message = '';

try {
    $database = new Database();
    $db = $database->getConnection();
    
    // Обработка действий с транзакциями
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        if (!verify_csrf_token($_POST['csrf_token'] ?? '')) {
            $error_message = 'Ошибка безопасности. Попробуйте еще раз.';
        } else {
            $action = sanitize_input($_POST['action'] ?? '');
            $transaction_id = intval($_POST['transaction_id'] ?? 0);
            $admin_note = sanitize_input($_POST['admin_note'] ?? '');
            
            if (in_array($action, ['approve', 'reject']) && $transaction_id > 0) {
                // Получение данных транзакции
                $transaction_query = "SELECT * FROM transactions WHERE id = :id";
                $transaction_stmt = $db->prepare($transaction_query);
                $transaction_stmt->bindParam(':id', $transaction_id, PDO::PARAM_INT);
                $transaction_stmt->execute();
                $transaction = $transaction_stmt->fetch(PDO::FETCH_ASSOC);
                
                if ($transaction) {
                    $new_status = $action === 'approve' ? 'completed' : 'rejected';
                    
                    // Начало транзакции
                    $db->beginTransaction();
                    
                    try {
                        // Обновление статуса транзакции
                        $update_query = "UPDATE transactions 
                                        SET status = :status, admin_note = :admin_note, 
                                            processed_by = :admin_id, processed_at = NOW() 
                                        WHERE id = :id";
                        $update_stmt = $db->prepare($update_query);
                        $update_stmt->bindParam(':status', $new_status);
                        $update_stmt->bindParam(':admin_note', $admin_note);
                        $update_stmt->bindParam(':admin_id', $_SESSION['user_id'], PDO::PARAM_INT);
                        $update_stmt->bindParam(':id', $transaction_id, PDO::PARAM_INT);
                        $update_stmt->execute();
                        
                        // Если одобряем депозит, обновляем баланс пользователя
                        if ($action === 'approve' && $transaction['type'] === 'deposit') {
                            $balance_query = "UPDATE users SET balance = balance + :amount WHERE id = :user_id";
                            $balance_stmt = $db->prepare($balance_query);
                            $balance_stmt->bindParam(':amount', $transaction['amount']);
                            $balance_stmt->bindParam(':user_id', $transaction['user_id'], PDO::PARAM_INT);
                            $balance_stmt->execute();
                        }
                        
                        // Если одобряем вывод, списываем с баланса
                        if ($action === 'approve' && $transaction['type'] === 'withdrawal') {
                            $balance_query = "UPDATE users SET balance = balance - :amount WHERE id = :user_id";
                            $balance_stmt = $db->prepare($balance_query);
                            $balance_stmt->bindParam(':amount', $transaction['amount']);
                            $balance_stmt->bindParam(':user_id', $transaction['user_id'], PDO::PARAM_INT);
                            $balance_stmt->execute();
                        }
                        
                        $db->commit();
                        $success_message = 'Транзакция успешно ' . ($action === 'approve' ? 'одобрена' : 'отклонена') . '.';
                        
                    } catch (Exception $e) {
                        $db->rollback();
                        $error_message = 'Ошибка при обработке транзакции: ' . $e->getMessage();
                    }
                } else {
                    $error_message = 'Транзакция не найдена.';
                }
            }
        }
    }
    
    // Построение запроса для получения транзакций
    $where_conditions = [];
    $params = [];
    
    if (!empty($type_filter)) {
        $where_conditions[] = "t.type = :type";
        $params[':type'] = $type_filter;
    }
    
    if (!empty($status_filter)) {
        $where_conditions[] = "t.status = :status";
        $params[':status'] = $status_filter;
    }
    
    $where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';
    
    // Получение общего количества транзакций
    $count_query = "SELECT COUNT(*) as total FROM transactions t $where_clause";
    $count_stmt = $db->prepare($count_query);
    foreach ($params as $key => $value) {
        $count_stmt->bindValue($key, $value);
    }
    $count_stmt->execute();
    $total_transactions = $count_stmt->fetch(PDO::FETCH_ASSOC)['total'];
    
    // Получение транзакций с пагинацией
    $transactions_query = "SELECT t.*, 
                                  u.username, u.first_name, u.last_name, u.email,
                                  admin.username as admin_username
                           FROM transactions t 
                           JOIN users u ON t.user_id = u.id 
                           LEFT JOIN users admin ON t.processed_by = admin.id
                           $where_clause 
                           ORDER BY t.created_at DESC 
                           LIMIT :limit OFFSET :offset";
    
    $transactions_stmt = $db->prepare($transactions_query);
    foreach ($params as $key => $value) {
        $transactions_stmt->bindValue($key, $value);
    }
    $transactions_stmt->bindValue(':limit', $per_page, PDO::PARAM_INT);
    $transactions_stmt->bindValue(':offset', $offset, PDO::PARAM_INT);
    $transactions_stmt->execute();
    $transactions = $transactions_stmt->fetchAll(PDO::FETCH_ASSOC);
    
} catch (Exception $e) {
    error_log("Admin transactions error: " . $e->getMessage());
    $error_message = "Ошибка загрузки данных. Попробуйте обновить страницу.";
}

$total_pages = ceil($total_transactions / $per_page);
$page_title = 'Управление транзакциями - Админ-панель';
?>
<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?></title>
    
    <!-- CSS -->
    <link rel="stylesheet" href="../assets/css/style.css">
    <link rel="stylesheet" href="../assets/css/dashboard.css">
    <link rel="stylesheet" href="../assets/css/admin.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body class="dashboard-page admin-page">
    <!-- Admin Sidebar -->
    <?php include 'includes/admin-sidebar.php'; ?>

    <!-- Main Content -->
    <main class="main-content">
        <!-- Header -->
        <header class="dashboard-header">
            <div class="header-left">
                <button class="mobile-menu-toggle" id="mobileMenuToggle">
                    <i class="fas fa-bars"></i>
                </button>
                <h1>Управление транзакциями</h1>
            </div>
            <div class="header-right">
                <div class="header-stats">
                    <span class="stat-item">
                        <i class="fas fa-exchange-alt"></i>
                        Всего: <?php echo number_format($total_transactions); ?>
                    </span>
                </div>
            </div>
        </header>

        <!-- Dashboard Content -->
        <div class="dashboard-content">
            <?php if (!empty($error_message)): ?>
                <div class="alert alert-error">
                    <i class="fas fa-exclamation-triangle"></i>
                    <?php echo htmlspecialchars($error_message); ?>
                </div>
            <?php endif; ?>

            <?php if (!empty($success_message)): ?>
                <div class="alert alert-success">
                    <i class="fas fa-check-circle"></i>
                    <?php echo htmlspecialchars($success_message); ?>
                </div>
            <?php endif; ?>

            <!-- Filters -->
            <div class="admin-filters">
                <form method="GET" class="filters-form">
                    <div class="filter-group">
                        <select name="type" class="filter-select">
                            <option value="">Все типы</option>
                            <option value="deposit" <?php echo $type_filter === 'deposit' ? 'selected' : ''; ?>>Пополнения</option>
                            <option value="withdrawal" <?php echo $type_filter === 'withdrawal' ? 'selected' : ''; ?>>Выводы</option>
                            <option value="investment" <?php echo $type_filter === 'investment' ? 'selected' : ''; ?>>Инвестиции</option>
                            <option value="profit" <?php echo $type_filter === 'profit' ? 'selected' : ''; ?>>Прибыль</option>
                            <option value="referral_bonus" <?php echo $type_filter === 'referral_bonus' ? 'selected' : ''; ?>>Реферальные бонусы</option>
                        </select>
                    </div>
                    <div class="filter-group">
                        <select name="status" class="filter-select">
                            <option value="">Все статусы</option>
                            <option value="pending" <?php echo $status_filter === 'pending' ? 'selected' : ''; ?>>Ожидание</option>
                            <option value="completed" <?php echo $status_filter === 'completed' ? 'selected' : ''; ?>>Завершено</option>
                            <option value="rejected" <?php echo $status_filter === 'rejected' ? 'selected' : ''; ?>>Отклонено</option>
                        </select>
                    </div>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-filter"></i>
                        Фильтр
                    </button>
                    <a href="transactions.php" class="btn btn-outline">
                        <i class="fas fa-times"></i>
                        Сбросить
                    </a>
                </form>
            </div>

            <!-- Transactions Table -->
            <div class="admin-table-card">
                <div class="table-header">
                    <h3><i class="fas fa-exchange-alt"></i> Транзакции</h3>
                    <div class="table-actions">
                        <button class="btn btn-primary" onclick="exportTransactions()">
                            <i class="fas fa-download"></i>
                            Экспорт
                        </button>
                    </div>
                </div>
                <div class="table-container">
                    <?php if (empty($transactions)): ?>
                        <div class="empty-state">
                            <i class="fas fa-exchange-alt"></i>
                            <p>Транзакции не найдены</p>
                        </div>
                    <?php else: ?>
                        <table class="admin-table">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Пользователь</th>
                                    <th>Тип</th>
                                    <th>Сумма</th>
                                    <th>Статус</th>
                                    <th>Дата создания</th>
                                    <th>Обработано</th>
                                    <th>Действия</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($transactions as $transaction): ?>
                                    <tr>
                                        <td><?php echo $transaction['id']; ?></td>
                                        <td>
                                            <div class="user-info">
                                                <strong><?php echo htmlspecialchars($transaction['first_name'] . ' ' . $transaction['last_name']); ?></strong>
                                                <small>@<?php echo htmlspecialchars($transaction['username']); ?></small>
                                            </div>
                                        </td>
                                        <td>
                                            <span class="transaction-type type-<?php echo $transaction['type']; ?>">
                                                <?php 
                                                $types = [
                                                    'deposit' => 'Пополнение',
                                                    'withdrawal' => 'Вывод',
                                                    'investment' => 'Инвестиция',
                                                    'profit' => 'Прибыль',
                                                    'referral_bonus' => 'Реферальный бонус'
                                                ];
                                                echo $types[$transaction['type']] ?? $transaction['type'];
                                                ?>
                                            </span>
                                        </td>
                                        <td>
                                            <span class="amount-value"><?php echo format_currency($transaction['amount']); ?></span>
                                        </td>
                                        <td>
                                            <span class="status-badge status-<?php echo $transaction['status']; ?>">
                                                <?php 
                                                $statuses = [
                                                    'pending' => 'Ожидание',
                                                    'completed' => 'Завершено',
                                                    'rejected' => 'Отклонено'
                                                ];
                                                echo $statuses[$transaction['status']] ?? $transaction['status'];
                                                ?>
                                            </span>
                                        </td>
                                        <td><?php echo date('d.m.Y H:i', strtotime($transaction['created_at'])); ?></td>
                                        <td>
                                            <?php if ($transaction['processed_at']): ?>
                                                <div class="processed-info">
                                                    <small><?php echo date('d.m.Y H:i', strtotime($transaction['processed_at'])); ?></small>
                                                    <?php if ($transaction['admin_username']): ?>
                                                        <small>@<?php echo htmlspecialchars($transaction['admin_username']); ?></small>
                                                    <?php endif; ?>
                                                </div>
                                            <?php else: ?>
                                                <span class="text-muted">—</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php if ($transaction['status'] === 'pending' && in_array($transaction['type'], ['deposit', 'withdrawal'])): ?>
                                                <div class="action-buttons">
                                                    <button class="btn btn-small btn-success" 
                                                            onclick="processTransaction(<?php echo $transaction['id']; ?>, 'approve')">
                                                        <i class="fas fa-check"></i>
                                                    </button>
                                                    <button class="btn btn-small btn-danger" 
                                                            onclick="processTransaction(<?php echo $transaction['id']; ?>, 'reject')">
                                                        <i class="fas fa-times"></i>
                                                    </button>
                                                </div>
                                            <?php else: ?>
                                                <span class="text-muted">—</span>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    <?php endif; ?>
                </div>
                
                <!-- Pagination -->
                <?php if ($total_pages > 1): ?>
                    <div class="pagination">
                        <?php if ($page > 1): ?>
                            <a href="?page=<?php echo $page - 1; ?>&type=<?php echo urlencode($type_filter); ?>&status=<?php echo urlencode($status_filter); ?>" 
                               class="pagination-btn">
                                <i class="fas fa-chevron-left"></i>
                            </a>
                        <?php endif; ?>
                        
                        <?php for ($i = max(1, $page - 2); $i <= min($total_pages, $page + 2); $i++): ?>
                            <a href="?page=<?php echo $i; ?>&type=<?php echo urlencode($type_filter); ?>&status=<?php echo urlencode($status_filter); ?>" 
                               class="pagination-btn <?php echo $i === $page ? 'active' : ''; ?>">
                                <?php echo $i; ?>
                            </a>
                        <?php endfor; ?>
                        
                        <?php if ($page < $total_pages): ?>
                            <a href="?page=<?php echo $page + 1; ?>&type=<?php echo urlencode($type_filter); ?>&status=<?php echo urlencode($status_filter); ?>" 
                               class="pagination-btn">
                                <i class="fas fa-chevron-right"></i>
                            </a>
                        <?php endif; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </main>

    <!-- Process Transaction Modal -->
    <div id="processTransactionModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="modalTitle">Обработка транзакции</h3>
                <button class="modal-close" onclick="closeProcessModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <form id="processTransactionForm" method="POST">
                <input type="hidden" name="csrf_token" value="<?php echo generate_csrf_token(); ?>">
                <input type="hidden" name="transaction_id" id="transactionId">
                <input type="hidden" name="action" id="transactionAction">
                
                <div class="modal-body">
                    <div class="form-group">
                        <label for="adminNote">Примечание администратора</label>
                        <textarea id="adminNote" name="admin_note" rows="3" 
                                  placeholder="Комментарий к обработке транзакции"></textarea>
                    </div>
                </div>
                
                <div class="modal-footer">
                    <button type="button" class="btn btn-outline" onclick="closeProcessModal()">Отмена</button>
                    <button type="submit" class="btn btn-primary" id="modalSubmit">Подтвердить</button>
                </div>
            </form>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="../assets/js/admin.js"></script>
    <script>
        function processTransaction(transactionId, action) {
            document.getElementById('transactionId').value = transactionId;
            document.getElementById('transactionAction').value = action;
            
            const modal = document.getElementById('processTransactionModal');
            const title = document.getElementById('modalTitle');
            const submit = document.getElementById('modalSubmit');
            
            if (action === 'approve') {
                title.textContent = 'Одобрить транзакцию';
                submit.textContent = 'Одобрить';
                submit.className = 'btn btn-success';
            } else {
                title.textContent = 'Отклонить транзакцию';
                submit.textContent = 'Отклонить';
                submit.className = 'btn btn-danger';
            }
            
            modal.style.display = 'block';
        }
        
        function closeProcessModal() {
            document.getElementById('processTransactionModal').style.display = 'none';
        }
        
        function exportTransactions() {
            const params = new URLSearchParams(window.location.search);
            params.set('export', '1');
            window.location.href = 'export.php?type=transactions&' + params.toString();
        }
    </script>
</body>
</html>
