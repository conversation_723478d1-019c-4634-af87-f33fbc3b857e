<?php
/**
 * AstroGenix - Административная боковая панель
 * Эко-майнинговая инвестиционная платформа
 */

// Определение текущей страницы для подсветки активного пункта меню
$current_page = basename($_SERVER['PHP_SELF'], '.php');
?>

<aside class="sidebar admin-sidebar">
    <div class="sidebar-header">
        <div class="sidebar-logo">
            <img src="../assets/images/logo.png" alt="AstroGenix">
            <span>AstroGenix Admin</span>
        </div>
        <button class="sidebar-toggle" id="sidebarToggle">
            <i class="fas fa-bars"></i>
        </button>
    </div>
    
    <nav class="sidebar-nav">
        <ul class="nav-list">
            <li class="nav-section">
                <span class="section-title">Основное</span>
            </li>
            <li class="nav-item <?php echo $current_page === 'dashboard' ? 'active' : ''; ?>">
                <a href="dashboard.php" class="nav-link">
                    <i class="fas fa-tachometer-alt"></i>
                    <span>Панель управления</span>
                </a>
            </li>
            
            <li class="nav-section">
                <span class="section-title">Пользователи</span>
            </li>
            <li class="nav-item <?php echo $current_page === 'users' ? 'active' : ''; ?>">
                <a href="users.php" class="nav-link">
                    <i class="fas fa-users"></i>
                    <span>Управление пользователями</span>
                </a>
            </li>
            <li class="nav-item <?php echo $current_page === 'user-activity' ? 'active' : ''; ?>">
                <a href="user-activity.php" class="nav-link">
                    <i class="fas fa-chart-bar"></i>
                    <span>Активность пользователей</span>
                </a>
            </li>
            
            <li class="nav-section">
                <span class="section-title">Финансы</span>
            </li>
            <li class="nav-item <?php echo $current_page === 'transactions' ? 'active' : ''; ?>">
                <a href="transactions.php" class="nav-link">
                    <i class="fas fa-exchange-alt"></i>
                    <span>Транзакции</span>
                    <?php
                    // Получение количества ожидающих транзакций
                    try {
                        $database = new Database();
                        $db = $database->getConnection();
                        $pending_query = "SELECT COUNT(*) as pending_count FROM transactions WHERE status = 'pending'";
                        $pending_stmt = $db->prepare($pending_query);
                        $pending_stmt->execute();
                        $pending_result = $pending_stmt->fetch(PDO::FETCH_ASSOC);
                        $pending_count = $pending_result['pending_count'];
                        
                        if ($pending_count > 0) {
                            echo '<span class="nav-badge">' . $pending_count . '</span>';
                        }
                    } catch (Exception $e) {
                        // Игнорируем ошибки
                    }
                    ?>
                </a>
            </li>
            <li class="nav-item <?php echo $current_page === 'deposits' ? 'active' : ''; ?>">
                <a href="deposits.php" class="nav-link">
                    <i class="fas fa-plus-circle"></i>
                    <span>Депозиты</span>
                </a>
            </li>
            <li class="nav-item <?php echo $current_page === 'withdrawals' ? 'active' : ''; ?>">
                <a href="withdrawals.php" class="nav-link">
                    <i class="fas fa-minus-circle"></i>
                    <span>Выводы</span>
                </a>
            </li>
            <li class="nav-item <?php echo $current_page === 'finance' ? 'active' : ''; ?>">
                <a href="finance.php" class="nav-link">
                    <i class="fas fa-chart-pie"></i>
                    <span>Финансовая отчетность</span>
                </a>
            </li>
            
            <li class="nav-section">
                <span class="section-title">Инвестиции</span>
            </li>
            <li class="nav-item <?php echo $current_page === 'investments' ? 'active' : ''; ?>">
                <a href="investments.php" class="nav-link">
                    <i class="fas fa-chart-line"></i>
                    <span>Инвестиции</span>
                </a>
            </li>
            <li class="nav-item <?php echo $current_page === 'packages' ? 'active' : ''; ?>">
                <a href="packages.php" class="nav-link">
                    <i class="fas fa-box"></i>
                    <span>Инвестиционные пакеты</span>
                </a>
            </li>
            <li class="nav-item <?php echo $current_page === 'profits' ? 'active' : ''; ?>">
                <a href="profits.php" class="nav-link">
                    <i class="fas fa-coins"></i>
                    <span>Начисление прибыли</span>
                </a>
            </li>
            
            <li class="nav-section">
                <span class="section-title">Маркетинг</span>
            </li>
            <li class="nav-item <?php echo $current_page === 'referrals' ? 'active' : ''; ?>">
                <a href="referrals.php" class="nav-link">
                    <i class="fas fa-users-cog"></i>
                    <span>Реферальная программа</span>
                </a>
            </li>
            <li class="nav-item <?php echo $current_page === 'promotions' ? 'active' : ''; ?>">
                <a href="promotions.php" class="nav-link">
                    <i class="fas fa-gift"></i>
                    <span>Акции и бонусы</span>
                </a>
            </li>
            <li class="nav-item <?php echo $current_page === 'notifications' ? 'active' : ''; ?>">
                <a href="notifications.php" class="nav-link">
                    <i class="fas fa-bell"></i>
                    <span>Уведомления</span>
                </a>
            </li>
            
            <li class="nav-section">
                <span class="section-title">Экология</span>
            </li>
            <li class="nav-item <?php echo $current_page === 'ecology' ? 'active' : ''; ?>">
                <a href="ecology.php" class="nav-link">
                    <i class="fas fa-leaf"></i>
                    <span>Зеленая энергия</span>
                </a>
            </li>
            <li class="nav-item <?php echo $current_page === 'eco-projects' ? 'active' : ''; ?>">
                <a href="eco-projects.php" class="nav-link">
                    <i class="fas fa-seedling"></i>
                    <span>Эко-проекты</span>
                </a>
            </li>
            
            <li class="nav-section">
                <span class="section-title">Система</span>
            </li>
            <li class="nav-item <?php echo $current_page === 'settings' ? 'active' : ''; ?>">
                <a href="settings.php" class="nav-link">
                    <i class="fas fa-cog"></i>
                    <span>Настройки системы</span>
                </a>
            </li>
            <li class="nav-item <?php echo $current_page === 'logs' ? 'active' : ''; ?>">
                <a href="logs.php" class="nav-link">
                    <i class="fas fa-file-alt"></i>
                    <span>Логи системы</span>
                </a>
            </li>
            <li class="nav-item <?php echo $current_page === 'backup' ? 'active' : ''; ?>">
                <a href="backup.php" class="nav-link">
                    <i class="fas fa-database"></i>
                    <span>Резервное копирование</span>
                </a>
            </li>

            <li class="nav-section">
                <span class="section-title">Инструменты</span>
            </li>
            <li class="nav-item <?php echo $current_page === 'simulate-activity' ? 'active' : ''; ?>">
                <a href="simulate-activity.php" class="nav-link">
                    <i class="fas fa-magic"></i>
                    <span>Симуляция активности</span>
                </a>
            </li>

            <li class="nav-section">
                <span class="section-title">Поддержка</span>
            </li>
            <li class="nav-item <?php echo $current_page === 'support' ? 'active' : ''; ?>">
                <a href="support.php" class="nav-link">
                    <i class="fas fa-headset"></i>
                    <span>Тикеты поддержки</span>
                    <?php
                    // Получение количества открытых тикетов
                    try {
                        $tickets_query = "SELECT COUNT(*) as open_tickets FROM support_tickets WHERE status = 'open'";
                        $tickets_stmt = $db->prepare($tickets_query);
                        $tickets_stmt->execute();
                        $tickets_result = $tickets_stmt->fetch(PDO::FETCH_ASSOC);
                        $open_tickets = $tickets_result['open_tickets'];
                        
                        if ($open_tickets > 0) {
                            echo '<span class="nav-badge nav-badge-warning">' . $open_tickets . '</span>';
                        }
                    } catch (Exception $e) {
                        // Игнорируем ошибки
                    }
                    ?>
                </a>
            </li>
            <li class="nav-item <?php echo $current_page === 'reports' ? 'active' : ''; ?>">
                <a href="reports.php" class="nav-link">
                    <i class="fas fa-chart-area"></i>
                    <span>Отчеты</span>
                </a>
            </li>
        </ul>
    </nav>
    
    <div class="sidebar-footer">
        <div class="admin-quick-stats">
            <div class="quick-stat">
                <i class="fas fa-users"></i>
                <span>Пользователи: <?php echo number_format($stats['total_users'] ?? 0); ?></span>
            </div>
            <div class="quick-stat">
                <i class="fas fa-coins"></i>
                <span>Инвестиций: <?php echo format_currency($stats['total_invested'] ?? 0); ?></span>
            </div>
        </div>
        
        <div class="admin-actions">
            <a href="../dashboard.php" class="admin-action" title="Пользовательская панель">
                <i class="fas fa-user"></i>
                <span>Пользователь</span>
            </a>
            <a href="../logout.php" class="admin-action logout" title="Выйти">
                <i class="fas fa-sign-out-alt"></i>
                <span>Выйти</span>
            </a>
        </div>
    </div>
</aside>
